package com.rjsoft.outPatient;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.utils.ObjectUtils;
import com.rjsoft.outPatient.Unitl.GzdService;
import com.rjsoft.outPatient.common.RestUtils;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.reception.Req;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugToHospitalMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.OldRecipeDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SysFunctionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.SystemTbPubItemsRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.impl.RecipeRepositoryImpl;
import com.rjsoft.outPatient.macomm.massert.MAssert;
import com.rjsoft.outPatient.macomm.ret.RetMsg;
import javafx.util.Pair;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
class ApplicationTests {

    //@Autowired
    private RestUtils restUtils;
    //@Autowired
    private GzdService gzdService;

    @Autowired
    SystemTbPubItemsRepository systemTbPubItemsRepository;

    @Autowired
    RecipeRepositoryImpl recipeRepository;

    @Autowired
    SysFunctionMapper sysFunctionMapper;
    @Autowired
    RecipeDetailMapper recipeDetailMapper;

    @Autowired
    OldRecipeDetailMapper oldRecipeDetailMapper;

    @Autowired
    DrugToHospitalMapper drugToHospitalMapper;

    @Test
    void getChargeItemByIdIsStoppedTest() {
        //TyFuture tyFuture = new TyFuture(20, true);
        //for (int j = 0; j < 10; j++) {
        //    tyFuture.addJob("his1", () -> {
        //        for (int i = 0; i < 10; i++) {
        //            recipeRepository.getRecipeById(200004546L, 1);
        //        }
        //        return null;
        //    });
        //}
        //
        //for (int j = 0; j < 10; j++) {
        //    tyFuture.addJob("zxhis1", () -> {
        //        for (int i = 0; i < 10; i++) {
        //            recipeRepository.getItemCodeTail(1286);
        //
        //        }
        //        return null;
        //    });
        //}
        //
        //
        //tyFuture.runAll();
        //tyFuture.printRunInfo("end");
        //tyFuture.close();


    }

    @Test
    void preRecipeTest() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
//        RetMsg ret = RespConst.CommErr.COMM_ERR;
//        RetMsg commRet = new RetMsg(ret.getCode(), "校验失败，未加载到处方明细", RetType.FAIL_DLG);
//        RetMsg noRecipeRet = new RetMsg(ret.getCode(), "已停用或没有库存，无法进行处方导入", RetType.FAIL_DLG);
//
//        preRecipeTestOneTime(commRet, noRecipeRet, "200003281");

    }

    private void preRecipeTestOneTime(RetMsg commRet, RetMsg noRecipeRet, String receptionNo) {
        Integer hospitalCode = 1;
        Integer recipeHospitalCode = 1;

        List<PreRecipeDetail> preRecipeDetails1 = new ArrayList<>();
        List<PreRecipeDetail> preRecipeDetails2 = new ArrayList<>();
        Exception e1 = null;
        Exception e2 = null;
        StopWatch myStopWatch = new StopWatch("preRecipeTest");
        try {
            preRecipe1(commRet, noRecipeRet, receptionNo, hospitalCode, recipeHospitalCode);
        } catch (Exception e) {

        }
        myStopWatch.start();
        try {
            preRecipeDetails1 = preRecipe1(commRet, noRecipeRet, receptionNo, hospitalCode, recipeHospitalCode);
        } catch (Exception e) {
            e.printStackTrace();
            e1 = e;
        }
        myStopWatch.stop();
        myStopWatch.start();
        try {
            preRecipeDetails2 = preRecipe2(commRet, noRecipeRet, receptionNo, hospitalCode, recipeHospitalCode);
        } catch (Exception e) {
            e.printStackTrace();
            e2 = e;
        }
        if (e1 != null) {
            Assert.assertEquals(e1.getMessage(), e2.getMessage());
        }
        myStopWatch.stop();
        Log.info(myStopWatch.prettyPrint());//打印详细信息
        Assert.assertEquals(preRecipeDetails1.size(), preRecipeDetails2.size());
        Log.info("data size: " + preRecipeDetails1.size() + "个");
        for (PreRecipeDetail prd1 : preRecipeDetails1) {
            for (PreRecipeDetail prd2 : preRecipeDetails2) {
                if (prd1.getItemCode().equals(prd2.getItemCode())) {
                    Assert.assertEquals(prd1.getItemName(), prd2.getItemName());
                    Assert.assertEquals(prd1.getSpecification(), prd2.getSpecification());
                    Assert.assertEquals(prd1.getAmount(), prd2.getAmount());
                }
            }
        }
    }

    private List<PreRecipeDetail> preRecipe1(RetMsg commRet, RetMsg noRecipeRet, String receptionNo, Integer hospitalCode, Integer recipeHospitalCode) {
        List<PreRecipeDetail> res = new ArrayList<>();
        if (ObjectUtils.isNumber(receptionNo)) {
            List<RecipeDetail> recipeDetails = recipeDetailMapper.getRecipeByReceptionNo(Converter.toInt64(receptionNo), hospitalCode, "");
            if (recipeDetails == null || recipeDetails.isEmpty()) {
                recipeDetails = recipeDetailMapper.getRecipeByReceptionNo(Converter.toInt64(receptionNo), hospitalCode, "MZYS_TB_MZCFMX_DATA");
            }
            for (RecipeDetail recipeDetail : recipeDetails) {
                res.add(new PreRecipeDetail(recipeDetail));
            }
        } else {
            String dataSource = recipeHospitalCode.equals(HospitalClassify.GENERAL.getHospitalCode()) ? DatasourceName.MZYS : DatasourceName.MZYS3;
            DataSourceSwitchAspect.changeDataSource(dataSource);
            List<OldRecipeDetail> oldRecipeDetails = oldRecipeDetailMapper.getOldRecipeDetailByReceptionNo(receptionNo, ItemCategoryEnum.getDrugTypeCodeList(), null);
            if (oldRecipeDetails == null || oldRecipeDetails.size() <= 0) {
                oldRecipeDetails = oldRecipeDetailMapper.getOldRecipeDetailByReceptionNo(receptionNo, ItemCategoryEnum.getDrugTypeCodeList(), "MZYS_TB_MZCFMX_DATA");
            }
            MAssert.massertListNotNull(oldRecipeDetails, commRet);

            for (OldRecipeDetail oldRecipeDetail : oldRecipeDetails) {
                //医院不同，需要查询药品主索引表，替换药品ID
                if (!hospitalCode.equals(recipeHospitalCode)) {
                    DrugToHospital drugToHospital = queryOneDrugToHospital(oldRecipeDetail.getItemCode(), commRet, hospitalCode);
                    oldRecipeDetail.setItemCode(drugToHospital.getDrugId());
                }
                res.add(new PreRecipeDetail(oldRecipeDetail));
            }
        }

        res.removeIf(i -> ItemCategoryEnum.notDrug(i.getFeeCategory()));
        MAssert.massertListNotNull(res, commRet);

        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        Long preSaveNoNew = sysFunctionMapper.getGetSequences(SequenceEnum.PRE_SAVE_NO);
        Date date = sysFunctionMapper.getDate();
        recipeRepository.batchChargeItemDeal(hospitalCode, noRecipeRet, preSaveNoNew, date, res);
        return res;
    }

    private List<PreRecipeDetail> preRecipe2(RetMsg commRet, RetMsg noRecipeRet, String receptionNo, Integer hospitalCode, Integer recipeHospitalCode) {
        List<PreRecipeDetail> res = new ArrayList<>();
        if (ObjectUtils.isNumber(receptionNo)) {
            List<RecipeDetail> recipeDetails = recipeDetailMapper.getRecipeByReceptionNo(Converter.toInt64(receptionNo), hospitalCode, "");
            if (recipeDetails == null || recipeDetails.isEmpty()) {
                recipeDetails = recipeDetailMapper.getRecipeByReceptionNo(Converter.toInt64(receptionNo), hospitalCode, "MZYS_TB_MZCFMX_DATA");
            }
            for (RecipeDetail recipeDetail : recipeDetails) {
                res.add(new PreRecipeDetail(recipeDetail));
            }
        } else {
            String dataSource = recipeHospitalCode.equals(HospitalClassify.GENERAL.getHospitalCode()) ? DatasourceName.MZYS : DatasourceName.MZYS3;
            DataSourceSwitchAspect.changeDataSource(dataSource);
            List<OldRecipeDetail> oldRecipeDetails = oldRecipeDetailMapper.getOldRecipeDetailByReceptionNo(receptionNo, ItemCategoryEnum.getDrugTypeCodeList(), null);
            if (oldRecipeDetails == null || oldRecipeDetails.size() <= 0) {
                oldRecipeDetails = oldRecipeDetailMapper.getOldRecipeDetailByReceptionNo(receptionNo, ItemCategoryEnum.getDrugTypeCodeList(), "MZYS_TB_MZCFMX_DATA");
            }
            MAssert.massertListNotNull(oldRecipeDetails, commRet);

            for (OldRecipeDetail oldRecipeDetail : oldRecipeDetails) {
                //医院不同，需要查询药品主索引表，替换药品ID
                if (!hospitalCode.equals(recipeHospitalCode)) {
                    DrugToHospital drugToHospital = queryOneDrugToHospital(oldRecipeDetail.getItemCode(), commRet, hospitalCode);
                    oldRecipeDetail.setItemCode(drugToHospital.getDrugId());
                }
                res.add(new PreRecipeDetail(oldRecipeDetail));
            }
        }
        res.removeIf(i -> ItemCategoryEnum.notDrug(i.getFeeCategory()));
        MAssert.massertListNotNull(res, commRet);

        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        Long preSaveNoNew = sysFunctionMapper.getGetSequences(SequenceEnum.PRE_SAVE_NO);
        Date date = sysFunctionMapper.getDate();
        recipeRepository.batchChargeItemDealBigList(hospitalCode, noRecipeRet, preSaveNoNew, date, res);
        return res;
    }

    private void testOneTime(int times) {
        Random random = new Random(System.currentTimeMillis());
        Integer itemCode = null;
        Integer deptId = null;
        Integer hospitalCode = null;
        Integer stopped = null;
        StopWatch myStopWatch = new StopWatch("getChargeItemByIdIsStoppedTest");
        systemTbPubItemsRepository.getChargeItemByIdIsStopped2(itemCode, deptId, hospitalCode, stopped);
        myStopWatch.start("原版");
        List<PreRecipeDetail> detailTmpList = new ArrayList<>();
        List<SystemTbPubItems> itemList = new ArrayList<>();

        List<SystemTbPubItems> retList1 = new ArrayList<>();
        Map<String, SystemTbPubItems> map1 = new HashMap<>();
        for (int i = 0; i < times; i++) {
            int id = random.nextInt(400);
            Req req = reqList.get(id);
            PreRecipeDetail preRecipeDetail = new PreRecipeDetail();
            preRecipeDetail.setItemCode(req.getItemCode());
            preRecipeDetail.setExecDept(req.getDeptId());

            detailTmpList.add(preRecipeDetail);

            SystemTbPubItems chargeItemByIdIsStopped = systemTbPubItemsRepository.getChargeItemByIdIsStopped(req.getItemCode(), req.getDeptId(), 1, 1);
            if (chargeItemByIdIsStopped != null) {
                itemList.add(chargeItemByIdIsStopped);
                Log.info("1111:" + chargeItemByIdIsStopped.getItemCode() + " | " + req.getDeptId());
                String str = String.format("%d_%d", chargeItemByIdIsStopped.getItemCode(),
                        req.getDeptId());
                map1.put(str, chargeItemByIdIsStopped);
                retList1.add(chargeItemByIdIsStopped);
            }
        }
        myStopWatch.stop();


        List<Pair<String, SystemTbPubItems>> systemTbPubItemList = systemTbPubItemsRepository.queryChargeItemByItemCodeList(detailTmpList, 1, 1);
//        Map<String, SystemTbPubItems> map2 = new HashMap<>();
//        for (SystemTbPubItems it : systemTbPubItems) {
//
//            map2.put("" + it.getItemCode() + it.getHospitalId() + it.getStopped(),it);
//            Log.info("2222:"+it.getItemCode());
//        }
        for (SystemTbPubItems st : retList1) {
            Log.info("ret1:" + st.getItemCode());
        }
        for (Pair<String, SystemTbPubItems> st2 : systemTbPubItemList) {
            Log.info("ret2:" + st2.getKey() + " " + st2.getValue().getItemCode());
        }
        Assert.assertEquals(retList1.size(), systemTbPubItemList.size());

//        myStopWatch.start("新版");
//        for(int i = 0;i<times;i++) {
//            int id  = random.nextInt(680);
//            Req req = reqList.get(id);
//            systemTbPubItemsRepository.getChargeItemByIdIsStopped(req.getItemCode(), req.getDeptId(), req.getHospitalCode(), req.getStopped());
//        }
//
//        myStopWatch.stop();

        myStopWatch.start("最新版");
        List<Integer> itemcodes = new ArrayList<>();
        for (int i = 0; i < times; i++) {
            int id = random.nextInt(680);
            itemcodes.add(id);
        }
        deptId = 20600 + random.nextInt(3);
        deptId = random.nextInt(3);
        stopped = 0;
        systemTbPubItemsRepository.queryChargeItemByItemCodeList(detailTmpList, hospitalCode, stopped);
        myStopWatch.stop();

        Log.info(myStopWatch.prettyPrint());//打印详细信息
    }

    List<Req> reqList = new ArrayList<>();


    @Test
    void contextLoads() throws IOException {

//  /*      MultiValueMap<String, String> param = new LinkedMultiValueMap<>();
//        param.add("patientid", "c7e2a16d-a041-40e3-bb4e-744499faf78c");
//        param.add("item", "\"百洛特\"草酸艾司西酞普兰片");
//        param.add("itemID", "72782");
//        param.add("ward", "门诊西药房");
//        param.add("bedno", "");
//        param.add("hosno", "-500104");
//        param.add("name", "杨三乐");
//        param.add("sex", "男");
//        param.add("age", "41");
//        param.add("visittime", "2018/5/16 9:04:00");
//        param.add("cardid", "000333");
//        param.add("regno", "ca829c79-919d-4102-8bee-587d7ed7a70d");
//        param.add("IDCard", "******************");
//        param.add("Doctor", "王勇");
//        param.add("Dept", "普通精神科");
//        param.add("SFtype", "1");
//        param.add("IsValid", "1");
//        param.add("IsValidKMXP", "0");
//        String xml = restUtils.post("http://10.0.0.116:8025/GzdService.asmx/Add", param, String.class);
//        String url = restUtils.extraXml(xml, "Url");
//        String ID = restUtils.extraXml(xml, "ID");
//        String Name = restUtils.extraXml(xml, "Name");
//        String Type = restUtils.extraXml(xml, "Type");
//        System.out.println(url);
//        System.out.println(ID);
//        System.out.println(Name);
//        System.out.println(Type);*/
//
//
//        MultiValueMap<String, String>  param = new LinkedMultiValueMap<>();
//        param.add("patientid", "c7e2a16d-a041-40e3-bb4e-744499faf78c");
//        param.add("item", "\"百洛特\"草酸艾司西酞普兰片");
//        param.add("itemID", "72782");
//        param.add("hosno", "-500104");
//        param.add("name", "杨三乐");
//        param.add("visittime", "2018/5/16 9:04:00");
//        param.add("cardid", "000333");
//        param.add("regno", "ca829c79-919d-4102-8bee-587d7ed7a70d");
//        param.add("IDCard", "******************");
//        param.add("Doctor", "王勇");
//        param.add("Dept", "普通精神科");
//        param.add("createby", "39");
//
////        String xml = restUtils.post("http://10.0.0.116:8025/GzdService.asmx/AddJGY", param, String.class);
////        XmlMapper xmlMapper = new XmlMapper();
////        JsonNode node = xmlMapper.readTree(xml.getBytes());
////        List<JsonNode> anyType = node.findValue("anyType").findValues("");
////        System.out.println( anyType.get(0).asText());
////        System.out.println( anyType.get(1).asText());
//
//        param.add("id","1");
//        param.add("opNo","1");
//        //JsonNode node1 = restUtils.post("http://10.0.0.116:8025/GzdService.asmx/DeleteJGY", param );
//        //System.out.println(node1.asText());

    }

    @Test
    public void test() {
//        ArrayList<Integer> list = new ArrayList<>();
//        for (int i = 0; i < 10; i++) {
//            list.add(i);
//        }
//        System.out.println(list.subList(0, 3));// 左闭右开
//        System.out.println(list.subList(3, 6));
//        System.out.println(list.subList(6, 9));
    }

    private DrugToHospital queryOneDrugToHospital(Integer oldRecipeDetail, RetMsg commRet, Integer hospitalCode) {
        DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
        List<DrugToHospital> drugToHospitalList = drugToHospitalMapper.getDrugToHospital(oldRecipeDetail);
        MAssert.massertNotTrue(drugToHospitalList.size() <= 0, commRet);

        List<DrugToHospital> drugToHospitalListHosp = drugToHospitalList.stream().filter(p -> p.getHospitalId().equals(hospitalCode)).collect(Collectors.toList());
        MAssert.massertNotTrue(drugToHospitalListHosp.size() <= 0, commRet);
        return drugToHospitalListHosp.get(0);
    }

}
