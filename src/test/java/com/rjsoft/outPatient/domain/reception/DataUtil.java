package com.rjsoft.outPatient.domain.reception;

import java.util.List;

public class DataUtil {
    public static void fillReqList(List<Req> reqList) {
        reqList.add(new Req(1001,1061,1,1));
        reqList.add(new Req(1001,1062,1,1));
        reqList.add(new Req(1001,1063,1,1));
        reqList.add(new Req(1001,1064,1,1));
        reqList.add(new Req(1001,1065,1,1));
        reqList.add(new Req(1001,1141,1,1));
        reqList.add(new Req(1001,2001,1,1));
        reqList.add(new Req(1003,1061,1,1));
        reqList.add(new Req(1003,1062,1,1));
        reqList.add(new Req(1003,1064,1,1));
        reqList.add(new Req(1003,1065,1,1));
        reqList.add(new Req(1003,1141,1,1));
        reqList.add(new Req(1003,2001,1,1));
        reqList.add(new Req(1004,1061,1,1));
        reqList.add(new Req(1004,1062,1,1));
        reqList.add(new Req(1004,1063,1,1));
        reqList.add(new Req(1004,1064,1,1));
        reqList.add(new Req(1004,1065,1,1));
        reqList.add(new Req(1004,1141,1,1));
        reqList.add(new Req(1004,2001,1,1));
        reqList.add(new Req(1005,1063,1,1));
        reqList.add(new Req(1005,1064,1,1));
        reqList.add(new Req(1005,2001,1,1));
        reqList.add(new Req(1006,1061,1,1));
        reqList.add(new Req(1006,1062,1,1));
        reqList.add(new Req(1006,1063,1,1));
        reqList.add(new Req(1006,1064,1,1));
        reqList.add(new Req(1006,1065,1,1));
        reqList.add(new Req(1006,1141,1,1));
        reqList.add(new Req(1006,2001,1,1));
        reqList.add(new Req(1008,1064,1,1));
        reqList.add(new Req(1008,1065,1,1));
        reqList.add(new Req(1008,2001,1,1));
        reqList.add(new Req(1009,1064,1,1));
        reqList.add(new Req(1012,1061,1,1));
        reqList.add(new Req(1012,1062,1,1));
        reqList.add(new Req(1012,1063,1,1));
        reqList.add(new Req(1012,1064,1,1));
        reqList.add(new Req(1012,1065,1,1));
        reqList.add(new Req(1012,1141,1,1));
        reqList.add(new Req(1012,2001,1,1));
        reqList.add(new Req(1013,1064,1,1));
        reqList.add(new Req(1013,1065,1,1));
        reqList.add(new Req(1013,2001,1,1));
        reqList.add(new Req(1016,1064,1,1));
        reqList.add(new Req(1016,1065,1,1));
        reqList.add(new Req(1016,2001,1,1));
        reqList.add(new Req(1017,1064,1,1));
        reqList.add(new Req(1017,1065,1,1));
        reqList.add(new Req(1017,2001,1,1));
        reqList.add(new Req(1019,1064,1,1));
        reqList.add(new Req(1019,1065,1,1));
        reqList.add(new Req(1019,2001,1,1));
        reqList.add(new Req(1020,1064,1,1));
        reqList.add(new Req(1020,1065,1,1));
        reqList.add(new Req(1020,2001,1,1));
        reqList.add(new Req(1021,1064,1,1));
        reqList.add(new Req(1021,1065,1,1));
        reqList.add(new Req(1021,2001,1,1));
        reqList.add(new Req(1022,1064,1,1));
        reqList.add(new Req(1022,1065,1,1));
        reqList.add(new Req(1022,2001,1,1));
        reqList.add(new Req(1023,1064,1,1));
        reqList.add(new Req(1023,1065,1,1));
        reqList.add(new Req(1023,2001,1,1));
        reqList.add(new Req(1025,1064,1,1));
        reqList.add(new Req(1027,1064,1,1));
        reqList.add(new Req(1027,1065,1,1));
        reqList.add(new Req(1027,2001,1,1));
        reqList.add(new Req(1028,1064,1,1));
        reqList.add(new Req(1029,1064,1,1));
        reqList.add(new Req(1029,1065,1,1));
        reqList.add(new Req(1029,2001,1,1));
        reqList.add(new Req(1030,1064,1,1));
        reqList.add(new Req(1030,1065,1,1));
        reqList.add(new Req(1030,2001,1,1));
        reqList.add(new Req(1031,1064,1,1));
        reqList.add(new Req(1032,1064,1,1));
        reqList.add(new Req(1033,1064,1,1));
        reqList.add(new Req(1035,1064,1,1));
        reqList.add(new Req(1037,1064,1,1));
        reqList.add(new Req(1038,1064,1,1));
        reqList.add(new Req(1039,1064,1,1));
        reqList.add(new Req(1040,1064,1,1));
        reqList.add(new Req(1041,1064,1,1));
        reqList.add(new Req(1042,1064,1,1));
        reqList.add(new Req(1043,1064,1,1));
        reqList.add(new Req(1045,1064,1,1));
        reqList.add(new Req(1048,1061,1,1));
        reqList.add(new Req(1048,1062,1,1));
        reqList.add(new Req(1048,1063,1,1));
        reqList.add(new Req(1048,1064,1,1));
        reqList.add(new Req(1048,1065,1,1));
        reqList.add(new Req(1048,1141,1,1));
        reqList.add(new Req(1048,2001,1,1));
        reqList.add(new Req(1051,1064,1,1));
        reqList.add(new Req(1052,1064,1,1));
        reqList.add(new Req(1053,1064,1,1));
        reqList.add(new Req(1055,1064,1,1));
        reqList.add(new Req(1056,1064,1,1));
        reqList.add(new Req(1057,1064,1,1));
        reqList.add(new Req(1058,1064,1,1));
        reqList.add(new Req(1059,1064,1,1));
        reqList.add(new Req(1060,1064,1,1));
        reqList.add(new Req(1061,1064,1,1));
        reqList.add(new Req(1062,1064,1,1));
        reqList.add(new Req(1063,1064,1,1));
        reqList.add(new Req(1066,1064,1,1));
        reqList.add(new Req(1067,1064,1,1));
        reqList.add(new Req(1068,1064,1,1));
        reqList.add(new Req(1069,1064,1,1));
        reqList.add(new Req(1070,1064,1,1));
        reqList.add(new Req(1071,1064,1,1));
        reqList.add(new Req(1072,1064,1,1));
        reqList.add(new Req(1073,1064,1,1));
        reqList.add(new Req(1075,1064,1,1));
        reqList.add(new Req(1079,1064,1,1));
        reqList.add(new Req(1080,1064,1,1));
        reqList.add(new Req(1081,1064,1,1));
        reqList.add(new Req(1083,1064,1,1));
        reqList.add(new Req(1085,1064,1,1));
        reqList.add(new Req(1086,1064,1,1));
        reqList.add(new Req(1087,1064,1,1));
        reqList.add(new Req(1089,1064,1,1));
        reqList.add(new Req(1090,1064,1,1));
        reqList.add(new Req(1091,1064,1,1));
        reqList.add(new Req(1092,1064,1,1));
        reqList.add(new Req(1093,1064,1,1));
        reqList.add(new Req(1094,1064,1,1));
        reqList.add(new Req(1095,1064,1,1));
        reqList.add(new Req(1097,1064,1,1));
        reqList.add(new Req(1098,1064,1,1));
        reqList.add(new Req(1100,1064,1,1));
        reqList.add(new Req(1102,1064,1,1));
        reqList.add(new Req(1103,1064,1,1));
        reqList.add(new Req(1104,1064,1,1));
        reqList.add(new Req(1105,1064,1,1));
        reqList.add(new Req(1109,1064,1,1));
        reqList.add(new Req(1115,1064,1,1));
        reqList.add(new Req(1116,1064,1,1));
        reqList.add(new Req(1117,1064,1,1));
        reqList.add(new Req(1118,1064,1,1));
        reqList.add(new Req(1121,1064,1,1));
        reqList.add(new Req(1122,1064,1,1));
        reqList.add(new Req(1123,1064,1,1));
        reqList.add(new Req(1126,1064,1,1));
        reqList.add(new Req(1126,1065,1,1));
        reqList.add(new Req(1126,2001,1,1));
        reqList.add(new Req(1128,1064,1,1));
        reqList.add(new Req(1129,1064,1,1));
        reqList.add(new Req(1129,1065,1,1));
        reqList.add(new Req(1129,2001,1,1));
        reqList.add(new Req(1130,1064,1,1));
        reqList.add(new Req(1131,1064,1,1));
        reqList.add(new Req(1131,1065,1,1));
        reqList.add(new Req(1131,2001,1,1));
        reqList.add(new Req(1134,1064,1,1));
        reqList.add(new Req(1141,1064,1,1));
        reqList.add(new Req(1141,1065,1,1));
        reqList.add(new Req(1141,2001,1,1));
        reqList.add(new Req(1142,1064,1,1));
        reqList.add(new Req(1143,1064,1,1));
        reqList.add(new Req(1144,1061,1,1));
        reqList.add(new Req(1144,1064,1,1));
        reqList.add(new Req(1144,1065,1,1));
        reqList.add(new Req(1144,2001,1,1));
        reqList.add(new Req(1145,1064,1,1));
        reqList.add(new Req(1145,1065,1,1));
        reqList.add(new Req(1145,2001,1,1));
        reqList.add(new Req(1146,1064,1,1));
        reqList.add(new Req(1146,1065,1,1));
        reqList.add(new Req(1146,2001,1,1));
        reqList.add(new Req(1149,1064,1,1));
        reqList.add(new Req(1150,1064,1,1));
        reqList.add(new Req(1150,1065,1,1));
        reqList.add(new Req(1150,2001,1,1));
        reqList.add(new Req(1153,1064,1,1));
        reqList.add(new Req(1153,1065,1,1));
        reqList.add(new Req(1153,2001,1,1));
        reqList.add(new Req(1154,1064,1,1));
        reqList.add(new Req(1154,1065,1,1));
        reqList.add(new Req(1154,2001,1,1));
        reqList.add(new Req(1156,1064,1,1));
        reqList.add(new Req(1157,1064,1,1));
        reqList.add(new Req(1161,1064,1,1));
        reqList.add(new Req(1161,1065,1,1));
        reqList.add(new Req(1161,2001,1,1));
        reqList.add(new Req(1162,1064,1,1));
        reqList.add(new Req(1162,1065,1,1));
        reqList.add(new Req(1162,2001,1,1));
        reqList.add(new Req(1163,1064,1,1));
        reqList.add(new Req(1163,1065,1,1));
        reqList.add(new Req(1163,2001,1,1));
        reqList.add(new Req(1165,1064,1,1));
        reqList.add(new Req(1166,1064,1,1));
        reqList.add(new Req(1166,1065,1,1));
        reqList.add(new Req(1166,2001,1,1));
        reqList.add(new Req(1167,1064,1,1));
        reqList.add(new Req(1167,1065,1,1));
        reqList.add(new Req(1167,2001,1,1));
        reqList.add(new Req(1168,1064,1,1));
        reqList.add(new Req(1168,1065,1,1));
        reqList.add(new Req(1168,2001,1,1));
        reqList.add(new Req(1169,1064,1,1));
        reqList.add(new Req(1169,1065,1,1));
        reqList.add(new Req(1169,2001,1,1));
        reqList.add(new Req(1170,1064,1,1));
        reqList.add(new Req(1171,1064,1,1));
        reqList.add(new Req(1172,1064,1,1));
        reqList.add(new Req(1173,1064,1,1));
        reqList.add(new Req(1174,1064,1,1));
        reqList.add(new Req(1175,1064,1,1));
        reqList.add(new Req(1176,1064,1,1));
        reqList.add(new Req(1177,1064,1,1));
        reqList.add(new Req(1178,1064,1,1));
        reqList.add(new Req(1181,1064,1,1));
        reqList.add(new Req(1182,1064,1,1));
        reqList.add(new Req(1183,1064,1,1));
        reqList.add(new Req(1185,1064,1,1));
        reqList.add(new Req(1186,1064,1,1));
        reqList.add(new Req(1187,1064,1,1));
        reqList.add(new Req(1192,1064,1,1));
        reqList.add(new Req(1194,1064,1,1));
        reqList.add(new Req(1195,1064,1,1));
        reqList.add(new Req(1197,1064,1,1));
        reqList.add(new Req(1198,1064,1,1));
        reqList.add(new Req(1203,1064,1,1));
        reqList.add(new Req(1205,1064,1,1));
        reqList.add(new Req(1206,1064,1,1));
        reqList.add(new Req(1209,1064,1,1));
        reqList.add(new Req(1210,1064,1,1));
        reqList.add(new Req(1211,1064,1,1));
        reqList.add(new Req(1213,1064,1,1));
        reqList.add(new Req(1214,1064,1,1));
        reqList.add(new Req(1215,1064,1,1));
        reqList.add(new Req(1216,1064,1,1));
        reqList.add(new Req(1217,1064,1,1));
        reqList.add(new Req(1220,1064,1,1));
        reqList.add(new Req(1222,1064,1,1));
        reqList.add(new Req(1223,1064,1,1));
        reqList.add(new Req(1224,1064,1,1));
        reqList.add(new Req(1226,1064,1,1));
        reqList.add(new Req(1227,1064,1,1));
        reqList.add(new Req(1228,1064,1,1));
        reqList.add(new Req(1230,1064,1,1));
        reqList.add(new Req(1232,1064,1,1));
        reqList.add(new Req(1235,1064,1,1));
        reqList.add(new Req(1236,1064,1,1));
        reqList.add(new Req(1238,1064,1,1));
        reqList.add(new Req(1241,1064,1,1));
        reqList.add(new Req(1242,1064,1,1));
        reqList.add(new Req(1243,1064,1,1));
        reqList.add(new Req(1245,1064,1,1));
        reqList.add(new Req(1246,1064,1,1));
        reqList.add(new Req(1247,1064,1,1));
        reqList.add(new Req(1248,1064,1,1));
        reqList.add(new Req(1250,1064,1,1));
        reqList.add(new Req(1255,1064,1,1));
        reqList.add(new Req(1257,1064,1,1));
        reqList.add(new Req(1259,1064,1,1));
        reqList.add(new Req(1260,1064,1,1));
        reqList.add(new Req(1261,1064,1,1));
        reqList.add(new Req(1263,1064,1,1));
        reqList.add(new Req(1264,1064,1,1));
        reqList.add(new Req(1265,1064,1,1));
        reqList.add(new Req(1270,1064,1,1));
        reqList.add(new Req(1271,1064,1,1));
        reqList.add(new Req(1272,1064,1,1));
        reqList.add(new Req(1274,1064,1,1));
        reqList.add(new Req(1276,1064,1,1));
        reqList.add(new Req(1277,1064,1,1));
        reqList.add(new Req(1279,1064,1,1));
        reqList.add(new Req(1283,1064,1,1));
        reqList.add(new Req(1285,1064,1,1));
        reqList.add(new Req(1287,1064,1,1));
        reqList.add(new Req(1288,1064,1,1));
        reqList.add(new Req(1289,1064,1,1));
        reqList.add(new Req(1290,1064,1,1));
        reqList.add(new Req(1291,1062,1,1));
        reqList.add(new Req(1291,1063,1,1));
        reqList.add(new Req(1291,1064,1,1));
        reqList.add(new Req(1291,1065,1,1));
        reqList.add(new Req(1291,1141,1,1));
        reqList.add(new Req(1291,2001,1,1));
        reqList.add(new Req(1293,1064,1,1));
        reqList.add(new Req(1295,1064,1,1));
        reqList.add(new Req(1297,1064,1,1));
        reqList.add(new Req(1298,1064,1,1));
        reqList.add(new Req(1301,1064,1,1));
        reqList.add(new Req(1302,1064,1,1));
        reqList.add(new Req(1303,1064,1,1));
        reqList.add(new Req(1304,1064,1,1));
        reqList.add(new Req(1305,1064,1,1));
        reqList.add(new Req(1306,1064,1,1));
        reqList.add(new Req(1307,1064,1,1));
        reqList.add(new Req(1308,1064,1,1));
        reqList.add(new Req(1311,1064,1,1));
        reqList.add(new Req(1313,1064,1,1));
        reqList.add(new Req(1315,1064,1,1));
        reqList.add(new Req(1316,1064,1,1));
        reqList.add(new Req(1317,1064,1,1));
        reqList.add(new Req(1318,1064,1,1));
        reqList.add(new Req(1321,1064,1,1));
        reqList.add(new Req(1322,1064,1,1));
        reqList.add(new Req(1323,1064,1,1));
        reqList.add(new Req(1324,1064,1,1));
        reqList.add(new Req(1325,1064,1,1));
        reqList.add(new Req(1326,1064,1,1));
        reqList.add(new Req(1328,1064,1,1));
        reqList.add(new Req(1330,1064,1,1));
        reqList.add(new Req(1331,1064,1,1));
        reqList.add(new Req(1332,1064,1,1));
        reqList.add(new Req(1333,1064,1,1));
        reqList.add(new Req(1334,1064,1,1));
        reqList.add(new Req(1335,1064,1,1));
        reqList.add(new Req(1336,1064,1,1));
        reqList.add(new Req(1337,1064,1,1));
        reqList.add(new Req(1338,1064,1,1));
        reqList.add(new Req(1339,1064,1,1));
        reqList.add(new Req(1340,1064,1,1));
        reqList.add(new Req(1341,1064,1,1));
        reqList.add(new Req(1344,1064,1,1));
        reqList.add(new Req(1346,1064,1,1));
        reqList.add(new Req(1347,1064,1,1));
        reqList.add(new Req(1348,1064,1,1));
        reqList.add(new Req(1349,1064,1,1));
        reqList.add(new Req(1350,1064,1,1));
        reqList.add(new Req(1353,1064,1,1));
        reqList.add(new Req(1354,1064,1,1));
        reqList.add(new Req(1355,1064,1,1));
        reqList.add(new Req(1356,1064,1,1));
        reqList.add(new Req(1357,1064,1,1));
        reqList.add(new Req(1358,1064,1,1));
        reqList.add(new Req(1359,1064,1,1));
        reqList.add(new Req(1360,1064,1,1));
        reqList.add(new Req(1361,1064,1,1));
        reqList.add(new Req(1363,1064,1,1));
        reqList.add(new Req(1364,1064,1,1));
        reqList.add(new Req(1365,1064,1,1));
        reqList.add(new Req(1368,1064,1,1));
        reqList.add(new Req(1369,1064,1,1));
        reqList.add(new Req(1370,1064,1,1));
        reqList.add(new Req(1371,1064,1,1));
        reqList.add(new Req(1372,1064,1,1));
        reqList.add(new Req(1373,1064,1,1));
        reqList.add(new Req(1374,1064,1,1));
        reqList.add(new Req(1375,1064,1,1));
        reqList.add(new Req(1376,1064,1,1));
        reqList.add(new Req(1377,1064,1,1));
        reqList.add(new Req(1378,1064,1,1));
        reqList.add(new Req(1379,1064,1,1));
        reqList.add(new Req(1380,1064,1,1));
        reqList.add(new Req(1383,1064,1,1));
        reqList.add(new Req(1384,1064,1,1));
        reqList.add(new Req(1386,1064,1,1));
        reqList.add(new Req(1388,1064,1,1));
        reqList.add(new Req(1389,1064,1,1));
        reqList.add(new Req(1390,1064,1,1));
        reqList.add(new Req(1391,1064,1,1));
        reqList.add(new Req(1392,1064,1,1));
        reqList.add(new Req(1393,1064,1,1));
        reqList.add(new Req(1394,1064,1,1));
        reqList.add(new Req(1395,1064,1,1));
        reqList.add(new Req(1396,1064,1,1));
        reqList.add(new Req(1399,1064,1,1));
        reqList.add(new Req(1400,1064,1,1));
        reqList.add(new Req(1401,1064,1,1));
        reqList.add(new Req(1405,1064,1,1));
        reqList.add(new Req(1408,1064,1,1));
        reqList.add(new Req(1409,1064,1,1));
        reqList.add(new Req(1410,1064,1,1));
        reqList.add(new Req(1412,1064,1,1));
        reqList.add(new Req(1413,1064,1,1));
        reqList.add(new Req(1414,1064,1,1));
        reqList.add(new Req(1415,1064,1,1));
        reqList.add(new Req(1416,1064,1,1));
        reqList.add(new Req(1417,1064,1,1));
        reqList.add(new Req(1418,1064,1,1));
        reqList.add(new Req(1419,1064,1,1));
        reqList.add(new Req(1420,1064,1,1));
        reqList.add(new Req(1421,1064,1,1));
        reqList.add(new Req(1423,1064,1,1));
        reqList.add(new Req(1424,1064,1,1));
        reqList.add(new Req(1425,1064,1,1));
        reqList.add(new Req(1427,1064,1,1));
        reqList.add(new Req(1428,1062,1,1));
        reqList.add(new Req(1428,1063,1,1));
        reqList.add(new Req(1428,1064,1,1));
        reqList.add(new Req(1428,1065,1,1));
        reqList.add(new Req(1428,1141,1,1));
        reqList.add(new Req(1428,2001,1,1));
        reqList.add(new Req(1429,1064,1,1));
        reqList.add(new Req(1430,1064,1,1));
        reqList.add(new Req(1431,1064,1,1));
        reqList.add(new Req(1432,1064,1,1));
        reqList.add(new Req(1433,1064,1,1));
        reqList.add(new Req(1434,1064,1,1));
        reqList.add(new Req(1435,1064,1,1));
        reqList.add(new Req(1437,1064,1,1));
        reqList.add(new Req(1438,1064,1,1));
        reqList.add(new Req(1439,1064,1,1));
        reqList.add(new Req(1440,1064,1,1));
        reqList.add(new Req(1442,1064,1,1));
        reqList.add(new Req(1443,1064,1,1));
        reqList.add(new Req(1444,1064,1,1));
        reqList.add(new Req(1445,1064,1,1));
        reqList.add(new Req(1446,1064,1,1));
        reqList.add(new Req(1447,1064,1,1));
        reqList.add(new Req(1449,1064,1,1));
        reqList.add(new Req(1450,1064,1,1));
        reqList.add(new Req(1451,1064,1,1));
        reqList.add(new Req(1452,1064,1,1));
        reqList.add(new Req(1453,1064,1,1));
        reqList.add(new Req(1454,1064,1,1));
        reqList.add(new Req(1455,1064,1,1));
        reqList.add(new Req(1456,1064,1,1));
        reqList.add(new Req(1458,1064,1,1));
        reqList.add(new Req(1459,1064,1,1));
        reqList.add(new Req(1461,1064,1,1));
        reqList.add(new Req(1462,1064,1,1));
        reqList.add(new Req(1463,1064,1,1));
        reqList.add(new Req(1464,1064,1,1));
        reqList.add(new Req(1465,1064,1,1));
        reqList.add(new Req(1466,1064,1,1));
        reqList.add(new Req(1467,1064,1,1));
        reqList.add(new Req(1468,1064,1,1));
        reqList.add(new Req(1469,1064,1,1));
        reqList.add(new Req(1470,1064,1,1));
        reqList.add(new Req(1471,1064,1,1));
        reqList.add(new Req(1473,1064,1,1));
        reqList.add(new Req(1683,1064,1,1));
        reqList.add(new Req(1684,1064,1,1));
        reqList.add(new Req(22350,2001,1,1));
        reqList.add(new Req(30053,1062,1,1));
        reqList.add(new Req(30053,1063,1,1));
        reqList.add(new Req(30053,1064,1,1));
        reqList.add(new Req(30053,1065,1,1));
        reqList.add(new Req(30053,1141,1,1));
        reqList.add(new Req(30053,2001,1,1));
        reqList.add(new Req(44605,1061,1,1));
        reqList.add(new Req(44605,1062,1,1));
        reqList.add(new Req(44605,1063,1,1));
        reqList.add(new Req(44605,1064,1,1));
        reqList.add(new Req(44605,1065,1,1));
        reqList.add(new Req(44605,1141,1,1));
        reqList.add(new Req(44605,2001,1,1));
        reqList.add(new Req(72118,1064,1,1));
        reqList.add(new Req(72118,2001,1,1));
        reqList.add(new Req(74648,1061,1,1));
        reqList.add(new Req(74648,2001,1,1));
        reqList.add(new Req(75028,2001,1,1));
    }
}
