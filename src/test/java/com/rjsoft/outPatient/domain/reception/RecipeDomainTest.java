package com.rjsoft.outPatient.domain.reception;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.param.ApiResult;
import com.rjsoft.outPatient.common.enums.CheckRuleExecEnum;
import com.rjsoft.outPatient.domain.diagnose.DiagnoseDomain;
import com.rjsoft.outPatient.domain.recipe.RecipeDomain;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeResDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.service.DiagnoseRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.PatientRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.ReceptionRecordRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.RegisterListRepository;
import com.rjsoft.outPatient.macomm.cons.RespConst;
import com.rjsoft.outPatient.macomm.massert.MAssert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.StopWatch;

import java.util.*;

@SpringBootTest
@RunWith(SpringRunner.class)
public class RecipeDomainTest {

    @Autowired
    RecipeDomain recipeDomain;
    @Autowired
    DiagnoseDomain diagnoseDomain;
    @Autowired
    ReceptionRecordRepository receptionRecordRepository;
    @Autowired
    DiagnoseRepository diagnoseRepository;
    @Autowired
    RegisterListRepository registerListRepository;
    @Autowired
    PatientRepository patientRepository;


    @Test
    public void checkRecipeRules(){
//        CheckRecipeDto inputData = buildParam();
//        inputData.setExecType(CheckRuleExecEnum.EXEC_ALL);
//
//        //就诊记录
//        ReceptionRecord receptionRecord = receptionRecordRepository.getReceptionById(inputData.getReceptionNo(), inputData.getHospitalCode());
//        MAssert.massertNotNull(receptionRecord, RespConst.CommErr.COMM_ERR, "没有看诊信息，不能开处方");
//        //挂号记录
//        RegisterList registerList = registerListRepository.getRegisterByRegNo(receptionRecord.getRegNo(), receptionRecord.getHospitalCode());
//        //患者信息
//        PatientList patientInfo = patientRepository.getPatientList(Converter.toInt32(registerList.getPatID()), registerList.getHospitalCode());
//        //查询患者明细信息
//        PatientDetail patientDetail = patientRepository.getPatientDetail(Converter.toInt32(registerList.getPatID()), registerList.getHospitalCode());
//
//        Set<Long> regNoList = new HashSet<Long>();
//        regNoList.add(receptionRecord.getRegNo());
//        Log.info("regNoList: " + JSON.toJSONString(regNoList));
//        List<Diagnose> diagnoseList = diagnoseRepository.getDiagnosesByRegNos(regNoList.isEmpty() ? null : new HashSet<Long>(regNoList), null);
//        MAssert.massertListNotNull(diagnoseList, RespConst.CommErr.COMM_ERR, "没有诊断信息，请先开立诊断再开处方");
//        Map errInfo = new HashMap();
//        //根据操作类型加载处方明细
//        List<PreRecipeDetail> recipeDetails = recipeDomain.getPreRecipeDetailsByOpera(inputData, errInfo, receptionRecord, registerList);
//        MAssert.massertListNotNull(recipeDetails, RespConst.CommErr.COMM_ERR, "校验失败，未加载到处方明细");
//
//        for (int i = 0; i < 10; i++) {
//            try {
//                Object result = recipeBiz.checkRecipeRulesAll(inputData, recipeDetails, receptionRecord, registerList, patientInfo, patientDetail, diagnoseList);
//
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }
//            try {
//                Thread.sleep(200);
//            } catch (InterruptedException e) {
//                throw new RuntimeException(e);
//            }
//        }
    }



//    private final String paramJson = "{\"receptionNo\":200004247,\"operatingType\":0,\"operatingId\":200026250,\"doctorId\":10397,\"skipRule\":[1,17,19],\"preSaveNo\":4589,\"hospitalCode\":1,\"deptId\":1076,\"registerType\":0,\"opFlag\":0}";
    private final String paramJson = "{\n" +
        "        \"receptionNo\": 200000140,\n" +
        "        \"operatingType\": 0,\n" +
        "        \"operatingId\": 200027170,\n" +
        "        \"doctorId\": 10397,\n" +
        "        \"skipRule\": [\n" +
        "            0,\n" +
        "            1,\n" +
        "            2,\n" +
        "            4,\n" +
        "            6,\n" +
        "            8,\n" +
        "            9,\n" +
        "            15,\n" +
        "            16,\n" +
        "            17,\n" +
        "            19\n" +
        "        ],\n" +
        "        \"preSaveNo\": 10550,\n" +
        "        \"hospitalCode\": 1,\n" +
        "        \"deptId\": 1073,\n" +
        "        \"registerType\": 0,\n" +
        "        \"opFlag\": 1\n" +
        "    }";
    private CheckRecipeDto buildParam(){
        CheckRecipeDto inputData = JSONObject.parseObject(paramJson, CheckRecipeDto.class);
        inputData.setReceiveDeptId(1073);
        return inputData;
    }

}
