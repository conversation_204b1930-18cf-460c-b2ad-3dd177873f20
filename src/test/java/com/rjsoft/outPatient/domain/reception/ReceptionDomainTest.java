package com.rjsoft.outPatient.domain.reception;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
public class ReceptionDomainTest {
    @Autowired
    private ReceptionDomain receptionDomain;

    @Test
    public void getRemindBeforeInfo() {
//        List<RemindInfoDto> remindInfoDtos = receptionDomain.getRemindBeforeInfo(213013350, 200555491L,1,39,"sf2001",1073,"192.168.6.123","ffff-ffff");
//        Assert.assertNotNull(remindInfoDtos);
//        Assert.assertNotNull(remindInfoDtos.get(0).getContent());
//        ReceptionListDto receptionListDto = (ReceptionListDto) remindInfoDtos.get(0).getContent();
//        Assert.assertEquals(receptionListDto.getRegNo().longValue(),200524625L);
    }

}