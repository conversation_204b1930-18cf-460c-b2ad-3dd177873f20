package com.rjsoft.outPatient;

import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tk.mybatis.spring.annotation.MapperScan;

import java.util.Date;

/**
 * <AUTHOR>
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class,PageHelperAutoConfiguration.class})
@ComponentScan({"com.rjsoft.common", "com.rjsoft.outPatient","com.ruijing.code.exc" })
@MapperScan(basePackages = {"com.rjsoft.outPatient.infrastructure.repository.mapper", "com.rjsoft.repository","com.rjsoft.outPatient.config"})
@EnableScheduling
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

}
