package com.rjsoft.outPatient.domain.caseHistory.Enum;



public enum CaseHistoryOpTypeEnum {

    /**
     * 新增
     */
    ADD(0, "add"),
    /**
     * 修改
     */
    UPDATE(1, "update"),
    /**
     * 提交
     */
    SUBMIT(2, "submit"),
    /**
     * 撤销提交
     */
    CANCEL_SUBMIT(3, "cancelSubmit"),
    /**
     * 科室审核通过
     */
    CHECK_PASS(4, "checkPass"),
    /**
     * 科室审核不通过
     */
    CHECK_FAIL(5, "checkFail"),
    /**
     * 抽查合格
     */
    SPOT_CHECK_PASS(6, "spotCheckPass"),
    /**
     * 抽查不合格
     */
    SPOT_CHECK_FAIL(7, "spotCheckFail"),
    /**
     * 抽查
     */
    SPOT_CHECK(8, "spotCheck"),
    /**
     * 查看
     */
    READ(9, "read"),
    /**
     * 审核修改
     */
    CHECK_UPDATE(10, "checkUpdate"),
    /**
     * 抽查合格自动
     */
    SPOT_CHECK_AUTO(11, "SpotCheckAuto");


    public static String

    getValueName(Integer code) {
        if (CaseHistoryOpTypeEnum.ADD.getCode().equals(code)) {
            return "新增";
        } else if (CaseHistoryOpTypeEnum.UPDATE.getCode().equals(code)) {
            return "坐诊修改";
        } else if (CaseHistoryOpTypeEnum.SUBMIT.getCode().equals(code)) {
            return "提交";
        } else if (CaseHistoryOpTypeEnum.CANCEL_SUBMIT.getCode().equals(code)) {
            return "撤销提交";
        } else if (CaseHistoryOpTypeEnum.CHECK_PASS.getCode().equals(code)) {
            return "科室审核通过";
        } else if (CaseHistoryOpTypeEnum.CHECK_FAIL.getCode().equals(code)) {
            return "科室审核不通过";
        } else if (CaseHistoryOpTypeEnum.SPOT_CHECK_PASS.getCode().equals(code)) {
            return "门办抽查合格";
        } else if (CaseHistoryOpTypeEnum.SPOT_CHECK_FAIL.getCode().equals(code)) {
            return "门办抽查不合格";
        } else if (CaseHistoryOpTypeEnum.SPOT_CHECK.getCode().equals(code)) {
            return "抽查";
        } else if (CaseHistoryOpTypeEnum.READ.getCode().equals(code)) {
            return "查看";
        } else if (CaseHistoryOpTypeEnum.CHECK_UPDATE.getCode().equals(code)) {
            return "审核修改";
        } else if (CaseHistoryOpTypeEnum.SPOT_CHECK_AUTO.getCode().equals(code)) {
            return "门诊办抽查合格(自动)";
        } else {
            return "";
        }
    }

    private Integer code;
    private String msg;

    CaseHistoryOpTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return this.code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}