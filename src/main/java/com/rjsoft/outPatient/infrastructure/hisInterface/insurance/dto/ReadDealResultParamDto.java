package com.rjsoft.outPatient.infrastructure.hisInterface.insurance.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 解析交易结果通用入参
 *
 * <AUTHOR>
@Data
@EqualsAndHashCode(callSuper = true)
public class ReadDealResultParamDto extends BaseDealDto {

    /**
     * 医保交易结果字符串
     */
    private String dealStr;

    public ReadDealResultParamDto() {
    }

    public ReadDealResultParamDto(String orgCode, String cardData, String opCode, String hospitalCode) {
        this.setOrgCode(orgCode);
        this.setCardData(cardData);
        this.setOpCode(Integer.parseInt(opCode));
        this.setHospitalCode(hospitalCode);
    }

    public ReadDealResultParamDto(String orgCode, String cardData, String opCode, String hospitalCode,String dealWay,String chargeType,String registerType) {
        this.setOrgCode(orgCode);
        this.setCardData(cardData);
        this.setOpCode(Integer.parseInt(opCode));
        this.setHospitalCode(hospitalCode);
        this.setDealWay(dealWay);
        this.setChargeType(chargeType);
        this.setRegType(registerType);
    }
}
