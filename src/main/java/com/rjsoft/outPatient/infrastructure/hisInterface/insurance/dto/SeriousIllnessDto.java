package com.rjsoft.outPatient.infrastructure.hisInterface.insurance.dto;

import com.rjsoft.outPatient.infrastructure.repository.entity.SeriousIllnessRegister;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;

/**
 * 大病登记
 *
 * <AUTHOR>

@Data
@EqualsAndHashCode(callSuper = true)
public class SeriousIllnessDto extends BaseDealDto {


    /**
     * 科室编码
     */
    private String deptCode;

    /**
     * 登记类型
     */
    private String regType;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 大病项目
     */
    private String seriousIllness;

    /**
     * 诊断
     */
    private String diagnose;

    /**
     * 委托人
     */
    private String consignor;

    /**
     * 委托人身份证
     */
    private String consignorSfz;

    /**
     * 大病登记原因
     */
    private String transfersReason;

    /**
     * 大病登记描述
     */
    private String transfersReasonRemark;

    /**
     * 项目登记子类
     */
    private String cureChildren;

    /**
     * 医生名称
     */
    private String doctorName;

    /**
     * 医生工号
     */
    private String doctorNo;

    public SeriousIllnessDto(SeriousIllnessRegister entity, String cardData, String orgCode) {
        // 卡内数据
        this.setCardData(cardData);
        // 机构编码
        this.setOrgCode(orgCode);
        // 医生工号
        this.setDoctorNo(String.valueOf(entity.getCreDoctorId()));
        // 医生姓名
        this.setDoctorName(entity.getDoctor());
        // 治疗项目
        this.setSeriousIllness(entity.getTreatmentItem());
        // 委托人
        this.setConsignor(entity.getConsignor());
        // 委托人身份证
        this.setConsignorSfz(entity.getConsignorSFZ());
        // 诊断
        this.setDiagnose(entity.getDiagnosis());
        this.setHospitalCode(String.valueOf(entity.getHospitalCode()));
    }

    @Override
    public MedicareDealDto createDealDto() {
        MedicareDealDto dealDto = super.createDealDto();
        HashMap hashMap = (HashMap) dealDto.getContent();
        hashMap.put("deptid", deptCode);
        hashMap.put("djtype", regType);
        hashMap.put("startdate", startDate);
        hashMap.put("enddate", endDate);
        hashMap.put("dbxm", seriousIllness);
        hashMap.put("zd", diagnose);
        hashMap.put("wtrxm", consignor);
        hashMap.put("wtrsfzh", consignorSfz);
        hashMap.put("yy", transfersReason);
        hashMap.put("des", transfersReasonRemark);
        hashMap.put("dbzl", cureChildren);
        hashMap.put("ysxm", doctorName);
        hashMap.put("ysgh", doctorNo);
        return dealDto;
    }
}
