package com.rjsoft.outPatient.infrastructure.hisInterface.msgCenter.service.dto;

import lombok.Data;

import java.util.Date;

/**
 * 消息实体
 *
 * <AUTHOR>
@Data
public class MsgSourceDto {

    /**
     * 标题
     */
    public String Title;

    /**
     * 正文
     */
    public String Content;

    /**
     * 消息内容类型
     */
    public Integer ContentType;

    /**
     * 消息级别
     */
    public Integer TheLevel;

    /**
     * 消息定义者类型
     */
    public Integer SubscriberType;

    /**
     * 订阅者信息
     */
    public String Subscriber;

    /**
     * 需要通知的业务模块（子系统）
     */
    public Integer SystemNo;

    /**
     * 消息操作类型
     */
    public Integer OperateType;

    /**
     * 后续操作页面
     */
    public String Url;

    /**
     * 后续操作入参
     */
    public String InputParameter;

    /**
     * 创建者
     */
    public String Creator;

    /**
     * 创建时间
     */
    public Date CreateOn;

    /**
     * 创建者IP
     */
    public String CreateIP;

    /**
     * 状态
     */
    public Integer Status;

    /**
     * 1：一人确认 则全院确认 2：全员确认 确保每个人都已读
     */
    public Integer AffirmLevel;

}
