package com.rjsoft.outPatient.infrastructure.hisInterface.insurance.dto;

import com.rjsoft.common.param.RequestHead;
import lombok.Builder;
import lombok.Data;

import java.util.HashMap;

/**
 * 用于医保交易入参dto
 *
 * <AUTHOR>
@Data
public class BaseDealDto {
    /**
     * 大病登记单id
     */
    private Integer id;
    /**
     * 医院编码
     */
    private String hospitalCode;
    /**
     * 收费类型 固定值：0
     */
    private String chargeType;
    /**
     * 交易渠道  10=线下交易  20=线上交易
     */
    private String dealWay;
    /**
     * 组织代码
     */
    private String orgCode;
    /**
     * 卡数据
     */
    private String cardData;
    /**
     * 账户标识
     */
    private String accountFlag;
    /**
     * 操作人编码
     */
    private int opCode;
    /**
     * 操作人姓名
     */
    private String opName;
    /**
     * 机器码
     */
    private String machineCode;

    /**
     * 登记类型
     */
    private String regType;

    /**
     * 撤销类别
     */
    private String cxType;

    /**
     * 撤销的大病项目
     */
    private String seriousProject;

    private Integer status;



    public MedicareDealDto createDealDto() {
        MedicareDealDto dealDto = new MedicareDealDto();
        dealDto.setOrgCode(orgCode);
        dealDto.setCardData(cardData);
        dealDto.setChargeType("0");
        dealDto.setDealWay("10");
        dealDto.setHospitalCode(RequestHead.get().getHospitalCode());
        dealDto.setMachineCode(machineCode);
        dealDto.setRegType(regType);
        dealDto.setCxType(cxType);
        dealDto.setSeriousProject(seriousProject);
        dealDto.setOpCode(opCode);
        dealDto.setOpName(opName);
        dealDto.setSettlementProcess(new SettlementProcessDto());
        dealDto.getSettlementProcess().setFunctionId("");
        dealDto.setContent(new HashMap<String, String>(16));
        return dealDto;
    }
}
