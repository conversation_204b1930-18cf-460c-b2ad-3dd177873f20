package com.rjsoft.outPatient.infrastructure.hisInterface.insurance.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 解码入参
 *
 * <AUTHOR>
@Data
@EqualsAndHashCode(callSuper = true)
public class DecodeParamDto extends BaseDealDto {

    /**
     * 渠道
     */
    private String channel;

    /**
     * 二维码
     */
    private String qrCode;

    /**
     * Constructor for {@link DecodeParamDto}
     */
    public DecodeParamDto(String channel, String qrCode) {
        this.channel = channel;
        this.qrCode = qrCode;
    }

    @Override
    public MedicareDealDto createDealDto() {
        return super.createDealDto();
    }
}
