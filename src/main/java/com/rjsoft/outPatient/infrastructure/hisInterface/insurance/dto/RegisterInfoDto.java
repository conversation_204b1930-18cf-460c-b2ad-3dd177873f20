package com.rjsoft.outPatient.infrastructure.hisInterface.insurance.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
public class RegisterInfoDto {

    /**
     * 登记类别
     * 1：家床 2：急观 3：住
     * 院 4：大病 5：本院定点
     * 9：造口袋
     */
    private String djtype;

    /**
     * 登记号
     * 急观：急观号，住院：住
     * 院号，其他：填空格
     */
    private String djno;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    /**
     * 诊断编码
     * 若为大病，则填写：门诊
     * 大病登记疾病诊断分类；
     * 其他：诊断编码
     */
    private String zdno;

    /**
     * 大病项目代码
     * 若为大病，则填写大病项
     * 目代码；其他：空格
     */
    private String dbtype;

    /**
     * 大病登记子类
     * 1：特指内分泌特异抗肿
     * 瘤治疗；0：其它
     */
    private String dbzl;

    /**
     * 登记医院标志
     * 0：本院，1：其他医院
     */
    private String djhossame;

    /**
     * 登记医院名称
     */
    private String djhosname;

}
