package com.rjsoft.outPatient.infrastructure.hisInterface.insurance.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;

/**
 * 登记查询入参
 *
 * <AUTHOR>
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryRegisterInfoParamDto extends BaseDealDto {
    /**
     * 收费类型
     */
    private String regType;

    @Override
    public MedicareDealDto createDealDto(){
        MedicareDealDto dealDto = super.createDealDto();
        HashMap<String,String> hashMap = (HashMap<String,String>) dealDto.getContent();
        hashMap.put("registerType",regType);
        return dealDto;

    }
}
