package com.rjsoft.outPatient.infrastructure.hisInterface.insurance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;

/**
 * 取消登记入参
 * <AUTHOR>
@Data
@NoArgsConstructor
public class CancelIllnessParamDto extends BaseDealDto{
    /**
     * 凭证类别
     */
    private String cardtype;
    /**
     * 凭证码
     */
    private String carddata;

    /**
     * 撤销类别
     */
    private String cxtype;

    /**
     * 撤销大病项目
     */
    private String dbxm;

    @Override
    public MedicareDealDto createDealDto(){
        MedicareDealDto dealDto = super.createDealDto();
        HashMap<String,String> hashMap = (HashMap<String,String>) dealDto.getContent();
        hashMap.put("cardtype",cardtype);
        hashMap.put("carddata",carddata);
        hashMap.put("cxtype",cxtype);
        hashMap.put("dbxm",dbxm);
        return dealDto;

    }
}
