package com.rjsoft.outPatient.infrastructure.hisInterface.insurance.dto;

import lombok.Data;

/**
 * 程序处理流程
 * <AUTHOR>
@Data
public class SettlementProcessDto {

    /**
     * 卡类型
     */
    private  String cardType;

    /**
     * 收费类型
     */
    private int ChargeType;

    /**
     * 处理流程
     */
    private String settlement;

    /**
     * 流程类型
     * 目前两种 0 仅计算费用不需要调用医保 1 需要调用医保接口
     */
    private  int settlementType;

    /**
     * 处理方法位置
     */
    private String settlementAddress;

    /**
     * 处理函数所在类
     */
    private String settlementClass;

    /**
     * 处理函数
     */
    private String settlementMethod;

    /**
     * 函数名
     */
    private String functionId;

    /**
     * 状态
     */
    private int status;

    /**
     * 排序
     */
    private int orderNo;
}
