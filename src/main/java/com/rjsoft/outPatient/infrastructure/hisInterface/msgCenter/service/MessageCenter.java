package com.rjsoft.outPatient.infrastructure.hisInterface.msgCenter.service;

/**
 * 消息中心接口
 *
 * <AUTHOR>
public interface MessageCenter {

    /**
     * 发送数据同步消息
     *
     * @param contentType
     * @param title
     * @param content
     * @return
     */
    boolean sendDataSyncMessage(Integer contentType, String title, String content);

    /**
     * 消息中心发送消息
     *
     * @param contentType
     * @param title
     * @param content
     * @param subscriberType
     * @param subscriber
     * @return
     */
    boolean sendMessage(Integer contentType, String title, String content, Integer subscriberType, String subscriber);

}
