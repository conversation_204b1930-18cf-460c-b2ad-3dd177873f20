package com.rjsoft.outPatient.infrastructure.hisInterface.insurance.service.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.rjsoft.common.SystemNo;
import com.rjsoft.common.configuration.SysSetting;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.json.JsonUtils;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.request.RequestApi;
import com.rjsoft.outPatient.config.HisConfig;
import com.rjsoft.outPatient.infrastructure.hisInterface.insurance.dto.*;
import com.rjsoft.outPatient.infrastructure.hisInterface.insurance.service.InsuranceService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 医保接口
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class InsuranceServiceImpl implements InsuranceService {

    HisConfig hisConfig;


    public static void main(String[] args) {
   /*     Object[] params = new Object[8];
        params[0]="1";
        params[1]="2";
        params[2]="1";
        params[3]="2";
        params[4]="1";
        params[5]="2";
        params[6]="1";
        params[7]="2";
       String s = AxisUtil.axis("http://10.0.0.116:8025/GzdService.asmx?wsdl","AddJskjz","http://tempuri.org/",params);
        System.out.println("返回参数"+s);*/
        String str = "http://10.0.0.214:8084/JSKJZHZGZS.aspx?ID=851&&Type=1";
        //截取?之前字符串
        String str1 = str.substring(0, str.indexOf("?"));
        System.out.println("截取？号之前的字符：" + str1);
        //截取？之后的字符
        String str2 = str.substring(str1.length() + 1, str.length());
        System.out.println("截取？号之后的字符：" + str2);

    }

    /**
     * 通用医保交易
     *
     * @param dealDto
     * @return
     */
    private MedicareDealRetDto insuranceTrade(MedicareDealDto dealDto) {
        String postJson = "";
        try {
            postJson = JsonUtils.serialize(dealDto);
            Log.info("postJson:" + postJson);
        } catch (Exception ex) {
            throw new JSONException(ex.getMessage());
        }
        if ("3".equals(dealDto.getHospitalCode())) {
            return RequestApi.post(hisConfig.getInsuranceFyApi() + "/insurance/Insurance/InsuranceTrade", postJson, MedicareDealRetDto.class);
        } else {
            return RequestApi.post(hisConfig.getInsuranceApi() + "/insurance/Insurance/InsuranceTrade", postJson, MedicareDealRetDto.class);
        }
    }

    @Override
    public String decode(DecodeParamDto paramDto) {
        MedicareDealDto dealDto = paramDto.createDealDto();
        dealDto.getSettlementProcess().setSettlement("门诊医生解码");
        MedicareDealRetDto dealRetDto = insuranceTrade(dealDto);
        if (dealRetDto == null) {
            return "{\"temp\": \"2\"}";
        }
        if (dealRetDto.getRet() != 1) {
            throw new RuntimeException(dealRetDto.getMsg());
        }
        if (dealRetDto.getContent() == null) {
            return null;
        }
        Map map = (LinkedHashMap) dealRetDto.getContent();
        return Converter.toString(map.get("ecToken"));
    }

    @Override
    public String getAccountStr(ReadAccountParamDto paramDto) {
        MedicareDealDto dealDto = paramDto.createDealDto();
        dealDto.getSettlementProcess().setSettlement("门诊医生读账户");
        MedicareDealRetDto dealRetDto = insuranceTrade(dealDto);
        if (dealRetDto == null) {
            throw new RuntimeException("获取读取账户消息体失败!");
        }
        if (dealRetDto.getRet() != 1) {
            throw new RuntimeException(dealRetDto.getMsg());
        }
        if (dealRetDto.getContent() == null) {
            return null;
        }
        return Converter.toString(dealRetDto.getContent());
    }

    @Override
    public AccountFlagDto getAccountRet(ReadDealResultParamDto paramDto) {
        MedicareDealDto dealDto = paramDto.createDealDto();
        dealDto.getSettlementProcess().setSettlement("门诊医生读账户");
        dealDto.getSettlementProcess().setFunctionId("readAccountRet");
        dealDto.setContent(paramDto.getDealStr());
        MedicareDealRetDto dealRetDto = insuranceTrade(dealDto);
        if (dealRetDto == null) {
            return null;
        }
        if (dealRetDto.getRet() != 1) {
            throw new RuntimeException(dealRetDto.getMsg());
        }
        if (dealRetDto.getContent() == null) {
            return null;
        }
        Map map = (Map) dealRetDto.getContent();
        AccountFlagDto account = new AccountFlagDto();
        account.setAccountFlag(String.valueOf(map.get("accountattr")));
        account.setCardNo(String.valueOf(map.get("cardid")));
        account.setCardType(String.valueOf(map.get("cardtype")));
        account.setName(String.valueOf(map.get("personname")));
        return account;
    }

    @Override
    public String getSeriousIllnessStr(SeriousIllnessDto param) {
        MedicareDealDto dealDto = param.createDealDto();
        dealDto.setOpCode(param.getOpCode());
        dealDto.setChargeType("0");
        dealDto.setDealWay("10");
        dealDto.getSettlementProcess().setSettlement("门诊医生大病登记");
        MedicareDealRetDto dealRetDto = insuranceTrade(dealDto);
        if (dealRetDto == null) {
            dealRetDto = new MedicareDealRetDto();
            dealRetDto.setContent("{\"jysj\":\"2021-02-25 08:11:42\",\"xxlxm\":\"SJ11\",\"xxfhm\":\"\",\"fhxx\":\"\",\"bbh\":\"0001\",\"msgid\":\"*******************000811421\",\"xzqhdm\":\"310000\",\"jgdm\":\"***********\",\"czybm\":\"1067\",\"czyxm\":\"\",\"xxnr\":{\"cardtype\":\"1\",\"CardData\":\"D0370063X310000D15600000536187514B920F67F\",\"deptid\":\"15\",\"djtype\":\"4\",\"djno\":\" \",\"startdate\":\"********\",\"enddate\":\"\",\"zdnos\":[{\"zdno\":\"031\",\"zdmc\":\"抑郁症（中、重度）\"}],\"dbxm\":\"A\",\"zd\":\"031\",\"wtrxm\":\"\",\"wtrsfzh\":\"                  \",\"yy\":\"\",\"des\":\"\",\"dbzl\":\"\",\"ysxm\":\"乔颖\",\"ysgh\":\"252786\"},\"jyqd\":\"10\",\"jyyzm\":\"\",\"zdjbhs\":\"\",\"RecvTime\":\"000414b31f017388\",\"SysResv\":\"12345678876543214tmotmooomtomoototomootomooottmomoto0a282160\"}");
            dealRetDto.setRet(1);
        }
        if (dealRetDto.getRet() != 1) {
            throw new RuntimeException(dealRetDto.getMsg());
        }
        if (dealRetDto.getContent() == null) {
            return null;
        }
        return Converter.toString(dealRetDto.getContent());
    }

    @Override
    public RegResultDto getSeriousIllnessRet(ReadDealResultParamDto paramDto) {
        MedicareDealDto dealDto = paramDto.createDealDto();
        dealDto.setContent(paramDto.getDealStr());
        dealDto.setOpCode(paramDto.getOpCode());
        dealDto.setChargeType("0");
        dealDto.setDealWay("10");
        dealDto.getSettlementProcess().setSettlement("门诊医生大病登记");
        dealDto.getSettlementProcess().setFunctionId("registerRet");
        MedicareDealRetDto dealRetDto = insuranceTrade(dealDto);
        if (dealRetDto == null) {
            throw new RuntimeException("解析登记结果失败!");
        }
        if (dealRetDto.getRet() != 1) {
            throw new RuntimeException(dealRetDto.getMsg());
        }
        if (dealRetDto.getContent() == null) {
            return null;
        }
        Map map = (Map) dealRetDto.getContent();
        RegResultDto regResultDto = new RegResultDto();
        regResultDto.setEndDate(Converter.toString(map.get("enddate")));
        regResultDto.setStartDate(Converter.toString(map.get("startdate")));
        regResultDto.setLsh(Converter.toString(map.get("lsh")));
        regResultDto.setZcyymc(Converter.toString(map.get("zcyymc")));
        return regResultDto;
    }

    /**
     * 获取取消登记请求字符串
     *
     * @param dealDto
     * @return
     */
    @Override
    public String getCancelIllnessRegisterStr(MedicareDealDto dealDto) {
        dealDto.getSettlementProcess().setSettlement("门诊医生登记撤销");
        dealDto.getSettlementProcess().setFunctionId("calRegisterStr");
        HashMap<String, String> map = new HashMap<>(8);
        map.put("cxtype", dealDto.getCxType());
        map.put("dbxm", dealDto.getSeriousProject());
        dealDto.setContent(map);
        MedicareDealRetDto dealRetDto = insuranceTrade(dealDto);
        if (dealRetDto.getRet() != 1) {
            throw new RuntimeException(dealRetDto.getMsg());
        }
        return Converter.toString(dealRetDto.getContent());
    }

    /**
     * 解析取消登记结果
     *
     * @param param
     * @return
     */
    @Override
    public boolean getCancelIllnessRegisterRet(ReadDealResultParamDto param) {
        String dealStr = param.getDealStr();
        JSONObject jsonObject = JSON.parseObject(dealStr);
        String xxfhm = jsonObject.getString("xxfhm");
        if (!"P001".equals(xxfhm)) {
            String fhxx = jsonObject.getString("fhxx");
            throw new RuntimeException(fhxx);
        }
        return true;
    }

    /**
     * 获取登记查询请求字符串
     *
     * @param dealDto
     * @return
     */
    @Override
    public String getQueryRegisterInfoStr(MedicareDealDto dealDto) {
        dealDto.getSettlementProcess().setSettlement("门诊医生登记查询");
        dealDto.getSettlementProcess().setFunctionId("getRegisterQueryStr");
        HashMap<String, String> map = new HashMap<>(16);
        map.put("registerType", dealDto.getRegType());
        dealDto.setContent(map);
        MedicareDealRetDto retDto = insuranceTrade(dealDto);
        if (retDto.getRet() != 1) {
            throw new RuntimeException(retDto.getMsg());
        }
        return Converter.toString(retDto.getContent());
    }

    /**
     * 解析登记查询结果
     *
     * @param param
     * @return
     */
    @Override
    public List<RegisterInfoDto> getQueryRegisterInfoRet(ReadDealResultParamDto param) {
        List<RegisterInfoDto> res = new ArrayList<>();
        //按节点解析前端返回数据
        String dealStr = param.getDealStr();
        JSONObject dealStrObject = JSON.parseObject(dealStr);
        String xxfhm = dealStrObject.getString("xxfhm");
        String xxlxm = dealStrObject.getString("xxlxm");
        if (!"SJ31".equals(xxlxm)) {
            throw new RuntimeException("消息内容格式不正确");
        }
        if (!"P001".equals(xxfhm)) {
            String fhxx = dealStrObject.getString("fhxx");
            throw new RuntimeException(fhxx);
        }
        String xxnr = dealStrObject.getString("xxnr");
        JSONObject xxnrObject = JSON.parseObject(xxnr);
        String djxxs = xxnrObject.getString("djxxs");
        List<RegisterInfoDto> list = JSON.parseArray(djxxs, RegisterInfoDto.class);
        for (RegisterInfoDto li : list) {
            RegisterInfoDto registerInfoDto = RegisterInfoDto.builder()
                    .djtype(li.getDjtype())
                    .djno(li.getDjno())
                    .startDate(li.getStartDate())
                    .endDate(li.getEndDate())
                    .zdno(li.getZdno())
                    .dbtype("A".equals(li.getDbtype()) ? "精神病" : "")
                    .dbzl(li.getDbzl())
                    .djhossame(li.getDjhossame())
                    .djhosname(li.getDjhosname())
                    .build();
            res.add(registerInfoDto);
        }
        return res;
    }
}
