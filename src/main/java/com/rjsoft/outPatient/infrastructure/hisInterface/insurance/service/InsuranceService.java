package com.rjsoft.outPatient.infrastructure.hisInterface.insurance.service;

import com.rjsoft.outPatient.infrastructure.hisInterface.insurance.dto.*;

import java.util.List;

/**
 * 封装医保服务
 * <AUTHOR>
public interface InsuranceService {

    /**
     * 解码，电子凭证是线上，无需本地读卡器，可以直接解出令牌
     * @param paramDto
     * @return
     */
    String decode(DecodeParamDto paramDto);


    /**
     * 获取读取账户请求字符串
     * @param paramDto
     * @return
     */
    String getAccountStr(ReadAccountParamDto paramDto);

    /**
     * 解析账户读取结果
     * @param paramDto
     * @return
     */
    AccountFlagDto getAccountRet(ReadDealResultParamDto paramDto);

    /**
     * 获取大病登记请求字符串
     * @param param
     * @return
     */
    String getSeriousIllnessStr(SeriousIllnessDto param);

    /**
     * 解析大病登记结果
     * @param param
     * @return
     */
    RegResultDto getSeriousIllnessRet(ReadDealResultParamDto param);


    /**
     * 获取取消登记请求字符串
     * @param dealDto
     * @return
     */
    String getCancelIllnessRegisterStr(MedicareDealDto dealDto);

    /**
     * 解析取消登记结果
     * @param param
     * @return
     */
    boolean getCancelIllnessRegisterRet(ReadDealResultParamDto param);

    /**
     * 获取登记查询请求字符串
     * @param param
     * @return
     */
    String getQueryRegisterInfoStr(MedicareDealDto param);

    /**
     * 解析登记查询结果
     * @param param
     * @return
     */
    List<RegisterInfoDto> getQueryRegisterInfoRet(ReadDealResultParamDto param);

}
