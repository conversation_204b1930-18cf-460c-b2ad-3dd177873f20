package com.rjsoft.outPatient.infrastructure.hisInterface.apply;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.rjsoft.outPatient.infrastructure.hisInterface.apply.dto.ApplyDTO;

/**
 * 电子申请单API接口定义
 *
 * <AUTHOR>
 * @since 2021/11/23-10:36 上午
 */
public interface ApplyApi {

    /**
     * 根据收费项目码值判断是否存在申请单项目
     *
     * @param param ApplyDTO.payItemCode 收费项目代码
     * @return ApiResult.Content.itemExistsFlag 申请单项目存在标志
     * @throws JsonProcessingException json处理异常
     */
    boolean isItemExistsApplyForm(ApplyDTO param);

}
