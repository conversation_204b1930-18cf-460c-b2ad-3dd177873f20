package com.rjsoft.outPatient.infrastructure.hisInterface.apply.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.param.ApiParam;
import com.rjsoft.common.param.ApiResult;
import com.rjsoft.common.param.Head;
import com.rjsoft.outPatient.common.RestUtils;
import com.rjsoft.outPatient.infrastructure.hisInterface.apply.ApplyApi;
import com.rjsoft.outPatient.infrastructure.hisInterface.apply.config.ApplyConfig;
import com.rjsoft.outPatient.infrastructure.hisInterface.apply.dto.ApplyDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/11/23-10:48 上午
 */
@Slf4j
@Component
@AllArgsConstructor
public class ApplyApiImpl implements ApplyApi {

    private final RestUtils restUtils;
    private final ObjectMapper objectMapper;
    private final ApplyConfig applyConfig;

    @Override
    public boolean isItemExistsApplyForm(ApplyDTO param) {
        String isItemExistsApplyFormUrl = "/apply/item/judgeItemExists";
        final String applyUrl = applyConfig.getUrl();
        if (applyUrl == null) {
            Log.error("调用申请单API接口失败: 未设置URL");
            return false;
        }
        final String url = applyUrl + isItemExistsApplyFormUrl;
        try {
            final ApiParam<Object> apiParam = new ApiParam<>();
            final Map<String, Object> map = new HashMap<>(1);
            map.put("payItemCode", param.getPayItemCode());
            apiParam.setKeys(map);
            String requestJson = objectMapper.writeValueAsString(apiParam);
            Log.info("调用API接口:{}; 入参:{}", url, requestJson);
            final ApiResult result = restUtils.post(url, requestJson, ApiResult.class);

            final Head head = result.getHead();
            if (head.getRet() <= 0) {
                log.error("api 接口 {} 异常响应;msg:{}", url, result.getHead().getMsg());
                return false;
            }
            final HashMap content = (HashMap) result.getContent();
            // FIXME: yutao 2024/6/19 content.items不存在，应该是content.itemExistsFlag ,
            //  还是说”http://*********:8088/apply/item/judgeItemExists“接口有调整？
            final HashMap items = (HashMap) content.get("Items");
            final Object itemExistsFlag = items.get("itemExistsFlag");
            log.info("API接口:{} 调用成功; content:{}", url, content);
            return (boolean) itemExistsFlag;
        } catch (Exception e) {
            log.error("API接口[" + url + "]调用失败: " + e.getMessage(), e);
            return false;
        }
    }

}
