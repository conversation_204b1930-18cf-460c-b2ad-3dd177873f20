package com.rjsoft.outPatient.infrastructure.hisInterface.msgCenter.service.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.json.JsonUtils;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.request.RequestApi;
import com.rjsoft.outPatient.common.enums.CommonEnum;
import com.rjsoft.outPatient.config.HisConfig;
import com.rjsoft.outPatient.infrastructure.hisInterface.msgCenter.service.MessageCenter;
import com.rjsoft.outPatient.infrastructure.hisInterface.msgCenter.service.dto.MsgSourceDto;
import com.ruijing.code.api.HeadInfo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;

/**
 * 消息中心接口
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class MessageCenterImpl implements MessageCenter {

    HisConfig hisConfig;

    /**
     * 发送数同步消息
     *
     * @param contentType
     * @param title
     * @param content
     * @return
     */
    @Override
    public boolean sendDataSyncMessage(Integer contentType, String title, String content) {
        MsgSourceDto msg = new MsgSourceDto();
        msg.setContentType(contentType);
        msg.setContent(content);
        msg.setTitle(title);
        return SendMessage(msg);
    }


    /**
     * 消息中心发送消息
     *
     * @param contentType
     * @param title
     * @param content
     * @param subscriberType
     * @param subscriber
     * @return
     */
    @Override
    public boolean sendMessage(Integer contentType, String title, String content, Integer subscriberType, String subscriber) {
        MsgSourceDto msg = new MsgSourceDto();
        msg.setTitle(title);
        msg.setContent(content);
        msg.setContentType(contentType);
        msg.setTheLevel(4);
        msg.setSubscriberType(subscriberType);
        msg.setSubscriber(subscriber);
        msg.setSystemNo(1000);
        msg.setOperateType(1);
        msg.setUrl("");
        msg.setInputParameter("");
        msg.setCreator(Converter.toString(HeadInfo.getWorkerId()));
        msg.setCreateOn(new Date());
        msg.setCreateIP(HeadInfo.getIp());
        msg.setStatus(0);
        msg.setAffirmLevel(2);
        return SendMessage(msg);
    }

    /**
     * 发送消息
     *
     * @param msg
     * @return
     */
    private boolean SendMessage(MsgSourceDto msg) {
        try {
            String apiUrl = hisConfig.getMsgCenterApi() + "/NserviceBus/NBusMessage/SendMessage";
            HashMap res = RequestApi.post(apiUrl, JsonUtils.serialize(msg), HashMap.class);
            String ret = Converter.toString(res.get("Ret"));
            if ("0".equals(ret)) {
                return true;
            }
            String context = Converter.toString(res.get("Context"));
            Log.error(context);
            return false;
        } catch (Exception ex) {
            Log.error(ex);
            return false;
        }
    }
}
