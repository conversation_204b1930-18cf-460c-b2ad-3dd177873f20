package com.rjsoft.outPatient.infrastructure.hisInterface.apply.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/11/23-1:19 下午
 */
@Data
@Component
@ConfigurationProperties(prefix = "apply")
public class ApplyConfig {

    private String url;
    /**
     * 默认不启用
     */
    private Boolean enableJudgeItemExists = false;
}
