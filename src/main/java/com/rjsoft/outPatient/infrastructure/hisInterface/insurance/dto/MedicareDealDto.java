package com.rjsoft.outPatient.infrastructure.hisInterface.insurance.dto;

import lombok.Data;

/**
 * 医保交易
 * <AUTHOR>
@Data
public class MedicareDealDto {

    /**
     * 医疗机构代码	16	医疗机构代码格式，不足右面补空格
     */
    private String orgCode;

    /**
     * 收费类型
     */
    private String chargeType;

    /**
     * 交易流水号
     */
    private long dealNo;

    /**
     * 处理流程
     */
    private SettlementProcessDto settlementProcess;

    /**
     * 账户标识
     */
    private String accountFlag;

    /**
     * 交易渠道 10:线下交易   20：线上交易
     */
    private  String dealWay;

    /**`
     * 操作人编码
     */
    private int opCode ;
    /**`
     * 操作人姓名
     */
    private String opName ;
    /**
     * 卡内数据
     */
    private String cardData;

    /**
     * 收费终端代码
     */
    private String machineCode;

    /**
     * 医院编码
     */
    private String hospitalCode;

    /**
     * his订单号
     */
    private String orderNo;

    /**
     * 消息体
     */
    private Object content;

    /**
     * 登记类别
     */
    private String regType;

    /**
     * 撤销类别
     */
    private String cxType;

    /**
     * 撤销的大病项目
     */
    private String seriousProject;

//    private String status;
}
