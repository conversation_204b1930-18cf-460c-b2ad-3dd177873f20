package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.DoctorLevelEnum;
import com.rjsoft.outPatient.domain.doctorElemMain.constant.HospitalIdEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList;
import com.rjsoft.outPatient.infrastructure.repository.mapper.WorkerMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DoctorLevelRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.RegisterListRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/27 - 9:38
 */
@Service
@AllArgsConstructor
public class DoctorLevelRepositoryImpl implements DoctorLevelRepository {

    //private final RegisterListRepository registerListRepository;
   WorkerMapper workerMapper;

    /**
     * 根据医生ID获取医生排班中的最高级别
     * @param doctorId     医生编码
     * @param hospitalCode 医院编码
     * @return
     */
    @Override
    @DatabaseAnnotation(name =DatasourceName.RJCONFIGER)
    public Integer getDoctorLevelByDoctorId(Integer doctorId, Integer hospitalCode) {
        if(HospitalIdEnum.BRANCH.getCode().equals(hospitalCode)){
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER3);
        }
       List<Integer> list=workerMapper.getDoctorLevelByWorkerId(doctorId,hospitalCode);
       if(list==null||list.size()==0){
           return null;
       }
       //匹配最高级别
        return DoctorLevelEnum.getMaxLevel(list).getCode();
    }

}
