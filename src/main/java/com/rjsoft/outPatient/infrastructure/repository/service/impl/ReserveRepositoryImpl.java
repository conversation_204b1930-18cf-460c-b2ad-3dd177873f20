package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.param.SearchParam;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.common.utils.GetHeadInfoDao;
import com.rjsoft.outPatient.config.HisConfig;
import com.rjsoft.outPatient.domain.reserve.bo.PhoneParam;
import com.rjsoft.outPatient.domain.reserve.dto.*;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.HospitalRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.ReserveRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.WorkerRepository;
import lombok.AllArgsConstructor;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/8/11 - 13:06
 */
@Service
@AllArgsConstructor
public class ReserveRepositoryImpl implements ReserveRepository {

    private final ReserveMapper doctorSchedulingInfoMapper;
    private final WorkerMapper workerMapper;
    private final ReserveMapper reserveMapper;
    private final DctAddNumDetailMapper dctAddNumDetailMapper;
    private final RegisterListMapper registerListMapper;
    private final ReturnRecordMapper returnRecordMapper;
    private final SqlSessionTemplate sqlSessionTemplate;
    private final TimeSpanExtMapper timeSpanExtMapper;
    private final HisConfig hisConfig;
    private final AppointmentPhoneMapper appointmentPhoneMapper;
    private final GetHeadInfoDao getHeadInfoDao;
    private final HospitalRepository hospitalRepository;
    private final WorkerRepository workerRepository;


    @Override
    public List<DoctorSchedulingInfoDto> queryDoctorSchedulingInfoDto(Integer doctorId, LocalDate scheduleDate, LocalDate endDate, Integer hospitalCode, Integer deptId, Integer scheType) {
        Integer hospId = getHeadInfoDao.getHospId();
        if (!hisConfig.getIsHistoryVersion().equals("true")) {
            //根据医生身份证号，查询对应医生工号信息 Start
            Integer zyWorkerId = doctorId;
            Integer fyWorkerId = doctorId;
            List<Worker> workerList = this.workerRepository.getRelationWorker(doctorId,hospId);
            List<Worker> zyWorkerList =  workerList.stream().filter(l -> l.getHospitalId().equals(HospitalClassify.GENERAL.getHospitalCode())).collect(Collectors.toList());
            List<Worker> fyWorkerList =  workerList.stream().filter(l -> l.getHospitalId().equals(HospitalClassify.BRANCH.getHospitalCode())).collect(Collectors.toList());
            if(zyWorkerList != null && zyWorkerList.size() > 0){
                zyWorkerId = zyWorkerList.get(0).getWorkerId();
            }

            if(fyWorkerList != null && fyWorkerList.size() > 0){
                fyWorkerId = fyWorkerList.get(0).getWorkerId();
            }
            //根据医生身份证号，查询对应医生工号信息 End
            if(hospitalCode.equals(0)){
                List<DoctorSchedulingInfoDto> allList = new ArrayList<>();
                //查询总院的医生排班信息
                DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER);
                List<DoctorSchedulingInfoDto> generalList = doctorSchedulingInfoMapper.queryDoctorSchedulingInfoDto(zyWorkerId, scheduleDate, endDate, deptId, scheType, HospitalClassify.GENERAL.getHospitalCode());
                for(DoctorSchedulingInfoDto dto:generalList){
                    dto.setHospitalCode(HospitalClassify.GENERAL.getHospitalCode());
                }
                //查询分院的医生排班信息
                DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER3);
                List<DoctorSchedulingInfoDto> branchList = doctorSchedulingInfoMapper.queryDoctorSchedulingInfoDto(fyWorkerId, scheduleDate, endDate, deptId, scheType, HospitalClassify.BRANCH.getHospitalCode());
                for(DoctorSchedulingInfoDto dto:branchList){
                    dto.setHospitalCode(HospitalClassify.BRANCH.getHospitalCode());
                }
                allList.addAll(generalList);
                allList.addAll(branchList);
                return allList;
            }else if(HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode)){
                DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER);
                List<DoctorSchedulingInfoDto> generalList = doctorSchedulingInfoMapper.queryDoctorSchedulingInfoDto(zyWorkerId, scheduleDate, endDate, deptId, scheType, HospitalClassify.GENERAL.getHospitalCode());
                for(DoctorSchedulingInfoDto dto:generalList){
                    dto.setHospitalCode(HospitalClassify.GENERAL.getHospitalCode());
                }
                return generalList;
            }else{
                DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER3);
                List<DoctorSchedulingInfoDto> branchList = doctorSchedulingInfoMapper.queryDoctorSchedulingInfoDto(fyWorkerId, scheduleDate, endDate, deptId, scheType, HospitalClassify.BRANCH.getHospitalCode());
                for(DoctorSchedulingInfoDto dto:branchList){
                    dto.setHospitalCode(HospitalClassify.BRANCH.getHospitalCode());
                }
                return branchList;
            }
//            String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.ZXHIS : DatasourceName.RJCONFIGER3;
//            DataSourceSwitchAspect.changeDataSource(dataSourceName);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER);
        }
        return doctorSchedulingInfoMapper.queryDoctorSchedulingInfoDto(doctorId, scheduleDate, endDate, deptId, scheType, hospId);
    }

    @Override
    public List<QueryTimeSpanDto> queryTimeSpan(String timeMs, Date scheduleDate, Integer deptId, Integer doctorId, String hospitalCode) {
        if(String.valueOf(HospitalClassify.BRANCH.getHospitalCode()).equals(hospitalCode.trim())){
            Log.info("查询分院医生当日排班时段");
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER3);
        }else{
            Log.info(hospitalCode+ "查询总院医生当日排班时段");
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER);
        }
        return doctorSchedulingInfoMapper.queryTimeSpan(timeMs, scheduleDate, deptId, doctorId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public List<AdjustReserveTimeDTO> getAdjustReserveView(AdjustReserveTimeDTO adjustReserveTimeDTO) {
        return doctorSchedulingInfoMapper.getAdjustReserveView(adjustReserveTimeDTO);
    }

    @Override
    public List<OnDutyDeptDTO> queryShiftDeptList(String inputCode, Integer dctCode, String startTime, String endTime) {
        Integer hospId = getHeadInfoDao.getHospId();
        //根据医生身份证号，查询对应医生工号信息 Start
        Integer zyWorkerId = dctCode;
        Integer fyWorkerId = dctCode;
        List<Worker> workerList = this.workerRepository.getRelationWorker(dctCode,hospId);
        List<Worker> zyWorkerList =  workerList.stream().filter(l -> l.getHospitalId().equals(HospitalClassify.GENERAL.getHospitalCode())).collect(Collectors.toList());
        List<Worker> fyWorkerList =  workerList.stream().filter(l -> l.getHospitalId().equals(HospitalClassify.BRANCH.getHospitalCode())).collect(Collectors.toList());
        if(zyWorkerList != null && zyWorkerList.size() > 0){
            zyWorkerId = zyWorkerList.get(0).getWorkerId();
        }

        if(fyWorkerList != null && fyWorkerList.size() > 0){
            fyWorkerId = fyWorkerList.get(0).getWorkerId();
        }
        //根据医生身份证号，查询对应医生工号信息 End
        DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER);
        List<OnDutyDeptDTO> generalList = doctorSchedulingInfoMapper.queryShiftDeptList(inputCode, String.valueOf(zyWorkerId), HospitalClassify.GENERAL.getHospitalCode(), startTime, endTime).stream().distinct().collect(Collectors.toList());
        if (generalList!=null&&generalList.size() > 0) {
            OnDutyDeptDTO dept = new OnDutyDeptDTO(null, zyWorkerId, "全部", "", "", HospitalClassify.GENERAL.getHospitalCode());
            generalList.add(0, dept);
        }else{
            OnDutyDeptDTO dept = new OnDutyDeptDTO(null, zyWorkerId, "全部", "", "", HospitalClassify.GENERAL.getHospitalCode());
            generalList.add(0, dept);
        }
        DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER3);
        List<OnDutyDeptDTO> branchList = doctorSchedulingInfoMapper.queryShiftDeptList(inputCode, String.valueOf(fyWorkerId), HospitalClassify.BRANCH.getHospitalCode(), startTime, endTime).stream().distinct().collect(Collectors.toList());
        if (branchList!=null&&branchList.size() > 0) {
            OnDutyDeptDTO dept = new OnDutyDeptDTO(null, fyWorkerId, "全部", "", "", HospitalClassify.BRANCH.getHospitalCode());
            branchList.add(0, dept);
        }else{
            OnDutyDeptDTO dept = new OnDutyDeptDTO(null, fyWorkerId, "全部", "", "", HospitalClassify.BRANCH.getHospitalCode());
            branchList.add(0, dept);
        }
        List<OnDutyDeptDTO> allList = new ArrayList<>();
        allList.addAll(generalList);
        allList.addAll(branchList);
        if (allList!=null&&allList.size() > 0) {
            OnDutyDeptDTO dept = new OnDutyDeptDTO(null, dctCode, "全部", "", "", 0);
            allList.add(0, dept);
        }else{
            OnDutyDeptDTO dept = new OnDutyDeptDTO(null, dctCode, "全部", "", "", 0);
            allList.add(0, dept);
        }
        return allList;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public List<OnDutyDoctorDTO> queryShiftDoctorList(String inputCode, Integer hospitalCode, String deptCode) {
        return doctorSchedulingInfoMapper.queryShiftDoctorList(inputCode, deptCode, hospitalCode).stream().distinct().collect(Collectors.toList());
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public List<Integer> getReserveDctCode(Integer deptCode, Integer hospId) {
        return doctorSchedulingInfoMapper.getReserveDctCode(deptCode, hospId);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<DoctorInfoResponse> getDoctorInfo(List<Integer> ids, Integer hospId) {
        return workerMapper.getDoctorInfo(ids, hospId);
    }

    @Override
    public List<AppointmentInfo> findAppointmentInfo(String source, Integer doctorId, Integer hospitalId, String startTime, String endTime, Integer deptId) {
        try {
            Calendar calendar = new GregorianCalendar();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            calendar.setTime(simpleDateFormat.parse(endTime));
            calendar.add(Calendar.DATE, 1);
            endTime = simpleDateFormat.format(calendar.getTime());
            Log.info("endTime转为:" + endTime);
        }catch (ParseException pe){
            Log.error("患者预约列表转换日期异常:" + pe.getMessage());
        }
        Integer hospId = getHeadInfoDao.getHospId();
        //根据医生身份证号，查询对应医生工号信息 Start
        Integer zyWorkerId = doctorId;
        Integer fyWorkerId = doctorId;
        List<Worker> workerList = this.workerRepository.getRelationWorker(doctorId,hospId);
        List<Worker> zyWorkerList =  workerList.stream().filter(l -> l.getHospitalId().equals(HospitalClassify.GENERAL.getHospitalCode())).collect(Collectors.toList());
        List<Worker> fyWorkerList =  workerList.stream().filter(l -> l.getHospitalId().equals(HospitalClassify.BRANCH.getHospitalCode())).collect(Collectors.toList());
        if(zyWorkerList != null && zyWorkerList.size() > 0){
            zyWorkerId = zyWorkerList.get(0).getWorkerId();
        }

        if(fyWorkerList != null && fyWorkerList.size() > 0){
            fyWorkerId = fyWorkerList.get(0).getWorkerId();
        }
        //根据医生身份证号，查询对应医生工号信息 End
        List<Hospital> hospitals = hospitalRepository.getAllHospital();
        List<AppointmentInfo> appointmentInfoList = new ArrayList<>();
        if("0".equals(hospitalId.toString())){
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER);
            List<AppointmentInfo> generalList = reserveMapper.findAppointmentInfo(source, zyWorkerId, HospitalClassify.GENERAL.getHospitalCode(), startTime, endTime, deptId);
            if(generalList!=null){
                for(AppointmentInfo appointmentInfo:generalList){
                    appointmentInfo.setHospitalCode(HospitalClassify.GENERAL.getHospitalCode());
                }
                appointmentInfoList.addAll(generalList);
            }
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER3);
            List<AppointmentInfo> branchList = reserveMapper.findAppointmentInfo(source, fyWorkerId, HospitalClassify.BRANCH.getHospitalCode(), startTime, endTime, deptId);
            if(branchList!=null){
                for(AppointmentInfo appointmentInfo:branchList){
                    appointmentInfo.setHospitalCode(HospitalClassify.BRANCH.getHospitalCode());
                }
                appointmentInfoList.addAll(branchList);
            }
        }else{
            Integer rst_doctorId =  HospitalClassify.GENERAL.getHospitalCode().equals(hospitalId) ? zyWorkerId : fyWorkerId;
            String dataName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalId)?DatasourceName.RJCONFIGER:DatasourceName.RJCONFIGER3;
            DataSourceSwitchAspect.changeDataSource(dataName);
            appointmentInfoList = reserveMapper.findAppointmentInfo(source, rst_doctorId, hospitalId, startTime, endTime, deptId);
            for(AppointmentInfo appointmentInfo:appointmentInfoList){
                appointmentInfo.setHospitalCode(hospitalId);
            }
        }
        if(appointmentInfoList.size()>0){
            for(AppointmentInfo appointmentInfo:appointmentInfoList){
                for(Hospital hospital:hospitals){
                    if(appointmentInfo.getHospitalCode().toString().equals(hospital.getHospitalCode())){
                        appointmentInfo.setSimpleName(hospital.getSimpleName());
                        break;
                    }
                }
            }
        }
        return appointmentInfoList;
    }


    @Override
    public List<DctAddNumDetail> queryDctAddNumDetail(SearchParam param) {
        String dataName = HospitalClassify.GENERAL.getHospitalCode().equals(param.getHospitalCode())?DatasourceName.RJCONFIGER:DatasourceName.RJCONFIGER3;
        DataSourceSwitchAspect.changeDataSource(dataName);
        Integer type = Converter.toInt32(param.getKeys().get("type"));
        Integer doctorId = Converter.toInt32(param.getKeys().get("doctorId"));
        Integer hospitalCode = Converter.toInt32(param.getHospitalCode());
        Integer visitFlag = Converter.toInt32(param.getKeys().get("visitFlag"));
        String cardNo = Converter.toString(param.getKeys().get("cardNo"));
        String patName = Converter.toString(param.getKeys().get("patName"));
        String certificateNo = Converter.toString(param.getKeys().get("certificateNo"));
        PageHelper.startPage(param.getPageNum(), param.getPageSize(), null);
        List<DctAddNumDetail> list = dctAddNumDetailMapper.queryDctAddNumDetail(type, doctorId, hospitalCode, visitFlag, cardNo, patName, certificateNo);

        PageInfo pageInfo = new PageInfo<>(list);
        param.setTotalCount(Converter.toInt32(pageInfo.getTotal()));
        return list;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public List<DoctorAddNum> getDoctorSchedulingInfo(Integer doctorId, Integer hospitalCode, Integer deptCode) {
        return dctAddNumDetailMapper.getDoctorSchedulingInfo(doctorId, hospitalCode, deptCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public int queryDctAddNumDetailNum(Integer subjectId, Integer hospitalCode, Integer deptCode, Integer doctorId) {
        return dctAddNumDetailMapper.queryDctAddNumDetailNum(subjectId, hospitalCode, deptCode, doctorId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public String GetTimeSpanExt(Integer hospitalCode) {
        return timeSpanExtMapper.GetTimeSpanExt(hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public List<DoctorAddNum> getDoctorScheduling(Integer doctorId, Integer deptCode, Integer hospitalCode, String timeMs) {
        return dctAddNumDetailMapper.getDoctorScheduling(doctorId, deptCode, hospitalCode, timeMs);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public int getAddNumDetailCount(String certificateNo, Integer hospitalCode) {
        return dctAddNumDetailMapper.getAddNumDetailCount(certificateNo, hospitalCode);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public List<TimeSpanExt> getTimeSpanExtInfo(String name, Integer hospitalCode) {
        return timeSpanExtMapper.getTimeSpanExtInfo(name, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public ReserveNum getVisitNum(Integer hospitalCode, Integer mainId) {
        return dctAddNumDetailMapper.getVisitNum(hospitalCode, mainId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public Integer getSeqNum(Integer subjectId, Integer hospitalCode) {
        return dctAddNumDetailMapper.getSeqNum(subjectId, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public Integer getSubjectSeqNum(Integer subjectId, Integer hospitalCode) {
        return dctAddNumDetailMapper.getSubjectSeqNum(subjectId, hospitalCode);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public void addNum(DctAddNumDetail detail) {
        dctAddNumDetailMapper.insertSelective(detail);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public void deleteRegister(Integer id, Integer doctorId) {
        DctAddNumDetail detail = new DctAddNumDetail();
        detail.setId(Converter.toInt64(id));
        detail.setIsUse(Converter.toInt64(0));
        detail.setUpdatedBy(Converter.toInt64(doctorId));
        detail.setUpdatedDate(new Date());
        detail.setStatus(3);
        dctAddNumDetailMapper.updateByPrimaryKeySelective(detail);
    }

    @Override
    public DctAddNumDetail getDctAddNumDetailOne(Integer id, Integer hospitalCode) {
        String dataName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode)?DatasourceName.RJCONFIGER:DatasourceName.RJCONFIGER3;
        DataSourceSwitchAspect.changeDataSource(dataName);
        DctAddNumDetail dctAddNumDetail = new DctAddNumDetail();
        dctAddNumDetail.setId(Converter.toInt64(id));
        dctAddNumDetail.setHospitalCode(Converter.toString(hospitalCode));
        return dctAddNumDetailMapper.selectOne(dctAddNumDetail);
    }


    @Override
    public boolean checkRegisterInfo(Integer id, Integer doctorId, Integer checkStatus, Integer hospitalCode) {
        String dataName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode)?DatasourceName.RJCONFIGER:DatasourceName.RJCONFIGER3;
        DataSourceSwitchAspect.changeDataSource(dataName);
        DctAddNumDetail dctAddNumDetail = new DctAddNumDetail();
        dctAddNumDetail.setId(Converter.toInt64(id));
        dctAddNumDetail.setApproveBy(doctorId);
        dctAddNumDetail.setApproveDate(new Date());
        //0审核通过 1驳回
        if (checkStatus.equals(0)) {
            dctAddNumDetail.setStatus(2);
        } else if (checkStatus.equals(1)) {
            dctAddNumDetail.setStatus(4);
        }
        return dctAddNumDetailMapper.updateByPrimaryKeySelective(dctAddNumDetail) > 0;
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public RegisterList regCount(Long regNo, Integer hospitalCode) {
        RegisterList registerList = null;
        registerList = registerListMapper.getRegisterByRegNo(regNo, hospitalCode, null);
        if (registerList == null) {
            registerList = registerListMapper.getRegisterByRegNo(regNo, hospitalCode, "Reg_Tb_RegisterList");
        }
        return registerList;
    }


    @Override
    @DatabaseAnnotation
    public int recordCount(Long regNo, Integer hospitalCode) {
        return returnRecordMapper.recordCount(regNo, hospitalCode);
    }


    @Override
    @DatabaseAnnotation
    public int addReturnRecord(RegisterList param, Integer operator) {
        return returnRecordMapper.addReturnRecord(param, operator);
    }


    /**
     * 医技预约
     *
     * @param time
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.YLSYS_NEW)
    public List<Map<String, Object>> medicalAppointment(String time) {
        sqlSessionTemplate.getSqlSessionFactory().getConfiguration().setCallSettersOnNulls(true);
        return reserveMapper.medicalAppointment(time);
        // TODO 正式环境需要切换，开发环境下返回假数据
        // ArrayList<Map<String, Object>> maps = new ArrayList<>();
        // Map<String, Object> map = new LinkedHashMap<>(16);
        // map.put("开始时间","08:05");
        // map.put("结束时间","11:30");
        // map.put("科室名称","B超科室");
        // map.put("科室编码","202");
        // map.put("2021-12-06(星期一)已约数","0");
        // map.put("2021-12-06(星期一)最大可约数","13");
        // map.put("2021-12-06(星期二)已约数","0");
        // map.put("2021-12-06(星期二)最大可约数","13");
        // map.put("2021-12-06(星期三)已约数","0");
        // map.put("2021-12-06(星期三)最大可约数","12");
        // map.put("2021-12-06(星期四)已约数","0");
        // map.put("2021-12-06(星期四)最大可约数","12");
        // maps.add(map);
        // return maps;
    }


    @Override
    public TimeSpanExt getTimeSpanInfo(Integer timeSpanId, Integer hospitalCode) {
        String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.RJCONFIGER : DatasourceName.RJCONFIGER3;
        DataSourceSwitchAspect.changeDataSource(dataSourceName);
        return timeSpanExtMapper.getTimeSpanInfo(timeSpanId, hospitalCode);
    }


    @Override
    public String getReserve(Integer regSerialNo, String appointmentOrderId, Integer hospitalCode) {
        String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.RJCONFIGER : DatasourceName.RJCONFIGER3;
        DataSourceSwitchAspect.changeDataSource(dataSourceName);
        return reserveMapper.getReserve(regSerialNo, appointmentOrderId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public AppointmentPhone getAppointmentPhone(Integer deptId,Integer doctorId,Integer patId,Integer hospitalCode,Integer regNo) {
        Weekend<AppointmentPhone> weekend = new Weekend<AppointmentPhone>(AppointmentPhone.class);
        weekend.weekendCriteria().andEqualTo(AppointmentPhone::getDeptId,deptId)
                .andEqualTo(AppointmentPhone::getDoctorId,doctorId)
                .andEqualTo(AppointmentPhone::getPatId,patId)
                .andEqualTo(AppointmentPhone::getHospitalCode,hospitalCode)
                .andEqualTo(AppointmentPhone::getRegNo,regNo);
        return appointmentPhoneMapper.selectOneByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public int updateAppointmentPhone(PhoneParam phoneParam) {
        Weekend<AppointmentPhone> weekend = new Weekend<>(AppointmentPhone.class);
        weekend.weekendCriteria().andEqualTo(AppointmentPhone::getDeptId,phoneParam.getDeptId())
                .andEqualTo(AppointmentPhone::getDoctorId,phoneParam.getDoctorId())
                .andEqualTo(AppointmentPhone::getPatId,phoneParam.getPatId())
                .andEqualTo(AppointmentPhone::getHospitalCode,phoneParam.getHospitalCode())
                .andEqualTo(AppointmentPhone::getRegNo,phoneParam.getRegNo());
        AppointmentPhone appointmentPhone = appointmentPhoneMapper.selectOneByExample(weekend);
        Date date = new Date();
        int result = 0;
        if(appointmentPhone==null){
            AppointmentPhone insertAppointmentPhone = new AppointmentPhone();
            insertAppointmentPhone.setHospitalCode(phoneParam.getHospitalCode());
            insertAppointmentPhone.setDelFlag(false);
            insertAppointmentPhone.setDeptId(phoneParam.getDeptId());
            insertAppointmentPhone.setDoctorId(phoneParam.getDoctorId());
            insertAppointmentPhone.setPatId(phoneParam.getPatId());
            insertAppointmentPhone.setRegNo(phoneParam.getRegNo());
            insertAppointmentPhone.setPhone(phoneParam.getPhone());
            insertAppointmentPhone.setCreTime(date);
            insertAppointmentPhone.setOpTime(date);
            result = appointmentPhoneMapper.insertSelective(insertAppointmentPhone);
        }else{
            AppointmentPhone updateAppointmentPhone = new AppointmentPhone();
            updateAppointmentPhone.setId(appointmentPhone.getId());
            updateAppointmentPhone.setPhone(phoneParam.getPhone());
            result = appointmentPhoneMapper.updateByPrimaryKeySelective(updateAppointmentPhone);
        }
        return result;
    }
}
