package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.SystemPubUsagenoteffectdruginfo;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SystemPubUsagenoteffectdruginfoMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.SystemPubUsagenoteffectdruginfoRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

@Service
@AllArgsConstructor
public class SystemPubUsagenoteffectdruginfoRepositoryImpl implements SystemPubUsagenoteffectdruginfoRepository {
    private SystemPubUsagenoteffectdruginfoMapper systemPubUsagenoteffectdruginfoMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<SystemPubUsagenoteffectdruginfo> getListByUsageIds(List<Integer> usageIds ,Integer hospId) {
        Weekend<SystemPubUsagenoteffectdruginfo> weekend = new Weekend<>(SystemPubUsagenoteffectdruginfo.class);
        weekend.weekendCriteria().andIn(SystemPubUsagenoteffectdruginfo::getUsageId,usageIds)
                .andEqualTo(SystemPubUsagenoteffectdruginfo::getHospId,hospId);
        return systemPubUsagenoteffectdruginfoMapper.selectByExample(weekend);
    }
}
