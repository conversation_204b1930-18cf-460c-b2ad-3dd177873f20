<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.CaseHistoryLogMapper">

    <select id="getCaseHistoryLogControl"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.CaseHistoryLog">
        select a.id,
        a.bllsh blNo,
        a.blkh blCardNo,
        a.hzbh patId,
        a.hzxm patName,
        a.ksbm deptId,
        a.ksmc deptName,
        a.ysbm doctorId,
        a.ysmc doctorName,
        a.jzlsh receptionNo,
        a.regno regNo,
        a.opType opType,
        a.opDoctorId,
        a.opDoctorName,
        a.opTime,
        a.yybm hospitalCode,
        [dbo].[fn_GetPy](a.hzxm) pym,
        b.sckzrq receptionDate
        from MZYS_TB_DZBLJL a
        inner join MZYS_TB_KZJL b on a.jzlsh = b.jzlsh
        where a.opTime between #{startTime} and #{endTime}
        and a.yybm = #{hospitalCode}
        <if test="hisCardNo != null">
            and a.blkh = #{hisCardNo}
        </if>
        <if test="regNo != null and regNo!=0 ">
            and a.regno = #{regNo}
        </if>
        <if test="patName != null">
            and a.hzxm = #{patName}
        </if>
        <if test="workerId != null and workerId!=0 ">
            and a.opDoctorId = #{workerId}
        </if>
        <if test="state != null and state!=-1 ">
            and a.opType = #{state}
        </if>
        order by a.id ASC, ${sortField}
    </select>

</mapper>