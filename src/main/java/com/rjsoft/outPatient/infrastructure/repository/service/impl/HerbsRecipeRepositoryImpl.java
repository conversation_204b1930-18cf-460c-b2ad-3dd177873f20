package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.ChargeStatusEnum;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 门诊处方
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class HerbsRecipeRepositoryImpl implements HerbsRecipeRepository {

    RecipeMapper recipeMapper;
    RecipeEXMapper recipeEXMapper;
    RecipeDetailMapper recipeDetailMapper;
    RecipeContactInfoMapper recipeContactInfoMapper;
    OldRecipeDetailMapper oldRecipeDetailMapper;
    OldRecipeMapper oldRecipeMapper;
    SystemTbPubItemsRepository systemTbPubItemsRepository;
    RecipeDetailLogRepository recipeDetailLogRepository;

    @Override
    @DatabaseAnnotation
    @Transactional(rollbackFor = {Exception.class})
    public boolean saveHerbRecipe(Recipe recipe) {
        boolean success = recipeMapper.saveRecipe(recipe);
        if (!success) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        success = recipeEXMapper.saveRecipeEx(recipe);
        if (!success) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        if (recipe.getRecipeDetails() != null) {
            for (RecipeDetail recipeDetail : recipe.getRecipeDetails()) {
                if (recipeDetail.getDataFlag() == -1) {
                    //删除日志记录
                    recipeDetailLogRepository.saveDeleteRecipeDetailLogByIds(Collections.singletonList(recipeDetail.getRecipeDetailNo()));
                    success = recipeDetailMapper.deleteByPrimaryKey(recipeDetail) > 0;
                } else {
                    success = recipeDetailMapper.saveRecipeDetail(recipeDetail);
                }
                if (!success) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return false;
                }
            }
        }
        success = recipeContactInfoMapper.saveRecipeContactInfo(recipe);
        if (!success) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }



    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailHerbsSpecial(Long recipeNo, Integer hospitalCode) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria()
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                .andEqualTo(RecipeDetail::getRecipeNo, recipeNo)
                .andEqualTo(RecipeDetail::getRecipeCategory, ItemCategoryEnum.ChineseHerbMed.getCategoryCode())
                .andNotEqualTo(RecipeDetail::getFeeCategory, ItemCategoryEnum.ChineseHerbMed.getCategoryCode())
                .andNotIn(RecipeDetail::getStatus, Arrays.asList(ChargeStatusEnum.CHARGED.getCode(),
                        ChargeStatusEnum.BEING_CHARGED.getCode(),
                        ChargeStatusEnum.REGISTERED.getCode(),
                        ChargeStatusEnum.DEREGISTER.getCode()));
        return recipeDetailMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailHerbsSpecialByReceptionNo(Long ReceptionNo, Integer hospitalCode) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria()
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                .andEqualTo(RecipeDetail::getReceptionNo, ReceptionNo)
                .andEqualTo(RecipeDetail::getRecipeCategory, ItemCategoryEnum.ChineseHerbMed.getCategoryCode())
                .andNotEqualTo(RecipeDetail::getFeeCategory, ItemCategoryEnum.ChineseHerbMed.getCategoryCode());
        return recipeDetailMapper.selectByExample(weekend);
    }

    @Override
    public List<OldRecipeDetail> getOldRecipeDetailbyReceptionNo(List<String> recipeNos, Integer feeCategory, Integer hospitalCode) {
        String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.MZYS : DatasourceName.MZYS3;
        DataSourceSwitchAspect.changeDataSource(dataSourceName);
        Weekend<OldRecipeDetail> weekend = new Weekend<>(OldRecipeDetail.class);
        weekend.weekendCriteria().andIn(OldRecipeDetail::getRecipeNo, recipeNos)
                .andEqualTo(OldRecipeDetail::getFeeCategory, feeCategory);
        return oldRecipeDetailMapper.selectByExample(weekend);
    }


    @Override
    @DatabaseAnnotation
    public boolean deleteHerbsRecipebyRrecipeNo(Long recipeNo, Integer feeCategory, Integer hospitalCode, Long receptionNo) {
        Weekend<Recipe> weekend = new Weekend<>(Recipe.class);
        weekend.weekendCriteria().andEqualTo(Recipe::getRecipeNo, recipeNo)
                .andEqualTo(Recipe::getHospitalCode, hospitalCode)
                .andEqualTo(Recipe::getFeeCategory, feeCategory);
        int countRecipe = recipeMapper.deleteByExample(weekend);

        Weekend<RecipeDetail> weekend1 = new Weekend<>(RecipeDetail.class);
        WeekendCriteria<RecipeDetail, Object> weekendCriteria = weekend1.weekendCriteria();
        weekendCriteria.andEqualTo(RecipeDetail::getRecipeNo, recipeNo).andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        List<SystemTbPubItems> chargeItems = systemTbPubItemsRepository.getSystemTbPubItems("ZYBZLZ", hospitalCode);
        if (chargeItems != null && chargeItems.size() > 0) {
            SystemTbPubItems systemTbPubItems = chargeItems.get(0);
            weekendCriteria.andNotEqualTo(RecipeDetail::getItemCode, systemTbPubItems.getItemCode());
        }
        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        int countDetail = recipeDetailMapper.deleteByExample(weekend1);
        Weekend<RecipeEX> weekend2 = new Weekend<>(RecipeEX.class);
        weekend2.weekendCriteria().andEqualTo(RecipeEX::getRecipeNo, recipeNo).andEqualTo(RecipeEX::getHospitalCode, hospitalCode);
        int countEX = recipeEXMapper.deleteByExample(weekend2);
        Weekend<RecipeContactInfo> weekend3 = new Weekend<>(RecipeContactInfo.class);
        weekend3.weekendCriteria().andEqualTo(RecipeContactInfo::getRecipeNo, recipeNo).andEqualTo(RecipeContactInfo::getHospitalCode, hospitalCode);
        int countContact = recipeContactInfoMapper.deleteByExample(weekend3);
        if (countRecipe > 0 && countDetail > 0 && countEX > 0 && countContact > 0) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public List<OldRecipe> getOldRecipeInfobyReceptionNos(List<String> receptionNos, Integer feeCategory, Integer hospitalCode) {
        String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.MZYS : DatasourceName.MZYS3;
        DataSourceSwitchAspect.changeDataSource(dataSourceName);
        Weekend<OldRecipe> weekend = new Weekend<>(OldRecipe.class);
        weekend.weekendCriteria().andIn(OldRecipe::getReceptionNo, receptionNos)
                .andEqualTo(OldRecipe::getFeeCategory, feeCategory);
        weekend.orderBy("firstDate").desc();
        return oldRecipeMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailbyReceptionNo(List<Long> recipeNos, Integer feeCategory, Integer hospitalCode) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria().andIn(RecipeDetail::getRecipeNo, recipeNos)
                .andEqualTo(RecipeDetail::getFeeCategory, feeCategory)
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        return recipeDetailMapper.selectByExample(weekend);
    }

}
