package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbPsychotherapyApplyListPre;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface MzysTbPsychotherapyApplyListPreMapper extends Mapper<MzysTbPsychotherapyApplyListPre> {

    @DatabaseAnnotation
    default List<MzysTbPsychotherapyApplyListPre> selectByPatientId(Integer patientId, Integer hospitalCode, Integer applyStatus) {
        Weekend<MzysTbPsychotherapyApplyListPre> weekend = new Weekend<>(MzysTbPsychotherapyApplyListPre.class);
        WeekendCriteria<MzysTbPsychotherapyApplyListPre, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(MzysTbPsychotherapyApplyListPre::getPatientId, patientId)
                .andEqualTo(MzysTbPsychotherapyApplyListPre::getHospitalId, hospitalCode)
                .andEqualTo(MzysTbPsychotherapyApplyListPre::getApplyStatus, applyStatus);
        return selectByExample(weekend);
    }

}