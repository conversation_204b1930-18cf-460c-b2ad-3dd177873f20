package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Recipe;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeEX;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;

/**
 * 门诊处方扩展表
 *
 * <AUTHOR>
public interface RecipeEXMapper extends BaseMapper<RecipeEX>, ExampleMapper<RecipeEX> {
    /**
     * 保存处方扩展
     *
     * @param recipe
     * @return
     */
    default boolean saveRecipeEx(Recipe recipe) {
        RecipeEX recipeEX = recipe.getRecipeEX();
        if (recipe.getDataFlag() == 0) {
            return insertSelective(recipeEX) > 0;
        } else if (recipe.getDataFlag() == 1) {
            return updateByPrimaryKeySelective(recipeEX) > 0;
        } else {
            return false;
        }
    }
}
