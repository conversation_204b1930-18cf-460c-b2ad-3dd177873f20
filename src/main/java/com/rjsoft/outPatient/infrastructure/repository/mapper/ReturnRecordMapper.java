package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Agent;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList;
import com.rjsoft.outPatient.infrastructure.repository.entity.ReturnRecord;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.Date;

public interface ReturnRecordMapper extends Mapper<ReturnRecord> {

    /**
     * 根据挂号流水号获取退号记录
     * @param regNo
     * @param hospitalCode
     */
    default int recordCount(Long regNo,Integer hospitalCode){
        ReturnRecord record = new ReturnRecord();
        record.setRegNo(regNo);
        record.setHospitalCode(hospitalCode);
        record.setStatus(0);
        return selectCount(record);
    }

    /**
     * 向退号表添加申请记录
     * @param param
     *  @param operator
     */
    default int addReturnRecord(RegisterList param,Integer operator){
        ReturnRecord record = new ReturnRecord();
        record.setRegNo(param.getRegNo());
        record.setPatNo(param.getPatID());
        record.setSqsj(new Date());
        record.setStatus(0);
        record.setYsbm(operator);
        record.setHospitalCode(param.getHospitalCode());
        return insertSelective(record);
    }


}
