package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "MZYS_TB_PsychotherapyRecord")
public class MzysTbPsychotherapyRecord implements Serializable {
    @Column(name = "SerialNo")
    private Long serialno;

    @Column(name = "HospitalId")
    private Integer hospitalid;

    @Column(name = "ApplyId")
    private Long applyid;

    @Column(name = "ApplyDetailId")
    private Long applydetailid;

    @Column(name = "ItemId")
    private Integer itemid;

    @Column(name = "ItemName")
    private String itemname;

    @Column(name = "DoctorId")
    private Integer doctorid;

    @Column(name = "RecordTime")
    private Date recordtime;

    @Column(name = "RecordContent")
    private String recordcontent;

    @Column(name = "CreateUserId")
    private Integer createuserid;

    @Column(name = "CreateOn")
    private Date createon;

    private static final long serialVersionUID = 1L;

}