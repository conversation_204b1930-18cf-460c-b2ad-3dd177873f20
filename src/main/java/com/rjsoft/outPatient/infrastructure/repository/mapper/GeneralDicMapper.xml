<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.GeneralDicMapper">


    <select id="getGenaralDic" resultType="com.rjsoft.outPatient.domain.config.dto.HisDictionaryDto">
        select HisDictionaryCode hisDictionaryCode, HisDictionaryName hisDictionaryName,DictionaryTypeID
        dictionaryTypeID
        from TB_Dic_HisDictionary
        where DictionaryTypeID=#{dictionaryTypeID}
        and HisDictionaryCode = #{hisDictionaryCode}
        and HospitalId=#{hospId}
    </select>
</mapper>