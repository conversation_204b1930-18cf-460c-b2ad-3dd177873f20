package com.rjsoft.outPatient.infrastructure.repository.strategy.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.PreRecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeRepository;
import com.rjsoft.outPatient.infrastructure.repository.strategy.PreRecipeStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component("PreRecipeByItemCodeAndAssayCategoryName")
public class PreRecipeByItemCodeAndAssayCategoryName extends PreRecipeByItemCodeBase implements PreRecipeStrategy {

    @Resource
    RecipeRepository recipeRepository;

    @Override
    @DatabaseAnnotation(name = "HISDB")

    public List<PreRecipeDetail> getPreRecipeDetails(CheckRecipeDto checkDto, Map errInfo) {
        List<PreRecipeDetail> details = getPreRecipeDetailsByItemCodeAndAssayCategoryName(checkDto);
        return details;
    }

    @DatabaseAnnotation(name = "HISDB")
    private List<PreRecipeDetail> getPreRecipeDetailsByItemCodeAndAssayCategoryName(CheckRecipeDto checkDto) {
        final List<PreRecipeDetail> preRecipeDetailList = getPreRecipeDetailsByItemCode(checkDto);
        preRecipeDetailList.forEach(i -> i.setAssayCategoryName(checkDto.getAssayCategoryName()));
        return preRecipeDetailList;
    }

}
