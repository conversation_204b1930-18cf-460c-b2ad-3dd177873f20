package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.ExecDeptConfig;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

/**
 * 执行科室配置
 *
 * <AUTHOR>
public interface ExecDeptConfigMapper extends Mapper<ExecDeptConfig> {


    /**
     * 查询执行科室列表
     *
     * @param configType
     * @param feeCategory
     * @param hospitalCode
     * @return
     */
    default List<ExecDeptConfig> getExecDeptByInputCode(String configType, Integer feeCategory, Integer hospitalCode) {
        Weekend<ExecDeptConfig> weekend = new Weekend<>(ExecDeptConfig.class);
        WeekendCriteria<ExecDeptConfig, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ExecDeptConfig::getConfigType, configType);
        if (feeCategory != null) {
            weekendCriteria.andEqualTo(ExecDeptConfig::getFeeCategory, feeCategory);
        }
        weekendCriteria.andEqualTo(ExecDeptConfig::getHospitalCode, hospitalCode);
        return selectByExample(weekend);
    }


    /**
     * 根据配置ID，配置类型获取执行科室
     *
     * @param configId
     * @param configType
     * @param itemCategory
     * @param hospitalCode
     * @return
     */
    @DatabaseAnnotation
    default ExecDeptConfig getExecDeptById(Integer configId, String configType, Integer itemCategory, Integer hospitalCode) {
        ExecDeptConfig entity = new ExecDeptConfig();
        entity.setConfigId(configId);
        entity.setConfigType(configType);
        if (itemCategory != null && itemCategory != 0) {
            entity.setFeeCategory(itemCategory);
        }
        entity.setHospitalCode(hospitalCode);
        List<ExecDeptConfig> list = select(entity);
        if (list.size() == 0) {
            return null;
        }
        return list.get(0);
    }


    /**
     * 根据[项目编码]和[医院编码] 获取默认执行科室
     *
     * @param itemCode
     * @param hospitalCode
     * @param feeCategory
     * @param configType
     * @return
     */
    default List<ExecDeptConfig> getDept(Integer itemCode, Integer hospitalCode, Integer feeCategory, String configType) {
        ExecDeptConfig config = new ExecDeptConfig();
        config.setConfigId(itemCode);
        config.setHospitalCode(hospitalCode);
        config.setFeeCategory(feeCategory);
        config.setConfigType(configType);
        return select(config);
    }

}
