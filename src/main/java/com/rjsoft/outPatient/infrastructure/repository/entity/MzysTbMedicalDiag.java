package com.rjsoft.outPatient.infrastructure.repository.entity;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

/**
    * 门诊病案首页诊断信息
    */
@Data
@Table(name = "MZYS_Tb_MedicalDiag")
public class MzysTbMedicalDiag implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 挂号流水号
     */
    @Column(name = "reg_no")
    private Long regNo;

    /**
     * 门（急）诊诊断编码
     */
    @Column(name = "diag_code")
    private String diagCode;

    /**
     * 门（急）诊诊断名称
     */
    @Column(name = "diag_name")
    private String diagName;

    /**
     * 主诊断标识 1是 0否
     */
    @Column(name = "main_flag")
    private Boolean mainFlag;

    /**
     * 序号
     */
    @Column(name = "order_no")
    private Integer orderNo;

    /**
     * 院区ID
     */
    @Column(name = "hospital_id")
    private Integer hospitalId;

    private static final long serialVersionUID = 1L;
}