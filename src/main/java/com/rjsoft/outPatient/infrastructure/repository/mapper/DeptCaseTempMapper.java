package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QueryBaseTempContrastResult;
import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QueryDefaultTempResult;
import com.rjsoft.outPatient.infrastructure.repository.entity.DeptCaseTemp;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

public interface DeptCaseTempMapper extends BaseMapper<DeptCaseTemp>, ExampleMapper<DeptCaseTemp> {


    /**
     * 根据科室编码+医院编码获取科室病历模板
     *
     * @param deptId
     * @param hospCode
     * @return
     */
    List<QueryDefaultTempResult> queryDeptTemp(@Param("deptId") Integer deptId,
                                               @Param("hospCode") Integer hospCode,
                                               @Param("searchType") Integer searchType,
                                               @Param("tempName") String tempName);

    /**
     * 获取全院模板(共享)
     *
     * @param hospCode
     * @param tempName
     * @return
     */
    List<QueryDefaultTempResult> queryHospTemp(@Param("hospCode") Integer hospCode,
                                               @Param("tempName") String tempName);


    /**
     * 查询科室默认模板
     *
     * @param deptId       科室编码
     * @param visitFlag    初复诊标记
     * @param sexFlag      性别编码
     * @param hospitalCode 医院编码
     * @return
     */
    QueryDefaultTempResult queryDeptDefaultTemp(@Param("deptId") int deptId,
                                                @Param("visitFlag") int visitFlag,
                                                @Param("sexFlag") int sexFlag,
                                                @Param("hospCode") int hospitalCode);

    /**
     * 查询基础模板对照列表
     *
     * @param deptId
     * @param hospCode
     * @return
     */
    List<QueryBaseTempContrastResult> queryBaseTempContrast(@Param("deptId") Integer deptId,
                                                            @Param("hospCode") Integer hospCode,
                                                            @Param("tempName") String tempName);


}
