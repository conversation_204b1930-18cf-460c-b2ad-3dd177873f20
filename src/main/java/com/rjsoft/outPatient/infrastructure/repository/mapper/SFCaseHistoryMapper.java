package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.SFCaseHistory;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;


public interface SFCaseHistoryMapper extends BaseMapper<SFCaseHistory>, ExampleMapper<SFCaseHistory> {


    /**
     * 查询患者舒辅病历信息
     *
     * @param patId
     * @param hospitalCode
     * @return
     */
    SFCaseHistory getPatShuFu(@Param("patId") String patId, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 查询患者舒辅病历信息
     *
     * @param patId
     * @param hospitalCode
     * @return
     */
    SFCaseHistory getPatShuFuOld(@Param("patId") String patId, @Param("hospitalCode") Integer hospitalCode);


}
