package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.DiagnosisProof;
import com.rjsoft.outPatient.infrastructure.repository.entity.PackageDetail;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

/**
 * 诊断证明
 */
public interface DiagnosisProofMapper extends BaseMapper<DiagnosisProof>, ExampleMapper<DiagnosisProof> {



}
