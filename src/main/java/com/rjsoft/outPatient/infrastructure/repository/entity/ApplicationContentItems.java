package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 申请单收费项目内容
 */
@Data
@Table(name = "MZYS_TB_JCSQDNRXM")
public class ApplicationContentItems  implements Serializable {

    @Id
    @Column(name = "Id", insertable = false, updatable = false)
    private Integer id;

    @Column(name = "nrid")
    private Integer contentId;

    @Column(name = "xmbm")
    private Integer itemCode;
    
    public ApplicationContentItems() {
    }

    public ApplicationContentItems(Integer contentId) {
        this.contentId = contentId;
    }
}