package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * 患者课题入组表
 * @TableName Sci_Patient_Project
 */
@Data
@Table(name = "Sci_Patient_Project")
public class SciPatientProject {
    /**
     * 患者id
     */
    @Column(name = "PatId")
    private Integer patId;

    /**
     * 
     */
    @Column(name = "GroupNo")
    private String groupNo;

    /**
     * 
     */
    @Column(name = "ProjectId")
    private String projectId;

    /**
     * 
     */
    @Column(name = "ProjectName")
    private String projectName;

    /**
     * 
     */
    @Column(name = "Creator")
    private Integer creator;

    /**
     * 
     */
    @Column(name = "CreateTime")
    private Date createTime;

    /**
     * 
     */
    @Column(name = "Updater")
    private Integer updater;

    /**
     * 
     */
    @Column(name = "UpdateTime")
    private Date updateTime;

    /**
     * 
     */
    @Column(name = "HospitalCode")
    private Integer hospitalCode;

}