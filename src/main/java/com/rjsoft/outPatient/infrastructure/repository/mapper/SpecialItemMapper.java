package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.SpecialItems;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * 药品门诊包装单位配置
 * <AUTHOR>
public interface SpecialItemMapper extends BaseMapper<SpecialItems> {

    /**
     * 根据ID获取门诊包装单位配置
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    default SpecialItems getSpecialItemById(Integer itemCode,Integer hospitalCode) {
        SpecialItems entity = new SpecialItems();
        entity.setItemCode(itemCode);
        entity.setHospitalCode(hospitalCode);
        return selectByPrimaryKey(entity);
    }

    /**
     * 根据多个ID 批量获取门诊包装单位配置
     * @param itemCodeList
     * @param hospitalCode
     * @return
     */
    List<SpecialItems> querySpecialItemByIdList(@Param("itemCodeList") List<Integer> itemCodeList, @Param("hospitalCode")Integer hospitalCode);

        /**
     * 根据多个ID 批量获取门诊包装单位配置
     * @param itemCodeList
     * @param hospitalCodeList
     * @return
     */
    List<SpecialItems> querySpecialItemByIdListAndHospitalCodeList(@Param("itemCodeList") List<Integer> itemCodeList, @Param("hospitalCodeList")List<Integer> hospitalCodeList);


}
