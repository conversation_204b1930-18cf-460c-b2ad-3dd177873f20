package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbXTPZ;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 系统常用函数（系统函数、自定义函数、一些通用的过程）
 * <AUTHOR>
public interface SysFunctionMapper {

    /**
     * 获取系统时间
     * @return
     */
    @Select("select getDate() nowTime")
    Date getDate();


    /**
     * 执行过程获取一个新ID
     * (不要放在事务中)
     * @param seq
     * @return
     */

    Long getGetSequences(@Param("seq") SequenceEnum seq);

    /**
     * 执行过程获取多个新ID
     * (不要放在事务中)
     * @param seq
     * @return
     */
    List<Long> getGetSequencesList(@Param("num") Integer num, @Param("seq") SequenceEnum seq);

    /**
     * 获取周次
     * 内部星期序号，周一0，周二1，周三2，周四3，周五4，周六5，周日6
     * @return
     */
    @Select("select case when datePart(weekday,getDate())=1 then 6 else  datePart(weekday,getDate())-2 end")
    Integer getWeekday();

    /**
     *
     * @param gjz
     * @return
     */
    MzysTbXTPZ getXtpz(String gjz);
}
