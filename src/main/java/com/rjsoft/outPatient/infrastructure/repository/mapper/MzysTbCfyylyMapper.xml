<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.MzysTbCfyylyMapper">
  <resultMap id="BaseResultMap" type="com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbCfyyly">
    <!--@mbg.generated generated on Mon Aug 04 17:41:15 CST 2025.-->
    <!--@Table MZYS_TB_CFYYLY-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="reception_no" jdbcType="BIGINT" property="receptionNo" />
    <result column="recipe_detail_id" jdbcType="BIGINT" property="recipeDetailId" />
    <result column="pre_save_no" jdbcType="BIGINT" property="preSaveNo" />
    <result column="item_code" jdbcType="INTEGER" property="itemCode" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="hospital_id" jdbcType="INTEGER" property="hospitalId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated generated on Mon Aug 04 17:41:15 CST 2025.-->
    id, reception_no, recipe_detail_id, pre_save_no, item_code, reason, hospital_id,
    status, create_by, create_time
  </sql>
</mapper>