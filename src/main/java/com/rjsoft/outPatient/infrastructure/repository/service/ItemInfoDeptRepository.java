package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.ItemInfoDept;

import java.util.List;

public interface ItemInfoDeptRepository {
    ItemInfoDept getDrugInfoDept(Integer drugId, Integer hospitalId, Integer deptId, Integer useDeptRange);

    List<ItemInfoDept> getItemInfoHospital(Integer itemCode, Integer hospitalId, Integer useDeptRange);

    List<ItemInfoDept> getItemInfoListByItemCodes(List<Integer> itemCodes, Integer hospitalId, Integer deptId, Integer useDeptRange, Integer receiveDeptId);

    @DatabaseAnnotation(name = DatasourceName.HISDB)
    List<ItemInfoDept> getItemInfoListByDrugIds(List<Integer> drugIds, Integer hospitalId, Integer deptId, Integer useDeptRange, Integer receiveDeptId);

    ItemInfoDept getItemInfoListByItemCode(Integer itemCode, Integer hospitalId, Integer deptId, Integer useDeptRange);
}
