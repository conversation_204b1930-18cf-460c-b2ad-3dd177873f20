package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.domain.packages.dto.ItemSetDto;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ItemSetMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ItemSetRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class ItemSetRepositoryImpl implements ItemSetRepository {
    private ItemSetMapper itemSetMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<ItemSetDto> getItemSetDtoByHospitalId(Integer itemCategory, Integer hospitalId, String itemNameOrInputCode) {
        return itemSetMapper.getItemSetDtoByHospitalId(itemCategory,hospitalId,itemNameOrInputCode);
    }
}
