package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.AssayRequest;
import io.swagger.models.auth.In;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * 化验申请
 * <AUTHOR>
public interface AssayRequestMapper extends BaseMapper<AssayRequest> {

    /**
     * 根据Id加载化验申请单
     * @param applyId
     * @param hospitalCode
     * @return
     */
    default   AssayRequest getAssayRequestByApplyIId(Integer applyId,Integer hospitalCode){
        AssayRequest entity=new AssayRequest();
        entity.setApplyId(applyId);
        entity.setHospitalCode(hospitalCode);
        List<AssayRequest> assayRequestList=select(entity);
        if(assayRequestList.size()==0){
            return null;
        }
        AssayRequest assayRequest=assayRequestList.get(0);
        if(assayRequest!=null){
            assayRequest.setOpFlag(1);
        }
        return assayRequest;
    }

    /**
     * 根据Id加载化验申请单
     * @param preSaveNo
     * @param hospitalCode
     * @return
     */
    default   AssayRequest getAssayRequestByPreNo(Long preSaveNo,Integer hospitalCode){
        AssayRequest entity=new AssayRequest();
        entity.setPreSaveNo(preSaveNo);
        entity.setHospitalCode(hospitalCode);
        List<AssayRequest> assayRequest=select(entity);
        if(assayRequest.stream().count()==0){
            return null;
        }
        assayRequest.get(0).setOpFlag(1);
        return assayRequest.get(0);
    }

    /**
     * 根据Id删除化验申请单
     * @param requestNo
     * @param hospitalCode
     * @return
     */
    default  boolean delAssayRequestById(Long requestNo,Integer hospitalCode) {
        AssayRequest entity=new AssayRequest();
        entity.setRequestNo(requestNo);
        entity.setHospitalCode(hospitalCode);
        return deleteByPrimaryKey(entity)>0;
    }

}
