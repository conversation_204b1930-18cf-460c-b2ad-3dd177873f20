package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 申请单内容
 */
@Data
@Table(name = "MZYS_TB_JCSQDNR")
public class ApplicationContent  implements Serializable {

    @Column(name = "id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    @Column(name = "jzlsh")
    private Integer receptionNo;

    @Id
    @Column(name = "bdbm")
    private Integer formId;

    @Column(name = "sqdbm")
    private String templateNo;

    @Column(name = "sqdmc")
    private String templateName;

    @Column(name = "zt")
    private Integer status;

    @Column(name = "cjr")
    private Integer creDoctor;

    @Column(name = "cjrq")
    private Date creTime;

    @Column(name = "xgr")
    private Integer uptDoctor;

    @Column(name = "xgrq")
    private Date uptTime;

    @Column(name = "yybm")
    private Integer hospitalCode;

    @Transient
    @Column(insertable = false, updatable = false)
    private Integer doctorId;

    public ApplicationContent() {
    }

    public ApplicationContent(Integer formId, Integer receptionNo, Integer hospitalCode) {
        this.formId = formId;
        this.receptionNo = receptionNo;
        this.hospitalCode = hospitalCode;
    }
}