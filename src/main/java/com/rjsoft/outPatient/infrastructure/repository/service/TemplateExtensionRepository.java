package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.Hospital;
import com.rjsoft.outPatient.infrastructure.repository.entity.TemplateExtension;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/30-9:18 上午
 */
public interface TemplateExtensionRepository {


    /**
     * 查询
     *
     * @param classId    药品 id
     * @param keyCode    keyCode
     * @param hospitalId 医院编码
     * @return {@link TemplateExtension}
     */
    TemplateExtension getTemplateExtensionByClassIdAndKeyCode(String classId, String keyCode, Integer hospitalId);


    /**
     * 查询药品高危登记
     *
     * @param drugId
     * @param hospitalCode
     * @return
     */
    List<TemplateExtension> getDrugDangerLev(Integer drugId, Integer hospitalCode);

    @DatabaseAnnotation(name = DatasourceName.HISDB)
    String getDrugPropertyName(Integer dicCode, Integer hospId);

    @DatabaseAnnotation(name = DatasourceName.HISDB)
    List<TemplateExtension> getDrugOriginal(Integer hospitalCode);
}
