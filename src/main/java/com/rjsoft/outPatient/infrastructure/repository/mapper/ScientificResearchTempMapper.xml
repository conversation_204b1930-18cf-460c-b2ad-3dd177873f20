<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ScientificResearchTempMapper">


    <select id="queryResearchTemp"
            resultType="com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QueryDefaultTempResult">
        SELECT b2.mbid as tempId,
        b2.mbysmc as tempElemName,
        b2.mbdm as tempCode,
        b2.mbmc as tempName,
        b3.wjbm as fileNo,
        b3.wjmc as fileName,
        b3.wjnr as fileContent,
        b2.yybm as hospCode
        FROM MZYS_TB_KYMBKS b1
        INNER JOIN MZYS_TB_MBINFO b2 ON b1.Mbid = b2.mbid
        INNER JOIN MZYS_TB_MBXML b3 ON b2.wjbm = b3.wjbm
        AND b2.yybm = b3.yybm
        WHERE b1.ProjectId = #{projectId}
        AND b2.yybm = #{hospCode}
        AND b1.Status = 0
        and b2.zt = 0
        and b3.del_status = 0
        and b2.mbfl = 1
        <if test="tempName!=null and tempName!=''">
            and b2.mbmc like '%'+ #{tempName}+'%'
        </if>
    </select>
    <select id="queryResearchDefaultTemp"
            resultType="com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QueryDefaultTempResult">
        SELECT b2.mbid   as tempId,
               b2.mbysmc as tempElemName,
               b2.mbdm   as tempCode,
               b2.mbmc   as tempName,
               b3.wjbm   as fileNo,
               b3.wjmc   as fileName,
               b3.wjnr   as fileContent,
               b2.yybm   as hospCode
        FROM MZYS_TB_KYMBKS b1
                 INNER JOIN MZYS_TB_MBINFO b2 ON b1.Mbid = b2.mbid
                 INNER JOIN MZYS_TB_MBXML b3 ON b2.wjbm = b3.wjbm
            AND b2.yybm = b3.yybm
        WHERE b1.ProjectId = #{projectId}
          AND b2.yybm = #{hospCode}
          and b2.mrmb = 1
          AND b1.Status = 0
          and b2.mbfl = 1
    </select>
    <select id="getResearchDefaultTemp"
            resultType="com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QueryDefaultTempResult">
        SELECT
        top 1
        b2.mbid   as tempId,
        b2.mbysmc as tempElemName,
        b2.mbdm   as tempCode,
        b2.mbmc   as tempName,
        b3.wjbm   as fileNo,
        b3.wjmc   as fileName,
        b3.wjnr   as fileContent,
        b2.yybm   as hospCode
        FROM MZYS_TB_KYMBKS b1
        INNER JOIN MZYS_TB_MBINFO b2 ON b1.Mbid = b2.mbid
        INNER JOIN MZYS_TB_MBXML b3 ON b2.wjbm = b3.wjbm
        AND b2.yybm = b3.yybm
        WHERE b1.ProjectId = #{projectId}
        AND b2.yybm = #{hospCode}
        and b2.mrmb = 1
        AND b1.Status = 0
        and b2.mbfl = 1
        <if test="sexFlag !=0 ">
            and isnull(b2.xb,'3')=#{sexFlag}
        </if>
        order by b2.cjsj desc
    </select>
    <select id="queryResearchTempContrast"
            resultType="com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QueryResearchTempContrastResult">
        select b1.Id AS researchTempId,
        b1.ProjectId AS projectId,
        b1.ProjectName AS projectName,
        b2.mbid AS tempId,
        b2.mbysmc AS tempElemName,
        b2.mbdm AS tempCode,
        b2.mbmc AS tempName,
        b2.mbfl AS tempType,
        b2.czbj AS firstVisit,
        b2.mrmb AS defaultTempFlag,
        b2.yybm AS hospCode
        from MZYS_TB_KYMBKS b1
        inner join MZYS_TB_MBINFO b2 on b1.Mbid = b2.mbid
        where b2.yybm = #{hospCode}
        and b2.mbfl = 1
        and b1.Status = 0
        <if test="projectId!=null">
            and b1.ProjectId = #{projectId}
        </if>
        <if test="tempName!=null and tempName!=''">
            and b2.mbmc like '%' + #{tempName} +'%'
        </if>
        and b2.del_status = 0
    </select>
</mapper>