<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ApplyItemMapper">

    <select id="getApplyExeDeptByItemCode" resultType="com.rjsoft.outPatient.domain.item.dto.ApplyItemExeDeptDto">
        SELECT d.deptId,d.hospitalId
        FROM Apply_Tb_Detail_Cost a (nolock)
            inner join Apply_Tb_Detail b on a.DetlId = b.Id
            inner join Apply_Tb_Item c on b.ItemCode = c.code
            inner join Apply_tb_ItemDept d on c.Id = d.itemId
        where a.PayItemId = #{itemCode} and b.ItemCode = #{examineItemCode}
        and a.ListId = #{examineNo} and a.id = #{applyDetailId}
    </select>
</mapper>