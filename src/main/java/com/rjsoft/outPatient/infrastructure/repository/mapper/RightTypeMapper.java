package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Agent;
import com.rjsoft.outPatient.infrastructure.repository.entity.RightType;
import org.omg.PortableInterceptor.HOLDING;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

public interface RightTypeMapper extends BaseMapper<RightType> {

    /**
     * 根据医院编号获取全部权限类型
     * @param hospitalCode
     */
    default List<RightType> getRightList(Integer hospitalCode){
        RightType rightType = new RightType();
        rightType.setHospitalCode(hospitalCode);
        return select(rightType);
    }
}
