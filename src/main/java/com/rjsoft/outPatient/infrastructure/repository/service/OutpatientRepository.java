package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeDetailView;
import com.rjsoft.outPatient.infrastructure.repository.entity.OutpatientInvoice;
import com.rjsoft.outPatient.infrastructure.repository.entity.OutpatientInvoiceView;

import java.util.List;

public interface OutpatientRepository {

    /**
     * 根据就诊流水号获取门诊发票
     *
     * @param receptionNo
     * @param hospitalCode
     */
    OutpatientInvoice getOutpatientInvoiceByRegNo(Long receptionNo, Integer hospitalCode);

    /**
     * 根据挂号流水号获取门诊发票
     *
     * @param regNoList
     * @param hospitalCode
     */
    List<OutpatientInvoiceView> getOutpatientInvoiceByRegNoList(List<Long> regNoList, Integer hospitalCode);

    /**
     * 根据挂号流水号获取收费明细
     *
     * @param chargeNoList
     * @param hospitalCode
     */
    List<ChargeDetailView> getChargeDetailByChargeNoList(List<Long> chargeNoList, Integer hospitalCode);


}
