package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplyList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Arrays;
import java.util.List;

/**
 * 申请单主表
 * <AUTHOR>
public interface ApplyListMapper extends BaseMapper<ApplyList>, ExampleMapper<ApplyList> {

    /**
     * 根据ID，获取申请单
     * @param param 参数顺序：ID
     * @return
     */
    default ApplyList getApplyListById(Object... param){
        ApplyList entity=new ApplyList();
        entity.setId(Converter.toInt32(param[0]));
        entity.setDelFlag(0);
        return  selectByPrimaryKey(entity);
    }

    /**
     * 根据就诊流水号，查询
     * @param VisitId
     * @param type
     * @return
     */
    default List<ApplyList> getApplyListByVisitId(Integer VisitId, List<Integer> type){
        Weekend weekend=new Weekend(ApplyList.class);
        WeekendCriteria<ApplyList,Object>weekendCriteria=weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ApplyList::getVisitId,VisitId);
        //只匹配门诊急诊类型申请单
        weekendCriteria.andIn(ApplyList::getPatSource, Arrays.asList(1,2));
        weekendCriteria.andEqualTo(ApplyList::getDelFlag,0);
        if(type!=null&&type.size()>0) {
            weekendCriteria.andIn(ApplyList::getBigformId, type);
        }
        return  selectByExample(weekend);
    }

    /**
     * 根据formCode获取对应申请单配置项目总数
     * @param formCode
     * @param hospitalCode
     * @return
     */
    Integer getApplyItemCount(@Param("formCode") String formCode,@Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据申请单ID，查询申请单
     * @param applyIds
     * @return
     */
    default List<ApplyList> queryApplyListById(List<Integer> applyIds, Integer hospitalCode) {
        Weekend<ApplyList> weekend = new Weekend<>(ApplyList.class);
        WeekendCriteria<ApplyList, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ApplyList::getId, applyIds);
        weekendCriteria.andEqualTo(ApplyList::getHospitalId, hospitalCode);
        //只匹配门诊急诊类型申请单
        weekendCriteria.andIn(ApplyList::getPatSource, Arrays.asList(1, 2));
        weekendCriteria.andEqualTo(ApplyList::getDelFlag, 0);
        return selectByExample(weekend);
    }

}
