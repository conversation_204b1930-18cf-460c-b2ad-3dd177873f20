<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.StockMapper">
    <select id="listStock"
            resultType="com.rjsoft.outPatient.infrastructure.cache.pojo.Stock">
        SELECT sum(isnull(v.Quantity * v.PackFactor, 0)) stock,v.DrugId,v.HospitalId,v.DeptId
                FROM Drug_Tb_StoreChangeOutPatientVirtual v
                WHERE v.DrugId in (${itemCodes})
                  AND v.HospitalId in (${hospitalIds})
                  AND v.DeptId in (${deptIds})
        group by v.DrugId,v.HospitalId,v.DeptId
    </select>

</mapper>