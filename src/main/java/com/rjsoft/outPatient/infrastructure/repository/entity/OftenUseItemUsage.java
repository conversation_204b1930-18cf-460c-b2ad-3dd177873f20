package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.*;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 常用项目用法
 * <AUTHOR>
@Data
@Table(name = "MZYS_TB_MZYSCYYF")
public class OftenUseItemUsage extends DataFlag implements Serializable {

    /**
     * id
     */
    @Id
    @Column(name = "id")
    private Integer id;

    /**
     * 项目编码
     */
    @Column(name = "ypbm")
    private Integer itemCode;

    /**
     * 用法
     */
    @Column(name = "gytj")
    private Integer usage;

    /**
     * 频率
     */
    @Column(name = "yf")
    private Integer frequency;

    /**
     * 剂量
     */
    @Column(name = "jl")
    private String dose;

    /**
     * 剂量单位
     */
    @Column(name = "jldw")
    private String doseUnit;

    /**
     * 剂量类型
     */
    @Column(name = "jllx")
    private Integer doseType;

    /**
     * 天数
     */
    @Column(name = "ts")
    private Integer days;

    /**
     * 数量
     */
    @Column(name = "xmsl")
    private BigDecimal quantity;

    /**
     * 医生id
     */
    @Column(name = "ysbm")
    private Integer doctorId;

    /**
     * 医生嘱托
     */
    @Column(name = "yszt")
    private String doctorEntrust;

    /**
     * 更新时间
     */
    @Column(name = "xgrq")
    private Date updateDate;

    /**
     * 创建时间
     */
    @Column(name = "cjrq")
    private Date createDate;

    /**
     * 医院编码
     */
    @Column(name = "hospitalCode")
    private Integer hospitalCode;
}
