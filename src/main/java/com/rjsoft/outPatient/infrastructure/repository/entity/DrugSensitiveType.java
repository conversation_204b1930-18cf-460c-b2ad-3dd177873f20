package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 药物过敏类型
 *
 * <AUTHOR>
 * @since 2021/7/27 - 10:20
 */
@Data
@Table(name = "Tbt_DrugSensitiveType")
@AllArgsConstructor
@NoArgsConstructor
public class DrugSensitiveType  implements Serializable {

    /**
     * 编码
     */
    @Column(name = "Code")
    private Integer code;

    /**
     * 名称
     */
    @Column(name = "Name")
    private String name;

    public DrugSensitiveType(Integer code) {
        this.code = code;
    }

}
