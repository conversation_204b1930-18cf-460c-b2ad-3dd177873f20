package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

@Data
@Table(name = "System_Tb_PubItems")
public class SystemTbPubItems {
    /**
     * 项目编码
     */
    @Id
    @Column(name = "ItemCode")
    private Integer itemCode;

    /**
     * 项目名称
     */
    @Column(name = "ItemName")
    private String itemName;

    /**
     * 输入码1
     */
    @Column(name = "InputCode1")
    private String inputCode1;

    /**
     * 输入码2
     */
    @Column(name = "InputCode2")
    private String inputCode2;

    /**
     * 输入码3
     */
    @Column(name = "InputCode3")
    private String inputCode3;

    /**
     * 项目分类
     */
    @Column(name = "ItemCategory")
    private Integer itemCategory;

    /**
     * 项目子类
     */
    @Column(name = "ChildItemCategory")
    private Integer childItemCategory;

    /**
     * 门诊可报金额
     */
    @Column(name = "ClinicExpensePrice")
    private BigDecimal clinicExpensePrice;

    /**
     * 门诊不可报金额
     */
    @Column(name = "ClinicNonExpensePrice")
    private BigDecimal clinicNonExpensePrice;

    /**
     * 门诊数量
     */
    @Column(name = "ClinicQty")
    private Integer clinicQty;

    /**
     * 门诊单位
     */
    @Column(name = "ClinicUnit")
    private String clinicUnit;

    /**
     * 病区单位
     */
    @Column(name = "WardUnit")
    private String wardUnit;

    /**
     * 剂量
     */
    @Column(name = "Dosage")
    private BigDecimal dosage;

    /**
     * 剂量单位
     */
    @Column(name = "DosageUnit")
    private String dosageUnit;

    /**
     * 规格
     */
    @Column(name = "DrugGuage")
    private String drugGuage;

    /**
     * 执行科室
     */
    @Column(name = "ExeDept")
    private Integer exeDept;

    /**
     * 停用标记
     */
    @Column(name = "Stopped")
    private Integer stopped;

    /**
     * 甲乙类
     * 9 自费 其它 医保
     * 0 甲
     * 1 乙
     */
    @Column(name = "ABClass")
    private Integer abClass;

    /**
     * 产地
     */
    @Column(name = "ProductPlace")
    private String productPlace;

    /**
     * 批发价
     */
    @Column(name = "TradePrice")
    private BigDecimal tradePrice;

    /**
     * 比例
     */
    @Column(name = "RetailPrice")
    private BigDecimal retailPrice;

    /**
     * 项目类型对应医嘱
     */
    @Column(name = "ItemTypeToAdv")
    private String itemTypeToAdv;

    /**
     * 医保编码
     */
    @Column(name = "CheckCode")
    private String checkCode;

    /**
     * 门诊项目ID
     */
    @Column(name = "MzItemId")
    private Integer mzItemId;

    /**
     * 病区项目ID
     */
    @Column(name = "WardItemId")
    private Integer wardItemId;

    /**
     * 使用范围 0 全部 1 门诊 2 住院
     */
    @Column(name = "UseRange")
    private Integer useRange;

    /**
     * 医院编码
     */
    @Id
    @Column(name = "HospitalId")
    private Integer hospitalId;

    /**
     * 两院合并唯一id
     */
    @Column(name= "GlobalId")
    private Integer globalId;


    @Column(name = "IsSet")
    private Integer isSet;

    @Column(name = "isFixed")
    private Integer isFixed;

    @Column(name= "MedicalType1")
    private String medicalType1;

    @Column(name= "MedicalType2")
    private String medicalType2;

    @Column(name= "MedicalType3")
    private String medicalType3;

}
