package com.rjsoft.outPatient.infrastructure.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;

import com.rjsoft.outPatient.domain.recipe.dto.TreatDto;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description  输液分组明细
 * <AUTHOR>
 * @Date 2021-12-28 
 */
@Data
@Table ( name ="MZHS_TB_SYFZMX" )
public class InfuseGroupDetail implements Serializable {

	private static final long serialVersionUID =  7815667453658206861L;

	/**
	 * 明细ID
	 */

   	@Column(name = "mxid")
	private String mxid;

	/**
	 * 分组ID
	 */
   	@Column(name = "fzid" )
	private String fzid;

	/**
	 * 药品编码
	 */
   	@Column(name = "ypbm" )
	private Integer ypbm;

	/**
	 * 药品名称
	 */
   	@Column(name = "ypmc" )
	private String ypmc;

	/**
	 * 药品规格
	 */
   	@Column(name = "ypgg" )
	private String ypgg;

	/**
	 * 剂量
	 */
   	@Column(name = "jl" )
	private String jl;

	/**
	 * 剂量单位
	 */
   	@Column(name = "jldw" )
	private String jldw;

	/**
	 * 频次
	 */
   	@Column(name = "pc" )
	private Integer pc;

	/**
	 * 数量
	 */
   	@Column(name = "sl" )
	private Integer sl;

	/**
	 * 单位
	 */
   	@Column(name = "dw" )
	private String dw;

	/**
	 * 组号
	 */
   	@Column(name = "zh" )
	private Integer zh;

	/**
	 * 处方id
	 */
   	@Column(name = "cfid" )
	private Integer cfid;

	/**
	 * 处方明细id
	 */
   	@Column(name = "cfmxid" )
	private Long cfmxid;

	/**
	 * 给药途径
	 */
   	@Column(name = "gytj" )
	private Integer gytj;

	/**
	 * 医院编码
	 */
	@Column(name = "hospitalCode" )
	private Integer hospitalCode;

   	public static InfuseGroupDetail getInfuseDetail(TreatDto treat,Integer hospitalCode){
		InfuseGroupDetail detail = new InfuseGroupDetail();
		detail.setMxid(UUID.randomUUID().toString().toUpperCase());
		detail.setFzid(treat.getFzid());
		detail.setYpbm(treat.getItemCode());
		detail.setYpmc(treat.getItemName());
		detail.setYpgg(treat.getYpgg());
		detail.setJl(treat.getDose());
		detail.setJldw(treat.getDoseUnit());
		detail.setPc(treat.getFrequent());
		detail.setSl(treat.getItemCount());
		detail.setDw(treat.getUnit());
		detail.setZh(treat.getGroupNo());
		detail.setCfid(treat.getRecipeNo());
		detail.setCfmxid(treat.getRecipeDetailNo());
		detail.setGytj(treat.getRoute());
		detail.setHospitalCode(hospitalCode);
		return detail;
	}
}
