package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.FeeLimit;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * 费用限制
 *
 * <AUTHOR>
public interface FeeLimitMapper extends BaseMapper<FeeLimit> {

    /**
     * 查询费用限制
     *
     * @param patientType
     * @param hospitalCode
     * @return
     */
    default List<FeeLimit> getFeeLimitByPatType(Integer patientType, Integer hospitalCode) {
        FeeLimit entity = new FeeLimit();
        entity.setPatientType(patientType);
        entity.setHospitalCode(hospitalCode);
        return select(entity);
    }
}
