package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ExamineRequestZxHis;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * 检查申请（ZxHis）
 * <AUTHOR>
public interface ExamineRequestZxHisMapper extends BaseMapper<ExamineRequestZxHis> {

    /**
     * 根据处方ID，检查类型,获取检查申请记录
     * @param receptionNo
     * @param recipeId
     * @param requestType
     * @return
     */
    default  ExamineRequestZxHis getExamineRequestZxHisByRecipeId(Long receptionNo,Long recipeId,Integer requestType) {
        ExamineRequestZxHis entity = new ExamineRequestZxHis();
        entity.setRecipeId(recipeId);
        entity.setExamineType(requestType);
        List<ExamineRequestZxHis> list = select(entity);
        if (list.stream().count() == 0) {
            return null;
        }
        return list.get(0);
    }
}
