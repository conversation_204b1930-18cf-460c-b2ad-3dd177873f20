<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.SickLeaveMapper">

    <select id="getSickInfo" resultType="java.lang.String">
        declare @SickName varchar(4000)
        declare @DiagnosisProofName varchar(4000)
		declare @DiseaseDiagnosis varchar(4000)
        select @SickName='病假单'+char(10)+'建议休息'+isnull(RestDayNum,'')+',病假'+leaveDayNum+'天'  from MZYS_TB_BJD where regno=#{regNo} and DelFlag=0 and hospitalCode=#{hospitalCode}
        select @DiagnosisProofName=char(10)+'疾病证明'+char(10)+'初诊日期:'+isnull(CONVERT(char(20),case when DiagnosisTime&lt;'1900-01-02' then null else DiagnosisTime end,120),'')+char(10)
            +'确诊日期:'+isnull(CONVERT(char(20),case when QDiagnosisTime&lt;'1900-01-02' then null else QDiagnosisTime end,120),'')+char(10)
            +'住院日期:'+isnull(CONVERT(char(20),case when InHospitalTime&lt;'1900-01-02' then null else InHospitalTime end,120),'')+char(10)
            +'诊断:'+STUFF((select char(10)+CONVERT(varchar(20),zdmc) from MZYS_TB_JBZM_ZD where MainId=a.id for xml path('')),1,1,'')+char(10)
            +'备注:'+Remarks
        from MZYS_TB_JBZM a
        where regno=#{regNo}  and DelFlag=0 and State=3 and hospitalCode=#{hospitalCode}
        select  @DiseaseDiagnosis=char(10)+'大病登记'+char(10)+'疾病诊断:'+Diagnosis+char(10)
            +'治疗机构:'+MedicalInstitution+char(10)
            +'签名时间:'+isnull(CONVERT(char(20),case when SignTime&lt;'1900-01-02' then null else SignTime end,120),'')+char(10)
            +'签名时间:'+isnull(CONVERT(char(20),case when RegisterTime&lt;'1900-01-02' then null else RegisterTime end,120),'')+char(10)
        from MZYS_TB_DBDJD
        where regno=#{regNo} and State=3 and DelFlag=0 and hospitalCode=#{hospitalCode}
        select isnull(@SickName,'')+isnull(@DiagnosisProofName,'')+isnull(@DiseaseDiagnosis,'')

    </select>

    <select id="queryReceptionListByRegNos" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ReceptionRecord">
        select
            t.jzlsh receptionNo,
            t.ghlsh regNo,
            t.hzbh patId,
            t.ysbm doctorId,
            t.ksbm deptId,
            t.sckzys firstDoctorId,
            t.sckzrq firstDate,
            t.zhkzys lastDoctorId,
            t.zhkzrq lastDate,
            t.hospitalCode
        from MZYS_TV_KZJL t (nolock)
        where t.hospitalCode = #{hospitalCode}
        and t.ghlsh in
        <foreach collection="regNos" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>