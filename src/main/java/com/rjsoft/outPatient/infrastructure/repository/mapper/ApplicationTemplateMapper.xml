<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ApplicationTemplateMapper">

    <select id="getApplicationTemplateByItemCode"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationTemplate">
        select
        c.sqdlsh id,
        c.sqdbm	 templateNo,
        c.sqdmc	 templateName,
        c.type	 templateType,
        c.jclxbm examineType,
        c.jclxmc examineName,
        c.bdbm	 fromId,
        c.sqdbz	 remarks,
        c.zt	 status,
        c.cjr	 creDoctor,
        c.cjrq	 creTime,
        c.xgr	 uptDoctor,
        c.xgrq	 uptTime,
        c.yybm	 hospitalCode
        from MZYS_TB_JCSQDZB_XM a (nolock)
        inner join MZYS_TB_JCSQDZB b (nolock) on a.sqdzblsh=b.lsh and a.yybm=b.yybm
        inner join MZYS_TB_JCSQD c (nolock) on b.sqdbm=c.sqdbm and b.yybm=c.yybm
        where sfxm=#{itemCode} and a.yybm=#{hospitalCode}
    </select>
</mapper>