package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.SciRegisterlistProject;

public interface SciRegisterlistProjectRepository {
    boolean insertSciRegisterlistProject(SciRegisterlistProject sciRegisterlistProject);
    int updateSciRegisterlistProject(String projectId
            , Long regNo
            , String followup
            , String followupTitle
            , Integer hospitalCode);
}
