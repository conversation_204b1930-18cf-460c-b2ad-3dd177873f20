package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.domain.doctorElemMain.constant.HospitalIdEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.OldReceptionRecord;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegNo2Guid;
import com.rjsoft.outPatient.infrastructure.repository.mapper.OldReceptionRecordMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RegNo2GuidMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.OldReceptionRecordRepository;
import lombok.AllArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/4 - 10:32
 */
@Service
@AllArgsConstructor
public class OldReceptionRecordRepositoryImpl implements OldReceptionRecordRepository {

    private final OldReceptionRecordMapper oldReceptionRecordMapper;
    RegNo2GuidMapper regNo2GuidMapper;

    @Override
    @Cacheable(cacheNames = "getReceptionByRegNo1", unless = "#result == null")
    public List<OldReceptionRecord> getReceptionByRegNo(List<String> regNo, Integer hospitalCode) {
        if(HospitalIdEnum.BRANCH.getCode().equals(hospitalCode)){
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }else{
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        }
        return oldReceptionRecordMapper.getReceptionByRegNo(regNo);
    }

    @Override
    public List<RegNo2Guid> getRegNoGuid(List<Long> regNos, Integer hospitalCode) {
        if(HospitalIdEnum.BRANCH.getCode().equals(hospitalCode)){
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
        }else{
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
        }
        return regNo2GuidMapper.getRegNoGuid(regNos);
    }
}
