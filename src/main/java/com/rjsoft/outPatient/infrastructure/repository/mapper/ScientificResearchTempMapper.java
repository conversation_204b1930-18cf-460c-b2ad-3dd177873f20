package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QueryDefaultTempResult;
import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QueryResearchTempContrastResult;
import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.ScientificResearchTempResult;
import com.rjsoft.outPatient.infrastructure.repository.entity.PackageDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.ScientificResearchTemp;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

public interface ScientificResearchTempMapper extends BaseMapper<ScientificResearchTemp>, ExampleMapper<ScientificResearchTemp> {


    /**
     * 根据科研课题id+医院编码获取科研模板
     *
     * @param projectId
     * @param hospCode
     * @return
     */
    List<QueryDefaultTempResult> queryResearchTemp(@Param("projectId") String projectId,
                                                   @Param("hospCode") Integer hospCode,
                                                   @Param("tempName") String tempName);

    /**
     * 获取科研课题默认模板
     *
     * @param projectId
     * @param visitFlag
     * @param hospitalCode
     * @return
     */
    QueryDefaultTempResult queryResearchDefaultTemp(@Param("projectId") String projectId,
                                                    @Param("visitFlag") int visitFlag,
                                                    @Param("hospCode") int hospitalCode);

    /**
     * 获取科研课题默认模板
     *
     * @param projectId
     * @param sexFlag
     * @param hospitalCode
     * @return
     */
    QueryDefaultTempResult getResearchDefaultTemp(@Param("projectId") String projectId,
                                                    @Param("sexFlag") int sexFlag,
                                                    @Param("hospCode") int hospitalCode);

    /**
     * 查询科研模板对照信息
     *
     * @param hospCode
     * @return
     */
    List<QueryResearchTempContrastResult> queryResearchTempContrast(@Param("hospCode") Integer hospCode,
                                                                    @Param("projectId") String projectId,
                                                                    @Param("tempName") String tempName);
}
