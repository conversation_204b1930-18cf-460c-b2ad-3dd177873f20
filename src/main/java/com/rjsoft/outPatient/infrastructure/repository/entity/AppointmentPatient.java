package com.rjsoft.outPatient.infrastructure.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-10-27
 * 患者预约表
 */

@Data
@Table ( name ="TB_AppointmentPatient" )
public class AppointmentPatient implements Serializable {

	private static final long serialVersionUID =  5123652626153128195L;

	/**
	 * id
	 */
   	@Column(name = "Id" )
	private Long id;

	/**
	 * 患者身份证号
	 */
   	@Column(name = "CertificateNo" )
	private String certificateNo;

	/**
	 * 患者姓名
	 */
   	@Column(name = "PatName" )
	private String patName;

	/**
	 * 电话
	 */
   	@Column(name = "Telephone" )
	private String telephone;

	/**
	 * 是否身份验证
	 */
   	@Column(name = "IsAuthentication" )
	private Long isAuthentication;

	/**
	 * 绑定状态
	 */
   	@Column(name = "BindStatu" )
	private Long bindStatu;

	/**
	 * 微信OpenID
	 */
   	@Column(name = "OpenID" )
	private String openId;

	/**
	 * 排序
	 */
   	@Column(name = "[order]" )
	private Long order;

	/**
	 * 是否删除
	 */
   	@Column(name = "IsDelete" )
	private Long isDelete;

	/**
	 * 是否使用
	 */
   	@Column(name = "IsUse" )
	private Long isUse;

	/**
	 * 创建人
	 */
   	@Column(name = "CreatedBy" )
	private Long createdBy;

	/**
	 * 创建时间
	 */
   	@Column(name = "CreatedDate" )
	private Date createdDate;

	/**
	 * 更新人
	 */
   	@Column(name = "UpdatedBy" )
	private Long updatedBy;

	/**
	 * 更新时间
	 */
   	@Column(name = "UpdatedDate" )
	private Date updatedDate;

	/**
	 * 医院编码
	 */
   	@Column(name = "HospitalCode" )
	private String hospitalCode;

	/**
	 * 患者卡号
	 */
   	@Column(name = "CardNo" )
	private String cardNo;

}
