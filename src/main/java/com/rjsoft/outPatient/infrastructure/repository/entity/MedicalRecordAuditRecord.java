package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 电子病历审核记录
 */
@Data
@Table(name = "MZYS_TB_DZBLSHJL")
public class MedicalRecordAuditRecord implements Serializable {

    @Id
    @Column(name = "Id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    @Column(name = "CheckId")
    private Integer checkId;

    @Column(name = "CheckSort")
    private Integer checkSort;

    @Column(name = "CheckType")
    private Integer checkType;

    @Column(name = "CheckState")
    private Integer checkState;

    @Column(name = "CheckResult")
    private String checkResult;

    @Column(name = "CheckDoctorId")
    private String checkDoctorId;

    @Column(name = "CheckDoctorName")
    private String checkDoctorName;

    @Column(name = "CheckDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkDate;

    @Column(name = "bl")
    private String blXml;

    @Column(name = "blHtml")
    private String blHtml;

    @Column(name = "HospitalCode")
    private Integer hospitalCode;

    @Column(name = "ReviseFlag")
    private Integer reviseFlag;
}
