package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.DictionaryCondition;

import java.util.HashMap;
import java.util.List;

public interface DiagnoseOldMapper {
    /**
     * 查询老数据诊断信息
     * @param regNo
     */
    List<HashMap<String, String>> getDiagnoseOldByRegNo(Integer regNo);

    /**
     * 根据 疾病证明 id 及诊断编码检索数据是否存在
     *
     * @param diseaseProofId 疾病证明 id
     * @param diagnosticCode 诊断编码
     * @return 存在 true
     */
    int existsWithDiseaseProofIdAndDiagnosticCode(Integer diseaseProofId, String diagnosticCode);

    /**
     * 获取状态类不明确诊断
     */
    List<DictionaryCondition> getDictionaryCondition();
}
