package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.seriousIllness.vo.SeriousIllnessRegisterVo;
import com.rjsoft.outPatient.infrastructure.repository.entity.SeriousIllnessRegister;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.Date;
import java.util.List;

/**
 * 大病登记查询
 *
 * <AUTHOR>
public interface SeriousIllnessRegisterMapper extends BaseMapper<SeriousIllnessRegister>, ExampleMapper<SeriousIllnessRegister> {
    /**
     * 新库大病登记查询
     *  @param cardNo
     *  @param patName
     *  @param hospitalCode
     *  @param hisCardNo
     *  @param stateList
     *  @return
     */
    List<SeriousIllnessRegisterVo> getNewList(Date startTime, Date endTime, String cardNo, String patName, Integer hospitalCode, String hisCardNo, String patSfz,List<Integer> stateList);
}
