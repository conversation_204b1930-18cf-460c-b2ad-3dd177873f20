<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DrugToHospitalMapper">


    <select id="getDrugToHospital" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.DrugToHospital">
        select b.*
        from Drug_tb_DrugToHospital a
                 inner join Drug_tb_DrugToHospital b on
            a.DrugCode = b.DrugCode
        where a.DrugId = #{drugId}
          and a.Status = 0
    </select>


</mapper>