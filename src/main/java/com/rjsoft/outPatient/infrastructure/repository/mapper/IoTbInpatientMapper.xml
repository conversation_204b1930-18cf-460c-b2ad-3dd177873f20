<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.IoTbInpatientMapper">
    <select id="queryInpatientInfos" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.IoTbInpatient">
        SELECT
        a.*,b.DeptName deptName
        FROM
            Io_Tb_Inpatient a (nolock)
            left join System_Tb_Department b on a.DeptId = b.DeptCode and a.HospitalId = b.HospitalId
        <where>
            a.Status = #{status}
            <if test="startTime != null and startTime !='' ">
                and a.OutTime >= #{startTime}
            </if>
            <if test="endTime != null and endTime !='' ">
                and a.OutTime <![CDATA[<=]]> #{endTime}
            </if>
            <if test="patId != null">
                and a.patId = #{patId}
            </if>
        </where>
    </select>

    <select id="getInpatientListByPatids" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.IoTbInpatient">
        SELECT
        a.*,b.DeptName deptName,c.name professorName
        FROM
        Io_Tb_Inpatient a (nolock)
        left join System_Tb_Department b on a.DeptId = b.DeptCode and a.HospitalId = b.HospitalId
        left join System_Tb_Worker c on a.Professor = c.WorkerId and a.HospitalId = c.HospitalId
        <where>
            a.Status = #{status}
            <if test="startTime != null and startTime !='' ">
                and a.OutTime >= #{startTime}
            </if>
            <if test="endTime != null and endTime !='' ">
                and a.OutTime <![CDATA[<=]]> #{endTime}
            </if>
            <if test="patIds != null and patIds.size > 0">
                AND a.patId IN
                <foreach collection="patIds" item="patId" open="(" separator="," close=")">
                    #{patId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getDischargeDrugDoctor" resultType="com.rjsoft.outPatient.domain.config.dto.DischargeDrugDoctorDTO">
        select
            top 1
	        isnull(t.VerfierDoctor,t.submitid) doctorId,
            a.name doctorName
        from Ward_Tb_Adv t (nolock)
        left join System_Tb_Worker a on isnull(t.VerfierDoctor,t.submitid) = a.WorkerId and t.HospitalId = a.HospitalId
        where t.RegNo =#{regNo} and t.AdvKind='出院带药' and t.upstatus!=2
        order by t.InputTime desc
    </select>
</mapper>
