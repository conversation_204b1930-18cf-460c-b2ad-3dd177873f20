package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 心理咨询医生看诊科室
 *
 * <AUTHOR>
 * @since 2021/11/25-2:51 下午
 */
@Data
@Table(name = "MZYS_TB_YSKS")
public class PsychConsultantJobDept implements Serializable {

    private static final long serialVersionUID = 7251851861193085235L;

    /**
     * 医生 id
     */
    @Column(name = "ysbm")
    private Integer doctorId;
    /**
     * 科室 id
     */
    @Column(name = "ksbm")
    private Integer deptId;
    /**
     * 医院编码
     */
    @Column(name = "hospitalCode")
    private Integer hospitalCode;

}
