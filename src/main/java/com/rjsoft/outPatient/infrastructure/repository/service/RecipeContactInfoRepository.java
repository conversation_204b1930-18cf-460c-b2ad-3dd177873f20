package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.OldRecipeContactInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeContactInfo;

import java.util.List;

/**
 * 门诊处方草药代煎
 *
 * <AUTHOR>
public interface RecipeContactInfoRepository {
    List<RecipeContactInfo> getRecipeContactInfoByRecipeNo(List<Integer> recipeNos, Integer hospitalCode);

    List<OldRecipeContactInfo> getOldRecipeContactInfoByRecipeNo(List<String> recipeNos, Integer hospitalCode);
}
