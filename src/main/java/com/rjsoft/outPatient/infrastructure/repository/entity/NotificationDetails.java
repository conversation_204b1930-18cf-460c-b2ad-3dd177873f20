package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 告知单明细
 *
 * <AUTHOR>
 */

@Data
@Table(name = "N_GzdDetail")
public class NotificationDetails implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @Column(name = "Id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer ID;
    @Column(name = "ward")
    private String ward;
    @Column(name = "bedNo")
    private String bedNo;
    @Column(name = "hosNo")
    private String hosNo;
    @Column(name = "name")
    private String name;
    @Column(name = "sex")
    private String sex;
    @Column(name = "age")
    private String age;
    /**
     * 院方告知项目
     */
    @Column(name = "HosItem")
    private String hosItem;
    /**
     * 监护人checkbox
     */
    @Column(name = "guardianCHB")
    private Integer guardianCHB;
    @Column(name = "doctorSignature")
    private String doctorSignature;
    @Column(name = "doctorSignatureDate")
    private LocalDateTime doctorSignatureDate;
    /**
     * 患者声明项目
     */
    @Column(name = "item")
    private String item;
    @Column(name = "isAgree")
    private String isAgree;
    /**
     * 监护人签名
     */
    @Column(name = "guardianSignature")
    private String guardianSignature;
    /**
     * 与患者关系
     */
    @Column(name = "relationship")
    private String relationship;
    @Column(name = "guardianSignatureDate")
    private LocalDateTime guardianSignatureDate;
    @Column(name = "otherSignature")
    private String otherSignature;
    @Column(name = "company")
    private String company;
    @Column(name = "otherDate")
    private LocalDateTime otherDate;
    /**
     * 治疗时间
     */
    @Column(name = "treatmentTime")
    private String treatmentTime;
    @Column(name = "weekCount")
    private String weekCount;
    /**
     * 一周几次
     */
    @Column(name = "frequency")
    private String frequency;
    /**
     * 来访者checkbox
     */
    @Column(name = "visitorCHB")
    private Integer visitorCHB;
    @Column(name = "visitorSignature")
    private String visitorSignature;
    @Column(name = "visitorSignatureDate")
    private LocalDateTime visitorSignatureDate;
    /**
     * 婚姻
     */
    @Column(name = "marriage")
    private String marriage;
    /**
     * 学历
     */
    @Column(name = "education")
    private String education;
    /**
     * 职业
     */
    @Column(name = "occupation")
    private String occupation;
    /**
     * 咨询号
     */
    @Column(name = "advisoryNo")
    private String advisoryNo;
    /**
     * 诊断一
     */
    @Column(name = "diagnosis1")
    private String diagnosis1;
    /**
     * 诊断二
     */
    @Column(name = "diagnosis2")
    private String diagnosis2;
    @Column(name = "status1")
    private String status1;
    @Column(name = "content1")
    private String content1;
    @Column(name = "technology1")
    private String technology1;
    @Column(name = "time1")
    private String time1;
    @Column(name = "signature1")
    private String signature1;
    @Column(name = "status2")
    private String status2;
    @Column(name = "content2")
    private String content2;
    @Column(name = "technology2")
    private String technology2;
    @Column(name = "time2")
    private String time2;
    @Column(name = "signature2")
    private String signature2;
    @Column(name = "status3")
    private String status3;
    @Column(name = "status4")
    private String status4;
    @Column(name = "status5")
    private String status5;
    @Column(name = "status6")
    private String status6;
    @Column(name = "content3")
    private String content3;
    @Column(name = "content4")
    private String content4;
    @Column(name = "content5")
    private String content5;
    @Column(name = "content6")
    private String content6;
    @Column(name = "technology3")
    private String technology3;
    @Column(name = "technology4")
    private String technology4;
    @Column(name = "technology5")
    private String technology5;
    @Column(name = "technology6")
    private String technology6;
    @Column(name = "time3")
    private String time3;
    @Column(name = "time4")
    private String time4;
    @Column(name = "time5")
    private String time5;
    @Column(name = "time6")
    private String time6;
    @Column(name = "signature3")
    private String signature3;
    @Column(name = "signature4")
    private String signature4;
    @Column(name = "signature5")
    private String signature5;
    @Column(name = "signature6")
    private String signature6;
    @Column(name = "talkCHB")
    private Integer talkCHB;
    /**
     * 谈话目的
     */
    @Column(name = "talkGoal")
    private String talkGoal;
    @Column(name = "talkContent")
    private String talkContent;
    /**
     * 患者内容
     */
    @Column(name = "patientContent")
    private String patientContent;
    /**
     * 自愿住院知情同意书checkbox
     */
    @Column(name = "ZYZYCHB")
    private Integer ZYZYCHB;
    @Column(name = "IDCard")
    private String IDCard;
    /**
     * 送诊人
     */
    @Column(name = "referralName")
    private String referralName;
    @Column(name = "referralIDCard")
    private String referralIDCard;
    /**
     * 初步诊断
     */
    @Column(name = "firstVisit")
    private String firstVisit;
    @Column(name = "firstVisitCHB")
    private Integer firstVisitCHB;
    @Column(name = "CHB7")
    private Integer CHB7;
    @Column(name = "CHB1")
    private Integer CHB1;
    @Column(name = "CHB2")
    private Integer CHB2;
    @Column(name = "CHB3")
    private Integer CHB3;
    @Column(name = "CHB4")
    private Integer CHB4;
    @Column(name = "CHB5")
    private Integer CHB5;
    @Column(name = "CHB6")
    private Integer CHB6;
    @Column(name = "CHB8")
    private Integer CHB8;
    @Column(name = "CHB9")
    private Integer CHB9;
    @Column(name = "CHB10")
    private Integer CHB10;
    @Column(name = "CHB11")
    private Integer CHB11;
    @Column(name = "CHB12")
    private Integer CHB12;
    @Column(name = "CHB13")
    private Integer CHB13;
    @Column(name = "CHB14")
    private Integer CHB14;
    @Column(name = "CHB15")
    private Integer CHB15;
    @Column(name = "otherContent1")
    private String otherContent1;
    @Column(name = "otherContent2")
    private String otherContent2;
    /**
     * 风险评估checkbox
     */
    @Column(name = "riskCHB")
    private Integer riskCHB;
    @Column(name = "riskTypeCHB")
    private Integer riskTypeCHB;
    /**
     * 评估人
     */
    @Column(name = "appraiser1")
    private String appraiser1;
    @Column(name = "appraiser2")
    private String appraiser2;
    /**
     * 评估时间
     */
    @Column(name = "appraiserDate1")
    private LocalDateTime appraiserDate1;
    @Column(name = "appraiserDate2")
    private LocalDateTime appraiserDate2;
    @Column(name = "GZDName")
    private String GZDName;
    @Column(name = "createBy")
    private String createBy;
    @Column(name = "createDate")
    private LocalDateTime createDate;
    @Column(name = "updateBy")
    private String updateBy;
    @Column(name = "updateDate")
    private LocalDateTime updateDate;
    /**
     * 就诊流水号
     */
    @Column(name = "rgNo")
    private String rgNo;
    @Column(name = "cardID")
    private String cardID;
    @Column(name = "pdf")
    private String pdf;
    @Column(name = "pdfReturn")
    private String pdfReturn;
    @Column(name = "guid")
    private String guid;
    /**
     * 0未删除，1已删除
     */
    @Column(name = "isDelete")
    private Integer isDelete;
    @Column(name = "finishStatu")
    private String finishStatue;
    @Column(name = "patientid")
    private String patientId;

    /**
     * 医院编码
     */
    @Column(name = "HospitalId")
    private Integer hospitalId;

}
