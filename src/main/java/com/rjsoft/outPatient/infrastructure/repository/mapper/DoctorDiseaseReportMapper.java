package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.diseaseReport.Enum.ReportStatus;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiseaseReportResponse;
import com.rjsoft.outPatient.infrastructure.repository.entity.DoctorDiseaseReport;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface DoctorDiseaseReportMapper extends Mapper<DoctorDiseaseReport> {

    /**
     * 获取医生疾病上报情况
     *
     * @param startTime
     * @param endTime
     * @param certificateNo
     * @param doctorId
     * @param status
     */
    List<DiseaseReportResponse> getDoctorReportDisease(@Param("startTime") String startTime,
                                                       @Param("endTime") String endTime,
                                                       @Param("CertificateNo") String certificateNo,
                                                       @Param("doctorId") Integer doctorId,
                                                       @Param("status") Integer status,
                                                       @Param("hospitalCode") Integer hospitalCode,
                                                       @Param("uploadTypeId") Integer uploadTypeId);


    /**
     * 根据就诊流水号和诊断编码获取疾病上报记录
     *
     * @param id
     * @param receptionNo
     * @param diagnoseCode
     * @return
     */
    default DoctorDiseaseReport getRecord(Integer id, String receptionNo, String diagnoseCode) {
        Weekend<DoctorDiseaseReport> weekend = new Weekend<>(DoctorDiseaseReport.class);
        WeekendCriteria<DoctorDiseaseReport, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(DoctorDiseaseReport::getJzlsh, receptionNo)
                .andEqualTo(DoctorDiseaseReport::getId, id)
                .andEqualTo(DoctorDiseaseReport::getDiagnoseCode, diagnoseCode)
                .andNotEqualTo(DoctorDiseaseReport::getStatus, ReportStatus.BACKED.getStatus());
        List<DoctorDiseaseReport> reportList = selectByExample(weekend);
        return reportList.size() == 0 ? new DoctorDiseaseReport() : reportList.get(0);
    }

    /**
     * 根据就诊流水号和诊断编码获取疾病上报记录【定稿状态】
     *
     * @param receptionNo
     * @param diagnoseCode
     */
    default DoctorDiseaseReport getRecordConfirm(String receptionNo, String diagnoseCode) {
        DoctorDiseaseReport response = new DoctorDiseaseReport();
        response.setJzlsh(receptionNo);
        response.setDiagnoseCode(diagnoseCode);
        response.setStatus(ReportStatus.FINAL.getStatus());
        return selectOne(response);
    }

    /**
     * 疾病上报退回
     *
     * @param
     * @return
     */
    default int returnReport(Integer id, String reception, String diagnoseCode, Integer hospitalCode) {
        Weekend<DoctorDiseaseReport> weekend = new Weekend<>(DoctorDiseaseReport.class);
        WeekendCriteria<DoctorDiseaseReport, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(DoctorDiseaseReport::getJzlsh, reception)
                .andEqualTo(DoctorDiseaseReport::getId, id)
                .andEqualTo(DoctorDiseaseReport::getDiagnoseCode, diagnoseCode);
        DoctorDiseaseReport response = new DoctorDiseaseReport();
        response.setJzlsh(reception);
        response.setDiagnoseCode(diagnoseCode);
        response.setStatus(ReportStatus.BACKED.getStatus());
        response.setHospitalCode(hospitalCode);
        return updateByExampleSelective(response, weekend);
    }

    /**
     * 疾病上报退回
     *
     * @param
     * @return
     */
    default int delReport(Integer id, String reception, String diagnoseCode, Integer hospitalCode) {
        Weekend<DoctorDiseaseReport> weekend = new Weekend<>(DoctorDiseaseReport.class);
        WeekendCriteria<DoctorDiseaseReport, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(DoctorDiseaseReport::getJzlsh, reception)
                .andEqualTo(DoctorDiseaseReport::getDiagnoseNo, id)
                .andEqualTo(DoctorDiseaseReport::getDiagnoseCode, diagnoseCode)
                .andEqualTo(DoctorDiseaseReport::getHospitalCode, hospitalCode);
        return deleteByExample(weekend);
    }

}
