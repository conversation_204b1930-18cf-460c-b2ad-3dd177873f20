package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.DrugToHospital;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface DrugToHospitalMapper extends Mapper<DrugToHospital>, ExampleMapper<DrugToHospital> {


    /**
     * 查询药品主索引信息
     *
     * @param drugId
     * @return
     */
    List<DrugToHospital> getDrugToHospital(@Param("drugId") Integer drugId);

}
