package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.domain.sickleave.dto.SickLeaveDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.PatIdPatNo;
import com.rjsoft.outPatient.infrastructure.repository.entity.SickLeave;
import com.rjsoft.outPatient.infrastructure.repository.entity.SickLeaveDiagnosis;
import com.rjsoft.outPatient.infrastructure.repository.entity.SickLeaveOld;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SickLeaveMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SickLeaveOldMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.OldPatientInfoRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.PatientRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.SickLeaveDiagnosisRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.SickLeaveRepository;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/29 - 14:33
 */
@Service
@AllArgsConstructor
public class SickLeaveRepositoryImpl implements SickLeaveRepository {

    private final SickLeaveMapper sickLeaveMapper;
    private final SickLeaveOldMapper sickLeaveOldMapper;
    private final SickLeaveDiagnosisRepository sickLeaveDiagnosisRepository;
    PatientRepository patientRepository;
    OldPatientInfoRepository oldPatientInfoRepository;

    @Override
    @DatabaseAnnotation
    public SickLeave getSickLeaveOne(SickLeave entity) {
        entity.setDelFlag(false);
        return sickLeaveMapper.selectOne(entity);
    }

    @Override
    @DatabaseAnnotation
    public List<SickLeave> getSickLeaveList(String cardNo, String hisCardNo, String patName, Integer status, Integer hospitalCode) {
        SickLeave entity = new SickLeave();
        if (StringUtils.isNotBlank(cardNo)) {
            entity.setCardNo(cardNo);
        }
        if (StringUtils.isNotBlank(hisCardNo)) {
            entity.setBlCardNum(hisCardNo);
        }
        if (StringUtils.isNotBlank(patName)) {
            entity.setName(patName);
        }
        if (status != null && status != -1) {
            entity.setState(status);
        }
        entity.setHospitalCode(hospitalCode);
        entity.setDelFlag(false);
        return sickLeaveMapper.select(entity);
    }

    @Override
//    @Cacheable(
//            cacheNames = {"SysSick::SickLeave"},
//            key = "#patId",
//            unless = "#result == null"
//    )
    public List<SickLeaveDto> getAllSickLeaveListByPatId(Integer patId) {
        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        List<SickLeaveDto> result = new ArrayList<>();
        Weekend<SickLeave> weekend = new Weekend<>(SickLeave.class);
        weekend.weekendCriteria().andEqualTo(SickLeave::getPatId,patId);
        List<SickLeave> sickLeaveList = sickLeaveMapper.selectByExample(weekend);
        for(SickLeave sickLeave:sickLeaveList){
            SickLeaveDto sickLeaveDto = new SickLeaveDto();
            BeanUtils.copyProperties(sickLeave,sickLeaveDto);
            result.add(sickLeaveDto);
        }
        DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
        List<PatIdPatNo> patIdPatNoList = patientRepository.getPatIDPatNoList(Converter.toInt32(patId), null);
        if(patIdPatNoList!=null&&patIdPatNoList.size()>0){
            for(PatIdPatNo patIdPatNo:patIdPatNoList){
                if(HospitalClassify.GENERAL.getHospitalCode().equals(patIdPatNo.getHospitalCode())){
                    String hzbh = oldPatientInfoRepository.getHzbhByPatId(patIdPatNo.getPatNo(),HospitalClassify.GENERAL.getHospitalCode());
                    if(StringUtils.isNotBlank(hzbh)){
                        List<SickLeaveOld> generalSickLeaveOld = this.getOldSickLeaveListByPatId(hzbh,HospitalClassify.GENERAL.getHospitalCode());
                        for(SickLeaveOld sickLeaveOld:generalSickLeaveOld){
                            SickLeaveDto sickLeaveDto = new SickLeaveDto();
                            BeanUtils.copyProperties(sickLeaveOld,sickLeaveDto);
                            sickLeaveDto.setOldFlag(1);
                            sickLeaveDto.setHospitalCode(HospitalClassify.GENERAL.getHospitalCode());
                            result.add(sickLeaveDto);
                        }
                    }
                }else{
                    String hzbh = oldPatientInfoRepository.getHzbhByPatId(patIdPatNo.getPatNo(),HospitalClassify.BRANCH.getHospitalCode());
                    if(StringUtils.isNotBlank(hzbh)){
                        List<SickLeaveOld> branchSickLeaveOld = this.getOldSickLeaveListByPatId(hzbh,HospitalClassify.BRANCH.getHospitalCode());
                        for(SickLeaveOld sickLeaveOld:branchSickLeaveOld){
                            SickLeaveDto sickLeaveDto = new SickLeaveDto();
                            BeanUtils.copyProperties(sickLeaveOld,sickLeaveDto);
                            sickLeaveDto.setOldFlag(1);
                            sickLeaveDto.setHospitalCode(HospitalClassify.BRANCH.getHospitalCode());
                            result.add(sickLeaveDto);
                        }
                    }
                }
            }
        }
        return result;
    }

    @Override
    public List<SickLeaveOld> getOldSickLeaveListByPatId(String patId, Integer hospitalCode) {
        String dataName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode)?DatasourceName.MZYS:DatasourceName.MZYS3;
        DataSourceSwitchAspect.changeDataSource(dataName);
        Weekend<SickLeaveOld> weekend = new Weekend<>(SickLeaveOld.class);
        weekend.weekendCriteria().andEqualTo(SickLeaveOld::getHzbh,patId);
        return sickLeaveOldMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public List<SickLeaveOld> getOldSickLeaveList(String cardNo, String hisCardNo, String patName, Integer status, Integer hospitalCode) {
        SickLeaveOld entity = new SickLeaveOld();
//        if (StringUtils.isNotBlank(cardNo)) {
//            entity.setCardNo(cardNo);
//        }
        if (StringUtils.isNotBlank(hisCardNo)) {
            entity.setBlCardNum(hisCardNo);
        }
        if (StringUtils.isNotBlank(patName)) {
            entity.setName(patName);
        }
        if (status != null && status != -1) {
            entity.setState(status);
        }
        entity.setDelFlag(false);
        return sickLeaveOldMapper.select(entity);
    }

    @Override
    @DatabaseAnnotation
    public SickLeave getOneSickLeaveByRegNo(Long regNo, Integer hospitalCode) {
        final Weekend<SickLeave> weekend = new Weekend<>(SickLeave.class);
        weekend.weekendCriteria().andEqualTo(SickLeave::getRegNo, regNo)
                .andEqualTo(SickLeave::getHospitalCode, hospitalCode)
                .andEqualTo(SickLeave::getDelFlag, false);
        final List<SickLeave> records = sickLeaveMapper.selectByExample(weekend);
        return records.isEmpty() ? null : records.get(0);
    }

    @Override
    @DatabaseAnnotation
    public List<SickLeave> getSickLeaveInRegNo(List<Long> regNos, Integer hospitalCode) {
        final Weekend<SickLeave> weekend = new Weekend<>(SickLeave.class);
        weekend.weekendCriteria().andIn(SickLeave::getRegNo, regNos)
                .andEqualTo(SickLeave::getHospitalCode, hospitalCode)
                .andEqualTo(SickLeave::getDelFlag, false);
        return sickLeaveMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public SickLeave getSickLeaveById(Integer id) {
        final SickLeave entity = new SickLeave();
        if (id == null) {
            return null;
        }
        entity.setId(id);
        entity.setDelFlag(false);
        return sickLeaveMapper.selectOne(entity);
    }

    @Override
    @DatabaseAnnotation
    public void updateSickLeave(SickLeave entity) {
        sickLeaveMapper.updateByPrimaryKeySelective(entity);
        final List<SickLeaveDiagnosis> sickLeaveDiagnoses = entity.getDiagnosisList();
        final Boolean delFlag = entity.getDelFlag();
        if (sickLeaveDiagnoses == null || sickLeaveDiagnoses.isEmpty() || delFlag) {
            return;
        }
        sickLeaveDiagnosisRepository.deleteSickLeaveDiagnosisByMainId(sickLeaveDiagnoses.get(0).getMainId());
        sickLeaveDiagnoses.forEach(sickLeaveDiagnosisRepository::saveSickLeaveDiagnosis);
    }

    @Override
    @DatabaseAnnotation
    public void saveSickLeave(SickLeave entity, List<SickLeaveDiagnosis> sickLeaveDiagnoses) {
        sickLeaveMapper.insertSelective(entity);
        final Integer mainId = entity.getId();
        for (SickLeaveDiagnosis sickLeaveDiagnosis : sickLeaveDiagnoses) {
            if (sickLeaveDiagnosis.getMainId() == null || sickLeaveDiagnosis.getMainId() == 0) {
                sickLeaveDiagnosis.setMainId(mainId);
            }
            sickLeaveDiagnosisRepository.saveSickLeaveDiagnosis(sickLeaveDiagnosis);
        }
    }

    @Override
    @DatabaseAnnotation
    public String getSickInfo(Integer regNo, Integer hospitalCode) {
        return sickLeaveMapper.getSickInfo(regNo, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public List<SickLeave> querySickLeaveListByPatIds(List<Integer> patIds, Integer status, Integer hospitalCode) {
        Weekend<SickLeave> weekend = new Weekend<>(SickLeave.class);
        WeekendCriteria<SickLeave, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(SickLeave::getPatId, patIds);
        criteria.andEqualTo(SickLeave::getHospitalCode, hospitalCode);
        criteria.andEqualTo(SickLeave::getDelFlag, false);
        if (status != null && status != -1) {
            criteria.andEqualTo(SickLeave::getState, status);
        }
        weekend.orderBy("creTime").desc();
        return sickLeaveMapper.selectByExample(weekend);
    }

}
