package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationContentLoca;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

/**
 * <AUTHOR>
 */
public interface ApplicationContentLocaMapper extends BaseMapper<ApplicationContentLoca>, ExampleMapper<ApplicationContentLoca> {

    /**
     * 根据内容id  删除 MZYS_TB_JCSQDNRBW
     *
     * @param contentId    内容id
     * @param hospitalCode 医院编码
     * @return 受影响行数
     */
    @DatabaseAnnotation
    default int deleteByContentIdAndHospitalCode(Integer contentId, Integer hospitalCode) {
        final ApplicationContentLoca entity = new ApplicationContentLoca(contentId, hospitalCode);
        return delete(entity);
    }

}
