package com.rjsoft.outPatient.infrastructure.repository.service.impl;


import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.DictionaryCondition;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DiagnoseOldMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DiagnoseOldRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * 老数据诊断信息
 */
@Service
@AllArgsConstructor
public class DiagnoseOldRepositoryImpl implements DiagnoseOldRepository {
    DiagnoseOldMapper diagnoseOldMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public List<HashMap<String, String>> getDiagnoseOldByRegNo(Integer regNo) {
        return diagnoseOldMapper.getDiagnoseOldByRegNo(regNo);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public int existsWithDiseaseProofIdAndDiagnosticCode(Integer diseaseProofId, String diagnosticCode) {
        return diagnoseOldMapper.existsWithDiseaseProofIdAndDiagnosticCode(diseaseProofId,diagnosticCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public List<DictionaryCondition> getDictionaryCondition() {
        return diagnoseOldMapper.getDictionaryCondition();
    }
}
