package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DrugInfo;
import com.rjsoft.outPatient.domain.diseaseReport.dto.ReceptionDrugDTO;
import com.rjsoft.outPatient.domain.item.vo.ItemExeDeptRespVO;
import com.rjsoft.outPatient.domain.prescriptionAudit.dto.AuditPrescriptionDto;
import com.rjsoft.outPatient.domain.prescriptionAudit.vo.AuditMedicine;
import com.rjsoft.outPatient.domain.recipe.dto.*;
import com.rjsoft.outPatient.domain.recipe.vo.DoctorFeeQuotaVo;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 门诊处方
 *
 * <AUTHOR>
public interface HerbsRecipeRepository {
    /**
     * 保存草药处方信息
     *
     * @param recipe
     * @return
     */
    boolean saveHerbRecipe(Recipe recipe);



    /**
     * 查询草药联动治疗项目
     *
     * @param recipeNo
     * @param hospitalCode
     * @return
     */
    List<RecipeDetail> getRecipeDetailHerbsSpecial(Long recipeNo, Integer hospitalCode);


    /**
     * 根据就诊号查询草药联动治疗项目
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    List<RecipeDetail> getRecipeDetailHerbsSpecialByReceptionNo(Long receptionNo, Integer hospitalCode);

    /**
     * 根据处方id查询老处方详情
     *
     * @param recipeNos
     * @return
     */
    List<OldRecipeDetail> getOldRecipeDetailbyReceptionNo(List<String> recipeNos, Integer feeCategory, Integer hospitalCode);



    /**
     * 删除草药处方通过处方流水号
     *
     * @param recipeNo
     * @param feeCategory
     * @param hospitalCode
     * @return
     */
    boolean deleteHerbsRecipebyRrecipeNo(Long recipeNo, Integer feeCategory, Integer hospitalCode, Long receptionNo);


    /**
     * 根据就就诊号查询老系统处方
     *
     * @param receptionNos
     * @return
     */
    List<OldRecipe> getOldRecipeInfobyReceptionNos(List<String> receptionNos, Integer feeCategory, Integer hospitalCode);


    /**
     * 根据处方id查询处方详情
     *
     * @param recipeNos
     * @return
     */
    List<RecipeDetail> getRecipeDetailbyReceptionNo(List<Long> recipeNos, Integer feeCategory, Integer hospitalCode);

}
