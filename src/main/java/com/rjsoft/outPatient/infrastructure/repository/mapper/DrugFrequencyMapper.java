package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.DrugFrequency;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

/**
 * 药品频次
 *
 * <AUTHOR>
public interface DrugFrequencyMapper extends BaseMapper<DrugFrequency>, ExampleMapper<DrugFrequency> {

    /**
     * 根据ID获取药品频次
     * @param id
     * @param hospitalCode
     * @return
     */
    default DrugFrequency getDrugFrequencyById(Integer id, Integer hospitalCode) {
        DrugFrequency entity = new DrugFrequency();
        entity.setId(id);
        entity.setHospitalCode(hospitalCode);
        return  selectByPrimaryKey(entity);
    }
}
