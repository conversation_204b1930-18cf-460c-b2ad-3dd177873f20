package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.research.dto.VisitPlanDto;
import com.rjsoft.outPatient.domain.research.dto.VisitPlanInputDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysKyQtsfjhzb;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

public interface MzysKyQtsfjhzbMapper extends BaseMapper<MzysKyQtsfjhzb>, ExampleMapper<MzysKyQtsfjhzb> {
    List<VisitPlanDto> visitPlan(@Param("visitPlanInputDto") VisitPlanInputDto visitPlanInputDto);
    String submitExtraVisitPlan(@Param("id") String id);
}
