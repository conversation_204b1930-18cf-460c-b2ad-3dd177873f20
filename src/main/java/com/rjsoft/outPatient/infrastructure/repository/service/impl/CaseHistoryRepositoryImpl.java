package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.param.SearchParam;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.domain.caseHistory.Enum.CaseHistoryOpTypeEnum;
import com.rjsoft.outPatient.domain.caseHistory.dto.*;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.CaseHistoryRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.PatientRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.*;

/**
 * 病历
 *
 * <AUTHOR>
@AllArgsConstructor
@Service
public class CaseHistoryRepositoryImpl implements CaseHistoryRepository {
    CaseHistoryMapper caseHistoryMapper;
    CaseHistoryOldMapper caseHistoryOldMapper;
    RegisterListMapper registerListMapper;
    SystemTbWorkerMapper systemTbWorkerMapper;
    SystemTbDepartmentMapper systemTbDepartmentMapper;
    CaseHistoryLogMapper caseHistoryLogMapper;
    CaseHistoryOpLogMapper caseHistoryOpLogMapper;
    SFCaseHistoryMapper sfCaseHistoryMapper;
    InHospitalInfoMapper inHospitalInfoMapper;
    EmrblMapper emrblMapper;
    PatientRepository patientRepository;
    DeptControlMapper deptControlMapper;
    SFCaseHistoryMapper sFCaseHistoryMapper;
    IoTbInpatientMapper ioTbInpatientMapper;

    /**
     * 根据挂号流水号集合，获取病历状态
     *
     * @param regNo
     * @return
     */

    @DatabaseAnnotation
    @Override
    public List<CaseHistory> getCaseHistoryStatusByRegNo(List<Long> regNo) {
        return caseHistoryMapper.getCaseHistoryStatusByRegNo(regNo);
    }

    /**
     * 根据就诊流水号获取病历状态（只返回状态相关字段，用于流程控制）
     * 打印标记、提交标记、完成标记、初审标记、处理标记
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public CaseHistory getCaseHistoryStatusByReceptionNo(Long receptionNo, Integer hospitalCode) {
        if (receptionNo == null || hospitalCode == null) {
            return null;
        }
        Weekend weekend = new Weekend(CaseHistory.class);
        WeekendCriteria<CaseHistory, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(CaseHistory::getReceptionNo, receptionNo);
        weekendCriteria.andEqualTo(CaseHistory::getHospitalCode, hospitalCode);
        weekend.selectProperties("printFlag", "submitFlag", "completeFlag", "auditFlag", "dealFlag", "firstAuditFlag", "blId");
        List<CaseHistory> caseHistories = caseHistoryMapper.selectByExample(weekend);
        if (caseHistories.size() == 0) {
            return null;
        }
        return caseHistories.get(0);
    }

    /**
     * 根据就诊流水号，检查是否存在有效病历
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean hasCaseHistoryByReceptionNo(Long receptionNo, Integer hospitalCode) {
        Weekend weekend = new Weekend(CaseHistory.class);
        WeekendCriteria<CaseHistory, Object> weekendCriteria = weekend.weekendCriteria();
        Integer jslsh = Converter.toInt32(receptionNo);
        weekendCriteria.andEqualTo(CaseHistory::getReceptionNo, jslsh);
        weekendCriteria.andEqualTo(CaseHistory::getHospitalCode, hospitalCode);
        weekendCriteria.andNotEqualTo(CaseHistory::getSubmitFlag, 0);
        return caseHistoryMapper.selectCountByExample(weekend) > 0;
    }

    @Override
    @DatabaseAnnotation
    public boolean delCaseHistoryByReceptionNo(Long receptionNo, Integer hospitalCode) {
        CaseHistory entity = new CaseHistory();
        entity.setReceptionNo(Converter.toString(receptionNo));
        entity.setHospitalCode(hospitalCode);
        return caseHistoryMapper.delete(entity) > 0;
    }

    @Override
    @DatabaseAnnotation
    public CaseHistory getCaseHistoryByMedicalNumAndSubmitFlag(Integer medicalNum, Integer submitFlag) {
        final Weekend<CaseHistory> weekend = new Weekend<>(CaseHistory.class);
        if (submitFlag != null) {
            weekend.weekendCriteria().andEqualTo(CaseHistory::getReceptionNo, medicalNum)
                    .andEqualTo(CaseHistory::getSubmitFlag, submitFlag);
        } else {
            weekend.weekendCriteria().andEqualTo(CaseHistory::getReceptionNo, medicalNum);
        }
        return caseHistoryMapper.selectOneByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public CaseHistory getCaseHistoryByMedicalNumAndPrintFlag(Integer medicalNum, Integer printFlag, Integer hospitalCode) {
        final Weekend<CaseHistory> weekend = new Weekend<>(CaseHistory.class);
        weekend.weekendCriteria().andEqualTo(CaseHistory::getReceptionNo, medicalNum)
                .andEqualTo(CaseHistory::getPrintFlag, printFlag)
                .andEqualTo(CaseHistory::getHospitalCode, hospitalCode);
        return caseHistoryMapper.selectOneByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public String getCaseHistoryBlByMedicalNum(Integer medicalNum) {
        return caseHistoryMapper.getCaseHistoryBlByMedicalNum(medicalNum);
    }

    @Override
    @DatabaseAnnotation
    @Cacheable(cacheNames = "getCaseHistoryByRegNos1", unless = "#result == null")
    public List<CaseHistory> getCaseHistoryByRegNos(List<Long> regNos, Date startTime, Date endTime, String... columns) {
        List<CaseHistory> caseHistoryList = caseHistoryMapper.getCaseHistoryByRegNos(startTime, endTime, regNos, null, columns);
        return caseHistoryList;
    }

    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    @Cacheable(cacheNames = "getInternetCaseHistoryIdByRegNos1", unless = "#result == null")
    public List<InternetCaseHistory> getInternetCaseHistoryIdByRegNos(List<Long> regNos, Date startTime, Date endTime) {
        List<InternetCaseHistory> internetCaseHistoryList = caseHistoryMapper.getInternetCaseHistoryIdByRegNos(startTime, endTime, regNos);
        return internetCaseHistoryList;
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<InternetCaseHistory> getInternetCaseHistoryByRegNos(Integer hospitalCode, List<String> internetRegNos) {
        List<InternetCaseHistory> internetCaseHistoryList = caseHistoryMapper.getInternetCaseHistoryByRegNos(hospitalCode, internetRegNos);
        return internetCaseHistoryList;
    }

    @Override
    public PatientDTO getPatient(String regNo, Integer hospitalCode, Integer dataSources) {
        // 新门诊
        if (dataSources.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
            return registerListMapper.queryPatientInfo(regNo);
        }
        // 老门诊
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }
        return registerListMapper.queryOldPatientInfo(regNo);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public Department getDept(int deptId, int hospId) {
//        Department department = new Department();
//        department.setDeptId(deptId);
//        department.setHospitalId(hospId);
//        return systemTbDepartmentMapper.selectByPrimaryKey(department);
        return systemTbDepartmentMapper.getDept(deptId, hospId);
    }


    @Override
    public int addCaseHistory(CaseHistory caseHistory, Integer hospitalCode, Integer dataSources, String blId) {
        // 新门诊
        if (dataSources.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
            return caseHistoryMapper.insertSelective(caseHistory);
        } else {
            // 老门诊
            CaseHistoryOld caseHistoryOld = new CaseHistoryOld();
            BeanUtils.copyProperties(caseHistory, caseHistoryOld);
            caseHistoryOld.setBlId(blId);
            caseHistory.setRegNo(null);
            caseHistory.setOpCode(null);
            caseHistory.setOpTime(null);
            caseHistory.setTempCode(null);
            caseHistory.setHospitalCode(null);
            caseHistory.setBlRtfText(null);
            if (hospitalCode.equals(1)) {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
            } else {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
            }
            return caseHistoryOldMapper.insertSelective(caseHistoryOld);
        }
    }

    @DatabaseAnnotation(name = "HISDB")
    @Override
    public Worker getDoctor(int doctorId, int hospId) {
        Worker workerPOJO = new Worker();
        workerPOJO.setWorkerId(doctorId);
        workerPOJO.setHospitalId(hospId);
        return systemTbWorkerMapper.selectByPrimaryKey(workerPOJO);
    }


    @Override
    @DatabaseAnnotation
    public void updateCaseHistoryStatus(CaseHistoryUpdateForm updateForm) {
        //根据flag修改不同字段，0修改打印标记、1修改提交标记、2完成标记、3初诊标记、4初审标记、5审核标记、6处理标记
        CaseHistory pojo = new CaseHistory();
        pojo.setBlId(Converter.toString(updateForm.getBlId()));
        pojo.setOpCode(updateForm.getOpCode());
        pojo.setOpTime(new Date());
        pojo.setUpdateTime(new Date());
        int flag = updateForm.getFlag();
        switch (flag) {
            case 0:
                pojo.setPrintFlag(1);
                break;
            case 1:
                pojo.setSubmitFlag(1);
                pojo.setSubmitDate(new Date());
                break;
            case 2:
                pojo.setCompleteFlag(1);
                pojo.setCompleteTime(new Date());
                break;
            case 3:
                pojo.setFirstDiagnosisFlag(1);
                break;
            case 4:
                pojo.setFirstAuditFlag(1);
                break;
            case 5:
                pojo.setAuditFlag(1);
                break;
            case 6:
                pojo.setDealFlag(1);
                break;
            default:
                pojo = new CaseHistory();
                break;
        }
        caseHistoryMapper.updateByPrimaryKeySelective(pojo);
    }

    @Override
    public void updateSubmitStatus(String blId, Integer submitStatus, Integer opCode, Integer dataSources, Integer hospitalCode) {
        CaseHistory caseHistory = new CaseHistory();
        // 新门诊
        if (dataSources.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
            // 老门诊
        } else {
            if (hospitalCode.equals(1)) {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
            } else {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
            }
        }
        caseHistory = caseHistoryMapper.selectById(blId);
        Assert.notNull(caseHistory, "未找到对应病历数据");

        Integer submitFlag = caseHistory.getSubmitFlag();
        Integer completeFlag = caseHistory.getCompleteFlag();
        Integer printFlag = caseHistory.getPrintFlag();
        Integer auditFlag = caseHistory.getAuditFlag();

        //定稿
        if (submitStatus.equals(1)) {
            if (submitFlag.equals(1)) {
                Assert.isTrue(false, "该病历已经提交，无法操作");
            }
            if (completeFlag.equals(1)) {
                Assert.isTrue(false, "该病历已经最终定稿，无法操作");
            }
        }

        //撤销定稿
        else if (submitStatus.equals(0)) {
            if (completeFlag.equals(1)) {
                Assert.isTrue(false, "该病历已经最终定稿，无法操作");
            }
            if (printFlag.equals(1)) {
                Assert.isTrue(false, "病历已打印，撤销提交需要到门办。");
            }
            if (auditFlag.equals(1)) {
                Assert.isTrue(false, "病历已审核，无法进行撤销提交操作。");
            }
        }
        // 新门诊
        if (dataSources.equals(1)) {
            CaseHistory updateCaseHistory = new CaseHistory();
            updateCaseHistory.setBlId(blId);
            updateCaseHistory.setSubmitFlag(submitStatus);
            if (1 == submitStatus) {
                updateCaseHistory.setSubmitDate(new Date());
            }
            updateCaseHistory.setUpdateTime(new Date());
            updateCaseHistory.setOpCode(opCode);
            updateCaseHistory.setOpTime(new Date());
            caseHistoryMapper.updateByPrimaryKeySelective(updateCaseHistory);

            //记录操作日志
            caseHistory = caseHistoryMapper.queryBlHistoryById(Converter.toInt32(blId));
            CaseHistoryLog caseHistoryLog = new CaseHistoryLog();
            caseHistoryLog = caseHistoryLog.CreateEntity(caseHistory);
            if (1 == submitStatus) {
                caseHistoryLog.setOpType(CaseHistoryOpTypeEnum.SUBMIT.getCode());
            } else {
                caseHistoryLog.setOpType(CaseHistoryOpTypeEnum.CANCEL_SUBMIT.getCode());
            }
            saveCaseHistoryLog(caseHistoryLog, hospitalCode, dataSources, blId);

        } else {
            // 老门诊
            if (hospitalCode.equals(1)) {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
            } else {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
            }
            CaseHistory updateCaseHistory = new CaseHistory();
            updateCaseHistory.setBlId(blId);
            updateCaseHistory.setSubmitFlag(submitStatus);
            if (1 == submitStatus) {
                updateCaseHistory.setSubmitDate(new Date());
            }
            updateCaseHistory.setUpdateTime(new Date());
            caseHistoryMapper.updateByPrimaryKeySelective(updateCaseHistory);
        }

    }

    @Override
    public void saveCaseHistoryLog(CaseHistoryLog caseHistoryLog, Integer hospitalCode, Integer dataSources, String
            blId) {
        // 新门诊
        if (dataSources.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);

        } else {
            // 老门诊
            caseHistoryLog.setId(null);
            caseHistoryLog.setBlNo(blId);
            if (hospitalCode.equals(1)) {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
            } else {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
            }
        }
        caseHistoryLogMapper.insertSelective(caseHistoryLog);
    }

    @Override
    @DatabaseAnnotation
    public void saveCaseHistoryOpLog(CaseHistoryOpLog caseHistoryOpLog, Integer dataSources) {
        if (dataSources.equals(1)) {
            caseHistoryOpLogMapper.insertSelective(caseHistoryOpLog);
        }
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public void updateSFCaseHistory(SFCaseHistoryForm sfbl, Integer hospitalCode, Integer dataSources) {
        // 新门诊
        //if (dataSources.equals(1)) {
        //    DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        //
        //} else {
        //    // 老门诊
        //    if (hospitalCode.equals(1)) {
        //        DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        //    } else {
        //        DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        //    }
        //}
        //DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        SFCaseHistory sfCaseHistory = new SFCaseHistory();
        BeanUtils.copyProperties(sfbl, sfCaseHistory);
        sfCaseHistory.setUpdateTime(new Date());
        sfCaseHistory.setHospitalCode(hospitalCode);
        sfCaseHistoryMapper.updateByPrimaryKeySelective(sfCaseHistory);
    }

    @Override
    @DatabaseAnnotation
    public List<CaseHistory> queryDzblFromMzysNew(Integer jzlsh) {
        CaseHistory caseHistory = new CaseHistory();
        caseHistory.setReceptionNo(Converter.toString(jzlsh));
        return caseHistoryMapper.select(caseHistory);
    }

    @Override
    @DatabaseAnnotation(name = "MZYS")
    public List<CaseHistoryVO> queryDzblFromMzys(int jzlsh) {
        return caseHistoryMapper.queryDzblFromMzys(jzlsh);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public SFCaseHistory queryPatShuFu(String patId, Integer hospitalCode, Integer dataSources) {
        // 新门诊
        if (dataSources.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
            return sFCaseHistoryMapper.getPatShuFu(patId, hospitalCode);
        }
        // 老门诊
        //if (hospitalCode.equals(1)) {
        //    DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        //} else {
        //    /// 舒辅病历，目前只有总院
        //    // DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        //    //return new SFCaseHistory();
        //    return null;
        //}
        return sFCaseHistoryMapper.getPatShuFuOld(patId, hospitalCode);
    }


    @Override
    @DatabaseAnnotation
    public List<PatChiefDTO> queryPatChief(int patId, int hospitalCode) {
        return caseHistoryMapper.queryPatChief(patId, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public CaseHistory loadReviewCaseFromNow(Integer receptionNo) {
        CaseHistory caseHistory = caseHistoryMapper.loadReviewCaseFromNow(receptionNo);
        return caseHistory;
    }

    /**
     * 获取最近一次挂号的复诊病历
     *
     * @param regNo
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public CaseHistory loadReviewCaseFromNew(List<Long> regNo, Integer receptionNo, Integer hospitalCode) {
        CaseHistory res = null;
        PageHelper.startPage(1, 1, "regno desc");
        List<CaseHistory> list = caseHistoryMapper.loadReviewCaseFromNew(regNo, receptionNo, hospitalCode, "");
        if (list.stream().count() > 0) {
            res = list.get(0);
        }
        return res;
    }

    @Override
    @DatabaseAnnotation(name = "MZYS")
    public CaseHistory loadReviewCaseFromOld(String outPatientNo, Integer deptId, Integer receptionNo) {
        List<CaseHistory> caseHistoryList = caseHistoryMapper.loadReviewCaseFromOld(outPatientNo, deptId);
        if (caseHistoryList.size() != 0) {
            return caseHistoryList.get(0);
        } else {
            return null;
        }
    }

    @Override
    @DatabaseAnnotation
    public CaseHistory getCaseHistoryByReceptionNoFromNew(Integer receptionNo, Integer hospCode) {
        return caseHistoryMapper.getCaseHistoryByReceptionNo(receptionNo, hospCode);
    }

    /**
     * 根据就诊流水号查询病历记录，不包括病历内容
     *
     * @param receptionNo
     * @param hospCode
     * @return
     */
    @Override
    public CaseHistory getCaseHistoryByReceptionNoFromContent(String receptionNo, Integer hospCode, Integer
            dataSources) {
        // 新门诊
        if (dataSources.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        } else {
            // 老门诊
            if (hospCode.equals(1)) {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
            } else {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
            }
        }
        return caseHistoryMapper.getCaseHistoryByReceptionNoNoContent(receptionNo, hospCode, dataSources);
    }

    @Override
    @DatabaseAnnotation
    public List<CaseHistory> getCaseHistoryByReceptionNoFromNewTwo(Integer receptionNo) {
        return caseHistoryMapper.getCaseHistoryByReceptionNoTwo(receptionNo);
    }

    @Override
    @DatabaseAnnotation
    public CaseHistory getCaseHistoryByReceptionNoFromOld(Integer receptionNo, Integer hospCode) {
        return caseHistoryMapper.getCaseHistoryByReceptionNo(receptionNo, hospCode);
    }


    @Override
    @DatabaseAnnotation
    public CaseHistory getCaseHistoryById(Integer blId) {
        return caseHistoryMapper.selectByPrimaryKey(blId);
    }


    @Override
    @DatabaseAnnotation
    public List<CaseHistoryLog> getCaseHistoryLog(Integer receptionNo, Integer blNo, Integer id) {
        Weekend weekend = new Weekend(CaseHistoryLog.class);
        WeekendCriteria<CaseHistoryLog, Object> weekendCriteria = weekend.weekendCriteria();

        if (receptionNo != 0) {
            weekendCriteria.andEqualTo(CaseHistoryLog::getReceptionNo, receptionNo);
            weekend.excludeProperties("blXml");
        }
        if (blNo != 0) {
            weekendCriteria.andEqualTo(CaseHistoryLog::getBlNo, "zk" + blNo);
            weekend.excludeProperties("blXml");
        }
        if (id != 0) {
            weekendCriteria.andEqualTo(CaseHistoryLog::getId, id);
        }
        return caseHistoryLogMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public int updateCaseHistory(CaseHistory updateCaseHistory) {
        return caseHistoryMapper.updateByPrimaryKeySelective(updateCaseHistory);
    }

    @Override
    public int updateCaseHistoryByReception(CaseHistory updateCaseHistory, Integer hospitalCode, Integer
            dataSources) {
        // 新门诊
        if (dataSources.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        } else {
            // 老门诊
            if (hospitalCode.equals(1)) {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
            } else {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
            }
        }

        if (dataSources.equals(1)) {
            Weekend<CaseHistory> weekend = new Weekend<>(CaseHistory.class);
            WeekendCriteria<CaseHistory, Object> criteria = weekend.weekendCriteria();
            criteria.andEqualTo(CaseHistory::getBlId, updateCaseHistory.getBlId());
            criteria.andEqualTo(CaseHistory::getHospitalCode, hospitalCode);
            return caseHistoryMapper.updateByExampleSelective(updateCaseHistory, weekend);
        } else {
            Weekend<CaseHistoryOld> weekend = new Weekend<>(CaseHistoryOld.class);
            WeekendCriteria<CaseHistoryOld, Object> criteria = weekend.weekendCriteria();
            criteria.andEqualTo(CaseHistoryOld::getReceptionNo, updateCaseHistory.getReceptionNo());
            CaseHistoryOld caseHistoryOld = new CaseHistoryOld();
            BeanUtils.copyProperties(updateCaseHistory, caseHistoryOld);
            return caseHistoryOldMapper.updateByExampleSelective(caseHistoryOld, weekend);
        }
    }

    @Override
    @DatabaseAnnotation
    public CaseHistory getCaseHistoryByBlIdFromMZYSNew(int blId, int hospCode) {
        CaseHistory caseHistory = new CaseHistory();
        caseHistory.setBlId(Converter.toString(blId));
        caseHistory.setHospitalCode(hospCode);
        return caseHistoryMapper.selectOne(caseHistory);
    }

    @Override
    @DatabaseAnnotation(name = "MZYS")
    public CaseHistory getCaseHistoryByBlIdFromMZYS(int blId, int hospCode) {
        CaseHistory caseHistory = new CaseHistory();
        caseHistory.setBlId(Converter.toString(blId));
        caseHistory.setHospitalCode(hospCode);
        return caseHistoryMapper.selectOne(caseHistory);
    }

    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    public List<InHospitalInfo> queryHospitalInfos(String certificateNo, int hospCode) {
        //根据医院编码切换数据源
        String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospCode) ? DatasourceName.ZXHIS : DatasourceName.ZXHIS3;
        DataSourceSwitchAspect.changeDataSource(dataSourceName);
        return inHospitalInfoMapper.queryRegNoByCertificateNo(certificateNo);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<IoTbInpatient> queryInpatientInfos(int patId, Integer status, String startTime, String endTime) {
        return ioTbInpatientMapper.queryInpatientInfos(patId, status, startTime, endTime);
    }

    @Override
    @DatabaseAnnotation(name = "ZYEMR")
    public List<EmrBl> queryBlByRegNo(Set<String> regNos, int hospCode) {
        if (regNos.size() == 0) {
            return new ArrayList<>();
        }
        //根据医院编码切换数据源
        String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospCode) ? DatasourceName.ZYEMR : DatasourceName.ZYEMR3;
        DataSourceSwitchAspect.changeDataSource(dataSourceName);
        return emrblMapper.queryBlInfo(regNos);
    }

    @Override
    @DatabaseAnnotation(name = "ZYEMR")
    public List<String> queryBlDetailGeneral(String regNo, String bcDmno, Date recDate) {
        List<String> bls = emrblMapper.queryDetail(regNo, bcDmno, recDate);
        if (bls.size() == 0) {
            return new ArrayList<String>();
        }
        return bls;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<String> getBlDetail(String regNo, String bcDmno, Date recDate, Integer hospitalCode) {
        List<String> bls =  emrblMapper.getBlDetail(regNo, bcDmno, recDate, hospitalCode);
        if (bls.size() == 0) {
            return new ArrayList<String>();
        }
        return bls;
    }

    @Override
    @DatabaseAnnotation(name = "ZYEMR3")
    public List<String> queryBlDetail(String regNo, String bcDmno, Date recDate) {
        List<String> bls = emrblMapper.queryDetail(regNo, bcDmno, recDate);
        if (bls.size() == 0) {
            return new ArrayList<String>();
        }
        return bls;
    }

    /**
     * 根据患者编号查询患者历史病历
     *
     * @param hospitalCode
     * @param patIds
     * @param createTime
     * @param endTime
     */
    @Override
    @DatabaseAnnotation
    public List<CaseHistory> queryBlHistory(Integer hospitalCode, Set<Integer> patIds, Date createTime, Date
            endTime) {
        return caseHistoryMapper.queryBlHistory(hospitalCode, patIds, createTime, endTime);
    }

    /**
     * 根据患者病历id获取患者病历信息
     *
     * @param id
     */
    @Override
    @DatabaseAnnotation
    public CaseHistoryByIdResponse queryBlHistoryFromMzysNew(Integer id) {
        CaseHistoryByIdResponse response = new CaseHistoryByIdResponse();
        CaseHistory caseHistory = caseHistoryMapper.queryBlHistoryById(id);
        if (caseHistory == null) {
            return response;
        }
        response.setBlHtml(caseHistory.getBlHtml());
        response.setBlXml(caseHistory.getBlXml());
        return response;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public CaseHistoryByIdResponse queryBlHistoryFromInternet(String blId) {
        CaseHistoryByIdResponse caseHistoryByIdResponse = new CaseHistoryByIdResponse();
        InternetCaseHistoryDetail internetCaseHistoryDetail = caseHistoryMapper.queryBlHistoryFromInternet(blId);
        if (null == internetCaseHistoryDetail) {
            return caseHistoryByIdResponse;
        }
        caseHistoryByIdResponse.setCharacterString("主诉：" + internetCaseHistoryDetail.getComplaint()
                + "\r\n病史:" + internetCaseHistoryDetail.getMedicalHistory()
                + "\r\n诊断:" + internetCaseHistoryDetail.getDiagnoseName()
                + "\r\n处理意见:" + internetCaseHistoryDetail.getHandlingSuggestion());
        return caseHistoryByIdResponse;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public String getDeptName(Integer dept, Integer hospitalCode) {
        return caseHistoryMapper.getDeptName(dept, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public String GetMedicalHistory(Long receptionNo, Integer hospitalCode) {
        return caseHistoryMapper.GetMedicalHistory(receptionNo, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public List<QueryHistoryCaseInfoResult> getCaseHistoryFromZY(String guidRegNo, Date startDate, Date endDate) {
        return caseHistoryMapper.getCaseHistoryByGuidRegNo(guidRegNo, startDate, endDate);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public List<QueryHistoryCaseInfoResult> getCaseHistoryFromZYProc(Integer patId, Date startDate, Date endDate) {
        return caseHistoryMapper.getCaseHistoryFromZYProc(patId, startDate, endDate);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public List<QueryHistoryCaseInfoResult> getCaseHistoryFromZyOffset(String guidRegNo, Date startDate, Date endDate) {
        return caseHistoryMapper.getCaseHistoryByGuidRegNoOffset(guidRegNo, startDate, endDate);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS3)
    public List<QueryHistoryCaseInfoResult> getCaseHistoryFromFY(String guidRegNo, Date startDate, Date endDate) {
        return caseHistoryMapper.getCaseHistoryByGuidRegNo(guidRegNo, startDate, endDate);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public CaseHistoryByIdResponse queryBlHistoryFromZY(String blId) {
        return caseHistoryMapper.queryBlHistoryByBlId(blId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public CaseHistoryByIdResponse queryBlHistoryFromZyOffset(String blId) {
        return caseHistoryMapper.queryBlHistoryFromZyOffset(blId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS3)
    public CaseHistoryByIdResponse queryBlHistoryFromFY(String blId) {
        return caseHistoryMapper.queryBlHistoryByBlId(blId);
    }

    /**
     * 获取医生病历信息 【新系统】
     *
     * @param doctorId
     * @param hospitalCode
     */
    @Override
    public List<CAAuthorizedData> caAuthorizedData(Integer doctorId, Integer hospitalCode) {
        // 获取新系统数据
        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        ArrayList<CAAuthorizedData> list = new ArrayList<>(caseHistoryMapper.caAuthorizedData(doctorId, hospitalCode));
        if (hospitalCode == 1) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }
        list.addAll(caseHistoryMapper.caAuthorizedData(doctorId, null));
        return list;
    }

    /**
     * 根据就诊流水号获取病历数据
     *
     * @param reception
     * @param hospitalCode
     */
    @Override
    public List<DiagnoseRecordInfo> getCaseHistory(String reception, Integer hospitalCode) {
        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        List<DiagnoseRecordInfo> caseHistory = caseHistoryMapper.getCaseHistory(reception);
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }
        caseHistory.addAll(caseHistoryMapper.getCaseHistory(reception));
        return caseHistory;
    }

    /**
     * 根据就诊流水号获取患者病历内容返回数据
     *
     * @param receptionNo
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public BLContentDto queryNewPatBl(String receptionNo, Integer hospitalCode) {
        return caseHistoryMapper.queryNewPatBl(receptionNo, hospitalCode);
    }

    /**
     * 根据就诊流水号获取患者病历内容返回数据
     *
     * @param receptionNo
     * @param hospitalCode
     */
    @Override
    public BLContentDto queryOldPatBl(String receptionNo, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }
        return caseHistoryMapper.queryOldPatBl(receptionNo);
    }

    /**
     * 【补写病历】查询审核病历列表
     *
     * @param param
     */
    @Override
    @DatabaseAnnotation
    public List<CheckBlDto> queryCheckBls(CheckBlParam param) {
        return caseHistoryMapper.queryCheckBls(param);
    }

    /**
     * 【补写病历】查询审核病历列表
     *
     * @param param
     */
    @Override
    public List<CheckBlDto> queryOldCheckBls(CheckBlParam param) {
        if (param.getHospitalCode().equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);

        }
        return caseHistoryMapper.queryOldCheckBls(param);
    }

    /**
     * 根据病历卡号获取患者初诊日期
     *
     * @param hisCardNo
     * @param hospitalCode
     */
    @Override
    public String getFirstVisitDate(String hisCardNo, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
        }
        return caseHistoryMapper.getFirstVisitDate(hisCardNo);
    }

    /**
     * 根据病历Id,新老数据源，医院Id,修改病历处理标记为【已处理 clbj】
     *
     * @param id           病历Id
     * @param dataSources  数据源
     * @param hospitalCode 医院编码
     */
    @Override
    public void updateBlHandleState(String id, Integer dataSources, Integer hospitalCode) {
        // 新门诊
        if (dataSources.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
            caseHistoryMapper.updateNewHandleState(id);
        } else {
            // 老门诊
            if (hospitalCode.equals(1)) {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
            } else {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
            }
            caseHistoryMapper.updateNewHandleState(id);
        }
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public List<DeptControl> GetDeptControl() {
        Weekend<DeptControl> weekend = new Weekend<>(DeptControl.class);
        weekend.weekendCriteria().andIn(DeptControl::getReportCode, Arrays.asList(1000, 1001));
        return deptControlMapper.selectByExample(weekend);
    }


    @Override
    @DatabaseAnnotation
    public List<CaseHistoryLog> getCaseHistoryLogControl(SearchParam param) {
        Integer hospitalCode = Converter.toInt32(param.getHospitalCode());
        String hisCardNo = Converter.toString(param.getKeys().get("hisCardNo"));
        Integer regNo = Converter.toInt32(param.getKeys().get("regNo"));
        String patName = Converter.toString(param.getKeys().get("patName"));
        Integer workerId = Converter.toInt32(param.getKeys().get("workerId"));
        Integer state = Converter.toInt32(param.getKeys().get("state"));
        String sortField = Converter.toString(param.getKeys().get("sortField"));
        if (state != null && state.equals(0)) {
            state = -1;
        }

        if (!StringUtils.isEmpty(sortField)) {
            //如果排序字段不为空，根据排序字段信息关联特殊处理
            if (sortField.startsWith("blCardNo")) {
                sortField = sortField.replace("blCardNo", "a.blkh");
            }
            if (sortField.startsWith("regNo")) {
                sortField = sortField.replace("regNo", "a.regno");
            }
            if (sortField.startsWith("deptName")) {
                sortField = sortField.replace("deptName", "a.ksbm");
            }
            if (sortField.startsWith("receptionDate")) {
                sortField = sortField.replace("receptionDate", "b.sckzrq");
            }
            if (sortField.startsWith("opDoctorName")) {
                sortField = sortField.replace("opDoctorName", "pym");
            }
            if (sortField.startsWith("doctorNo")) {
                sortField = sortField.replace("doctorNo", "a.opDoctorId");
            }
            if (sortField.startsWith("opTypeName")) {
                sortField = sortField.replace("opTypeName", "a.opType");
            }
            if (sortField.startsWith("opTime")) {
                sortField = sortField.replace("opTime", "a.opTime");
            }
        }

        //设置结束时间
        Calendar calendar = new GregorianCalendar(TimeZone.getTimeZone("GMT+8"));
        calendar.setTime(Optional.ofNullable(param.getEndTime()).orElse(new Date()));
        calendar.add(Calendar.DATE, 1);
        Date endTime = calendar.getTime();

        //设置开始时间
        Calendar calendar1 = new GregorianCalendar(TimeZone.getTimeZone("GMT+8"));
        calendar1.setTime(Converter.toDate("1970-01-01", "yyyy-MM-dd"));
        Date startTime = Optional.ofNullable(param.getStartTime()).orElse(calendar1.getTime());

        PageHelper.startPage(param.getPageNum(), param.getPageSize(), sortField);
        List<CaseHistoryLog> list = caseHistoryLogMapper.getCaseHistoryLogControl(startTime, endTime, hospitalCode, hisCardNo, regNo, patName, workerId, state, sortField);

        PageInfo pageInfo = new PageInfo<>(list);
        param.setTotalCount(Converter.toInt32(pageInfo.getTotal()));
        return list;
    }


    @Override
    @DatabaseAnnotation
    public void saveCaRecord(String receptionNo, Long caId) {
        caseHistoryMapper.saveCaRecord(receptionNo, caId);
    }

    @Override
    @DatabaseAnnotation
    public Integer getCaRecordId(Long caId) {
        return caseHistoryMapper.getCaRecordId(caId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public List<QueryHistoryCaseInfoResult> getCaseHistoryByPatientGuid(String patientGuid, Date startDate, Date endDate) {
        return caseHistoryMapper.getCaseHistoryByPatientGuid(patientGuid, startDate, endDate);
    }

    @Override
    public List<QueryHistoryCaseInfoResult> getOldestCaseHistory(String certificateNo, Date startDate, Date endDate, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
        }
        return caseHistoryMapper.getOldestCaseHistory(certificateNo, startDate, endDate);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.MZEMR)
    public CaseHistoryByIdResponse getOldestCaseHistoryDetail(Integer id) {
        return caseHistoryMapper.getOldestCaseHistoryDetail(id);
    }

    @Override
    public List<QueryHistoryCaseInfoResult> getOldestCaseHistoryByPatNo(Integer patNo, Date startDate, Date endDate, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
        }
        return caseHistoryMapper.getOldestCaseHistoryByPatNo(patNo, startDate, endDate);
    }


}
