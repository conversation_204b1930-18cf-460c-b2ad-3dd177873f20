package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.EnterHospitalRequisition;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbMzCfMx;
import com.rjsoft.outPatient.infrastructure.repository.entity.OldRecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/4 - 9:24
 */
public interface OldRecipeDetailRepository {

    /**
     * 根据挂号流水号查询药品处方(历史处方接口使用)
     *
     * @param regNo      挂号流水号
     * @param dataSource 数据源
     * @param firstDate
     * @return {@link List<RecipeDetail>}
     */
    List<OldRecipeDetail> getDrugRecipeByRegNo(String regNo, String dataSource, Date firstDate);

    /**
     * 根据就诊流水号查询处方明细信息
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    MzysTbMzCfMx getTopMzCfMx(Long receptionNo, Integer hospitalCode);

    /**
     * 查询有效期内的处方明细
     *
     * @param jzlsh
     * @param months
     * @param hospitalCode
     * @return
     */
    MzysTbMzCfMx getTimeMzCfMx(Long jzlsh, Integer months, Integer hospitalCode);

    /**
     * 查询门诊处方明细信息
     *
     * @param cfmxlsh
     * @param hospitalCode
     * @return
     */
    MzysTbMzCfMx getRecipeByRecipeDetailId(Long cfmxlsh, Integer hospitalCode);

    /**
     * 根据就诊流水号，套餐名称，医院id查询患者是否存在入院前套餐
     *
     * @param jzlsh
     * @param tcmc
     * @param hospitalCode
     * @return
     */
    int listMzysTbMzcfmx(String jzlsh, List<String> tcmc, Integer hospitalCode);

    /**
     * 修改套餐开立状态
     *
     * @param requisition
     */
    void changeIssueInspectStatus(EnterHospitalRequisition requisition);
}
