package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * 申请单明细对照
 */
@Data
@Table(name = "MZYS_TB_JCSQDZB")
public class ApplicationControl  implements Serializable {

    @Id
    @Column(name = "lsh", insertable = false, updatable = false)
    @GeneratedValue(generator ="JDBC")
    private Integer id;

    @Column(name = "sqdbm")
    private String templateNo;

    @Column(name = "bwfz")
    private String fromGroup;

    @Column(name = "dzlx")
    private Integer selectedType;

    @Column(name = "xzbw")
    private String selectedPlace;

    @Column(name = "xzbwsl")
    private Integer placeNum;

    @Column(name = "jzsl")
    private Integer mediumNum;

    @Column(name = "zqjzsl")
    private Integer mediumAllNum;

    @Column(name = "tjlx")
    private Integer selCondition;

    @Column(name = "nlsx")
    private Integer maxAge;

    @Column(name = "nlxx")
    private Integer minAge;

    @Column(name = "xb")
    private Integer sexCondition;

    @Column(name = "sfjg")
    private Integer tollInterval;

    @Column(name = "gzzh")
    private Integer ruleGroupNo;

    @Column(name = "gzzf")
    private Integer IsruleGroup;

    @Column(name = "yybm")
    private Integer hospitalCode;

    @Transient
    @Column(insertable = false, updatable = false)
    private List<ApplicationControlItems> ItemList;

    @Transient
    @Column(insertable = false, updatable = false)
    private Integer doctorId;

    @Transient
    @Column(insertable = false, updatable = false)
    private String selectedTypeName;

    @Transient
    @Column(insertable = false, updatable = false)
    private String selConditionName;

    @Transient
    @Column(insertable = false, updatable = false)
    private String sexConditionName;

}


