package com.rjsoft.outPatient.infrastructure.repository.mapper;

import cn.hutool.core.collection.CollUtil;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbMedicalDiag;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Collections;
import java.util.List;

public interface MzysTbMedicalDiagMapper extends Mapper<MzysTbMedicalDiag> {
    default List<MzysTbMedicalDiag> getMedicalDiagByRegNo(Long regNo, Integer hospitalCode) {
        Weekend<MzysTbMedicalDiag> weekend = Weekend.of(MzysTbMedicalDiag.class);
        WeekendCriteria<MzysTbMedicalDiag, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andEqualTo(MzysTbMedicalDiag::getRegNo, regNo).andEqualTo(MzysTbMedicalDiag::getHospitalId, hospitalCode);
        return this.selectByExample(weekend);
    }

    default List<MzysTbMedicalDiag> queryMedicalDiagByRegNoList(List<Long> regNoList, Integer hospitalCode) {
        if (CollUtil.isEmpty(regNoList)) {
            return Collections.emptyList();
        }
        Weekend<MzysTbMedicalDiag> weekend = Weekend.of(MzysTbMedicalDiag.class);
        WeekendCriteria<MzysTbMedicalDiag, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andIn(MzysTbMedicalDiag::getRegNo, regNoList).andEqualTo(MzysTbMedicalDiag::getHospitalId, hospitalCode);
        return this.selectByExample(weekend);
    }

    default boolean deleteByRegNo(Long regNo, Integer hospitalCode) {
        Weekend<MzysTbMedicalDiag> weekend = Weekend.of(MzysTbMedicalDiag.class);
        WeekendCriteria<MzysTbMedicalDiag, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andEqualTo(MzysTbMedicalDiag::getRegNo, regNo).andEqualTo(MzysTbMedicalDiag::getHospitalId, hospitalCode);
        return this.deleteByExample(weekend) > 0;
    }

    default boolean deleteByRegNoList(List<Long> regNoList, Integer hospitalCode) {
        Weekend<MzysTbMedicalDiag> weekend = Weekend.of(MzysTbMedicalDiag.class);
        WeekendCriteria<MzysTbMedicalDiag, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andIn(MzysTbMedicalDiag::getRegNo, regNoList).andEqualTo(MzysTbMedicalDiag::getHospitalId, hospitalCode);
        return this.deleteByExample(weekend) > 0;
    }

}