package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.report.dto.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @create 2021/9/11 11:08
 * @description
 **/
public interface ReportMapper {

    /**
     * 危急值
     *
     * @param hisCardNo
     * @return
     */
    List<CrisisValueDto> getCrisisValue(@Param("hisCardNo") String hisCardNo);

    /**
     * 放射报告
     *
     * @param hisCardNo
     * @return
     */
    RadiationReportDto getRadiationReport(@Param("hisCardNo") String hisCardNo);


    /**
     * 化验报告明细
     *
     * @param newSqdh
     * @param mid
     * @param endTime
     * @return
     */
    List<TestReportDetailDto> getTestReportDetail(@Param("newSqdh") String newSqdh,
                                                  @Param("mid") Integer mid,
                                                  @Param("endTime") Date endTime);


    /**
     * 总院-化验报告列表
     *
     * @param patName
     * @param patSfz
     * @return
     */
    List<TestReportDto> getHyMaterListGeneral(@Param("patName") String patName,
                                              @Param("patSfz") String patSfz,
                                              @Param("startTime") Date startTime,
                                              @Param("endTime") Date endTime);

    /**
     * 分院-化验报告列表
     *
     * @param patName
     * @param patSfz
     * @return
     */
    List<TestReportDto> getHyMaterList(@Param("patName") String patName,
                                       @Param("patSfz") String patSfz,
                                       @Param("startTime") Date startTime,
                                       @Param("endTime") Date endTime);

    /**
     * 模糊搜索化验项目列表
     *
     * @param code
     */
    List<TestTypeResponse> getTestType(@Param("code") String code,
                                       @Param("hospId") Integer hospId);


    /**
     * 根据化验列表匹配单价和单位
     */
    List<TestItemResponse> getTestItemList(@Param("list") List<TestTypeResponse> testTypeResponses,
                                           @Param("hospId") Integer hospId);

    /**
     * 检查报告
     *
     * @param hisCardNo
     * @return
     */
    List<CheckReportDto> getCheckReportList(@Param("hisCardNo") String hisCardNo,
                                      @Param("startTime") Date startTime);

}
