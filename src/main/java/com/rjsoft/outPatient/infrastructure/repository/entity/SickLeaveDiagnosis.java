package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 病假单诊断
 *
 * <AUTHOR>
 * @since 2021/7/30 - 13:31
 */
@Data
@Table(name = "MZYS_TB_BJD_ZD")
public class SickLeaveDiagnosis implements Serializable {

    /**
     * id
     */
    @Id
    @Column(name = "id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 病假单表主键id
     */
    @Column(name = "MainId")
    private Integer mainId;

    /**
     * 诊断编码
     */
    @Column(name = "zdbm")
    private String diagnoseCode;

    /**
     * 诊断名称
     */
    @Column(name = "zdmc")
    private String diagnoseName;

    /**
     * 创建r
     */
    @Column(name = "CreateBy")
    private Integer createBy;

    /**
     * 创建人姓名
     */
    @Column(name = "CreateName")
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "CreateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 医院编码
     */
    @Column(name = "hospitalCode")
    private Integer hospitalCode;

}
