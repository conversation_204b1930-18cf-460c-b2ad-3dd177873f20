package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.InjectionFeeAssociative;
import com.rjsoft.outPatient.infrastructure.repository.entity.SpecialDrug;
import com.rjsoft.outPatient.infrastructure.repository.entity.SpecialDrugDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.SpecialDrugItem;
import com.rjsoft.outPatient.infrastructure.repository.mapper.InjectionFeeAssociativeMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SpecialDrugDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SpecialDrugItemMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SpecialDrugMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.SpecialDrugRepository;
import lombok.AllArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021/11/24-2:57 下午
 */
@Service
@AllArgsConstructor
public class SpecialDrugRepositoryImpl implements SpecialDrugRepository {

    private final SpecialDrugMapper specialDrugMapper;
    private final SpecialDrugDetailMapper specialDrugDetailMapper;
    private final InjectionFeeAssociativeMapper injectionFeeAssociativeMapper;
    private final SpecialDrugItemMapper specialDrugItemMapper;

    /**
     * 根据参数查询 categoryId
     * 如果未检索到数据 返回 0
     *
     * @param itemCode      收费项目编码
     * @param drugUsageCode 给药途径编码
     * @param hospitalCode  医院编码
     * @return categoryId
     */
    @Override
    @DatabaseAnnotation
    @Cacheable(cacheNames = "SpecialDrugConfig", unless = "#drugUsageCode == null")
    public Integer getCategoryIdByItemCodeAndDrugUsageCode(Integer itemCode, Integer drugUsageCode, Integer hospitalCode) {
        final Weekend<SpecialDrug> weekend = new Weekend<>(SpecialDrug.class);
        weekend.weekendCriteria().andEqualTo(SpecialDrug::getItemCode, itemCode)
                .andEqualTo(SpecialDrug::getDrugUsageCode, drugUsageCode)
                .andEqualTo(SpecialDrug::getHospitalCode, hospitalCode);
        final List<SpecialDrug> record = specialDrugMapper.selectByExample(weekend);

        if (record == null || record.isEmpty()) {
            return 0;
        }

        return Optional.ofNullable(record.get(0).getCategoryId()).orElse(0);
    }

    /**
     * 根据特殊药品分类 id 查询 特殊收费项目编码
     * 如果未检索到数据 返回 0
     *
     * @param categoryId   特殊药品分类 id
     * @param hospitalCode 医院编码
     * @return 收费项目编码
     */
    @Override
    @DatabaseAnnotation
    @Cacheable(cacheNames = "SpecialDrugDetailConfig", unless = "#result == null")
    public Integer getProjectNumByCategoryId(Integer categoryId, Integer hospitalCode) {
        final Weekend<SpecialDrugDetail> weekend = new Weekend<>(SpecialDrugDetail.class);
        weekend.weekendCriteria().andEqualTo(SpecialDrugDetail::getCategory, categoryId)
                .andEqualTo(SpecialDrugDetail::getHospitalCode, hospitalCode);
        final List<SpecialDrugDetail> record = specialDrugDetailMapper.selectByExample(weekend);

        if (record == null || record.isEmpty()) {
            return 0;
        }

        return Optional.ofNullable(record.get(0).getProjectNum()).orElse(0);
    }

    /**
     * 是否存在注射费用对照
     *
     * @param receptionNo       就诊流水号
     * @param recipeDetailNo    处方明细流水号
     * @param injectionItemCode 注射项目编码
     * @return boolean
     */
    @Override
    @DatabaseAnnotation
    public boolean existInjectionFeeAssociative(Long receptionNo, Long recipeDetailNo, Integer injectionItemCode) {
        final Weekend<InjectionFeeAssociative> weekend = new Weekend<>(InjectionFeeAssociative.class);
        weekend.setCountProperty("id");
        weekend.weekendCriteria().andEqualTo(InjectionFeeAssociative::getReceptionNo, receptionNo)
                .andEqualTo(InjectionFeeAssociative::getRecipeDetailNo, recipeDetailNo)
                .andEqualTo(InjectionFeeAssociative::getInjectionItemCode, injectionItemCode);
        final int count = injectionFeeAssociativeMapper.selectCountByExample(weekend);
        return count > 0;
    }

    @Override
    public boolean existInjectionFeeAssociativeItems(Long receptionNo, Long recipeDetailNo, Set<Integer> xmbms) {
        final Weekend<InjectionFeeAssociative> weekend = new Weekend<>(InjectionFeeAssociative.class);
        weekend.setCountProperty("id");
        weekend.weekendCriteria().andEqualTo(InjectionFeeAssociative::getReceptionNo, receptionNo)
                .andEqualTo(InjectionFeeAssociative::getRecipeDetailNo, recipeDetailNo)
                .andIn(InjectionFeeAssociative::getInjectionItemCode,xmbms);
        final int count = injectionFeeAssociativeMapper.selectCountByExample(weekend);
        return count > 0;
    }

    /**
     * 查询注射费用对照
     *
     * @param receptionNo       就诊流水号
     * @param recipeDetailNo    处方明细流水号
     * @param injectionItemCode 注射项目编码
     * @return boolean
     */
    @Override
    @DatabaseAnnotation
    public InjectionFeeAssociative getInjectionFeeAssociative(Long receptionNo, Long recipeDetailNo, Integer injectionItemCode) {
        if(receptionNo==null||recipeDetailNo==null||injectionItemCode==null){
            return null;
        }
        final Weekend<InjectionFeeAssociative> weekend = new Weekend<>(InjectionFeeAssociative.class);
        weekend.weekendCriteria().andEqualTo(InjectionFeeAssociative::getReceptionNo, receptionNo)
                .andEqualTo(InjectionFeeAssociative::getRecipeDetailNo, recipeDetailNo)
                .andEqualTo(InjectionFeeAssociative::getInjectionItemCode, injectionItemCode);

        final List<InjectionFeeAssociative> records = injectionFeeAssociativeMapper.selectByExample(weekend);
        if (records == null || records.isEmpty()) {
            return null;
        }
        return records.get(0);
    }

    /**
     * 保存注射费用对照
     *
     * @param record {@link InjectionFeeAssociative}
     */
    @Override
    @DatabaseAnnotation
    public void saveInjectionFeeAssociative(InjectionFeeAssociative record) {
        injectionFeeAssociativeMapper.insert(record);
    }

    /**
     * 保存注射费用对照
     *
     * @param id 主键
     */
    @Override
    @DatabaseAnnotation
    public void deleteInjectionFeeAssociative(Integer id) {
        final InjectionFeeAssociative record = new InjectionFeeAssociative();
        record.setId(id);
        injectionFeeAssociativeMapper.deleteByPrimaryKey(record);
    }

    /**
     * 根据给药途径 id 查询 特殊收费项目编码
     * 如果未检索到数据 返回 null
     *
     * @param usageId   给药途径 id
     * @param hospitalCode 医院编码
     * @return 收费项目
     */
    @Override
    @DatabaseAnnotation
    public List<SpecialDrugItem> getProjectItemsByUsageId(Integer usageId, Integer hospitalCode) {
        DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
        final Weekend<SpecialDrugItem> weekend = new Weekend<>(SpecialDrugItem.class);
        weekend.weekendCriteria().andEqualTo(SpecialDrugItem::getUsage, usageId)
                .andEqualTo(SpecialDrugItem::getHospitalCode, hospitalCode)
                .andEqualTo(SpecialDrugItem::getType, 1)
                .andEqualTo(SpecialDrugItem::getIsDel,0)
                .andEqualTo(SpecialDrugItem::getEnableFlag,1)
                .andIn(SpecialDrugItem::getUseRange, Arrays.asList(0, 1));
        final List<SpecialDrugItem> record = specialDrugItemMapper.selectByExample(weekend);
        return record;
    }

    @Override
    public List<SpecialDrugItem> getAllProjectItemsByType(Integer hospitalCode, Integer type) {
        DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
        final Weekend<SpecialDrugItem> weekend = new Weekend<>(SpecialDrugItem.class);
        weekend.weekendCriteria().andEqualTo(SpecialDrugItem::getHospitalCode, hospitalCode)
                .andEqualTo(SpecialDrugItem::getType, type)
                .andIn(SpecialDrugItem::getUseRange, Arrays.asList(0, 1));
        final List<SpecialDrugItem> record = specialDrugItemMapper.selectByExample(weekend);
        return record;
    }

    @Override
    public List<InjectionFeeAssociative> getInjectionFeeAssociativeList(Long receptionNo, Long recipeDetailNo) {
        if(receptionNo==null||recipeDetailNo==null){
            return null;
        }
        final Weekend<InjectionFeeAssociative> weekend = new Weekend<>(InjectionFeeAssociative.class);
        weekend.weekendCriteria().andEqualTo(InjectionFeeAssociative::getReceptionNo, receptionNo)
                .andEqualTo(InjectionFeeAssociative::getRecipeDetailNo, recipeDetailNo);

        final List<InjectionFeeAssociative> records = injectionFeeAssociativeMapper.selectByExample(weekend);
        return records;
    }
}
