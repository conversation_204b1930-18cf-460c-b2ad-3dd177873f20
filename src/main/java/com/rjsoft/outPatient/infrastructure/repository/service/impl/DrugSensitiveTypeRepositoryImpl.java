package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugSensitiveType;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugSensitiveTypeMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DrugSensitiveTypeRepository;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021/7/27 - 10:25
 */
@Service
@AllArgsConstructor
public class DrugSensitiveTypeRepositoryImpl implements DrugSensitiveTypeRepository {

    private final DrugSensitiveTypeMapper drugSensitiveTypeMapper;

    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    public String getDrugSensitiveTypeNameByCode(@NonNull Integer code) {
        DrugSensitiveType entity = new DrugSensitiveType(code);
        return drugSensitiveTypeMapper.selectOne(entity).getName();
    }

    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    public DrugSensitiveType getDrugSensitiveType(@NonNull Integer code) {
        DrugSensitiveType entity = new DrugSensitiveType(code);
        return drugSensitiveTypeMapper.selectOne(entity);
    }
}
