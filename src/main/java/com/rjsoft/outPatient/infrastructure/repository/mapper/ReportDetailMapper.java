package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationContentItems;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList;
import com.rjsoft.outPatient.infrastructure.repository.entity.ReportDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Example;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.Date;
import java.util.List;

public interface ReportDetailMapper extends BaseMapper<ReportDetail>, Example<ReportDetail> {


    /**
     * 通用表单保存明细信息
     *
     * @param formId
     * @param hospitalCode
     * @return
     */
    List<ReportDetail> getReportDetail(Integer formId, Integer hospitalCode);


}
