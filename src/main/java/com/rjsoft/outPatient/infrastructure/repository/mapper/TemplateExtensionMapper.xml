<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.TemplateExtensionMapper">

    <select id="getDrugDangerLev" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.TemplateExtension">
        select a.ClassId, b.DictionaryCode Code, b.DictionaryName Name
        from hisdb..System_Tb_TemplateExtension a
                 inner join hisdb..TB_Dic_Dictionary b on a.KeyValue = b.DictionaryCode
        where a.KeyCode = 'DangerLev'
          and b.DictionaryTypeID = '9001'
          and b.IsUse = 1
          and a.HospitalId = #{hospitalCode}

    </select>

    <select id="getDrugOriginal" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.TemplateExtension">
        select classId
        from System_Tb_TemplateExtension
        where keyCode='YYY' and KeyValue='True'
        and hospitalId=#{hospId}
    </select>

    <select id="getDrugPropertyName" resultType="java.lang.String">
        select RTRIM(HisDictionaryName) from TB_Dic_HisDictionary
        where DictionaryTypeId=79 and HospitalId=#{hospId} and HisDictionaryCode=#{dicCode}
    </select>


</mapper>