package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.DictionaryCondition;
import com.rjsoft.outPatient.infrastructure.repository.entity.SickLeaveDiagnosis;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DictionaryConditionMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SickLeaveDiagnosisMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.SickLeaveDiagnosisRepository;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/30 - 13:52
 */
@Service
@AllArgsConstructor
public class SickLeaveDiagnosisRepositoryImpl implements SickLeaveDiagnosisRepository {

    private final SickLeaveDiagnosisMapper sickLeaveDiagnosisMapper;
    private final DictionaryConditionMapper dictionaryConditionMapper;

    @Override
    @DatabaseAnnotation
    public boolean existsWithPrimaryKey(Integer mainId, String diagnoseCode) {
        if (ObjectUtils.isEmpty(mainId) && StringUtils.isBlank(diagnoseCode)) {
            return false;
        }
        final Weekend<SickLeaveDiagnosis> weekend = new Weekend<>(SickLeaveDiagnosis.class);
        weekend.setCountProperty("mainId");
        weekend.weekendCriteria().andEqualTo(SickLeaveDiagnosis::getMainId, mainId).andEqualTo(SickLeaveDiagnosis::getDiagnoseCode, diagnoseCode);
        return sickLeaveDiagnosisMapper.selectCountByExample(weekend) > 0;
    }

    @Override
    @DatabaseAnnotation
    public void deleteSickLeaveDiagnosisByMainId(Integer mainId) {
        if (ObjectUtils.isEmpty(mainId)) {
            return;
        }
        final SickLeaveDiagnosis entity = new SickLeaveDiagnosis();
        entity.setMainId(mainId);
        sickLeaveDiagnosisMapper.delete(entity);
    }

    @Override
    @DatabaseAnnotation
    public void saveSickLeaveDiagnosis(SickLeaveDiagnosis entity) {
        sickLeaveDiagnosisMapper.insertSelective(entity);
    }

    @Override
//    @Cacheable(
//            cacheNames = {"SysDiagnose::DictionaryCondition"},
//            key = "#hospitalCode",
//            unless = "#result == null"
//    )
    @DatabaseAnnotation
    public List<DictionaryCondition> getDictionaryCondition(Integer hospitalCode) {
        return dictionaryConditionMapper.getDictionaryCondition(hospitalCode);
    }

}
