package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbXTPZ;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021/11/6-11:11 上午
 */
public interface SysFunctionRepository {

    /**
     * 获取系统时间
     *
     * @return
     */
    Date getDate();


    /**
     * 执行过程获取一个新ID
     * (不要放在事务中)
     *
     * @param seq
     * @return
     */
    Long getGetSequences(SequenceEnum seq);

    /**
     * 获取周次
     * 内部星期序号，周一0，周二1，周三2，周四3，周五4，周六5，周日6
     *
     * @return
     */
    Integer getWeekday();

    /**
     * @param gjz
     * @return
     */
    MzysTbXTPZ getXtpz(String gjz);




}
