<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DiagnoseMapper">

    <select id="getPatDianosis" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.PatDianosisDTO">
        SELECT
        jzlsh as receptionNo,
        zdbm as diagnosticCode,
        zdmc as diagnosticName
        FROM MZYS_TB_MZYSZD (nolock)
        WHERE jzlsh in
        <foreach item="receptionNo" collection="receptionNoSet" separator="," open="(" close=")" index="">
            #{receptionNo}
        </foreach>
        and yybm = #{hospitalCode}
    </select>
    <select id="getDiagnoseByReceptionNo"
            resultType="com.rjsoft.outPatient.domain.prescriptionAudit.vo.AuditDiagnose">
        select '2'  as type,
               zdmc as name,
               zdbm as code
        from MZYS_TB_MZYSZD (nolock)
        where jzlsh = #{receptionNo}
          and yybm = #{hospitalCode}
        order by cjrq desc
    </select>
    <select id="getDiagnose" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.Diagnose">
        select  a.Id id,
        a.regno regNo,
        a.jzlsh receptionNo,
        a.zdlx diagnoseType,
        a.zdbm diagnoseCode,
        a.zdmc diagnoseName,
        a.zdzt diagnoseState,
        a.zdbj diagnoseMark,
        isnull(a.fhbz,0) isReview,
        a.fhys reviewDoctor,
        a.xh orderNo,
        a.cjr creDoctor,
        a.cjrq creTime,
        a.xgr uptDoctor,
        a.xgrq uptTime,
        a.yybm hospitalCode
        from MZYS_TB_MZYSZD a (nolock)
        inner join MZYS_TB_KZJL b (nolock) on a.jzlsh = b.jzlsh and a.yybm=b.hospitalCode
        where  a.regno in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by a.fhbz desc, a.cjrq desc
    </select>
    <select id="getNewDiagnose" resultType="com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo">
        select jzlsh reception from MZYS_TB_MZYSZD where cast(jzlsh as varchar(50)) = #{receptionNo} and zdbm = #{diagnoseCode}
    </select>
    <select id="queryDiagListByRegNos" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.Diagnose">
        select  a.Id id,
        a.regno regNo,
        a.jzlsh receptionNo,
        a.zdlx diagnoseType,
        rtrim(a.zdbm) diagnoseCode,
        rtrim(a.zdmc) diagnoseName,
        a.zdzt diagnoseState,
        a.zdbj diagnoseMark,
        isnull(a.fhbz,0) isReview,
        a.fhys reviewDoctor,
        a.xh orderNo,
        a.cjr creDoctor,
        a.cjrq creTime,
        a.xgr uptDoctor,
        a.xgrq uptTime,
        a.yybm hospitalCode
        from MZYS_TB_MZYSZD a (nolock)
        inner join (
            select DiagnosisCode, DiagnosisName,IsStop from System_Tb_Diagnosis sub
            union all
            select DiagnosisCode, DiagnosisName,IsStop from System_Tb_ChineseDiagnose sub2
        ) b on rtrim(a.zdbm) = rtrim(b.DiagnosisCode) and b.IsStop=0
        where  a.regno in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by a.fhbz desc, a.cjrq desc
    </select>
    <select id="queryIoDiagListByPatId" resultType="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.InPatientResult">
        select
            a.regNo,
            a.outTime,
            a.wardId,
            LTrim(RTrim(a.OutDiagnosisCode)) diagnoseCode,
            LTrim(RTrim(a.OutDiagnosisName)) diagnoseName,
            LTrim(RTrim(c.DeptName)) wardName,
            a.hospitalId hospitalCode,
            1 isReview,
            a.Professor reviewDoctor
        from
        Io_Tb_InPatient a (nolock)
        inner join (
            select DiagnosisCode, DiagnosisName,IsStop from System_Tb_Diagnosis sub
            union all
            select DiagnosisCode, DiagnosisName,IsStop from System_Tb_ChineseDiagnose sub2
        ) b on LTrim(RTrim(a.OutDiagnosisCode)) = rtrim(b.DiagnosisCode) and b.IsStop=0
        LEFT JOIN System_Tb_Department c ON a.wardId = c.DeptId AND a.hospitalId = c.hospitalId
        where a.hospitalId =#{hospitalCode} and a.status =9 and a.OutDiagnosisCode is not null
        and a.patId in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>