package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.DiagnosisProof;
import com.rjsoft.outPatient.infrastructure.repository.entity.DiagnosisProofDisease;
import com.rjsoft.outPatient.infrastructure.repository.entity.DiagnosisProofOld;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/11-5:43 下午
 */
public interface DiagnosisProofRepository {

    /**
     * 根据 卡号、门诊号、姓名、状态查询疾病证明
     *
     * @param cardNo    卡号
     * @param blCardNum 门诊号
     * @param name      姓名
     * @param status    状态 使用的枚举{@link com.rjsoft.outPatient.common.enums.RegistrationFormStatusEnum}
     * @return {@link DiagnosisProofDisease} for list
     */
    List<DiagnosisProof> findByCardNoAndBlCardNumAndNameAndStatus(String cardNo, String blCardNum, String name, Integer status);

    /**
     * 根据 卡号、门诊号、姓名、状态查询老系统疾病证明
     *
     * @param cardNo    卡号
     * @param blCardNum 门诊号
     * @param name      姓名
     * @param status    状态 使用的枚举{@link com.rjsoft.outPatient.common.enums.RegistrationFormStatusEnum}
     * @return {@link DiagnosisProofDisease} for list
     */
    List<DiagnosisProofOld> findOldByCardNoAndBlCardNumAndNameAndStatus(String cardNo, String blCardNum, String name, Integer status);

    /**
     * 查询疾病证明，通过主键、挂号流水号、医院编码
     * 参数 id 可以为 NULL
     *
     * @param id           主键
     * @param regNo        挂号流水号
     * @param hospitalCode 医院编码
     * @return {@link DiagnosisProof} for List
     */
    List<DiagnosisProof> getDiseaseProofByIdAndRegNo(Integer id, Integer regNo, Integer hospitalCode);

    /**
     * 查询疾病证明，通过主键
     *
     * @param id 主键
     * @return {@link DiagnosisProof}
     */
    DiagnosisProof getDiseaseProofById(Integer id);

    /**
     * 保存疾病证明以及所属的诊断
     *
     * @param addEntity {@link DiagnosisProof}
     */
    void save(DiagnosisProof addEntity);

    /**
     * 修改疾病证明以及所属的诊断
     *
     * @param updateEntity {@link DiagnosisProof}
     */
    void update(DiagnosisProof updateEntity);

    /**
     * 根据 疾病证明 id 及诊断编码检索数据是否存在
     *
     * @param diseaseProofId 疾病证明 id
     * @param diagnosticCode 诊断编码
     * @return 存在 true
     */
    Boolean existsWithDiseaseProofIdAndDiagnosticCode(Integer diseaseProofId, String diagnosticCode);

    /**
     * 新增
     *
     * @param diagnosisProofDisease 实体
     * @return 影响行数
     */
    Integer saveDiseaseProofDiagnosis(DiagnosisProofDisease diagnosisProofDisease);

    /**
     * 删除疾病证明诊断信息
     *
     * @param mainId 疾病证明主键
     * @return 影响行数
     */
    Integer deleteDiseaseProofDiagnosisByMainId(Integer mainId);

}
