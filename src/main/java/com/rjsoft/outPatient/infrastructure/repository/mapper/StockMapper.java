package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.cache.pojo.Stock;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface StockMapper extends Mapper<Stock> {

    /**
     * 查询药库包装库存
     *
     * @param itemCodes     药品编码
     * @param hospitalIds 医院编码
     * @param deptIds     药房编码
     * @return 药品库存
     */
    List<Stock> listStock(@Param("itemCodes") String itemCodes, @Param("hospitalIds") String hospitalIds, @Param("deptIds") String deptIds);

}
