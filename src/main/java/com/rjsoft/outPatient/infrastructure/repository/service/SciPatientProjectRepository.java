package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.SciPatientProject;

import java.util.List;

public interface SciPatientProjectRepository {

    List<SciPatientProject> getSciPatientProject(Integer patId,Integer creator,Integer hospitalCode,String projectId);

    List<SciPatientProject> querySciPatientProjectByPatIdAndProjectId(Integer patId, String projectId, Integer hospitalCode);

}
