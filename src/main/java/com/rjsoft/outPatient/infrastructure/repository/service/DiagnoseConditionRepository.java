package com.rjsoft.outPatient.infrastructure.repository.service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/2 - 15:10
 */
public interface DiagnoseConditionRepository {

    /**
     * 通过诊断编码判断是否重症
     *
     * @param diagnoseCode 诊断编码
     * @param hospitalCode 医院编码
     * @return true 重症
     */
    boolean isSevereByDiagnoses(List<String> diagnoseCode, Integer hospitalCode);

    /**
     * 通过诊断编码判断是否轻症
     *
     * @param diagnoseCode 诊断编码
     * @param hospitalCode 医院编码
     * @return true 轻症
     */
    boolean notSevereByDiagnoses(List<String> diagnoseCode, Integer hospitalCode);

}
