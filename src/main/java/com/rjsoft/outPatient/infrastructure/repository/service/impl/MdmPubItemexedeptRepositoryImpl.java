package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.ExecDeptTypeEnum;
import com.rjsoft.outPatient.domain.execDept.dto.ItemExecDeptAddDto;
import com.rjsoft.outPatient.domain.execDept.dto.ItemExecDeptSelectDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubItemexedept;
import com.rjsoft.outPatient.infrastructure.repository.mapper.MdmPubItemexedeptMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.MdmPubItemexedeptRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service
@AllArgsConstructor
public class MdmPubItemexedeptRepositoryImpl implements MdmPubItemexedeptRepository {
    private MdmPubItemexedeptMapper mdmPubItemexedeptMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public PageInfo<MdmPubItemexedept> getItemExecDeptItemList(ItemExecDeptSelectDto itemExecDeptDto) {
        Weekend<MdmPubItemexedept> weekend = new Weekend<>(MdmPubItemexedept.class);
        WeekendCriteria<MdmPubItemexedept, Object> weekendCriteria = weekend.weekendCriteria();
        if(itemExecDeptDto.getItemCode()!=null){
            weekendCriteria.andEqualTo(MdmPubItemexedept::getItemCode,itemExecDeptDto.getItemCode());
        }
        if(itemExecDeptDto.getDeptId()!=null){
            weekendCriteria.andEqualTo(MdmPubItemexedept::getUseDept,itemExecDeptDto.getDeptId());
        }
        if(!StringUtils.isEmpty(itemExecDeptDto.getInputItemStr())){
            weekendCriteria.andLike(MdmPubItemexedept::getItemName,itemExecDeptDto.getInputItemStr());
        }
        if(!StringUtils.isEmpty(itemExecDeptDto.getInputDeptStr())){
            weekendCriteria.andLike(MdmPubItemexedept::getUseDeptName,itemExecDeptDto.getInputDeptStr());
        }
        PageHelper.startPage(itemExecDeptDto.getPageNum(), itemExecDeptDto.getPageSize());
        List<MdmPubItemexedept> mdmPubItemexedeptList = mdmPubItemexedeptMapper.selectByExample(weekend);
        PageInfo<MdmPubItemexedept> pageInfo = new PageInfo<>(mdmPubItemexedeptList);
        return pageInfo;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public int addItem(ItemExecDeptAddDto itemExecDeptAddDto) {
        MdmPubItemexedept mdmPubItemexedept = new MdmPubItemexedept();
        BeanUtils.copyProperties(itemExecDeptAddDto,mdmPubItemexedept);
        Date date = new Date();
        Integer userId = itemExecDeptAddDto.getUserId();
        mdmPubItemexedept.setType(ExecDeptTypeEnum.OUTPATIENT_TYPE.getType());
        mdmPubItemexedept.setCreateTime(date);
        mdmPubItemexedept.setUpdateTime(date);
        mdmPubItemexedept.setCreateUserId(userId);
        mdmPubItemexedept.setUpdateUserId(userId);
        return mdmPubItemexedeptMapper.insert(mdmPubItemexedept);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<HashMap<String,String>> getItemExecDeptItem(ItemExecDeptAddDto itemExecDeptAddDto,Integer id) {
        return mdmPubItemexedeptMapper.getItemExecDeptItem(itemExecDeptAddDto.getItemCode(),itemExecDeptAddDto.getUseDept(),itemExecDeptAddDto.getStartTime(),itemExecDeptAddDto.getEndTime(),id);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public int updateItem(ItemExecDeptAddDto itemExecDeptAddDto) {
        MdmPubItemexedept mdmPubItemexedept = new MdmPubItemexedept();
        BeanUtils.copyProperties(itemExecDeptAddDto,mdmPubItemexedept);
        Date date = new Date();
        Integer userId = itemExecDeptAddDto.getUserId();
        mdmPubItemexedept.setUpdateTime(date);
        mdmPubItemexedept.setUpdateUserId(userId);
        return mdmPubItemexedeptMapper.updateByPrimaryKeySelective(mdmPubItemexedept);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public int deleteItem(Integer id) {
        return mdmPubItemexedeptMapper.deleteByPrimaryKey(id);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<MdmPubItemexedept> getItemExecDeptByTime(String itemCode, String curTime, Integer curDeptId, Integer curHospitalId) {
        return mdmPubItemexedeptMapper.getItemExecDeptByTime(itemCode, curTime, curDeptId, curHospitalId);
    }
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<MdmPubItemexedept> getItemExecDeptByTimeItemCodeList(List<Integer> itemCodeList, String curTime, Integer curDeptId, Integer curHospitalId) {
        List<MdmPubItemexedept> mdmPubItemexedeptList = new ArrayList<>();
        if (itemCodeList.isEmpty()||itemCodeList.size() == 0){
            return mdmPubItemexedeptList;
        }
        return mdmPubItemexedeptMapper.getItemExecDeptByTimeItemCodeList(itemCodeList, curTime, curDeptId, curHospitalId);
    }

    @Override
    public List<MdmPubItemexedept> getExecDeptList(String curTime, Integer curDeptId, Integer curHospitalId) {
        return mdmPubItemexedeptMapper.getExecDeptList(curTime, curDeptId, curHospitalId);
    }
}
