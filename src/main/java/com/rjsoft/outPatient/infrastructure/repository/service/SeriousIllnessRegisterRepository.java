package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.seriousIllness.vo.SeriousIllnessRegisterVo;
import com.rjsoft.outPatient.infrastructure.repository.entity.Hospital;
import com.rjsoft.outPatient.infrastructure.repository.entity.OldSeriousIllnessRegister;
import com.rjsoft.outPatient.infrastructure.repository.entity.SeriousIllnessRegister;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * 大病登记
 *
 * <AUTHOR>
public interface SeriousIllnessRegisterRepository {

    /**
     * 根据ID查询大病登记
     *
     * @param id           id
     * @param hospitalCode 医院编码
     * @return {@link SeriousIllnessRegister}
     */
    SeriousIllnessRegister getSeriousIllnessRegisterById(Integer id, Integer hospitalCode);

    /**
     * 根据ID查询老大病登记
     *
     * @param id           id
     * @return {@link SeriousIllnessRegister}
     */
    OldSeriousIllnessRegister getOldSeriousIllnessRegisterById(Integer id,Integer hospitalCode);

    /**
     * 根据卡号，状态，医院编码，查询登记记录
     *
     * @param cardNo       卡号
     * @param patName      患者姓名
     * @param status       状态
     * @param hospitalCode 医院编码
     * @param hisCardNo    病历卡号
     * @return {@link List<SeriousIllnessRegister>}
     */
    List<SeriousIllnessRegister> getSeriousIllnessRegister(Date startTime, Date endTime, String cardNo, String patName, Integer status, Integer hospitalCode, String hisCardNo);

    /**
     * 保存大病登记
     *
     * @param entity 参数实体
     * @return success or failure
     */
    boolean saveSeriousIllnessRegister(SeriousIllnessRegister entity);

    boolean updateSeriousIllness(SeriousIllnessRegister entity);

    /**
     * 修改大病登记
     *
     * @param entity 参数实体
     * @return success or failure
     */
    boolean updateSeriousIllnessRegisterCancel(SeriousIllnessRegister entity);

    boolean updateOldSeriousIllness(OldSeriousIllnessRegister entity);
    /**
     * 修改老大病登记
     *
     * @param entity 参数实体
     * @return success or failure
     */
    boolean updateOldSeriousIllnessRegisterCancel(OldSeriousIllnessRegister entity);

    /**
     * 修改老数据大病登记
     *
     * @param entity 参数实体
     * @return success or failure
     */
    boolean updateOldRegister(OldSeriousIllnessRegister entity);

    /**
     * 根据id删除大病登记
     *
     * @param id           唯一标识符
     * @param hospitalCode
     * @param opName
     * @param operator
     * @return
     */
    boolean delSeriousIllnessRegisterById(Integer id, Integer hospitalCode, String opName, Integer operator);

    /**
     *  根据医院编号获取hospital
     * <AUTHOR>
     * @param hospitalCode
     * @return com.rjsoft.outPatient.infrastructure.repository.entity.Hospital
     **/
    Hospital queryHospitalByHospitalCode(String hospitalCode);

    /**
     *  取消大病申请
     * @param id,statue
     * @return
     **/
    boolean cancelSeriousIllnessApply(Integer id,Integer operator,String opName);

    /**
     *  获取患者身份证号
     * @param cardNo,hisCardNo,hospitalId
     * @return
     **/
    String getSFZ(String cardNo,String hisCardNo,Integer hospitalId);

    /**
     *  查询新库大病登记
     * @param cardNo,hisCardNo,hospitalId
     * @return
     **/
    List<SeriousIllnessRegisterVo> getNewSeriousIllnessList(Date startTime, Date endTime,String cardNo, String patName, Integer hospitalCode, String hisCardNo,String patSfz,List<Integer> statusList);

    /**
     *  查询老库大病登记
     * @param cardNo,hisCardNo,hospitalId
     * @return
     **/
    List<SeriousIllnessRegisterVo> getOldSeriousIllnessList(Date startTime, Date endTime, String cardNo, String patName, Integer hospitalCode, String hisCardNo,List<Integer> statusList, Integer doctorNo,String patSFZ);


    SeriousIllnessRegister getNewSeriousIllnessSurplusDays(Integer patId, String idCard,Integer hospitalCode);

    Date getOldSeriousIllnessSurplusDays(String patId, String idCard, Integer hospitalCode);
}
