package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.utils.ObjectUtils;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 处方明细基础类
 *
 * <AUTHOR>
@Data
public class BaseRecipeDetail implements Serializable {

    /**
     * 就诊流水号
     * 就诊流水号
     */
    @Column(name = "jzlsh")
    protected Long receptionNo;

    /**
     * 挂号流水号
     */
    @Column(name = "ghlsh")
    protected Long regNo;

    /**
     * 处方流水号
     */
    @Column(name = "cflsh")
    protected Long recipeNo;

    /**
     * 项目编码
     */
    @Column(name = "xmbm")
    protected Integer itemCode;


    /**
     * 项目名称
     */
    @Column(name = "xmmc")
    protected String itemName;

    /**
     * 费用类别
     */
    @Column(name = "sflx")
    protected Integer feeCategory;

    /**
     * 处方类型
     */
    @Column(name = "cflx")
    protected Integer recipeCategory;

    /**
     * 剂量
     */
    @Column(name = "jl")
    protected String dose;

    /**
     * 剂量单位
     */
    @Column(name = "jldw")
    protected String doseUnit;

    /**
     * 频率
     */
    @Column(name = "yf")
    protected Integer frequency;
    /**
     * 规格
     */
    @Column(name = "gg")
    protected String specification;
    /**
     * 给药途径
     */
    @Column(name = "gytj")
    protected Integer usage;
    /**
     * 特殊用法
     */
    @Column(name = "tsyf")
    protected String specialUsage;
    /**
     * 天数
     */
    @Column(name = "ts")
    protected Integer days;
    /**
     * 项目数量
     */
    @Column(name = "xmsl")
    protected BigDecimal quantity;
    /**
     * 单价
     */
    @Column(name = "dj")
    protected BigDecimal price;
    /**
     * 单位
     */
    @Column(name = "dw")
    protected String unit;
    /**
     * 门诊单位数量
     */
    @Column(name = "mzdwsl")
    protected Integer clincUnitNum;
    /**
     * 处方金额
     */
    @Column(name = "cfje")
    protected BigDecimal amount;
    /**
     * 组号
     */
    @Column(name = "zh")
    protected Integer groupNo;
    /**
     * 配药途径编码
     */
    @Column(name = "pytjbm")
    protected Integer dispensing;
    /**
     * 医生嘱托
     */
    @Column(name = "yszt")
    protected String doctorEntrust;
    /**
     * 收费状态
     */
    @Column(name = "zt")
    protected Integer status;
    /**
     * 执行科室
     */
    @Column(name = "zxks")
    protected Integer execDept;
    /**
     * 换方标记
     */
    @Column(name = "hfbj")
    protected Integer changeRecipeFlag;
    /**
     * 套餐标记
     */
    @Column(name = "tcbj")
    protected Integer packageFlag;
    /**
     * 套餐明细流水号
     */
    @Column(name = "tcmxlsh")
    protected Long packageDetailNo;
    /**
     * 申请单
     */
    @Column(name = "sqd")
    protected String applicationForm;
    /**
     * 检查流水号
     */
    @Column(name = "jclsh")
    protected String examineNo;

    /**
     * 检查明细流水号
     */
    @Column(name = "jcmxlsh")
    protected Integer examineDetailNo;

    /**
     * 套餐ID
     */
    @Column(name = "tcid")
    protected Long packageId;
    /**
     * 处方状态
     */
    @Column(name = "cfzt")
    protected Integer recipeStatus;
    /**
     * 医院编码
     */
    @Column(name = "hospitalCode")
    protected Integer hospitalCode;

    /**
     * 审方标识
     */
    @Transient
    protected Integer reviewStatus;

    /**
     * 项目编码
     */
    @Transient
    protected String examineItemCode;


    public BaseRecipeDetail() {
    }

    public BaseRecipeDetail(BaseRecipeDetail baseRecipeDetail) {
        if (baseRecipeDetail == null) {
            return;
        }
        regNo = baseRecipeDetail.regNo;
        receptionNo = baseRecipeDetail.receptionNo;
        recipeNo = baseRecipeDetail.recipeNo;
        itemCode = baseRecipeDetail.itemCode;
        itemName = baseRecipeDetail.itemName;
        feeCategory = baseRecipeDetail.feeCategory;
        dose = baseRecipeDetail.dose;
        doseUnit = baseRecipeDetail.doseUnit;
        frequency = baseRecipeDetail.frequency;
        specification = baseRecipeDetail.specification;
        usage = baseRecipeDetail.usage;
        specialUsage = baseRecipeDetail.specialUsage;
        days = baseRecipeDetail.days;
        quantity = baseRecipeDetail.quantity;

        price = baseRecipeDetail.price;

        unit = baseRecipeDetail.unit;
        clincUnitNum = baseRecipeDetail.clincUnitNum;
        amount = baseRecipeDetail.amount;
        groupNo = baseRecipeDetail.groupNo;
        dispensing = baseRecipeDetail.dispensing;
        doctorEntrust = baseRecipeDetail.doctorEntrust;
        status = baseRecipeDetail.status;
        execDept = baseRecipeDetail.execDept;

        changeRecipeFlag = baseRecipeDetail.changeRecipeFlag;
        packageFlag = baseRecipeDetail.packageFlag;
        packageDetailNo = baseRecipeDetail.packageDetailNo;
        applicationForm = baseRecipeDetail.applicationForm;
        examineNo = baseRecipeDetail.examineNo;
        packageId = baseRecipeDetail.packageId;
        recipeStatus = baseRecipeDetail.recipeStatus;

        hospitalCode = baseRecipeDetail.hospitalCode;
        reviewStatus = baseRecipeDetail.reviewStatus;
    }

    public BaseRecipeDetail(OldBaseRecipeDetail baseRecipeDetail) {
        if (baseRecipeDetail == null) {
            return;
        }
/*        regNo = baseRecipeDetail.getRegNo();
        receptionNo = baseRecipeDetail.getReceptionNo();
        recipeNo = baseRecipeDetail.getRecipeNo();*/
        itemCode = baseRecipeDetail.getItemCode();
        itemName = baseRecipeDetail.getItemName();
        feeCategory = baseRecipeDetail.getFeeCategory();
        dose = baseRecipeDetail.getDose();
        doseUnit = baseRecipeDetail.getDoseUnit();
        frequency = baseRecipeDetail.getFrequency();
        specification = baseRecipeDetail.getSpecification();
        usage = baseRecipeDetail.getUsage();
        specialUsage = baseRecipeDetail.getSpecialUsage();
        days = baseRecipeDetail.getDays();
        quantity = baseRecipeDetail.getQuantity();

        price = baseRecipeDetail.getPrice();

        unit = baseRecipeDetail.getUnit();
        clincUnitNum = baseRecipeDetail.getClincUnitNum();
        amount = baseRecipeDetail.getAmount();
        groupNo = baseRecipeDetail.getGroupNo();
        dispensing = baseRecipeDetail.getDispensing();
        doctorEntrust = baseRecipeDetail.getDoctorEntrust();
        status = baseRecipeDetail.getStatus();
        execDept = baseRecipeDetail.getExecDept();

        changeRecipeFlag = baseRecipeDetail.getChangeRecipeFlag();
        packageFlag = baseRecipeDetail.getPackageFlag();
        //packageDetailNo = baseRecipeDetail.getPackageDetailNo();
        applicationForm = baseRecipeDetail.getApplicationForm();
        examineNo = baseRecipeDetail.getExamineNo();
        //packageId = baseRecipeDetail.getPackageId();
        recipeStatus = baseRecipeDetail.getRecipeStatus();


        reviewStatus = baseRecipeDetail.getReviewStatus();
    }

    public String getDoseUnit() {
        return doseUnit == null ? null : doseUnit.trim();
    }

    public BigDecimal getAmount() {
        if (amount == null) {
            if (price != null && quantity != null) {
                amount = price.multiply(quantity);
            }
        }
        return amount;
    }

    public BigDecimal getHerbsAmount() {
        if (amount == null) {
            if (price != null && StringUtils.isNoneBlank(dose)) {
                amount = price.multiply(new BigDecimal(dose));
            }
        }
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 判断剂量是否为特殊用法
     *
     * @return 是特殊用法返回 true 否则返回 false
     */
    public boolean whetherSpecialUsage() {
        if (StringUtils.isBlank(this.dose) || ObjectUtils.isNumber(this.dose) || ObjectUtils.compare(this.dose, "0")) {
            return false;
        }
        String[] doseList = this.dose.split("-");
        for (String dose : doseList) {
            if (!ObjectUtils.isNumber(dose)) {
                return false;
            }
        }
        return doseList.length >= 2;
    }

    /**
     * 如果是特殊用法，遍历求和后返回
     * 否则直接返回剂量，如果剂量为 null 则返回 BigDecimal.ZERO
     *
     * @return 剂量
     */
    public BigDecimal specialUsageSum() {
        if (whetherSpecialUsage()) {
            BigDecimal doseSum = BigDecimal.ZERO;
            String[] doseList = this.dose.split("-");
            for (String dose : doseList) {
                doseSum = doseSum.add(new BigDecimal(dose));
            }
            return doseSum;
        }
        return this.dose == null ? BigDecimal.ZERO : new BigDecimal(this.dose);
    }

    /**
     * 修改剂量单位，并换算剂量
     *
     * @param newDoseUnit 新剂量单位
     * @param chargeItem  收费项目实体
     */
    public void conversionDoseUnit(String newDoseUnit, ChargeItem chargeItem) {
        if (ItemCategoryEnum.notDrug(this.feeCategory)) {
            return;
        }
        final BigDecimal dosage = chargeItem.getDosage();
        final String wardUnit = StringUtils.trim(chargeItem.getWardUnit());
        final String dosageUnit = StringUtils.trim(chargeItem.getDosageUnit());
        newDoseUnit = StringUtils.trim(newDoseUnit);

        // step.1 设置剂量单位
        if (StringUtils.isBlank(this.doseUnit) || StringUtils.isBlank(this.dose)) {
            this.doseUnit = newDoseUnit;
            this.dose = chargeItem.getDosage().toString();
        }
        // step.2 换算剂量
        BigDecimal doseDecimal = new BigDecimal(this.dose);
        if (ObjectUtils.compare(this.doseUnit, dosageUnit) && ObjectUtils.compare(newDoseUnit, wardUnit)) {
            // 剂量单位转病区单位
            doseDecimal = doseDecimal.divide(dosage, 1, RoundingMode.CEILING);
        }
        if (ObjectUtils.compare(this.doseUnit, wardUnit) && ObjectUtils.compare(newDoseUnit, dosageUnit)) {
            // 病区单位转剂量单位
            doseDecimal = doseDecimal.multiply(dosage);
        }
        // step.3 设置剂量及剂量单位
        this.doseUnit = newDoseUnit;
        this.dose = doseDecimal.toString();
    }

    /**
     * 通过【剂量、频率】计算用药量
     *
     * @param drugFrequency 药品频次
     * @param scale         保留几位小数
     * @param roundingMode  舍入模式
     */
    public void calculateQuantityFormDoseAndFrequency(DrugFrequency drugFrequency, int scale, RoundingMode roundingMode) {
        if (ItemCategoryEnum.notDrug(this.feeCategory)) {
            return;
        }
        this.quantity = Converter.toDecimal(this.dose)
                .multiply(Converter.toDecimal(drugFrequency.getCount()))
                .divide(Converter.toDecimal(drugFrequency.getType()), scale, roundingMode);
    }

    /**
     * 计算用药量
     * 先通过【剂量、频率】计算出基础用药量
     * 再根据包装单位换算
     *
     * @param chargeItem 收费项目
     */
    public void calculateQuantity(DrugFrequency drugFrequency, ChargeItem chargeItem, int scale, RoundingMode roundingMode) {
        if (ItemCategoryEnum.isDrug(this.feeCategory)) {
            this.calculateQuantityFormDoseAndFrequency(drugFrequency, scale, roundingMode);
            if (StringUtils.isEmpty(this.doseUnit)) {
                return;
            }
            if (this.doseUnit.equals(chargeItem.getDosageUnit())) {
                this.quantity = this.quantity.divide(Converter.toDecimal(chargeItem.getDosage()), scale, roundingMode)
                        .divide(Converter.toDecimal(chargeItem.getClinicQty()), scale, roundingMode);
            }
            if (this.doseUnit.equals(chargeItem.getWardUnit())) {
                this.quantity = this.quantity.divide(Converter.toDecimal(chargeItem.getClinicQty()), scale, roundingMode);
            }
        }
        if (this.days > 0) {
            //总量 门诊包装向上取整
            this.quantity = this.quantity.multiply(Converter.toDecimal(days)).setScale(0, RoundingMode.UP);
        }
    }

    /**
     * 根据传入的处方明细，对字段进行修改
     *
     * @param baseRecipeDetail
     */
    public void changeValues(BaseRecipeDetail baseRecipeDetail) {
        if (baseRecipeDetail == null) {
            return;
        }
        receptionNo = baseRecipeDetail.receptionNo;
        regNo = baseRecipeDetail.regNo;
        recipeNo = baseRecipeDetail.recipeNo;
        itemCode = baseRecipeDetail.itemCode;
        itemName = baseRecipeDetail.itemName;
        feeCategory = baseRecipeDetail.feeCategory;
        dose = baseRecipeDetail.dose;
        doseUnit = baseRecipeDetail.doseUnit;
        frequency = baseRecipeDetail.frequency;
        specification = baseRecipeDetail.specification;
        usage = baseRecipeDetail.usage;
        specialUsage = baseRecipeDetail.specialUsage;
        days = baseRecipeDetail.days;
        quantity = baseRecipeDetail.quantity;
        unit = baseRecipeDetail.unit;
        clincUnitNum = baseRecipeDetail.clincUnitNum;

        price = baseRecipeDetail.price;

        amount = baseRecipeDetail.amount;
        groupNo = baseRecipeDetail.groupNo;
        dispensing = baseRecipeDetail.dispensing;
        doctorEntrust = baseRecipeDetail.doctorEntrust;
        status = baseRecipeDetail.status;
        execDept = baseRecipeDetail.execDept;

        changeRecipeFlag = baseRecipeDetail.changeRecipeFlag;
        packageFlag = baseRecipeDetail.packageFlag;
        packageDetailNo = baseRecipeDetail.packageDetailNo;
        applicationForm = baseRecipeDetail.applicationForm;
        examineNo = baseRecipeDetail.examineNo;
        packageId = baseRecipeDetail.packageId;
        recipeStatus = baseRecipeDetail.recipeStatus;

        hospitalCode = baseRecipeDetail.hospitalCode;
        reviewStatus = baseRecipeDetail.reviewStatus;
    }

    /**
     * 根据传入的处方明细，对字段进行修改，如果为空则不修改
     *
     * @param baseRecipeDetail
     */
    public void changeValuesSelective(BaseRecipeDetail baseRecipeDetail) {
        if (baseRecipeDetail == null) {
            return;
        }
        receptionNo = baseRecipeDetail.receptionNo != null ? baseRecipeDetail.receptionNo : receptionNo;
        regNo = baseRecipeDetail.regNo != null ? baseRecipeDetail.regNo : regNo;
        recipeNo = baseRecipeDetail.recipeNo != null ? baseRecipeDetail.recipeNo : recipeNo;
        itemCode = baseRecipeDetail.itemCode != null ? baseRecipeDetail.itemCode : itemCode;
        itemName = baseRecipeDetail.itemName != null ? baseRecipeDetail.itemName : itemName;
        feeCategory = baseRecipeDetail.feeCategory != null ? baseRecipeDetail.feeCategory : feeCategory;
        dose = baseRecipeDetail.dose != null ? baseRecipeDetail.dose : dose;
        doseUnit = baseRecipeDetail.doseUnit != null ? baseRecipeDetail.doseUnit : doseUnit;
        frequency = baseRecipeDetail.frequency != null ? baseRecipeDetail.frequency : frequency;
        specification = baseRecipeDetail.specification != null ? baseRecipeDetail.specification : specification;
        usage = baseRecipeDetail.usage != null ? baseRecipeDetail.usage : usage;
        specialUsage = baseRecipeDetail.specialUsage != null ? baseRecipeDetail.specialUsage : specialUsage;
        days = baseRecipeDetail.days != null ? baseRecipeDetail.days : days;
        quantity = baseRecipeDetail.quantity != null ? baseRecipeDetail.quantity : quantity;
        unit = baseRecipeDetail.unit != null ? baseRecipeDetail.unit : unit;
        clincUnitNum = baseRecipeDetail.clincUnitNum != null ? baseRecipeDetail.clincUnitNum : clincUnitNum;

        price = baseRecipeDetail.price != null ? baseRecipeDetail.price : price;

        amount = baseRecipeDetail.amount != null ? baseRecipeDetail.amount : amount;
        groupNo = baseRecipeDetail.groupNo != null ? baseRecipeDetail.groupNo : groupNo;
        dispensing = baseRecipeDetail.dispensing != null ? baseRecipeDetail.dispensing : dispensing;
        doctorEntrust = baseRecipeDetail.doctorEntrust != null ? baseRecipeDetail.doctorEntrust : doctorEntrust;

        changeRecipeFlag = baseRecipeDetail.changeRecipeFlag != null ? baseRecipeDetail.changeRecipeFlag : changeRecipeFlag;
        packageFlag = baseRecipeDetail.packageFlag != null ? baseRecipeDetail.packageFlag : packageFlag;
        packageDetailNo = baseRecipeDetail.packageDetailNo != null ? baseRecipeDetail.packageDetailNo : packageDetailNo;
        applicationForm = baseRecipeDetail.applicationForm != null ? baseRecipeDetail.applicationForm : applicationForm;
        examineNo = baseRecipeDetail.examineNo != null ? baseRecipeDetail.examineNo : examineNo;
        packageId = baseRecipeDetail.packageId != null ? baseRecipeDetail.packageId : packageId;

        execDept = baseRecipeDetail.execDept != null ? baseRecipeDetail.execDept : execDept;
        hospitalCode = baseRecipeDetail.hospitalCode != null ? baseRecipeDetail.hospitalCode : hospitalCode;
        reviewStatus = baseRecipeDetail.reviewStatus != null ? baseRecipeDetail.reviewStatus : reviewStatus;
    }

    /**
     * 修改收费项目
     *
     * @param chargeItem
     */
    public void ChangeChargeItem(ChargeItem chargeItem) {
        this.itemCode = chargeItem.getItemCode();
        this.itemName = chargeItem.getItemName();
        this.feeCategory = chargeItem.getItemCategory();


        this.dose = "1";
        this.doseUnit = com.rjsoft.common.utils.StringUtils.trim(chargeItem.getWardUnit());
        this.specification = com.rjsoft.common.utils.StringUtils.trim(chargeItem.getDrugGuage());
        this.packageFlag = chargeItem.getIsSet();
        this.price = chargeItem.getClinicExpensePrice()
                .add(chargeItem.getClinicNonExpensePrice())
                .multiply(Converter.toDecimal(chargeItem.getClinicQty())).setScale(6, RoundingMode.UNNECESSARY);
        this.unit = chargeItem.getClinicUnit();
        if (new Integer("1").equals(chargeItem.getIsSet())) {
            this.applicationForm = chargeItem.getItemName();
        }
    }

    /**
     * 修改收费项目
     *
     * @param chargeItem
     */
    public void ChangeChargeItem(SystemTbPubItems chargeItem) {
        this.itemCode = chargeItem.getItemCode();
        this.itemName = chargeItem.getItemName();
        this.feeCategory = chargeItem.getItemCategory();


        this.dose = "1";
        this.doseUnit = com.rjsoft.common.utils.StringUtils.trim(chargeItem.getWardUnit());
        this.specification = com.rjsoft.common.utils.StringUtils.trim(chargeItem.getDrugGuage());
        this.packageFlag = chargeItem.getIsSet();
        this.price = chargeItem.getClinicExpensePrice()
                .add(chargeItem.getClinicNonExpensePrice())
                .multiply(Converter.toDecimal(chargeItem.getClinicQty())).setScale(6, RoundingMode.UNNECESSARY);
        this.unit = chargeItem.getClinicUnit();
        if (new Integer("1").equals(chargeItem.getIsSet())) {
            this.applicationForm = chargeItem.getItemName();
        }
    }

    /**
     * 修改收费项目信息
     *
     * @param chargeItem
     */
    public void ChangeChargeItemPart(ChargeItem chargeItem) {
        if (chargeItem != null) {
            this.itemName = chargeItem.getItemName();
            this.feeCategory = chargeItem.getItemCategory();
            this.price = chargeItem.getClinicExpensePrice()
                    .add(chargeItem.getClinicNonExpensePrice())
                    .multiply(Converter.toDecimal(chargeItem.getClinicQty())).setScale(6, RoundingMode.UNNECESSARY);
            //this.specification = StringUtils.trim(chargeItem.getDrugGuage());
            //this.packageFlag = chargeItem.getIsSet();
            this.unit = chargeItem.getClinicUnit();
        }
    }

    /**
     * 修改收费项目信息
     *
     * @param chargeItem
     */
    public void ChangeChargeItemPart(SystemTbPubItems chargeItem) {
        if (chargeItem != null) {
            this.itemName = chargeItem.getItemName();
            this.feeCategory = chargeItem.getItemCategory();
            this.price = chargeItem.getClinicExpensePrice()
                    .add(chargeItem.getClinicNonExpensePrice())
                    .multiply(Converter.toDecimal(chargeItem.getClinicQty())).setScale(6, RoundingMode.UNNECESSARY);
            //this.specification = StringUtils.trim(chargeItem.getDrugGuage());
            //this.packageFlag = chargeItem.getIsSet();
            this.unit = chargeItem.getClinicUnit();
        }
    }

    public void calculateAmount() {
        this.amount = this.quantity == null || this.price == null ? BigDecimal.ZERO : this.quantity.multiply(this.price);
    }
}
