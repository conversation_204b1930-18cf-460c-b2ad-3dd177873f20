package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.TbtSyscureCode;
import com.rjsoft.outPatient.infrastructure.repository.mapper.TbtSyscureCodeMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.TbtSyscureCodeRepository;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/23-6:27 下午
 */
@Service
@AllArgsConstructor
public class TbtSyscureCodeRepositoryImpl implements TbtSyscureCodeRepository {

    private final TbtSyscureCodeMapper tbtSyscureCodeMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public Map<String, String> getByCodesAsMap(Set<String> diagnoseCodeSet) {
        if (diagnoseCodeSet == null || diagnoseCodeSet.isEmpty()) {
            return new HashMap<>(0);
        }
        final Weekend<TbtSyscureCode> weekend = new Weekend<>(TbtSyscureCode.class);
        weekend.weekendCriteria().andIn(TbtSyscureCode::getCode, diagnoseCodeSet.stream().map(StringUtils::trim).collect(Collectors.toSet()));
        final List<TbtSyscureCode> records = tbtSyscureCodeMapper.selectByExample(weekend);
        HashMap<String, String> map = new HashMap<>();
        records.forEach(i->map.put(i.getCode().trim(),i.getName()));
        return map;
    }
}
