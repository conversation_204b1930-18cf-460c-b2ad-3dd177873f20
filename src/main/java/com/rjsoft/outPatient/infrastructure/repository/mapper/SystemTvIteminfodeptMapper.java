package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDeptDto;
import com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDeptDtoWithItemCode;
import com.rjsoft.outPatient.infrastructure.repository.entity.SystemTvIteminfodept;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

public interface SystemTvIteminfodeptMapper extends BaseMapper<SystemTvIteminfodept>, ExampleMapper<SystemTvIteminfodept> {
    List<SystemTvIteminfodept> getSystemTbItemexecdept(Integer itemCode, Integer hospitalId);
    List<ChargeItemExecDeptDto> getItemsExecDept(@Param("itemCode") Integer itemCode);
    List<ChargeItemExecDeptDto> getNotDrugsExecDept(@Param("itemCode") Integer itemCode);
    List<ChargeItemExecDeptDto> getDrugsExecDept(@Param("itemCode") Integer itemCode);
    List<ChargeItemExecDeptDtoWithItemCode> getItemsExecDeptByItemCodeList(@Param("itemCodeList") List<Integer> itemCodeList);
}
