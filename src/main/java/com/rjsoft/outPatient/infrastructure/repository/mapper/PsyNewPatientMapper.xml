<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.PsyNewPatientMapper">


    <select id="getInfoByIdNoSbname" resultType="java.lang.Integer">
        SELECT count(*)
        FROM tbt_PsyNewPatient a
                 INNER JOIN Tbv_PsySixClassCode b ON a.diagnoseCode = b.sbcode
        WHERE a.idNo = #{idNo}
          AND b.sbname = #{sbname}
    </select>

</mapper>