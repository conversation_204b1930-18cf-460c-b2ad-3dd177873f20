package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.TemplateExtension;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/30-9:18 上午
 */
public interface TemplateExtensionMapper extends Mapper<TemplateExtension> {

    /**
     * 查询药品高危等级
     *
     * @param drugId
     * @param hospitalCode
     * @return
     */
    List<TemplateExtension> getDrugDangerLev(@Param("drugId") Integer drugId, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 查询药品原研药信息
     */
    List<TemplateExtension> getDrugOriginal(@Param("hospId")Integer hospId);

    String getDrugPropertyName(@Param("dicCode")Integer dicCode,@Param("hospId")Integer hospId);

}
