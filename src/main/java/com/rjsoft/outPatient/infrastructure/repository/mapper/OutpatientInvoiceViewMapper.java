package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.OutpatientInvoiceView;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface OutpatientInvoiceViewMapper extends Mapper<OutpatientInvoiceView> {

    default List<OutpatientInvoiceView> getOutpatientInvoiceByRegNoList(List<Long> regNoList, Integer hospitalCode) {
        Weekend<OutpatientInvoiceView> weekend = new Weekend<>(OutpatientInvoiceView.class);
        WeekendCriteria<OutpatientInvoiceView, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(OutpatientInvoiceView::getRegno, regNoList)
                .andEqualTo(OutpatientInvoiceView::getHospitalcode, hospitalCode)
                .andEqualTo(OutpatientInvoiceView::getStatus, 0);
        return selectByExample(weekend);
    }

}