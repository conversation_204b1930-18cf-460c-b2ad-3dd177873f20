package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 联动配置无效药品表
 * @TableName System_Pub_UsageNotEffectDrugInfo
 */
@Data
@Table(name = "System_Pub_UsageNotEffectDrugInfo")
public class SystemPubUsagenoteffectdruginfo implements Serializable {
    /**
     * ID
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "id")
    private Integer id;

    /**
     * 联动id
     */
    @Column(name = "UsageId")
    private Integer usageId;

    /**
     * 收费项目id
     */
    @Column(name = "ItemCode")
    private Integer itemCode;

    /**
     * 创建人
     */
    @Column(name = "createor")
    private Integer createor;

    /**
     * 创建时间
     */
    @Column(name = "CreateTime")
    private Date createTime;

    /**
     * 删除标识
     */
    @Column(name = "DelFlag")
    private Integer delFlag;

    /**
     * 修改人
     */
    @Column(name = "updator")
    private Integer updator;

    /**
     * 修改时间
     */
    @Column(name = "UpdateTime")
    private Date updateTime;

    /**
     * 院区编码
     */
    @Column(name = "HospId")
    private Integer hospId;

    /**
     * 全局id
     */
    @Column(name = "GlobalId")
    private Integer globalId;

}