package com.rjsoft.outPatient.infrastructure.repository.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

public interface ScientificMapper {

    /**
     * 查询当前医生科研课题信息
     */
    List<HashMap> getProjectsList(String doctorId, Integer hospitalCode);


    String patJoinProject(@Param("projectId") String projectId,
                          @Param("hisCardNo") String hisCardNo,
                          @Param("patName") String patName,
                          @Param("sex") Integer sex,
                          @Param("patSfz") String patSfz,
                          @Param("workerId") String workerId);
}
