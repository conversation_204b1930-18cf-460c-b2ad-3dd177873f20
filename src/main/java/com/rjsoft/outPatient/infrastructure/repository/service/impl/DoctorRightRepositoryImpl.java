package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.domain.config.dto.AddDoctorParams;
import com.rjsoft.outPatient.domain.config.dto.AddDoctorRightRequest;
import com.rjsoft.outPatient.infrastructure.repository.entity.DoctorRight;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DoctorRightMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SysFunctionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DoctorRightRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Date;
import java.util.List;

/**
 * 医生对应权限
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class DoctorRightRepositoryImpl implements DoctorRightRepository {

    Doctor<PERSON><PERSON>M<PERSON><PERSON> doctorRightMapper;
    SysFunctionMapper sysFunctionMapper;

    /**
     * 根据搜索类型获取医院权限列表
     *
     * @param searchType   1 职工 2 权限
     * @param id           职工ID或权限ID
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<DoctorRight> getDoctorRight(Integer searchType, Integer id, Integer hospitalCode) {
        return doctorRightMapper.getDoctorRightByType(searchType, id, hospitalCode);
    }

    /**
     * 保存医生对照权限
     *
     * @param doctorRight
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean saveDoctorRight(DoctorRight doctorRight) {
        boolean success;
        Date date = sysFunctionMapper.getDate();
        doctorRight.setOpTime(date);
        if (doctorRight.getId() == null || doctorRight.getId() == 0) {
            success = doctorRightMapper.insert(doctorRight) > 0;
        } else {
            success = doctorRightMapper.updateByPrimaryKeySelective(doctorRight) > 0;
        }
        return success;
    }

    /**
     * 删除权限
     *
     * @param id
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean delDoctorRight(List<Integer> id, Integer typeId, Integer hospitalCode) {
        doctorRightMapper.delDoctorRight(id, typeId, hospitalCode);
        return true;
    }

    @Override
    @DatabaseAnnotation
    public List<DoctorRight> getInfoByTypeWorkerId(int type, int opCode, Integer hospCode) {
        return doctorRightMapper.getInfoByTypeWorkerId(type, opCode, hospCode);
    }

    /**
     * 检查职工是否存在此权限
     *
     * @param type
     * @param workerId
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean checkRight(Integer type, Integer workerId, Integer hospitalCode) {
        return doctorRightMapper.checkRight(type, workerId, hospitalCode);
    }

    /**
     * 根据type和当前医生id查询告知单类型
     *
     * @param type
     * @param workerId
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public DoctorRight getDoctorType(Integer type, Integer workerId, Integer hospitalCode) {
        DoctorRight doctorRight = new DoctorRight();
        doctorRight.setRightType(type);
        doctorRight.setWorkerId(workerId);
        doctorRight.setHospitalCode(hospitalCode);
        return doctorRightMapper.selectOne(doctorRight);
    }

    /**
     * 根据权限类型和医生Id查询医生
     *
     * @param typeId
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public List<DoctorRight> queryDoctorInfoByRight(Integer typeId, Integer hospitalCode) {
        return doctorRightMapper.queryDoctorInfoByRight(typeId, hospitalCode);
    }

    /**
     * 根据传入添加权限列表获取已经存在列表
     *
     * @param params
     */
    @Override
    @DatabaseAnnotation
    public List<DoctorRight> getDoctorRightList(List<AddDoctorParams> params) {
        return doctorRightMapper.getDoctorRightList(params);
    }

}
