<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.CaseTempMapper">


    <update id="updateDeptOtherNotDefault">
        UPDATE MZYS_TB_MBINFO
        SET mrmb = 0
        WHERE mbid IN (SELECT mbid FROM MZYS_TB_KSMB WHERE ksdm = #{deptId})
          and yybm = #{hospCode}
          and czbj = #{firstDiagnosisFlag}
          and zt = 0
    </update>
    <update id="updateResearchOtherNotDefault">
        UPDATE MZYS_TB_MBINFO
        SET mrmb = 0
        WHERE mbid IN (
            SELECT Mbid
            FROM MZYS_TB_KYMBKS
            WHERE ProjectId = #{projectId})
          and czbj = #{firstDiagnosisFlag}
          and yybm = #{hospCode}
          and zt = 0
    </update>
</mapper>