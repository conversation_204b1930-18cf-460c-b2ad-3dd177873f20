package com.rjsoft.outPatient.infrastructure.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-10-27
 * 微信推送中间表
 */

@Data
@Table ( name ="tbt_PushTemplate" )
public class WechatPushTemplate implements Serializable {

	private static final long serialVersionUID =  49565351728300081L;

   	@Column(name = "Id" )
	private String id;

   	@Column(name = "Content" )
	private String content;

   	@Column(name = "TelPhone" )
	private String telPhone;

   	@Column(name = "SourceFrom" )
	private String sourceFrom;

   	@Column(name = "CreateTime" )
	private Date createTime;

   	@Column(name = "Creator" )
	private String creator;

   	@Column(name = "UpdateTime" )
	private Date updateTime;

   	@Column(name = "Updator" )
	private String updator;

   	@Column(name = "Status" )
	private Integer status;

   	@Column(name = "SceneCode" )
	private Long sceneCode;

   	@Column(name = "CategoryID" )
	private Long categoryId;

   	@Column(name = "SendMode" )
	private Long sendMode;

   	@Column(name = "DelayTime" )
	private Date delayTime;

}
