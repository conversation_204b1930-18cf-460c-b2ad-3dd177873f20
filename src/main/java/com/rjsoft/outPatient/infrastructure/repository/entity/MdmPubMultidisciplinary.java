package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.*;
import java.math.BigInteger;
import java.util.Date;

/**
 * MDM_Pub_Multidisciplinary  多学科门诊表
 * @TableName MDM_Pub_Multidisciplinary
 */
@Data
@Table( name ="MDM_Pub_Multidisciplinary" )
public class MdmPubMultidisciplinary {
    /**
     * 唯一码
     */
    @Id
    @Column(name = "uniqueID")
    private Long uniqueID;
    @Transient
    private String uniqueIDStr;
    /**
     * 多学科门诊类型
     */
    @Column(name = "typeName")
    private String typeName;
    /**
     * 简介
     */
    @Column(name = "remark")
    private String remark;
    /**
     * 拼音码,小写
     */
    @Column(name = "pinyinCode1")
    private String pinyinCode1;
    /**
     * 拼音码,大写
     */
    @Column(name = "pinyinCode2")
    private String pinyinCode2;
    /**
     * 五笔码
     */
    @Column(name = "wubiCode")
    private String wubiCode;
    /**
     * 最大可申请数量，为空时不限制可申请数量
     */
    @Column(name = "num")
    private Integer num;
    /**
     * 医院编码
     */
    @Column(name = "hospitalId")
    private Integer hospitalId;
    @Transient
    private String hospitalName;
    /**
     * 状态，1:启用，0:停用
     */
    @Column(name = "state")
    private String state;
    @Transient
    private String stateName;
    /**
     *
     */
    @Column(name = "updateTime")
    private Date updateTime;
    /**
     *
     */
    @Column(name = "updateUserid")
    private Integer updateUserId;
    /**
     *
     */
    @Column(name = "createTime")
    private Date createTime;
    /**
     *
     */
    @Column(name = "createUserid")
    private Integer createUserId;
}
