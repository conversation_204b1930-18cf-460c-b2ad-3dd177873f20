package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "Reg_Tv_ChargeDetail")
public class ChargeDetailView implements Serializable {
    @Column(name = "ChargeDetl")
    private Long chargedetl;

    @Column(name = "ChargeNo")
    private Long chargeno;

    @Column(name = "RecipeNum")
    private Integer recipenum;

    @Column(name = "RecipeID")
    private Long recipeid;

    @Column(name = "RecipeDetlID")
    private Long recipedetlid;

    @Column(name = "ItemID")
    private Integer itemid;

    @Column(name = "ItemName")
    private String itemname;

    @Column(name = "Quantiy")
    private Long quantiy;

    @Column(name = "ItemCategory")
    private Integer itemcategory;

    @Column(name = "PackageNo")
    private Integer packageno;

    @Column(name = "GroupNo")
    private Integer groupno;

    @Column(name = "ClinicUnit")
    private String clinicunit;

    @Column(name = "BasicUnit")
    private String basicunit;

    @Column(name = "ClinicQty")
    private Integer clinicqty;

    @Column(name = "[Usage]")
    private String usage;

    @Column(name = "Dosage")
    private String dosage;

    @Column(name = "DosageUnit")
    private String dosageunit;

    @Column(name = "DrugGauge")
    private String druggauge;

    @Column(name = "CheckCode")
    private String checkcode;

    @Column(name = "ExecuteDept")
    private Integer executedept;

    @Column(name = "Times")
    private Integer times;

    @Column(name = "Price")
    private BigDecimal price;

    @Column(name = "ExpensePrice")
    private BigDecimal expenseprice;

    @Column(name = "NonExpensePrice")
    private BigDecimal nonexpenseprice;

    @Column(name = "[Status]")
    private Integer status;

    @Column(name = "TotalAmount")
    private BigDecimal totalamount;

    @Column(name = "DoctorId")
    private Integer doctorid;

    @Column(name = "DeptId")
    private Integer deptid;

    @Column(name = "DoctorLevel")
    private Integer doctorlevel;

    @Column(name = "FeeType")
    private Integer feetype;

    @Column(name = "IsDrug")
    private Integer isdrug;

    @Column(name = "SpecialFlag")
    private Integer specialflag;

    @Column(name = "DataFrom")
    private Integer datafrom;

    @Column(name = "FromFlag")
    private Integer fromflag;

    @Column(name = "DiscountAmount")
    private BigDecimal discountamount;

    @Column(name = "IsDelete")
    private Boolean isdelete;

    @Column(name = "CreatedBy")
    private Integer createdby;

    @Column(name = "CreatedDate")
    private Date createddate;

    @Column(name = "UpdateBy")
    private Integer updateby;

    @Column(name = "UpdateDate")
    private Date updatedate;

    @Column(name = "InsuranceTradeAmount")
    private BigDecimal insurancetradeamount;

    @Column(name = "SelfAmount")
    private BigDecimal selfamount;

    @Column(name = "ClassifyTotal")
    private BigDecimal classifytotal;

    @Column(name = "InsuranceCashAmount")
    private BigDecimal insurancecashamount;

    @Column(name = "HospitalCode")
    private Integer hospitalcode;

    @Column(name = "CreditAmt")
    private BigDecimal creditamt;

    @Column(name = "RealAmt")
    private BigDecimal realamt;

    @Column(name = "OtherAmt")
    private BigDecimal otheramt;

    @Column(name = "JzAmount")
    private BigDecimal jzamount;

    @Column(name = "OldChargeDetl")
    private Long oldchargedetl;

    @Transient
    private String medicalType1;

    @Transient
    private String medicalType2;

    @Transient
    private String medicalType3;

    private static final long serialVersionUID = 1L;
}