package com.rjsoft.outPatient.infrastructure.repository.entity;


import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
* 
* 特需号挂号表
*/
@Data
@Table(name = "Reg_Tb_SpecialSourceRelation")
public class RegTbSpecialsourcerelation implements Serializable {
    /**
    * 特需号挂号流水号
    */
    @Column(name = "SpecialRegNo")
    private Long specialRegNo;
    /**
    * 特需号号源id
    */
    @Column(name = "SpecialSourceId")
    private Integer specialSourceId;
    /**
    * 配药号挂号流水号
    */
    @Column(name = "DispensingRegNo")
    private Long dispensingRegNo;
    /**
    * 配药号号源id
    */
    @Column(name = "DispensingSourceId")
    private Integer dispensingSourceId;
    /**
    * 医院编码
    */
    @Column(name = "HospitalCode")
    private Integer hospitalCode;
    /**
    * 创建时间
    */
    @Column(name = "CreateTime")
    private Date createTime;

    /**
     * 状态 0：有效，1：无效
     */
    @Column(name = "status")
    private Integer status;


}
