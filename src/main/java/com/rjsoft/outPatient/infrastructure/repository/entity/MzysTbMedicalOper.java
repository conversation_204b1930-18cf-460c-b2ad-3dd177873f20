package com.rjsoft.outPatient.infrastructure.repository.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Data;

/**
    * 门诊病案首页手术信息
    */
@Data
@Table(name = "MZYS_Tb_MedicalOper")
public class MzysTbMedicalOper implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 挂号流水号
     */
    @Column(name = "reg_no")
    private Long regNo;

    /**
     * 手术及操作日期
     */
    @Column(name = "oper_time")
    private Date operTime;

    /**
     * 手术及操作编码
     */
    @Column(name = "oper_code")
    private String operCode;

    /**
     * 手术及操作名称
     */
    @Column(name = "oper_name")
    private String operName;

    /**
     * 手术分级管理级别
     */
    @Column(name = "oper_level")
    private String operLevel;

    /**
     * 手术及操作者
     */
    @Column(name = "oper_doctor")
    private Integer operDoctor;

    /**
     * 麻醉方式
     */
    @Column(name = "anesthetist_mode")
    private String anesthetistMode;

    /**
     * 麻醉医师
     */
    @Column(name = "anesthetist_doctor")
    private Integer anesthetistDoctor;

    /**
     * 院区ID
     */
    @Column(name = "hospital_id")
    private Integer hospitalId;

    private static final long serialVersionUID = 1L;
}