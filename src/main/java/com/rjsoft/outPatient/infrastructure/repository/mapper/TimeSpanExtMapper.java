package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.doctorElemMain.constant.StatusEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.TimeSpanExt;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TimeSpanExtMapper extends Mapper<TimeSpanExt> {

    /**
     * 根据时段id获取对应的时段信息
     *
     * @param timeSpanId
     * @param hospitalCode
     * @return
     */
    default TimeSpanExt getTimeSpanInfo(Integer timeSpanId, Integer hospitalCode) {
        TimeSpanExt span = new TimeSpanExt();
        span.setId(timeSpanId);
        span.setIsDelete(StatusEnum.NO_DELETE.getCode());
        span.setIsUse(1);
        span.setHospitalcode(String.valueOf(hospitalCode));
        return selectOne(span);
    }

    /**
     * 查询当前时间上下午
     *
     * @param hospitalCode
     * @return
     */
    String GetTimeSpanExt(@Param("hospitalCode") Integer hospitalCode);

    /**
     * 获取时段id
     *
     * @param name
     * @param hospitalCode
     * @return
     */
    default List<TimeSpanExt> getTimeSpanExtInfo(String name, Integer hospitalCode) {
        TimeSpanExt span = new TimeSpanExt();
        span.setName(name);
        span.setHospitalcode(String.valueOf(hospitalCode));
        return select(span);
    }


}
