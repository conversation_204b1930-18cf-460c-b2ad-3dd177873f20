package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Allergy;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;
import java.util.Set;

public interface AllergyMapper extends BaseMapper<Allergy>, ExampleMapper<Allergy> {


    /**
     * 获取患者过敏信息
     *
     * @param id
     * @param patId
     * @param hisCardNo
     * @param hospitalCode
     * @return
     */
    default List<Allergy> getPatAllergyInfo(Integer id, Integer patId, String hisCardNo, Integer hospitalCode) {
        Allergy allergy = new Allergy();
        if (id != 0) {
            allergy.setId(id);
        }
        allergy.setPatId(patId);
        if (!hisCardNo.isEmpty()) {
            allergy.setHisCardNo(hisCardNo);
        }
        allergy.setHospitalCode(hospitalCode);
        return select(allergy);
    }

    default Allergy getPatAllergyInfo(Integer allergyCode, Integer patId) {
        Allergy allergy = new Allergy();
        allergy.setAllergyCode(allergyCode);
        allergy.setPatId(patId);
        allergy.setIsDelete(0);
        return selectOne(allergy);
    }


    default boolean delPatAllergyInfo(Integer id, Integer patId, Integer state, Integer doctorId, Integer hospitalCode) {
        Allergy allergy = new Allergy();
        allergy.setId(id);
        allergy.setPatId(patId);
        allergy.setHospitalCode(hospitalCode);
        allergy.setIsDelete(state);
        Integer row = updateByPrimaryKeySelective(allergy);
        if (row > 0) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * 根据患者编号获取患者过敏信息
     *
     * @param patIds
     * @param hospitalId
     * @return
     */
    default List<Allergy> getAllergyList(Set<Integer> patIds, Integer hospitalId) {
        Weekend<Allergy> weekend = new Weekend<>(Allergy.class);
        WeekendCriteria<Allergy, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(Allergy::getPatId, patIds)
                .andEqualTo(Allergy::getHospitalCode, hospitalId)
                .andEqualTo(Allergy::getResult, "2")
                .andEqualTo(Allergy::getIsDelete, 0);
        return selectByExample(weekend);
    }


    /**
     * 获取患者过敏阳性信息
     *
     * @param patId
     * @param hospitalCode
     * @return
     */
    default List<Allergy> getConfirmAllergies(Integer patId, Integer hospitalCode) {
        Allergy allergy = new Allergy();
        allergy.setPatId(patId);
        allergy.setHospitalCode(hospitalCode);
        allergy.setResult("2");
        allergy.setIsDelete(0);
        return select(allergy);
    }


    /**
     * 根据过敏源code集和患者编号集获取过敏信息
     *
     * @param codeList
     * @param patIdList
     * @return
     */
    default List<Allergy> queryByCodesAndPatIds(List<Integer> codeList, List<Long> patIdList) {
        Weekend<Allergy> weekend = new Weekend<>(Allergy.class);
        WeekendCriteria<Allergy, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(Allergy::getAllergyCode, codeList)
                .andIn(Allergy::getPatId, patIdList)
                .andEqualTo(Allergy::getResult, "2")
                .andEqualTo(Allergy::getIsDelete, 0);
        return selectByExample(weekend);
    }
}
