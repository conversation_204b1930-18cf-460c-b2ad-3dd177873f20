package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationTemplate;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface ApplicationTemplateMapper extends BaseMapper<ApplicationTemplate>, ExampleMapper<ApplicationTemplate> {


    /**
     * 查询申请单模板列表
     *
     * @param templateType
     * @param templateNo
     * @param hospitalCode
     * @return
     */
    default List<ApplicationTemplate> getApplicationTemplate(Integer templateType, String templateNo, Integer hospitalCode) {
        ApplicationTemplate applicationTemplate = new ApplicationTemplate();
        applicationTemplate.setTemplateType(templateType);

        if (templateNo.isEmpty()) {
            templateNo = null;
        }
        if (templateNo != null) {
            applicationTemplate.setTemplateNo(templateNo);
        }
        applicationTemplate.setHospitalCode(hospitalCode);
        applicationTemplate.setStatus(1);
        return select(applicationTemplate);
    }

    /**
     * 根据申请单流水号获取申请单
     * @param templateNo
     * @param hospitalCode
     * @return
     */
    default  ApplicationTemplate getApplicationTemplateByNo(String templateNo,Integer hospitalCode) {
        ApplicationTemplate applicationTemplate = new ApplicationTemplate();
        applicationTemplate.setTemplateNo(templateNo);
        applicationTemplate.setHospitalCode(hospitalCode);
        List<ApplicationTemplate> list = select(applicationTemplate);
        if (list.stream().count() == 0) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 根据模板编码集合获取模板列表
     * @param templateNo
     * @param hospitalCode
     * @return
     */
  default   List<ApplicationTemplate> getApplicationTemplateByTemplateNos(List<String> templateNo, Integer hospitalCode) {
      Weekend weekend = new Weekend(ApplicationTemplate.class);
      WeekendCriteria<ApplicationTemplate, Object> weekendCriteria = weekend.weekendCriteria();
      weekendCriteria.andIn(ApplicationTemplate::getTemplateNo, templateNo);
      weekendCriteria.andEqualTo(ApplicationTemplate::getHospitalCode, hospitalCode);
      return selectByExample(weekend);
  }

    /**
     * 根据项目编码查询申请单模板
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    List<ApplicationTemplate>getApplicationTemplateByItemCode(@Param("itemCode") Integer itemCode,@Param("hospitalCode") Integer hospitalCode);
}
