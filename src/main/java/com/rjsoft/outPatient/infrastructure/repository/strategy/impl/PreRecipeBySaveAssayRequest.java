package com.rjsoft.outPatient.infrastructure.repository.strategy.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.domain.recipe.RecipeDomain;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.domain.recipe.dto.SaveAssayRequestDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeItem;
import com.rjsoft.outPatient.infrastructure.repository.entity.PreRecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.service.PreRecipeDetailRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeRepository;
import com.rjsoft.outPatient.infrastructure.repository.strategy.PreRecipeStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.rjsoft.outPatient.infrastructure.repository.service.ChargeItemRepository;

@Component("PreRecipeBySaveAssayRequest")
@AllArgsConstructor
public class PreRecipeBySaveAssayRequest implements PreRecipeStrategy {

    @Resource
    RecipeDomain recipeDomain;

    @Resource
    RecipeRepository recipeRepository;

    @Resource
    PreRecipeDetailRepository recipeDetailRepository;

    @Resource
    ChargeItemRepository chargeItemRepository;

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<PreRecipeDetail> getPreRecipeDetails(CheckRecipeDto checkDto, Map errInfo) {
        List<PreRecipeDetail> details = new ArrayList<PreRecipeDetail>();
        final ChargeItem chargeItem = chargeItemRepository.getChargeItemById(Converter.toInt32(checkDto.getOperatingId()), checkDto.getHospitalCode());

        // 化验系统套餐
        if (ItemCategoryEnum.Laboratory.getCategoryCode().equals(chargeItem.getItemCategory())) {
            //final List<Integer> packageDetails = itemSetDetailRepository.getItemIdsBySetId(checkDto.getOperatingId().intValue(), checkDto.getHospitalCode());
            details = recipeDomain.saveAssayRequest(new SaveAssayRequestDto(checkDto, chargeItem, null));
            return details;
        }

        details = recipeDetailRepository.getPreRecipeDetailsByPackageId(Converter.toInt64(checkDto.getOperatingId()), checkDto.getDoctorId(), checkDto.getHospitalCode(), checkDto.getPreSaveNo(), checkDto, errInfo);

        return details;
    }
}
