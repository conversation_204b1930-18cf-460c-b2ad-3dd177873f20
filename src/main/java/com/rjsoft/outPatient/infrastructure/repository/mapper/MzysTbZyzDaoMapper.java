package com.rjsoft.outPatient.infrastructure.repository.mapper;


import com.rjsoft.outPatient.domain.hospitalProve.dto.MdDigResult;
import com.rjsoft.outPatient.domain.hospitalProve.dto.MzysTbZyzParams;
import com.rjsoft.outPatient.domain.hospitalProve.dto.MzysTbZyzResult;

public interface MzysTbZyzDaoMapper {

    /**
     * 查询住院证
     *
     * @param params
     * @return
     */
    MzysTbZyzResult getMzysTbZyz(MzysTbZyzParams params);

    MdDigResult getMdDig(Integer workerId);

}