package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 处方菜单
 */
@Data
@Table(name = "MZYS_TB_MZYSCFCD")
public class MenuByPrescription implements Serializable {

    @Id
    @Column(name = "id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    @Column(name = "cdmc")
    private String menuName;

    @Column(name = "cdbj")
    private Integer menuFlag;

    @Column(name = "cdkd")
    private Integer menuWidth;

    @Column(name = "cdxh")
    private Integer orderNo;

    @Column(name = "zt")
    private Integer status;

    @Column(name = "yybm")
    private Integer hospitalCode;

}
