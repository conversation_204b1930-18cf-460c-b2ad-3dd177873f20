package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.domain.research.dto.PatientListDto;
import com.rjsoft.outPatient.domain.research.dto.PatientListSearchDto;
import com.rjsoft.outPatient.domain.research.dto.RecipePatientListDto;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ResearchMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ResearchRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
@AllArgsConstructor
public class ResearchRepositoryImpl implements ResearchRepository {
    private ResearchMapper researchMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<PatientListDto> patientList(PatientListSearchDto patientListSearchDto) {
        if(StringUtils.isEmpty(patientListSearchDto.getCardNo())&&StringUtils.isEmpty(patientListSearchDto.getHisCardNo())&&StringUtils.isEmpty(patientListSearchDto.getCertificateNo())&&StringUtils.isEmpty(patientListSearchDto.getPatName())){
            return new ArrayList<>();
        }
        List<PatientListDto> patientListDtoList = researchMapper.patientList(patientListSearchDto);
        return patientListDtoList;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public PageInfo<RecipePatientListDto> recipePatientList(PatientListSearchDto patientListSearchDto) {
        PageHelper.startPage(patientListSearchDto.getPageNum(), patientListSearchDto.getPageSize());
        List<RecipePatientListDto> recipePatientListDtos = researchMapper.recipePatientList(patientListSearchDto);
        PageInfo<RecipePatientListDto> pageInfo = new PageInfo<>(recipePatientListDtos);
        return pageInfo;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public HashMap getHisCardNo(Integer type) {
        String hisCardNo = researchMapper.getHisCardNo(type);
        HashMap<String,String> hashMap = new HashMap<>();
        hashMap.put("hisCardNo",hisCardNo);
        return hashMap;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Long getRegNo(String tabName, String columnName) {
        return researchMapper.getRegNo(tabName,columnName);
    }
}
