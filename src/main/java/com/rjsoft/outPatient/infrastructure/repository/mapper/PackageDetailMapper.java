package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.common.enums.YesOrNoEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.PackageDetail;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

/**
 * 套餐明细Mapper
 *
 * <AUTHOR>
 * @since 2021/7/6 - 17:00
 */
public interface PackageDetailMapper extends BaseMapper<PackageDetail>, ExampleMapper<PackageDetail> {

    /**
     * 根据套餐ID加载明细
     * @param packageId
     * @param hospitalCode
     * @return
     */
    default List<PackageDetail> getPackageDetailsByPackageId(Integer packageId, Integer hospitalCode) {
        PackageDetail entity = new PackageDetail();
        entity.setPackageId(packageId);
        entity.setHospitalCode(hospitalCode);
        entity.setStatus(YesOrNoEnum.NO.getCode());
        return select(entity);
    }

    /**
     * 根据套餐明细ID加载明细
     *
     * @param packageDetailId
     * @param hospitalCode
     * @return
     */
    default List<PackageDetail> getPackageDetailsByPackageDetailId(Integer packageDetailId, Integer hospitalCode) {
        PackageDetail entity = new PackageDetail();
        entity.setPackageDetailId(packageDetailId);
        entity.setHospitalCode(hospitalCode);
        entity.setStatus(YesOrNoEnum.NO.getCode());
        return select(entity);
    }
}
