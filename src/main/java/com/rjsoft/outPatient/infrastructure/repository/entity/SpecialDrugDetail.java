package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 特殊药品明细
 *
 * <AUTHOR>
 * @since 2021/11/24-2:53 下午
 */
@Data
@Table(name = "MZYS_TB_TSYPXM")
public class SpecialDrugDetail implements Serializable {

    private static final long serialVersionUID = -3027952254462881131L;

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "id", insertable = false, updatable = false)
    private Integer id;
    /**
     * 分类 id
     * 关联
     * 数据库: MZYS_NEW 架构: dbo 表: MZYS_TB_TSYP 特殊药品表
     */
    @Column(name = "category")
    private Integer category;
    /**
     * 收费项目编码
     * 关联
     * 数据库: HISDB 架构: dbo 表: System_Tb_PubItems 收费项目表
     */
    @Column(name = "projectNum")
    private Integer projectNum;
    /**
     * 医院编码
     */
    @Column(name = "hospitalCode")
    private Integer hospitalCode;
}
