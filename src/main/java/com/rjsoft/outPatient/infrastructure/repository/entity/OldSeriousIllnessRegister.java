package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rjsoft.outPatient.domain.seriousIllness.bo.DiseaseRegisterBo;
import com.rjsoft.outPatient.domain.seriousIllness.vo.SeriousIllnessRegisterVo;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 大病登记
 *
 * <AUTHOR>
@Data
@Table(name = "MZYS_TB_DBDJD")
public class OldSeriousIllnessRegister implements Serializable {

    /**
     * Id
     */
    @Id
    @Column(name = "Id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * regno
     */
    @Column(name = "regno")
    private Integer regNo;

    /**
     * 患者编号
     */
    @Column(name = "hzbh")
    private String patId;

    /**
     * 卡号
//     */
//    @Column(name = "cardNo")
//    private String cardNo;

    /**
     * 病历卡号
     */
//    @Column(name = "hisCardNo")
//    private String hisCardNo;

    /**
     * 姓名
     */
    @Column(name = "Name")
    private String patName;

    /**
     * 身份证号码
     */
    @Column(name = "SFZ")
    private String patSfz;

    /**
     * 联系电话
     */
    @Column(name = "Phone")
    private String phone;

    /**
     * 联系地址
     */
    @Column(name = "Adress")
    private String address;

    /**
     * 疾病诊断
     */
    @Column(name = "Diagnosis")
    private String diagnosis;

    /**
     * 治疗项目
     */
    @Column(name = "TreatmentItem")
    private String treatmentItem;

    /**
     * 医疗机构名称
     */
    @Column(name = "MedicalInstitution")
    private String medicalInstitution;

    /**
     * 医师签名
     */
    @Column(name = "Doctor")
    private String doctor;

    /**
     * 签名时间
     */
    @Column(name = "SignTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signTime;

    /**
     * 盖章人ID
     */
    @Column(name = "Sealdoctorid")
    private Integer sealDoctorId;

    /**
     * 盖章
     */
    @Column(name = "Seal")
    private String seal;

    /**
     * 盖章时间
     */
    @Column(name = "SealTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sealTime;

    /**
     * 被委托人
     */
    @Column(name = "Consignor")
    private String consignor;

    /**
     * 被委托人身份证
     */
    @Column(name = "ConsignorSFZ")
    private String consignorSFZ;

    /**
     * 登记时间
     */
    @Column(name = "RegisterTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date registerTime;

    /**
     * 经办人
     */
    @Column(name = "Trustees")
    private String trustees;

    /**
     * 状态
     */
    @Column(name = "State")
    private Integer state;
    /**
     * 使用时间
     */
    @Column(name = "UseLift")
    private String useLift;

    /**
     * 医保流水号
     */
    @Column(name = "ybSerialNo")
    private String ybSerialNo;

    /**
     * 转出机构名称
     */
    @Column(name = "TransfersOutName")
    private String transfersOutName;

    /**
     * 创建人ID
     */
    @Column(name = "Credoctorid")
    private Integer creDoctorId;

    /**
     * 创建人
     */
    @Column(name = "CreName")
    private String creName;

    /**
     * 创建时间
     */
    @Column(name = "CreTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date creTime;

    /**
     * 撤销从医保中心时间
     */
//    @Column(name = "CancelTime")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date cancelTime;

    /**
     * 修改人ID
     */
    @Column(name = "Upddoctorid")
    private Integer updDoctorId;

    /**
     * 修改人
     */
    @Column(name = "UpdName")
    private String updName;

    /**
     * 修改时间
     */
    @Column(name = "UpdTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updTime;

    /**
     * 删除状态
     */
    @Column(name = "DelFlag")
    private Integer delFlag;

    @Transient
    private Integer hospitalCode;

    public OldSeriousIllnessRegister(Integer id, Integer hospitalCode) {
        this.id = id;
    }

    public OldSeriousIllnessRegister(Integer id) {
        this.id = id;
    }

    public OldSeriousIllnessRegister() {
    }

    public SeriousIllnessRegisterVo toSeriousIllnessRegisterVo() {
        final SeriousIllnessRegisterVo seriousIllnessRegisterDto = new SeriousIllnessRegisterVo();
        if (patSfz != null) {
            patSfz = patSfz.trim();
        }
        if (consignorSFZ != null) {
            consignorSFZ = consignorSFZ.trim();
        }

        BeanUtils.copyProperties(this, seriousIllnessRegisterDto);
        return seriousIllnessRegisterDto;
    }

    public OldSeriousIllnessRegister(DiseaseRegisterBo diseaseRegisterBo) {
        BeanUtils.copyProperties(diseaseRegisterBo, this);
        this.registerTime = this.signTime;
    }
}
