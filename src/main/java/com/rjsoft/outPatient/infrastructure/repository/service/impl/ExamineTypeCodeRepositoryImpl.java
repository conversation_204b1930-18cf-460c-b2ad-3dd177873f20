package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.ExamineTypeCode;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ExamineTypeCodeMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ExamineTypeCodeRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021/10/13-2:04 下午
 */
@Service
@AllArgsConstructor
public class ExamineTypeCodeRepositoryImpl implements ExamineTypeCodeRepository {

    private final ExamineTypeCodeMapper examineTypeCodeMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.REPORTS)
    public ExamineTypeCode getExamineTypeCodeByExamineType(Integer examineType) {
        final ExamineTypeCode record = new ExamineTypeCode();
        record.setHisSbTypeCode(examineType);
        return examineTypeCodeMapper.selectOne(record);
    }
}
