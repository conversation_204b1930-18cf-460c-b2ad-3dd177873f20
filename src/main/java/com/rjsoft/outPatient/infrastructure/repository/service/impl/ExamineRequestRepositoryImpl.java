package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.dictionary.AutoTranslate;
import com.rjsoft.common.utils.ObjectUtils;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.ExamineRequestRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 封装检查申请所有操作（MZYS,ZXHIS,RIS库共六张表数据）
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class ExamineRequestRepositoryImpl implements ExamineRequestRepository {
    ExamineRequestMapper examineRequestMapper;
    ExamineRequestDetailMapper examineRequestDetailMapper;
    ExamineRequestZxHisMapper examineRequestZxHisMapper;
    ExamineRequestDetailZxHisMapper examineRequestDetailZxHisMapper;
    ExamineRequestRisMapper examineRequestRisMapper;
    ExamineRequestDetailRisMapper examineRequestDetailRisMapper;
    ItemExamineTypeMapper itemExamineTypeMapper;
    RequestTypeMapper requestTypeMapper;
    RegisterListMapper registerListMapper;
    PatientDetailMapper patientDetailMapper;
    ExecDeptConfigMapper execDeptConfigMapper;
    ApplicationContentMapper applicationContentMapper;
    SysFunctionMapper sysFunctionMapper;
    ExecDeptConfigRepositoryImpl execDeptConfigRepositoryImpl;


    /**
     * 检查处方明细是否需要保存申请单（如果需要，返回申请类型，申请名称）
     *
     * @param recipeDetail
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    public RequestType checkNeedRequest(RecipeDetail recipeDetail) {
        //只有治疗，检查，摄片需要保存申请单
        if (!ItemCategoryEnum.Treatment.getCategoryCode().equals(recipeDetail.getFeeCategory()) &&
                !ItemCategoryEnum.Examination.getCategoryCode().equals(recipeDetail.getFeeCategory()) &&
                !ItemCategoryEnum.Filming.getCategoryCode().equals(recipeDetail.getFeeCategory())) {
            return null;
        }

        Integer requestType = itemExamineTypeMapper.getExamineTypeById(recipeDetail.getItemCode());
        if (requestType == null) {
            return null;
        }
        return requestTypeMapper.getRequestTypeById(requestType);
    }


    @Override
    @AutoTranslate
    @DatabaseAnnotation
    public ExamineRequest getExamineRequestByRecipe(ApplyDetail applyDetail, ApplyList applyList, RegisterList registerList) {
        //检查处方明细是否需要保存检查申请单
        if (applyDetail == null) {
            throw new RuntimeException("生成申请单数据失败，原因：未加载到申请单明细！");
        }
        ExamineRequest request = examineRequestMapper.getExamineRequestByExamineNo(applyDetail.getId(), applyDetail.getListId());
        if (request == null) {
            request = new ExamineRequest();
            request.setExamineNo(applyDetail.getListId());
            request.setExamineDetailNo(applyDetail.getId());
            request.setHospNo(registerList.getOutPatientNo());
            request.setExamineCode("");
            request.setExamineAppointmentNo("");
            if (applyList.getBigForm() == null) {
                throw new RuntimeException("未加载到申请单类型!");
            }
            request.setExamineType(Converter.toInt32(applyList.getBigForm().getCodeType()));
            request.setExamineTypeName(applyList.getBigForm().getCodeName());
            request.setCardNo(registerList.getCardNo());
            request.setHisCardNo(registerList.getOutPatientNo());
            request.setPatName(registerList.getPatName());
            request.setSex(Converter.toInt32(registerList.getPatSex()));
            request.setAge(Converter.toInt32(registerList.getPatAge()));
            request.setInRegNo(Converter.toString(registerList.getRegNo()));
            request.setHospZone(0);
            request.setHospBedNo("");
            request.setDiagnoseTime("");
            request.setClinicalDiagnose("");
            request.setExaminePart("");
            request.setExaminePartName("");
            request.setExamineItemCode(applyDetail.getItemCode());
            request.setExamineItemName(applyDetail.getItemName());
            request.setApplyCode(applyList.getBigForm().getCode());
            request.setApplyName(applyList.getBigForm().getName());
            request.setApplyDeptId(applyList.getApplyDeptId());
            //request.setApplyDeptName();
            request.setApplyDoctor(applyList.getApplyUserId());
            //request.setApplyDoctorName();
            request.setApplyDate(sysFunctionMapper.getDate());
            request.setApplyContent("");
            if (ObjectUtils.compare(applyList.getBigForm().getBigFormType(), 1)) {
                request.setFeeCategory(ItemCategoryEnum.Examination.getCategoryCode());
            } else if (ObjectUtils.compare(applyList.getBigForm().getBigFormType(), 2)) {
                request.setFeeCategory(ItemCategoryEnum.Laboratory.getCategoryCode());
            } else {
                request.setFeeCategory(ItemCategoryEnum.Treatment.getCategoryCode());
            }
            request.setSpecimen("0");
            request.setDataFlag(0);
            request.setStatus(0);
            request.setRequestDetailList(new ArrayList<>());
        } else {
            request.setDataFlag(1);
            ExamineRequestDetail entity = new ExamineRequestDetail();
            entity.setExamineNo(request.getExamineNo());
            List<ExamineRequestDetail> list = examineRequestDetailMapper.select(entity);
            list.forEach(examineRequestDetail -> {
                examineRequestDetail.setDataFlag(-1);
            });
            request.setRequestDetailList(list);
        }
        request.setHospitalCode(registerList.getHospitalCode());
        request.setDeleteFlag(false);
        request.setApplyGroupId(applyList.getGroupId());
        return request;
    }

    @Override
    @DatabaseAnnotation
    public List<ExamineRequest> getExamineRequestByIds(List<Integer> examineNos, Integer hospitalCode) {
        if (examineNos == null || examineNos.size() == 0) {
            return new ArrayList<>();
        }
        Weekend weekend = new Weekend(ExamineRequest.class);
        WeekendCriteria<ExamineRequest, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ExamineRequest::getExamineNo, examineNos);
        weekendCriteria.andEqualTo(ExamineRequest::getDeleteFlag, false);
        return examineRequestMapper.selectByExample(weekend);
    }

    @Override
    public List<ExamineRequest> getExamineRequestByDetailIds(List<Integer> examineDetailNos, Integer hospitalCode) {
        if (examineDetailNos == null || examineDetailNos.size() == 0) {
            return new ArrayList<>();
        }
        Weekend weekend = new Weekend(ExamineRequest.class);
        WeekendCriteria<ExamineRequest, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ExamineRequest::getExamineDetailNo, examineDetailNos);
        return examineRequestMapper.selectByExample(weekend);
    }

    @Override
    public List<ExamineRequest> getExamineRequestByGroupIds(List<Integer> groupIds, Integer hospitalCode) {
        if (groupIds == null || groupIds.size() == 0) {
            return new ArrayList<>();
        }
        Weekend weekend = new Weekend(ExamineRequest.class);
        WeekendCriteria<ExamineRequest, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ExamineRequest::getApplyGroupId, groupIds);
        weekendCriteria.andEqualTo(ExamineRequest::getDeleteFlag, false);
        return examineRequestMapper.selectByExample(weekend);
    }

    @Override
    public List<ExamineRequestDetail> getExamineRequestDetailNos(Integer examineDetailNo, Integer hospitalCode) {
        Weekend weekend = new Weekend(ExamineRequest.class);
        WeekendCriteria<ExamineRequestDetail, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ExamineRequestDetail::getExamineDetailNo, examineDetailNo);
        weekendCriteria.andEqualTo(ExamineRequestDetail::getDeleteFlag, false);
        weekend.setOrderByClause("cfmxlsh desc");
        return examineRequestDetailMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public ExamineRequestDetail getExamineRequestDetail(RecipeDetail recipeDetail, ExamineRequest request) {
        List<ExamineRequestDetail> requestDetails = request.getRequestDetailList().stream()
                .filter(p -> ObjectUtils.compare(p.getExamineDetailNo(), recipeDetail.getExamineDetailNo()) &&
                        ObjectUtils.compare(p.getItemCode(), recipeDetail.getItemCode())).collect(Collectors.toList());
        ExamineRequestDetail requestDetail = null;
        if (requestDetails.stream().count() > 0) {
            requestDetail = requestDetails.get(0);
        }
        if (requestDetail == null) {
            requestDetail = new ExamineRequestDetail();
            requestDetail.setExamineDetailNo(request.getExamineDetailNo());
            requestDetail.setExamineNo(Converter.toInt32(recipeDetail.getExamineNo()));
            requestDetail.setRisId(request.getRisId());
            requestDetail.setExaminePart("");
            requestDetail.setRecipeNo(recipeDetail.getRecipeNo());
            requestDetail.setRecipeId(recipeDetail.getRecipeId());
            requestDetail.setRecipeDetailNo(recipeDetail.getRecipeDetailNo());
            requestDetail.setRecipeDetailId(recipeDetail.getRecipeDetailId());
            requestDetail.setItemCode(recipeDetail.getItemCode());
            requestDetail.setItemName(recipeDetail.getItemName());
            requestDetail.setSfFlag(0);
            requestDetail.setReceptionNo(recipeDetail.getReceptionNo());
            requestDetail.setExaminePart("");
            requestDetail.setExaminePartName("");
            requestDetail.setQuantity(recipeDetail.getQuantity());
            requestDetail.setInspectSerialNo(Converter.toString(request.getExamineNo()));
            requestDetail.setDataFlag(0);
        } else {
            requestDetail.setQuantity(recipeDetail.getQuantity());
            requestDetail.setDataFlag(1);
        }
        requestDetail.setAmount(recipeDetail.getAmount());
        requestDetail.setDeleteFlag(false);
        return requestDetail;
    }


    /**
     * 保存检查申请单
     *
     * @param requestList
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean saveExamineRequest(List<ExamineRequest> requestList) {
        boolean success = false;
        for (ExamineRequest request : requestList) {
            if (request == null) {
                return false;
            }
            if (request.getDataFlag() == null || request.getDataFlag() == 0) {
                success = examineRequestMapper.insert(request) > 0;
                if (!success) {
                    return false;
                }
            } else {
                success = examineRequestMapper.updateByPrimaryKeySelective(request) > 0;
                if (!success) {
                    return false;
                }
            }

            for (ExamineRequestDetail requestDetail : request.getRequestDetailList()) {
                if (requestDetail.getDataFlag() == null || requestDetail.getDataFlag() == 0) {
                    success = examineRequestDetailMapper.insert(requestDetail) > 0;
                    if (!success) {
                        return false;
                    }
                } else if (requestDetail.getDataFlag() == -1) {
                    examineRequestDetailMapper.deleteByPrimaryKey(requestDetail);
                    success = true;
                } else {
                    success = examineRequestDetailMapper.updateByPrimaryKey(requestDetail) > 0;
                    if (!success) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 根据处方明细，生成检查申请记录
     *
     * @param recipeDetail
     * @return
     */
    @Override
    @AutoTranslate
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public ExamineRequestZxHis createExamineRequestZxHisByRecipe(RecipeDetail recipeDetail, RequestType requestType, Integer opCode) {
        //检查是否存在检查申请
        ExamineRequestZxHis request = examineRequestZxHisMapper.getExamineRequestZxHisByRecipeId(recipeDetail.getReceptionNo(), recipeDetail.getRecipeId(), requestType.getRequestType());
        if (request == null) {
            RegisterList registerList = registerListMapper.getRegisterByRegNo(recipeDetail.getRegNo(), recipeDetail.getHospitalCode(), null);
            if (registerList == null) {
                registerList = registerListMapper.getRegisterByRegNo(recipeDetail.getRegNo(), recipeDetail.getHospitalCode(), "Reg_Tb_RegisterList");
            }
            PatientDetail patientDetail = patientDetailMapper.getPatientDetail(Converter.toInt32(registerList.getPatID()), registerList.getHospitalCode());
            request = new ExamineRequestZxHis();
            request.setRecipeId(recipeDetail.getRecipeId());
            request.setExamineType(requestType.getRequestType());
            request.setExamineTypeName(requestType.getRequestName());
            request.setPatId(Converter.toInt32(registerList.getPatID()));
            request.setCardNo(registerList.getCardNo());
            request.setHisCardNo(registerList.getOutPatientNo());
            request.setPatName(registerList.getPatName());
            request.setSex(Converter.toInt32(registerList.getPatSex()));
            request.setPatAddress(patientDetail.getContactsRelationShipAddress());
            request.setHospNo(registerList.getOutPatientNo());
            request.setInRegNo(Converter.toString(registerList.getRegNo()));
            request.setApplyDoctor(opCode);
            request.setApplyDate(recipeDetail.getLastDate());
            request.setSfFlag(0);
            request.setApplyDeptId(registerList.getDeptID());
            request.setHospZone(registerList.getDeptID());
        }
        return request;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public ExamineRequestDetailZxHis getExamineRequestDetailZxHis(RecipeDetail recipeDetail, Integer risId, Integer opCode) {
        ExamineRequestDetailZxHis requestDetail = examineRequestDetailZxHisMapper.getExamineRequestDetailByRecipeDetailId(
                recipeDetail.getRecipeId(), recipeDetail.getRecipeDetailId(), risId);
        Date date = sysFunctionMapper.getDate();
        if (requestDetail == null) {
            requestDetail = new ExamineRequestDetailZxHis();
            requestDetail.setRecipeId(recipeDetail.getRecipeId());
            requestDetail.setRecipeDetailId(recipeDetail.getRecipeDetailId());
            requestDetail.setItemCode(recipeDetail.getItemCode());
            requestDetail.setItemName(recipeDetail.getItemName());
            requestDetail.setSfFlag(0);
            requestDetail.setQuantity(recipeDetail.getQuantity().intValue());
            requestDetail.setOperator(opCode);
            requestDetail.setCreateTime(date);
            requestDetail.setExaminePart(Converter.toString(recipeDetail.getExaminePartNo()));
            requestDetail.setExaminePartName("");
        }
        requestDetail.setUpdateTime(date);
        return requestDetail;
    }

    @Override
    public List<ExamineRequestRis> createExamineRequestRisByMzRequest(List<RecipeDetail> recipeDetails) {
        return null;
    }


    @Override
    public boolean saveExamineRequestZxHis(ExamineRequestZxHis request, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
        } else if (hospitalCode.equals(3)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
        }
        boolean success;
        boolean hasData = examineRequestZxHisMapper.existsWithPrimaryKey(request);
        if (hasData) {
            success = examineRequestZxHisMapper.updateByPrimaryKeySelective(request) > 0;
        } else {
            request.setApplyDate(new Date());
            success = examineRequestZxHisMapper.insert(request) > 0;
        }
        return success;
    }

    @Override
    public boolean saveExamineRequestRis(ExamineRequestRis request) {
        return false;
    }

    /**
     * 根据检查流水号和医院编码，修改检查申请单主表收费状态
     *
     * @param examineNos
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public void updateExamineChargeStatus(List<String> examineNos, Integer hospitalCode) {
        examineRequestMapper.updateExamineChargeStatus(examineNos, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public int deleteByExamineNoAndRecipeDetailIdAndReceptionNo(Integer examineNo, Long recipeDetailId, Long receptionNo) {
        final ExamineRequest entity = new ExamineRequest();
        entity.setDeleteFlag(true);
        Weekend weekend = new Weekend(ExamineRequest.class);
        WeekendCriteria<ExamineRequest, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ExamineRequest::getExamineNo, examineNo);
        weekendCriteria.andEqualTo(ExamineRequest::getRecipeDetailId, recipeDetailId);
        weekendCriteria.andEqualTo(ExamineRequest::getReceptionNo, receptionNo);
        return examineRequestMapper.updateByExampleSelective(entity, weekend);
    }
}
