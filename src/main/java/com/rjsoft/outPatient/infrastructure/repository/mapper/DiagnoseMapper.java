package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.InPatientResult;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatDianosisDTO;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo;
import com.rjsoft.outPatient.domain.prescriptionAudit.vo.AuditDiagnose;
import com.rjsoft.outPatient.infrastructure.repository.entity.Diagnose;
import com.ruijing.code.util.StringUtils;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;
import java.util.Set;

public interface DiagnoseMapper extends BaseMapper<Diagnose>, ExampleMapper<Diagnose> {


    /**
     * 查询诊断(本次就诊)
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    default List<Diagnose> getDiagnoseList(Integer receptionNo, Integer hospitalCode, String table) {
        Weekend<Diagnose> weekend = new Weekend<>(Diagnose.class);
        WeekendCriteria<Diagnose, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(Diagnose::getReceptionNo, receptionNo);
        criteria.andEqualTo(Diagnose::getHospitalCode, hospitalCode);
        if (!StringUtils.isEmpty(table)) {
            weekend.setTableName(table);
        }
        return selectByExample(weekend);
    }

    /**
     * 根据就诊流水号查询诊断
     *
     * @param receptionNo
     * @return
     */
    default List<Diagnose> getDiagnoseListByReceptionNo(Integer receptionNo){
        Weekend<Diagnose> weekend = new Weekend<>(Diagnose.class);
        WeekendCriteria<Diagnose, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(Diagnose::getReceptionNo, receptionNo);
        return selectByExample(weekend);
    }

    /**
     * 查询诊断
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    default List<Diagnose> getDiagnoseList(Set<Long> receptionNo, Integer hospitalCode, String table) {
        Weekend<Diagnose> weekend = new Weekend<>(Diagnose.class);
        WeekendCriteria<Diagnose, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(Diagnose::getReceptionNo, receptionNo);
        criteria.andEqualTo(Diagnose::getHospitalCode, hospitalCode);
        if (!StringUtils.isEmpty(table)) {
            weekend.setTableName(table);
        }
        return selectByExample(weekend);
    }

    /**
     * 查询患者当前诊断信息
     *
     * @param receptionNoSet
     * @return
     */
    List<PatDianosisDTO> getPatDianosis(@Param("receptionNoSet") Set<Long> receptionNoSet, @Param("hospitalCode") Integer hospitalCode);

    default Diagnose getDiagnoseBydiagnoseNo(Integer diagnoseNo, Integer hospCode) {
        Diagnose diagnose = new Diagnose();
        diagnose.setId(diagnoseNo);
        diagnose.setHospitalCode(hospCode);
        return selectOne(diagnose);
    }

    List<AuditDiagnose> getDiagnoseByReceptionNo(@Param("receptionNo") String receptionNo,
                                                 @Param("hospitalCode") String hospitalCode);

    /**
     * 根据患者编号列表，获取最后一次经复核的诊断信息
     *
     * @param regNos
     */
    List<Diagnose> getDiagnose(@Param("list") List<Long> regNos);

    /**
     * 添加患者历史诊断进诊断表
     */
    default void addHistoryDiagnose(List<Diagnose> diagnose) {
        for (Diagnose dia : diagnose) {
            insertSelective(dia);
        }
    }

    /**
     * 根据就诊流水号+医院编码+诊断编码获取诊断信息
     *
     * @param receptionNo
     * @param hospCode
     * @param diagnoseCode
     * @return
     */
    default Diagnose getDiagnoseByJzlshAndZdbm(String receptionNo, String hospCode, String diagnoseCode) {
        Weekend<Diagnose> weekend = new Weekend<>(Diagnose.class);
        weekend.weekendCriteria().andEqualTo(Diagnose::getReceptionNo, receptionNo)
                .andEqualTo(Diagnose::getHospitalCode, hospCode)
                .andEqualTo(Diagnose::getDiagnoseCode, diagnoseCode);
        return selectOneByExample(weekend);
    }

    /**
     * 根据就诊流水号列表获取所有诊断信息
     *
     * @param regNos
     * @param hospitalId
     */
    default List<Diagnose> getDiagnosesByRegNos(Set<Long> regNos, Integer hospitalId) {
        Weekend<Diagnose> weekend = new Weekend<>(Diagnose.class);
        WeekendCriteria<Diagnose, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(Diagnose::getHospitalCode, hospitalId)
                .andIn(Diagnose::getRegNo, regNos);
        return selectByExample(weekend);
    }

    /**
     * 根据就诊流水号，诊断编码获取诊断记录
     * @param receptionNo
     * @param diagnoseCode
     */
    List<DiagnoseRecordInfo> getNewDiagnose(@Param("receptionNo") String receptionNo,@Param("diagnoseCode") String diagnoseCode);

    /**
     * 根据诊断主键id获取诊断信息
     * @param diagnoseNos
     */
    default List<Diagnose> getDiagnoseByIds(List<Integer> diagnoseNos){
        Weekend<Diagnose> weekend = new Weekend<>(Diagnose.class);
        WeekendCriteria<Diagnose, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(Diagnose::getId,diagnoseNos)
        .andEqualTo(Diagnose::getDiagnoseMark,0);
        return selectByExample(weekend);
    }

    /**
     * 根据就诊流水号，诊断编码，医院编码，修改sbbj状态
     * @param receptionNo
     * @param diagnoseCode
     * @param hospitalCode
     * @param reportedStatus
     */
    default void updateDiagnoseReportedStatus(Long receptionNo,String diagnoseCode,Integer hospitalCode,Integer reportedStatus){
        Weekend<Diagnose> weekend = new Weekend<>(Diagnose.class);
        WeekendCriteria<Diagnose, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(Diagnose::getReceptionNo,receptionNo)
                .andEqualTo(Diagnose::getDiagnoseCode,diagnoseCode)
                .andEqualTo(Diagnose::getHospitalCode,hospitalCode);
        Diagnose diagnose = new Diagnose();
        diagnose.setReportedState(reportedStatus);
        updateByExampleSelective(diagnose,weekend);
    }

    /**
     * 根据挂号列表获取所有诊断信息
     *
     */
    List<Diagnose> queryDiagListByRegNos(@Param("list") List<Long> regNos, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据患者ID获取所有住院诊断信息
     *
     */
    List<InPatientResult> queryIoDiagListByPatId(@Param("list") List<Integer> patientIds, @Param("hospitalCode") Integer hospitalCode);

}
