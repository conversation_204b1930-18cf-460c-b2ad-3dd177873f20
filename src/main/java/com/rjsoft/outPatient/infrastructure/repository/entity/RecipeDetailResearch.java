package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 处方明细科研
 *
 * <AUTHOR>
 * @since 2021/8/18 - 15:06
 */
@Data
@Table(name = "MZYS_TB_MZCFMXKY")
public class RecipeDetailResearch implements Serializable {

    /**
     * id
     */
    @Id
    @Column(name = "ID", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;
    /**
     * 就诊流水号
     */
    @Column(name = "jzlsh")
    private Long receptionNo;
    /**
     * 处方流水号
     */
    @Column(name = "cflsh")
    private Long recipeNo;
    /**
     * 处方明细流水号
     */
    @Column(name = "cfmxlsh")
    private Long recipeDetailNo;
    /**
     * 收费项目名称
     */
    @Column(name = "xmmc")
    private String itemName;
    /**
     * 收费项目编码
     */
    @Column(name = "xmbm")
    private String itemCode;
    /**
     *
     */
    @Column(name = "FPlanId")
    private Integer fPlanId;
    /**
     *
     */
    @Column(name = "PrimaryKey")
    private Integer primaryKey;
    /**
     * 状态
     */
    @Column(name = "State")
    private Integer state;
    /**
     * 类型
     */
    @Column(name = "Type")
    private Integer type;
    /**
     * 报告状态
     */
    @Column(name = "ReportState")
    private Integer reportState;
    /**
     *
     */
    @Column(name = "sqh")
    private String sqh;
    /**
     * 创建人id
     */
    @Column(name = "CreateID")
    private String createId;
    /**
     * 创建时间
     */
    @Column(name = "CreateDate")
    private Date createDate;
    /**
     * 删除人id
     */
    @Column(name = "RemovedID")
    private String removedId;
    /**
     * 删除时间
     */
    @Column(name = "RemovedDate")
    private Date removedDate;
    /**
     * 删除标识
     */
    @Column(name = "IsDeleted")
    private Integer isDeleted;
    /**
     * 外部计划
     */
    @Column(name = "OutsidePlan")
    private Integer outsidePlan;
    /**
     * 项目编号
     */
    @Column(name = "ProjectID")
    private String projectId;
    /**
     * 项目名称
     */
    @Column(name = "ProjectName")
    private String projectName;
    /**
     * 患者编码
     */
    @Column(name = "PatNo")
    private Integer patNo;
    /**
     * 挂号流水号
     */
    @Column(name = "RegNo")
    private Integer regNo;

    /**
     * 医院编码
     */
    @Column(name = "hospitalCode")
    private Integer hospitalCode;

}
