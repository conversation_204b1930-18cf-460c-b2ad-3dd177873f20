<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.EmrblMapper">

    <select id="queryDetail" resultType="java.lang.String">
        select BlValue
        from tbt_Emr_Bl (nolock)
        where RegNo = #{regNo}
          and BcDmno = #{bcDmno}
          and datediff(day,RecDate, #{recDate}) = 0
          and IsConfirm = 1;
    </select>

    <select id="getBlDetail" resultType="java.lang.String">
        SELECT
            b.content
        FROM
            hisdb..EMR_Tb_RecInfo a (nolock)
                INNER JOIN EMR_Data..EMR_Tb_RecInfo_Content b (nolock) ON a.RecId = b.RecId
        WHERE
            a.DelFlag = 0
          AND a.RegNo = #{regNo}
          AND a.RecTempCode = #{bcDmno}
          AND a.hospitalId = #{hospitalCode}
          AND CONVERT ( VARCHAR ( 10 ), a.RecDate, 120 ) = #{recDate}
          AND a.RecStatus = 1
    </select>
</mapper>
