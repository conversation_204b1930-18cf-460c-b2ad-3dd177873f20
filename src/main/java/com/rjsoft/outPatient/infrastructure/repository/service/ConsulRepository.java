package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.Consul;
import com.rjsoft.outPatient.infrastructure.repository.entity.ConsulDept;
import com.rjsoft.outPatient.infrastructure.repository.entity.ConsulType;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/10 - 9:45
 */
public interface ConsulRepository {


    /**
     * 查询患者当前就诊会诊信息
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    Consul queryConsulList(Long receptionNo, Integer hospitalCode);


    /**
     * 查询患者会诊信息
     *
     * @param regNo
     * @param patId
     * @param hospitalCode
     * @return
     */
    Consul queryConsul(Long regNo, Long patId, Integer hospitalCode);


    /**
     * 根据会诊ID查询会诊列表
     *
     * @param consulId
     * @param hospitalCode
     * @return
     */
    Consul queryConsulByConsulId(Long consulId, Integer hospitalCode);


    /**
     * 根据挂号流水号查询会诊列表
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    Consul queryConsulByRegNo(Long regNo, Integer hospitalCode);

    /**
     * 根据挂号流水号查询会诊列表
     * @param regNos
     * @param hospitalCode
     * @return
     */
    List<Consul>queryConsulByRegNos(List<Long> regNos, Integer hospitalCode);

    /**
     * 查询
     *
     * @param regNo        挂号流水号
     * @param hospitalCode 医院编码
     * @param deptId       科室编码
     * @return Consul
     */
    Consul queryConsulByDeptId(Long regNo, Integer hospitalCode, Integer deptId);


    /**
     * 查询
     *
     * @param regNo        挂号流水号
     * @param hospitalCode 医院编码
     * @param workerId     医生编码
     * @return Consul
     */
    Consul queryConsulByWorkerId(Long regNo, Integer hospitalCode, Integer workerId);


    /**
     * 保存
     *
     * @param consul {@link Consul}
     * @return 受影响行数
     */
    Boolean saveConsul(Consul consul);


    /**
     * 修改
     *
     * @param consul {@link Consul}
     * @return 受影响行数
     */
    Boolean updateConsul(Consul consul);


    /**
     * 删除
     *
     * @param consul {@link Consul}
     * @return 受影响行数
     */
    Boolean deleteConsul(Consul consul);


    /**
     * 查询会诊申请科室列表
     *
     * @param deptId       科室id  可以为null
     * @param hospitalCode 医院编码
     * @return List<ConsulDept>
     */
    List<ConsulDept> queryConsulDeptList(Integer deptId, Integer hospitalCode);


    /**
     * 查询会诊类型列表
     * id为null则查询全部
     *
     * @param id           唯一标识符
     * @param hospitalCode 医院编码
     * @return {@link List<ConsulType>}
     */
    List<ConsulType> queryConsulTypeList(Integer id, Integer hospitalCode);

}
