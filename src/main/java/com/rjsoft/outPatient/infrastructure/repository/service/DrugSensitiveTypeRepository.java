package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.DrugSensitiveType;

/**
 * <AUTHOR>
 * @since 2021/7/27 - 10:23
 */
public interface DrugSensitiveTypeRepository {

    /**
     * 根据编码查询过敏药物类型名称
     *
     * @param code 编码
     * @return 过敏药物名称
     */
    String getDrugSensitiveTypeNameByCode(Integer code);

    /**
     * 根据编码查询 药物过敏类型 for {@link DrugSensitiveType}
     *
     * @param code 编码
     * @return {@link DrugSensitiveType}
     */
    DrugSensitiveType getDrugSensitiveType(Integer code);

}
