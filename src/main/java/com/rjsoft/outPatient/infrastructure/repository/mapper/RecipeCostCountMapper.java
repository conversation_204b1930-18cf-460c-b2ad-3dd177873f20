package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeCostCount;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2021/7/28 - 9:13
 */
public interface RecipeCostCountMapper extends BaseMapper<RecipeCostCount>, ExampleMapper<RecipeCostCount> {

    /**
     * 根据医生编码，日期范围，查询自费病人人均次费用
     *
     * @param doctorId 医生编码
     * @param start    开始日期
     * @param end      结束日期
     * @return 病人人均次费用
     */
    BigDecimal getZfPerCapitaCost(@Param("doctorId") int doctorId, @Param("start") LocalDate start, @Param("end") LocalDate end);


}
