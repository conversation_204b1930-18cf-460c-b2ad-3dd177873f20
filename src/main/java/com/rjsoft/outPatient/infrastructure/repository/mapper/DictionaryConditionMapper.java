package com.rjsoft.outPatient.infrastructure.repository.mapper;


import com.rjsoft.outPatient.infrastructure.repository.entity.DictionaryCondition;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;


import java.util.List;

public interface DictionaryConditionMapper extends BaseMapper<DictionaryCondition>, ExampleMapper<DictionaryCondition> {


    /**
     * 查询状态类诊断信息
     *
     * @param hospitalCode
     * @return
     */
    default List<DictionaryCondition> getDictionaryCondition(Integer hospitalCode) {
        DictionaryCondition dictionaryCondition = new DictionaryCondition();
        return select(dictionaryCondition);
    }

}
