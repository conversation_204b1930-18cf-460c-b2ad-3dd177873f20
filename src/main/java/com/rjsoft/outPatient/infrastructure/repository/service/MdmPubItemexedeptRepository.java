package com.rjsoft.outPatient.infrastructure.repository.service;

import com.github.pagehelper.PageInfo;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.domain.execDept.dto.ItemExecDeptAddDto;
import com.rjsoft.outPatient.domain.execDept.dto.ItemExecDeptSelectDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubItemexedept;

import java.util.HashMap;
import java.util.List;

public interface MdmPubItemexedeptRepository {
    /**
     * 查询项目默认执行科室列表
     *
     * @param itemExecDeptDto
     * @return
     */
    PageInfo<MdmPubItemexedept> getItemExecDeptItemList(ItemExecDeptSelectDto itemExecDeptDto);

    /**
     * 新增项目默认执行科室列表
     *
     * @param itemExecDeptAddDto
     * @return
     */
    int addItem(ItemExecDeptAddDto itemExecDeptAddDto);

    /**
     * 查询是否有重复的项目
     *
     * @param itemExecDeptAddDto
     * @return
     */
    List<HashMap<String,String>> getItemExecDeptItem(ItemExecDeptAddDto itemExecDeptAddDto,Integer id);

    /**
     * 更新项目默认执行科室列表
     *
     * @param itemExecDeptAddDto
     * @return
     */
    int updateItem(ItemExecDeptAddDto itemExecDeptAddDto);

    /**
     * 删除项目默认执行科室列表
     *
     * @param id
     * @return
     */
    int deleteItem(Integer id);

    /**
     * 当前时间段内，项目可执行科室
     * @param itemCode
     * @param curTime
     * @param curDeptId
     * @param curHospitalId
     * @return
     */
    List<MdmPubItemexedept> getItemExecDeptByTime(String itemCode, String curTime, Integer curDeptId, Integer curHospitalId);

    List<MdmPubItemexedept> getItemExecDeptByTimeItemCodeList(List<Integer> itemCodeList, String curTime, Integer curDeptId, Integer curHospitalId);

    /**
     * 当前时间段内，可执行科室列表
     * @param curTime
     * @param curDeptId
     * @param curHospitalId
     * @return
     */
    List<MdmPubItemexedept> getExecDeptList(String curTime, Integer curDeptId, Integer curHospitalId);
}
