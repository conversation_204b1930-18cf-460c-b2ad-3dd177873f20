<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.TimeSpanExtMapper">

    <select id="GetTimeSpanExt" resultType="java.lang.String">
        SELECT top(1) Name FROM TB_Dic_TimeSpanExt
        where
                    CONVERT(varchar(10),getdate(),120)+' '+EndTime&gt;'' + CONVERT(varchar(30), GETDATE(), 120)+''
          and CONVERT(varchar(10),getdate(),120)+' '+StartTime&lt;''+CONVERT(varchar(30), GETDATE(), 120)+''
          and IsDelete=0
          and IsUse=1
        order by StartTime desc
    </select>


</mapper>