package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubMultidisciplinary;
import com.rjsoft.outPatient.infrastructure.repository.entity.MenuByPrescription;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

public interface MultidisciplinaryMapper extends BaseMapper<MdmPubMultidisciplinary>, ExampleMapper<MdmPubMultidisciplinary> {
    public List<MdmPubMultidisciplinary> getMultidisciplinaryList(String name, Integer hospitalId, String state);
}
