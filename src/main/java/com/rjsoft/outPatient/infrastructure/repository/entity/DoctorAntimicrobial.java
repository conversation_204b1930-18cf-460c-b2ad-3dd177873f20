package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;
import net.sf.jsqlparser.expression.DateTimeLiteralExpression;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 医生抗菌药权限信息
 *
 * <AUTHOR>
@Data
@Table(name = "System_Tb_Doctor_Antimicrobial_Authority")
public class DoctorAntimicrobial implements Serializable {

    @Column(name = "serial_no")
    private Integer serialNo;

    @Column(name = "worker_id")
    private Integer workerId;

    @Column(name = "worker_name")
    private String workerName;

    @Column(name = "dept_Id")
    private Integer deptId;

    @Column(name = "dept_name")
    private String deptName;

    @Column(name = "position_code")
    private String positionCode;

    @Column(name = "position_name")
    private String positionName;

    @Column(name = "antimicrobial_grade")
    private String antimicrobialGrade;

    @Column(name = "antimicrobial_name")
    private String antimicrobialName;

    @Column(name = "antibacterial_code")
    private String antibacterialCode;

    @Column(name = "antibacterial_name")
    private String antibacterialName;

    @Column(name = "status")
    private Integer status;

    @Column(name = "create_user_id")
    private Integer createUserId;

    @Column(name = "create_on")
    private Date createOn;

    @Column(name = "update_user_id")
    private Integer updateUserId;

    @Column(name = "update_on")
    private Date updateOn;

    @Column(name = "hospital_id")
    private Integer hospitalId;

}
