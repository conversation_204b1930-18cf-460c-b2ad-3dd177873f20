package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetailResearch;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeDetailResearchMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeDetailResearchRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/18 - 15:27
 */
@Service
@AllArgsConstructor
public class RecipeDetailResearchRepositoryImpl implements RecipeDetailResearchRepository {

    private final RecipeDetailResearchMapper recipeDetailResearchMapper;

    @Override
    @DatabaseAnnotation
    public RecipeDetailResearch getByRecipeDetailNo(Long recipeDetailNo, Integer hospitalCode) {
        final RecipeDetailResearch entity = new RecipeDetailResearch();
        entity.setRecipeDetailNo(recipeDetailNo);
        entity.setHospitalCode(hospitalCode);
        return recipeDetailResearchMapper.selectOne(entity);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetailResearch> getByRecipeDetailNoList(List<Long> recipeDetailNoList, Integer hospitalCode) {
        Weekend<RecipeDetailResearch> weekend = new Weekend<>(RecipeDetailResearch.class);
        weekend.weekendCriteria().andIn(RecipeDetailResearch::getRecipeDetailNo, recipeDetailNoList)
                .andEqualTo(RecipeDetailResearch::getHospitalCode, hospitalCode);
        return recipeDetailResearchMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetailResearch> getByItemCodeAndPatId(List<String> itemCodeList, Integer patId, String projectId) {
        Weekend<RecipeDetailResearch> weekend = new Weekend<>(RecipeDetailResearch.class);
        weekend.weekendCriteria().andEqualTo(RecipeDetailResearch::getPatNo,patId).andEqualTo(RecipeDetailResearch::getProjectId,projectId).andIn(RecipeDetailResearch::getItemCode,itemCodeList);
        return recipeDetailResearchMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public boolean insert(RecipeDetailResearch recipeDetailResearch) {
        return recipeDetailResearchMapper.insertSelective(recipeDetailResearch)>0;
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetailResearch> getByReceptionNoAndItemCodes(Long receptionNo, List<String> itemCodeList, Integer hospitalCode) {
//        if(itemCodeList!=null&&itemCodeList.size()>0){
//            Weekend<RecipeDetailResearch> weekend = new Weekend<>(RecipeDetailResearch.class);
//            weekend.weekendCriteria().andEqualTo(RecipeDetailResearch::getReceptionNo,receptionNo)
//                    .andIn(RecipeDetailResearch::getItemCode,itemCodeList)
//                    .andEqualTo(RecipeDetailResearch::getHospitalCode,hospitalCode)
//                    .andEqualTo(RecipeDetailResearch::getIsDeleted,0);
//            return recipeDetailResearchMapper.selectByExample(weekend);
//        }else{
//            return null;
//        }
        List<RecipeDetailResearch> list = this.recipeDetailResearchMapper.getByReceptionNoAndItemCodes(receptionNo,itemCodeList,hospitalCode);
        return list;
    }

    @Override
    @DatabaseAnnotation

    public int updateRecipeDetailResearch(Long receptionNo, List<String> recipeDetailIds, Integer hospitalCode) {
        return this.recipeDetailResearchMapper.updateRecipeDetailResearch(receptionNo, recipeDetailIds, hospitalCode);
    }
}
