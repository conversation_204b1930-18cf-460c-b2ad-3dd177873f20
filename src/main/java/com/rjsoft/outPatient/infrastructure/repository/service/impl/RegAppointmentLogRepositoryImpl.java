package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegAppointmentLog;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RegAppointmentLogMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RegAppointmentLogRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

@Service
@AllArgsConstructor
public class RegAppointmentLogRepositoryImpl implements RegAppointmentLogRepository {
    private RegAppointmentLogMapper regAppointmentLogMapper;
    @Override
    @DatabaseAnnotation
    public PageInfo<RegAppointmentLog> getList(String cardNo, String beginTime, String endTime, Integer type, Integer hsopitalId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        Weekend<RegAppointmentLog> weekend = new Weekend<>(RegAppointmentLog.class);
        WeekendCriteria<RegAppointmentLog, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(RegAppointmentLog::getHospitalId,hsopitalId).andEqualTo(RegAppointmentLog::getType,type);
        if(!StringUtils.isEmpty(beginTime)&&!StringUtils.isEmpty(endTime)){
            weekendCriteria.andBetween(RegAppointmentLog::getCreateTime,beginTime,endTime);
        }
        if(!StringUtils.isEmpty(cardNo)){
            weekendCriteria.andEqualTo(RegAppointmentLog::getCardNo,cardNo);
        }
        List<RegAppointmentLog> regAppointmentLogList = regAppointmentLogMapper.selectByExample(weekend);
        return new PageInfo<>(regAppointmentLogList);
    }

    @Override
    @DatabaseAnnotation
    public int insertLog(RegAppointmentLog regAppointmentLog) {
        return regAppointmentLogMapper.insertSelective(regAppointmentLog);
    }
}
