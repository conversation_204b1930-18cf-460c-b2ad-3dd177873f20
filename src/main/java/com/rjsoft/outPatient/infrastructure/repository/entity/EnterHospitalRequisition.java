package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rjsoft.common.dictionary.AutoTranslateDict;
import com.rjsoft.outPatient.common.DicTypeConst;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * 住院申请单
 *
 * <AUTHOR>
 */

@Data
@Table(name = "MZYS_TB_MZZYSQD")
public class EnterHospitalRequisition implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id")
    private Integer id;
    /**
     * 就诊流水号
     */
    @Column(name = "JZLSH")
    private Long receptionNo;
    /**
     * 姓名
     */
    @Column(name = "XM")
    private String name;
    /**
     * 性别
     */
    @Column(name = "XB")
    @AutoTranslateDict(target = "sexName", hospital = "hospitalCode", dictType = DicTypeConst.SEX)
    private Integer sex;

    /**
     *
     */
    @Transient
    private String sexName;
    /**
     * 门诊号
     */
    @Column(name = "MZH")
    private String hisCardNo;
    /**
     * 卡号
     */
    @Column(name = "KH")
    private String cardNo;
    /**
     * 证件号
     */
    @Column(name = "ZJH")
    private String certificateNo;
    /**
     * 证件类型
     */
    @Column(name = "ZJLX")
    private String certificateType;

    @Column(name = "CWYS")
    private Integer CWYS;
    /**
     * 操作人
     */
    @Column(name = "CZR")
    private Integer opCode;
    /**
     * 操作时间
     */
    @Column(name = "CZSJ")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate opTime;
    /**
     * 申请单内容
     */
    @Column(name = "SQDXML")
    private String content;
    /**
     * 科室编码
     */
    @Column(name = "deptId")
    @AutoTranslateDict(target = "deptName", hospital = "hospitalCode", dictType = DicTypeConst.DEPT)
    private Integer deptId;
    /**
     * 科室名称
     */
    @Column(name = "deptName")
    private String deptName;
    /**
     * 诊断编码
     */
    @Column(name = "ZDBM")
    private String diagnosticCode;
    /**
     * 诊断名称
     */
    @Column(name = "ZDMC")
    private String diagnosticName;
    /**
     * 入院时间
     */
    @Column(name = "inHospTime")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inHospTime;
    /**
     * 身份证号
     */
    @Column(name = "SFZH")
    private String patSfz;
    /**
     * 联系电话
     */
    @Column(name = "SJHM")
    private String phone;
    /**
     * 状态
     */
    @Column(name = "status")
    private Integer status;
    /**
     * 实际入院时间
     */
    @Column(name = "SJRYSJ")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualInHospTime;
    /**
     * 病床
     */
    @Column(name = "bed")
    private String bed;
    /**
     * 审核人
     */
    @Column(name = "reviewDoctor")
    private Integer reviewDoctor;
    /**
     * 审核时间
     */
    @Column(name = "reviewTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reviewTime;
    /**
     * 创建人
     */
    @Column(name = "createId")
    private Integer createId;
    /**
     * 创建时间
     */
    @Column(name = "createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 医院编码
     */
    @Column(name = "HospitalCode")
    private Integer hospitalCode;

    /**
     * 医生开检查检验
     */
    @Column(name = "issueInspectStatus")
    private Boolean issueInspectStatus;

    /**
     * 医生开告知单
     */
    @Column(name = "noticeStatus")
    private Boolean noticeStatus;

    /**
     * 医生查检查检验
     */
    @Column(name = "queryInspectStatus")
    private Boolean queryInspectStatus;

    /**
     * 医生填风险评估单
     */
    @Column(name = "riskStatus")
    private Boolean riskStatus;
    /**
     * 医生填风险评估单
     */
    @Column(name = "RisSignStatus")
    private Boolean risSignStatus;

    /**
     * 患者签名
     */
    @Column(name = "patSignStatus")
    private Boolean patSignStatus;

    /**
     * 出入院审核
     */
    @Column(name = "ioStatus")
    private Boolean ioStatus;

    /**
     * 病区审核
     */
    @Column(name = "wardStatus")
    private Boolean wardStatus;

    /**
     * 办理入院
     */
    @Column(name = "handleStatus")
    private Boolean handleStatus;

    /**
     * 病假单内容
     */
    @Column(name = "BJDNR")
    private String sickLeaveContent;

    /**
     * 入院科室
     */
    @Column(name = "IoDeptId")
    private Integer ioDeptId;

    /**
     * 入院科室名称
     */
    @Column(name = "IoDeptName")
    private String ioDeptName;

    /**
     * 入院医院编码
     */
    @Column(name = "IoHospitalCode")
    private Integer ioHospitalCode;

    /**
     * 入院类型，0：自愿、1：非自愿、2：紧急
     */
    @Column(name = "IoType")
    private Integer ioType;

    /**
     * 申请科室id
     */
    @Column(name = "reqDeptId")
    private Integer reqDeptId;

    /**
     * 申请科室名称
     */
    @Column(name = "reqDeptName")
    private String reqDeptName;

    /**
     * 提交入院申请，0：否，1：是
     */
    @Column(name = "SubmitStatus")
    private Integer submitStatus;

    /**
     * 患者id
     */
    @Column(name = "patId")
    private Integer patientId;

    /**
     * 患者编号
     */
    @Transient
    private Integer patId;

    /**
     * 状态名称
     */
    @Transient
    private String statusName;

    /**
     * 入院类型
     */
    @Transient
    private String isExemptName;

    /**
     * 入院类型
     */
    @Transient
    private String docName;

    /**
     *收费类型
     */
    @Transient
    @AutoTranslateDict(target = "chargeType",hospital = "hospitalCode",dictType = DicTypeConst.CHARGETYPE)
    private String chargeType;

    @Transient
    private Long regNo;

}
