package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.SciRegisterlistProject;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SciRegisterlistProjectMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.SciRegisterlistProjectRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class SciRegisterlistProjectRepositoryImpl implements SciRegisterlistProjectRepository {
    private SciRegisterlistProjectMapper sciRegisterlistProjectMapper;

    @Override
    public boolean insertSciRegisterlistProject(SciRegisterlistProject sciRegisterlistProject) {
        return sciRegisterlistProjectMapper.insertSelective(sciRegisterlistProject)>0;
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public int updateSciRegisterlistProject(String projectId, Long regNo, String followup, String followupTitle, Integer hospitalCode) {
        return sciRegisterlistProjectMapper.updateSciRegisterlistProject(projectId, regNo, followup, followupTitle, hospitalCode);
    }
}
