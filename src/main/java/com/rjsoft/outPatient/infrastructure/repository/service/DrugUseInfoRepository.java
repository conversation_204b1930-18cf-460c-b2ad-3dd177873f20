package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.recipe.vo.DrugUseInfoVO;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugUseInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.Recipe;

/**
 * <AUTHOR>
 * @since 2023/5/30
 */
public interface DrugUseInfoRepository {
    Boolean isExistsDrugUseInfo(Long receptionNo, Long recipeDetailNo,Integer itemCode, Integer hospitalCode);

    boolean saveDrugUseInfo(DrugUseInfo drugUseInfo);

    boolean saveDrugUseInfo(DrugUseInfoVO drugUseInfoVO);
}
