package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.CaseTemp;
import com.rjsoft.outPatient.infrastructure.repository.entity.CaseTempFileIndex;
import org.apache.ibatis.annotations.Param;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface CaseTempMapper extends BaseMapper<CaseTemp>, ExampleMapper<CaseTemp> {


    /**
     * 修改其他为非默认
     *
     * @param firstDiagnosisFlag 初复诊
     * @return
     */
    int updateDeptOtherNotDefault(@Param("firstDiagnosisFlag") Integer firstDiagnosisFlag,
                                  @Param("deptId") Integer deptId,
                                  @Param("hospCode") Integer hospCode);

    int updateResearchOtherNotDefault(@Param("projectId") Integer projectId,
                                      @Param("firstDiagnosisFlag") Integer firstDiagnosisFlag,
                                      @Param("hospCode") Integer hospCode);

    default List<CaseTemp> queryTempInfoList(Integer hospCode, Integer tempType, Integer type, String tempName) {
        Weekend<CaseTemp> weekend = new Weekend<>(CaseTemp.class);
        WeekendCriteria<CaseTemp, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(CaseTemp::getHospCode, hospCode);
        if (!StringUtils.isEmpty(tempName)) {
            weekendCriteria.andLike(CaseTemp::getTempName, "%" + tempName + "%");
        }
        if (tempType != null) {
            if (tempType == 0) {
                //查询基础模板
                weekendCriteria.andEqualTo(CaseTemp::getTempType, 0);
            } else {
                //查询科研
                weekendCriteria.andEqualTo(CaseTemp::getTempType, 1);
            }
        }
        if(type != null){
            weekendCriteria.andEqualTo(CaseTemp::getType, type);
        }
        weekendCriteria.andNotEqualTo(CaseTemp::getDelStatus, "1");
        return selectByExample(weekend);
    }

    default int judgeTempCode(String tempCode, Integer hospCode, Integer tempId) {
        Weekend<CaseTemp> weekend = new Weekend<>(CaseTemp.class);
        WeekendCriteria<CaseTemp, Object> criteria = weekend.weekendCriteria();
        if (tempId != null) {
            criteria.andNotEqualTo(CaseTemp::getTempId, tempId);
        }
        criteria.andEqualTo(CaseTemp::getTempCode, tempCode);
        criteria.andEqualTo(CaseTemp::getHospCode, hospCode);
        return selectCountByExample(weekend);
    }
}
