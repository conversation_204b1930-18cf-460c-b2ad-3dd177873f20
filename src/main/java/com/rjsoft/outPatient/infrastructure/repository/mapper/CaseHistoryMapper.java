package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.outPatient.domain.caseHistory.dto.*;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public interface CaseHistoryMapper extends BaseMapper<CaseHistory>, ExampleMapper<CaseHistory> {

    /**
     * 根据挂号流水号集合，获取病历状态
     *
     * @param regNo
     * @return
     */
    default List<CaseHistory> getCaseHistoryStatusByRegNo(List<Long> regNo) {
        if (regNo == null || (long) regNo.size() == 0) {
            return new ArrayList<>();
        }
        ArrayList<CaseHistory> list = new ArrayList<>();
        if (regNo.size() > 2000) {
            int count = 2000;
            int batch = regNo.size() / count;
            if (regNo.size() % count != 0) {
                batch++;
            }
            for (int i = 0; i < batch; i++) {
                int start = i * count;
                int end = (i + 1) * count;
                if (end >= regNo.size()) {
                    end = regNo.size();
                }
                List<Long> extraRegNo = regNo.subList(start, end);
                Example example = new Example(CaseHistory.class);
                example.selectProperties("regNo", "submitFlag", "submitDate");
                Example.Criteria criteria = example.createCriteria();
                criteria.andIn("regNo", extraRegNo);
                list.addAll(selectByExample(example));
            }
        } else {
            Example example = new Example(CaseHistory.class);
            example.selectProperties("regNo", "submitFlag", "submitDate");
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("regNo", regNo);
            list.addAll(selectByExample(example));
        }
        return list;
    }

    /**
     * 根据挂号流水号机组所有病历
     *
     * @param regNos
     * @param tableName
     * @param columns
     * @return
     */
    default List<CaseHistory> getCaseHistoryByRegNos(Date startTime, Date endTime, List<Long> regNos, String tableName, String... columns) {
        Weekend<CaseHistory> weekend = new Weekend<>(CaseHistory.class);
        if (columns != null && columns.length > 0) {
            weekend.selectProperties(columns);
        }
        weekend.setTableName(tableName);
        WeekendCriteria<CaseHistory, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(CaseHistory::getRegNo, regNos);
        if (endTime != null) {
            weekendCriteria.andLessThanOrEqualTo(CaseHistory::getCreateTime, endTime);
        }
        if (startTime != null) {
            weekendCriteria.andGreaterThanOrEqualTo(CaseHistory::getCreateTime, startTime);
        }
        return selectByExample(weekend);
    }

    /**
     * 根据挂号流水号机组所有互联网病历ID
     *
     * @param startTime
     * @param endTime
     * @param regNos
     * @return
     */
    List<InternetCaseHistory> getInternetCaseHistoryIdByRegNos(@Param("startTime") Date startTime, @Param("endTime") Date endTime, List<Long> regNos);


    /**
     * 根据挂号流水号机组所有互联网病历
     *
     * @param internetRegNos
     * @return
     */
    List<InternetCaseHistory> getInternetCaseHistoryByRegNos(@Param("hospitalCode") Integer hospitalCode, @Param("internetRegNos") List<String> internetRegNos);

    @Select("SELECT bl.query('XTextDocument/BodyText').value('(/BodyText)[1]','nvarchar(max)') FROM  MZYS_TB_DZBL WHERE jzlsh= #{medicalNum}")
    String getCaseHistoryBlByMedicalNum(@Param("medicalNum") Integer medicalNum);


    /**
     * 根据病历id获取电子病历
     *
     * @param blId
     * @return
     */
    List<CaseHistoryVO> getCaseHistoryById(@Param("blId") int blId);


    /**
     * 获取病历打印状态
     *
     * @param blId
     * @return
     */
    @Select("select id as blId,dybj as printStatus from MZYS_TB_DZBL where id =#{blId}")
    HashMap getPrintStatus(@Param("blId") int blId);

    /**
     * 根据就诊流水号获取
     *
     * @param jzlsh
     * @return
     */
    List<CaseHistoryVO> queryDzblFromMzys(int jzlsh);


    /**
     * 查询主诉信息
     *
     * @param patId
     * @param hospitalCode
     * @return
     */
    List<PatChiefDTO> queryPatChief(@Param("patId") int patId, @Param("hospitalCode") int hospitalCode);


    /**
     * 加载本次病历
     *
     * @param receptionNo
     * @return
     */
    default CaseHistory loadReviewCaseFromNow(Integer receptionNo) {
        CaseHistory entity = new CaseHistory();
        entity.setReceptionNo(Converter.toString(receptionNo));
        List<CaseHistory> list = select(entity);
        if (list.stream().count() == 0) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 加载最新一条病历
     *
     * @param regNo
     * @param receptionNo
     * @param hospitalCode
     * @param tableName
     * @return
     */
    default List<CaseHistory> loadReviewCaseFromNew(List<Long> regNo, Integer receptionNo, Integer hospitalCode, String tableName) {
        Weekend weekend = new Weekend(CaseHistory.class);
        WeekendCriteria<CaseHistory, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(CaseHistory::getRegNo, regNo);
        weekendCriteria.andNotEqualTo(CaseHistory::getReceptionNo, receptionNo);
        weekendCriteria.andEqualTo(CaseHistory::getFirstDiagnosisFlag, 0);
        weekendCriteria.andEqualTo(CaseHistory::getHospitalCode, hospitalCode);
        weekend.setTableName(tableName);
        return selectByExample(weekend);
    }

    default List<CaseHistory> loadReviewCaseFromOld(String outPatientNo, Integer deptId) {
        Weekend<CaseHistory> weekend = new Weekend<>(CaseHistory.class);
        weekend.weekendCriteria().andEqualTo(CaseHistory::getBlCardNo, outPatientNo)
                .andEqualTo(CaseHistory::getDeptId, deptId)
                .andEqualTo(CaseHistory::getFirstDiagnosisFlag, 0);
        //老病历表没有挂号流水号字段
        //排除新老系统类型不一致的字段
        weekend.excludeProperties("regNo", "tempCode", "hospitalCode", "opCode", "opTime", "blId", "patientId", "receptionNo", "blHtml");
        weekend.setOrderByClause("cjrq desc");
        return selectByExample(weekend);
    }

    default CaseHistory getCaseHistoryByReceptionNo(Integer receptionNo, Integer hospCode) {
        CaseHistory caseHistory = new CaseHistory();
        caseHistory.setReceptionNo(Converter.toString(receptionNo));
        caseHistory.setHospitalCode(hospCode);
        return selectOne(caseHistory);
    }

    /**
     * 根据就诊流水号查询病历记录，不包括病历内容
     *
     * @param receptionNo
     * @param hospCode
     * @return
     */
    default CaseHistory getCaseHistoryByReceptionNoNoContent(String receptionNo, Integer hospCode, Integer dataSources) {
        Weekend<CaseHistory> weekend = new Weekend<>(CaseHistory.class);
        WeekendCriteria<CaseHistory, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(CaseHistory::getReceptionNo, receptionNo);
        if (dataSources.equals(1)) {
            criteria.andEqualTo(CaseHistory::getHospitalCode, hospCode);
        }
        weekend.selectProperties("submitFlag", "completeFlag", "blId", "blCardNo", "patientId", "patientName", "deptName", "doctorName", "regNo");
        List<CaseHistory> caseHistories = selectByExample(weekend);
        if (caseHistories.size() == 0) {
            return null;
        }
        return caseHistories.get(0);
    }

    default List<CaseHistory> getCaseHistoryByReceptionNoOld(Integer receptionNo) {
        Weekend<CaseHistory> weekend = new Weekend<>(CaseHistory.class);
        WeekendCriteria<CaseHistory, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(CaseHistory::getReceptionNo, receptionNo);
        //排除新老系统类型不一致的字段
        weekend.excludeProperties("regNo", "tempCode", "hospitalCode", "opCode", "opTime", "blId", "patientId", "receptionNo", "blHtml");
        return selectByExample(weekend);
    }

    default List<CaseHistory> getCaseHistoryByReceptionNoTwo(Integer receptionNo) {
        Weekend<CaseHistory> weekend = new Weekend<>(CaseHistory.class);
        WeekendCriteria<CaseHistory, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(CaseHistory::getReceptionNo, receptionNo);
        return selectByExample(weekend);

    }


    /**
     * 根据患者编号查询患者历史病历
     *
     * @param hospitalCode
     * @param patIds
     * @param createTime
     * @param endTime
     */
    default List<CaseHistory> queryBlHistory(Integer hospitalCode, Set<Integer> patIds, Date createTime, Date endTime) {
        Weekend<CaseHistory> weekend = new Weekend<>(CaseHistory.class);
        WeekendCriteria<CaseHistory, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(CaseHistory::getPatientId, patIds);
        criteria.andBetween(CaseHistory::getCreateTime, createTime, endTime);
//        criteria.andEqualTo(CaseHistory::getHospitalCode, hospitalCode);
        return selectByExample(weekend);
    }

    /**
     * 根据患者病历id获取患者病历信息
     *
     * @param id
     */
    default CaseHistory queryBlHistoryById(Integer id) {
        CaseHistory history = new CaseHistory();
        history.setBlId(Converter.toString(id));
        return selectOne(history);
    }

    /**
     * 根据患者病历id获取互联网患者病历信息
     *
     * @param blId
     * @return
     */
    InternetCaseHistoryDetail queryBlHistoryFromInternet(@Param("blId") String blId);

    /**
     * 获取科室名称
     *
     * @param dept
     * @param hospitalCode
     * @return
     */
    String getDeptName(@Param("dept") Integer dept, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 查询患者病历中现病历信息
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    String GetMedicalHistory(@Param("receptionNo") Long receptionNo, @Param("hospitalCode") Integer hospitalCode);


    /**
     * 根据guidRegNo获取电子病历数据
     *
     * @return
     */
    List<QueryHistoryCaseInfoResult> getCaseHistoryByGuidRegNo(@Param("guidRegNo") String guidRegNo,
                                                               @Param("startDate") Date startDate,
                                                               @Param("endDate") Date endDate);


    List<QueryHistoryCaseInfoResult> getCaseHistoryFromZYProc(@Param("patId") Integer patId,
                                                               @Param("startDate") Date startDate,
                                                               @Param("endDate") Date endDate);

    /**
     * 根据guidRegNo从总院偏移表获取电子病历
     *
     * @param guidRegNo
     * @return
     */
    List<QueryHistoryCaseInfoResult> getCaseHistoryByGuidRegNoOffset(@Param("guidRegNo") String guidRegNo,
                                                                     @Param("startDate") Date startDate,
                                                                     @Param("endDate") Date endDate);

    /**
     * 查询电子病历数据信息-分院
     *
     * @param blId
     * @return
     */
    CaseHistoryByIdResponse queryBlHistoryByBlId(@Param("blId") String blId);

    /**
     * 查询电子病历数据信息-迁移表
     *
     * @param blId
     * @return
     */
    CaseHistoryByIdResponse queryBlHistoryFromZyOffset(@Param("blId") String blId);

    /**
     * 获取医生病历信息
     *
     * @param doctorId
     * @param hospitalCode
     */
    List<CAAuthorizedData> caAuthorizedData(@Param("doctorId") Integer doctorId, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据就诊流水号获取病历数据
     *
     * @param reception
     */
    List<DiagnoseRecordInfo> getCaseHistory(String reception);

    /**
     * 根据就诊流水号获取患者病历内容返回数据
     *
     * @param receptionNo
     */
    BLContentDto queryNewPatBl(@Param("receptionNo") String receptionNo, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据就诊流水号获取患者病历内容返回数据
     *
     * @param receptionNo
     */
    BLContentDto queryOldPatBl(String receptionNo);

    /**
     * 【补写病历】查询审核病历列表
     *
     * @param param
     */
    List<CheckBlDto> queryCheckBls(CheckBlParam param);

    /**
     * 【补写病历】查询审核病历列表
     *
     * @param param
     */
    List<CheckBlDto> queryOldCheckBls(CheckBlParam param);

    /**
     * 根据病历卡号获取患者初诊日期
     *
     * @param hisCardNo
     */
    String getFirstVisitDate(String hisCardNo);

    /**
     * 根据病历Id和医院编码修改处理标记
     *
     * @param id
     */
    void updateNewHandleState(String id);

    /**
     * 根据病历id获取提交标记，完成标记，打印标记
     *
     * @param blId
     */
    CaseHistory selectById(String blId);


    /**
     * 病历审核查询病历信息
     *
     * @param startTime
     * @param endTime
     * @param hospitalCode
     * @param deptId
     * @return
     */
    List<CaseHistory> getCaseHistoryByTime(@Param("startTime") Date startTime,
                                           @Param("endTime") Date endTime,
                                           @Param("hospitalCode") Integer hospitalCode,
                                           @Param("deptId") String deptId);


    /**
     * 保存CA病历信息
     *
     * @param receptionNo
     * @param caId
     */
    void saveCaRecord(@Param("receptionNo") String receptionNo, @Param("caId") Long caId);

    /**
     * 查询CA病历主键信息
     *
     * @param caId
     * @return
     */
    Integer getCaRecordId(@Param("caId") Long caId);


    /**
     * 查询患者不挂号就诊，药理基地病历信息
     *
     * @param patientGuid
     * @param startDate
     * @param endDate
     * @return
     */
    List<QueryHistoryCaseInfoResult> getCaseHistoryByPatientGuid(@Param("patientGuid") String patientGuid,
                                                                 @Param("startDate") Date startDate,
                                                                 @Param("endDate") Date endDate);

    /**
     * 查询最老版本病历信息
     *
     * @param certificateNo
     * @param startDate
     * @param endDate
     * @return
     */
    List<QueryHistoryCaseInfoResult> getOldestCaseHistory(@Param("certificateNo") String certificateNo,
                                                          @Param("startDate") Date startDate,
                                                          @Param("endDate") Date endDate);

    /**
     * 根据患者老版本病历详情信息
     *
     * @param blId
     * @return
     */
    CaseHistoryByIdResponse getOldestCaseHistoryDetail(@Param("blId") Integer blId);

    /**
     * 查询最老版本病历信息
     *
     * @param patNo
     * @param startDate
     * @param endDate
     * @return
     */
    List<QueryHistoryCaseInfoResult> getOldestCaseHistoryByPatNo(@Param("patNo") Integer patNo,
                                                                 @Param("startDate") Date startDate,
                                                                 @Param("endDate") Date endDate);


}
