package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.doctorElemMain.dto.ElemParams;
import com.rjsoft.outPatient.infrastructure.repository.entity.ElemMain;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface ElemMainMapper extends BaseMapper<ElemMain>, ExampleMapper<ElemMain> {


    /**
     * 查询元素子级信息
     *
     * @param elemParams
     * @return
     */
    default List<ElemMain> querySonDirectory(ElemParams elemParams) {
        Weekend<ElemMain> weekend = new Weekend<>(ElemMain.class);
        WeekendCriteria<ElemMain, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ElemMain::getParentElemCode, elemParams.getElemCode());
        weekendCriteria.andEqualTo(ElemMain::getCreateUser, elemParams.getCreateUser());
        weekendCriteria.andEqualTo(ElemMain::getHospitalCode, elemParams.getHospitalCode());
        weekendCriteria.andEqualTo(ElemMain::getStatus, 1);
        weekend.setOrderByClause("create_date asc");
        return selectByExample(weekend);
    }


}
