package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.param.SearchParam;
import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.HistoryDiagnoseResult;
import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.InPatientResult;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatDianosisDTO;
import com.rjsoft.outPatient.domain.diagnose.dto.DiagnoseCodeName;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiseaseReportResponse;
import com.rjsoft.outPatient.domain.prescriptionAudit.vo.AuditDiagnose;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Set;

/**
 * 诊断信息
 */
public interface DiagnoseRepository {


    /**
     * 查询诊断(本次就诊)
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    List<Diagnose> getDiagnoseList(Integer receptionNo, Integer hospitalCode, String table);

    /**
     * 根据就诊流水号查询诊断
     *
     * @param receptionNo
     * @return
     */
    List<Diagnose> getDiagnoseListByReceptionNo(Integer receptionNo);

    /**
     * 查询诊断
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    List<Diagnose> getDiagnoseList(Set<Long> receptionNo, Integer hospitalCode, String table);

    /**
     * 查询诊断(本次就诊)
     *
     * @param id           唯一标识符
     * @param hospitalCode
     * @return Diagnose
     */
    Diagnose getDiagnose(Integer id, Integer hospitalCode);

    /**
     * 查询诊断 根据诊断编码
     *
     * @param receptionNo  就诊流水号
     * @param diagnoseCode 诊断编码
     * @param hospitalCode 医院编码
     * @return List<Diagnose>
     */
    List<Diagnose> getDiagnoseListByDiagnoseCode(Integer receptionNo, String diagnoseCode, Integer hospitalCode);

    /**
     * 查询诊断(本次就诊)
     *
     * @param example
     * @return
     */
    List<Diagnose> getDiagnoseListByExample(Example example);


    /**
     * 诊断录入(新增、修改)
     *
     * @param diagnose
     * @return
     */
    boolean saveDiagnose(Diagnose diagnose);

    /**
     * 根据诊断编码，更新上报标记字段
     * 0 无须上报 1 暂存 2 已上报 3 重复上报
     *
     * @param id
     * @param status
     */
    void updateDiagnoseReported(Integer id, Integer status);

    /**
     * 根据诊断编码更新状态
     *
     * @param receptionNo
     * @param diagnoseCode
     * @param status
     */
    void updateDiagnoseReportedByCode(Integer receptionNo, String diagnoseCode, Integer status);

    /**
     * 诊断信息删除
     *
     * @param id
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    boolean delDiagnose(Integer id, Integer doctorId, Integer hospitalCode);


    /**
     * 查询诊断字字典
     *
     * @param param
     * @return
     */
    List<DiagnoseTypeDic> getDiagnoseListByType(SearchParam param);

    /**
     * 查询诊断字字典
     *
     * @param code         诊断编码
     * @param hospitalCode 医院编码
     * @return
     */
    DiagnoseTypeDic getDiagnoseListByCode(String code, Integer hospitalCode);


    /**
     * 诊断复核
     *
     * @param id
     * @param receptionNo
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    boolean checkReviewDiagnose(Integer id, Integer receptionNo, Integer doctorId, Integer hospitalCode);

    /**
     * 查询主诉信息
     *
     * @param patId
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    List<ChiefComplaint> getPatChiefComplaint(Integer patId, Integer receptionNo, Integer hospitalCode);

    /**
     * 主诉信息（保存，修改）
     *
     * @param chiefComplaint
     * @return
     */
    boolean savePatChiefComplaint(ChiefComplaint chiefComplaint);

    /**
     * 主诉信息删除
     *
     * @param id
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    boolean delPatChiefComplaint(Integer id, Integer doctorId, Integer hospitalCode);


    /**
     * 查询主诉配置信息
     *
     * @param type
     * @param hospitalCode
     * @return
     */
    List<ChiefComplaintConfig> getPatChiefComplaintConfig(Integer type, Integer hospitalCode);


    /**
     * 根据ID查询患者特殊信息
     *
     * @param id
     * @param hospitalCod
     * @return
     */
    SpecialInfo getPatientSpecialInfoOne(Integer id, Integer hospitalCod);

    /**
     * 查询患者特殊信息
     *
     * @param patId
     * @param isRecent
     * @param hospitalCode
     * @return
     */
    List<SpecialInfo> getPatientSpecialInfo(Integer patId, Integer isRecent, Integer hospitalCode);

    /**
     * 查询患者老系统总分院特殊信息
     *
     * @param lhosno
     * @param isRecent
     * @param hospitalCode
     * @return
     */
    List<SpecialInfoMzys> getPatientSpecialInfoMzys(List<String> lhosno, Integer isRecent, Integer hospitalCode);

    /**
     * 查询患者老系统特殊信息
     *
     * @param hisCardNo
     * @return
     */
    List<SpecialInfoOld> getPatientSpecialInfoOld(String hisCardNo);


    /**
     * 患者特殊情况（保存，修改）
     *
     * @param specialInfo
     * @return
     */
    boolean savePatientSpecialInfo(SpecialInfo specialInfo);

    /**
     * 患者特殊情况删除
     *
     * @param id
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    boolean delPatientSpecialInfo(Integer id, Integer doctorId, Integer hospitalCode);

    /**
     * 根据就诊流水号获取患者诊断信息
     *
     * @param receptionNoSet
     * @return
     */
    List<PatDianosisDTO> getPatDianosis(Set<Long> receptionNoSet, Integer hospitalCode);

    /**
     * 根据诊断流水号和医院编码获取诊断信息
     *
     * @param regno    诊断流水号
     * @param hospCode 医院编码
     * @return
     */
    Diagnose getDiagnoseByDiagnoseNo(Integer regno, Integer hospCode);

    /**
     * 根据就诊流水号清除本次就诊诊断
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    boolean delDiagnoseByReceptionNo(Long receptionNo, Integer hospitalCode);

    /**
     * 根据诊断编码获取六大类信息
     *
     * @param diagnoseCode 诊断编码
     * @return
     */
    SixClassCode getSixClass(String diagnoseCode, Integer hospCode);

    /**
     * 根据诊断编码获取诊断名称
     *
     * @param diagnoseCodes
     * @param hospitalCode
     */
    List<SixClassCode> getSixClassByCodes(List<String> diagnoseCodes, Integer hospitalCode);


    /**
     * 根据身份证号+大类编码名称判断是否上报新病例
     *
     * @param idNo
     * @param sbname
     * @param hospCode
     * @return
     */
    boolean judgePsyNewPatient(String idNo, String sbname, Integer hospCode);

    /**
     * 根据身份证号+大类名称判断是否有数据（加载报告内容使用）
     *
     * @param idNo   身份证
     * @param sbName 大类名称
     * @return
     */
    boolean judgeClinicNewPatient(String idNo, String sbName, String receptionNo, Integer hospCode);


    /**
     * 根据身份证号+大类名称判断室友存在定稿数据
     *
     * @param idNo
     * @param sbCode
     * @return
     */
    boolean judgeFinalizedClinicNewPatient(Integer reportDiseaseId, String idNo, String sbCode, Integer hospCode);

    /**
     * 根据身份证号+大类名称判断是否存在疾病报告卡
     *
     * @param idNo
     * @param sbname
     * @return
     */
    boolean judgeDiseaseReportCard(String idNo, String sbname, Integer hospCode);

    /**
     * 判断是否需要有疾病上报数据
     *
     * @param receptionNo
     * @param diagnoseCode
     * @param hospCode
     * @return
     */
    ClinicNewPatient getReportFromQpmzxbr(Integer reportDiseaseId, Integer receptionNo, String diagnoseCode, Integer hospCode);

    /**
     * 判断是否需要有疾病上报数据
     *
     * @param id
     * @param diagnoseNo
     * @param diagnoseCode
     * @param hospCode
     * @return
     */
    List<ClinicNewPatient> getReportFromQpmzxbrId(Integer id, Integer diagnoseNo, Integer receptionNo, String diagnoseCode, Integer hospCode);

    /**
     * 保存疾病上报数据
     *
     * @param clinicNewPatient
     * @param hospCode
     * @return
     */
    boolean addClinicNewPatient(ClinicNewPatient clinicNewPatient, Integer hospCode);

    /**
     * 修改疾病上报
     *
     * @param clinicNewPatient
     * @param hospCode
     * @return
     */
    boolean updateClinicNewPatient(ClinicNewPatient clinicNewPatient, Integer hospCode);

    /**
     * 修改疾病上报状态
     *
     * @param id
     * @param receptionNo
     * @param diagnoseCode
     * @param operator
     * @param opType
     * @param hospCode
     * @return
     */
    boolean alterStatusForQpmzxbr(Integer id, Integer receptionNo, String diagnoseCode, String operator, Integer opType, Integer hospCode);


    /**
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    List<AuditDiagnose> getDiagnoseByReceptionNo(String receptionNo, String hospitalCode);

    /**
     * 根据诊断流水号+医院编码删除诊断信息
     *
     * @param diagnoseNo
     * @param hospCode
     */
    void delDiagnoseByDiagnoseNo(Long diagnoseNo, Integer hospCode);

    /**
     * 根据患者身份证+大类名称+就诊流水号获取报告内容
     *
     * @param patIdNo
     * @param sbName
     * @param receptionNo
     * @return
     */
    ClinicNewPatient getReportFromQpmzxbr2(String patIdNo, String sbName, String receptionNo, Integer hospCode);

    /**
     * 根据患者编号列表，获取最后一次经复核的诊断信息
     *
     * @param regNos
     */
    List<Diagnose> getDiagnoseByRegNos(List<Long> regNos);

    /**
     * 添加患者历史诊断进诊断表
     */
    void addHistoryDiagnose(List<Diagnose> diagnoses);

    /**
     * 根据诊断编码获取 诊断名称
     *
     * @param bms
     */
    List<DiagnoseTypeDic> getDiagnoseTypeName(List<String> bms);

    /**
     * 根据诊断编码获取 诊断名称 【总院】
     *
     * @param bms
     */
    List<DiagnoseCodeName> getDiagnoseTypeNameGeneral(List<String> bms);

    /**
     * 根据诊断编码获取 诊断名称 【分院】
     *
     * @param bms
     */
    List<DiagnoseCodeName> getDiagnoseTypeNameBranch(List<String> bms);

    /**
     * 根据诊断编码获取 诊断名称
     *
     * @param bm
     */
    DiagnoseTypeDic getDiagnoseTypeName(String bm);

    /**
     * 根据就诊流水号+医院编码+诊断编码获取诊断信息
     *
     * @param receptionNo
     * @param hospCode
     * @param diagnoseCode
     * @return
     */
    Diagnose getDiagnoseByJzlshAndZdbm(String receptionNo, String hospCode, String diagnoseCode);

    /**
     * 根据就诊流水号列表获取所有诊断信息
     *
     * @param regNos
     * @param hospitalId
     */
    List<Diagnose> getDiagnosesByRegNos(Set<Long> regNos, Integer hospitalId);

    /**
     * 查询主诉信息
     *
     * @param receptionNo
     * @param hospitalCode
     */
    List<ChiefComplaint> getChiefComplaint(Integer receptionNo, Integer hospitalCode);

    /**
     * 根据就诊流水号获取诊断记录
     *
     * @param receptionNo 就诊流水号
     * @param table       表名称
     */
    List<OldDiagnose> getOldDiagnose(String receptionNo, String table);

    List<OldDiagnoseView> getOldDiagnoseView(String receptionNo, String table);

    /**
     * 根据就诊流水号获取诊断记录
     *
     * @param receptionNo 就诊流水号
     * @param table       表名称
     */
    List<OldDiagnose> getOldDiagnoseBranch(String receptionNo, String table);

    /**
     * 查询患者诊断信息
     *
     * @param oldDataRegNoList
     * @param hospitalCode
     * @return
     */
    List<HistoryDiagnoseResult> getAllFromOldData(List<String> oldDataRegNoList, int hospitalCode);

    /**
     * 根据传染病列表获取传染病
     *
     * @param diagnoseCode
     */
    List<Infectious> getInfectiousList(String diagnoseCode);

    /**
     * 查询医生疾病上报情况
     *
     * @param startTime
     * @param endTime
     * @param certificateNo
     * @param status
     * @param hospitalCode
     * @param doctorId
     * @param uploadTypeId
     * @return
     */
    List<DiseaseReportResponse> reportDisease(String startTime, String endTime, String certificateNo, Integer status, Integer hospitalCode, Integer doctorId, Integer uploadTypeId);

    /**
     * 根据就诊流水号和诊断编码获取疾病上报记录
     *
     * @param receptionNo
     * @param diagnoseCode
     */
    DoctorDiseaseReport getRecord(Integer id, String receptionNo, String diagnoseCode, Integer hospitalCode);

    /**
     * 根据就诊流水号和诊断编码获取疾病上报记录【定稿状态】
     *
     * @param receptionNo
     * @param diagnoseCode
     */
    DoctorDiseaseReport getRecordConfirm(String receptionNo, String diagnoseCode, Integer hospitalCode);

    /**
     * 根据就诊流水号，诊断编码获取诊断记录
     *
     * @param receptionNo
     * @param diagnoseCode
     */
    List<DiagnoseRecordInfo> getDiagnose(String receptionNo, String diagnoseCode, Integer hospitalCode);

    /**
     * 疾病上报退回
     *
     * @param
     * @return
     */
    int returnReport(Integer id, String reception, String diagnoseCode, Integer hospitalCode);

    /**
     * 疾病上报删除
     *
     * @param reception
     * @param diagnoseCode
     * @param hospitalCode
     */
    void delReport(Integer id, String reception, String diagnoseCode, Integer hospitalCode);

    /**
     * 根据诊断主键id获取诊断信息
     *
     * @param diagnoseNos
     */
    List<Diagnose> getDiagnoseByIds(List<Integer> diagnoseNos);

    /**
     * 根据就诊流水号，诊断编码，医院编码，修改sbbj状态
     *
     * @param receptionNo
     * @param diagnoseCode
     * @param hospitalCode
     * @param reportedStatus
     */
    void updateDiagnoseReportedStatus(Long receptionNo, String diagnoseCode, Integer hospitalCode, Integer reportedStatus);

    /**
     * 查询诊断上报方式
     *
     * @param diagnoseCode
     * @param hospitalCode
     * @return
     */
    List<DiagnoseUploadType> getDiagnoseUploadType(String diagnoseCode, Integer hospitalCode);


    /**
     * 查询疾病诊断类型
     *
     * @param hospitalCode
     * @return
     */
    List<DiagnoseUploadType> getReportUploadType(Integer hospitalCode);

    /**
     * 获取所有诊断信息
     *
     * @param hospitalCode
     * @return
     */
    List<DiagnoseUploadType> getDiagnoseList(Integer hospitalCode);

    /**
     * 根据诊断编码获取 诊断名称（西医+中医）
     *
     * @param bms
     */
    List<DiagnoseTypeDic> queryDiagnoseListByCodes(List<String> bms);

    /**
     * 根据挂号列表获取所有诊断信息
     *
     * @param regNos
     * @param hospitalId
     */
    List<Diagnose> queryDiagListByRegNos(List<Long> regNos, Integer hospitalId);

    /**
     * 根据患者ID获取所有住院诊断信息
     *
     * @param patientIds
     * @param hospitalId
     */
    List<InPatientResult> queryIoDiagListByPatId(List<Integer> patientIds, Integer hospitalId);

}


