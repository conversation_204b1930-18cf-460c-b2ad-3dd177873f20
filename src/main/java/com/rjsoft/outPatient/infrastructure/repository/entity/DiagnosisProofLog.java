package com.rjsoft.outPatient.infrastructure.repository.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import org.apache.ibatis.annotations.Param;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 诊断证明操作日志
 *
 * <AUTHOR>
@Data
@Table(name = "MZYS_TB_JBZM_LOG")
public class DiagnosisProofLog  implements Serializable{
    /**
     * 主键自增
     */
    @Id
    @Column(name = "id", insertable = false, updatable = false)
    private Integer id;

    /**
     * MZYS_TB_JBZM表的id
     */
    @Column(name = "jbzm_id")
    private Integer jbzmId;

    /**
     * 操作人id
     */
    @Column(name = "opCode")
    private Integer opCode;

    /**
     * 操作人姓名
     */
    @Column(name = "opName")
    private String opName;

    /**
     * 操作时间
     */
    @Column(name = "opTime")
    private Date opTime;

    /**
     * 操作类型：新增add,修改update,删除delete,查询query
     */
    @Column(name = "opType")
    private String opType;

    /**
     * 操作ip地址
     */
    @Column(name = "ip")
    private String ip;

}