package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.diseaseReport.dto.DrugInfo;
import com.rjsoft.outPatient.domain.diseaseReport.dto.ReceptionDrugDTO;
import com.rjsoft.outPatient.domain.prescriptionAudit.vo.AuditMedicine;
import com.rjsoft.outPatient.domain.recipe.dto.HistoryRecipeDetailDto;
import com.rjsoft.outPatient.domain.recipe.dto.TreatDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

public interface RecipeDetailRepository {
    /**
     * 删除草药处方明细通过明细id
     *
     * @param recipeDetailNos
     * @param hospitalCode
     */
    Integer deleteByRecipeDetailNos(List<Long> recipeDetailNos, Integer feeCategory, Integer hospitalCode);

    /**
     * 删除草药处方明细通过处方id
     *
     * @param recipeNo
     * @param feeCategory
     * @param hospitalCode
     */
    Integer deleteByRecipeNos(Long recipeNo, Integer feeCategory, Integer hospitalCode);

    /**
     * 获取处方明细通过就诊流水号、项目编号
     *
     * @param receptionNo
     * @param itemCode
     * @param hospitalCode
     */
    RecipeDetail getByReceptionNoAndItemCode(Long receptionNo, Integer itemCode, Integer hospitalCode);

    /**
     * 获取处方明细通过就诊流水号、项目编号
     *
     * @param recipeDetailNo
     * @param usage
     * @param hospitalCode
     */
    List<RecipeDetail> getByReceptionNoAndUsage(Long recipeDetailNo, Integer usage, Integer hospitalCode);


    /**
     * 通过regno和status查询处方信息
     *
     * @param regNo
     * @param hospitalCode
     * @param statusList
     * @return
     */
    List<RecipeDetail> getRecipeDetailByRegNoAndStatus(Long regNo, Integer hospitalCode, List<Integer> statusList, String tableName);



    /**
     * 查询草药非联动项目
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    List<RecipeDetail> getRecipeDetailHerbsNotSpecial(Long receptionNo, Integer hospitalCode);

    boolean updateRecipeDetailRegno(Long regNo, Long receptionNo, List<Long> recipeDetailNos, Integer hospitalCode, Integer doctorId);

    public Integer deleteRecipeDetailByDetailIds(List<Long> detailIds, Integer hospitalCode);



    /**
     * 根据处方明细id 删除处方明细
     *
     * @param detailId     处方明细id
     * @param hospitalCode 医院编码
     * @return Boolean
     */
    Integer deleteRecipeDetailByDetailId(Long detailId, Integer hospitalCode);


    /**
     * 保存处方明细
     *
     * @param recipeDetail
     * @return
     */
    boolean saveRecipeDetail(RecipeDetail recipeDetail);

    /**
     * 修改处方明细
     *
     * @param recipeDetail
     * @return
     */
    int updateRecipeDetail(RecipeDetail recipeDetail);


    /**
     * 化验处方加急
     *
     * @param recipeDetailIds
     * @param urgent
     * @return
     */
    int updateRecipeDetail(List<Long> recipeDetailIds, String urgent);

    /**
     * 批量保存处方明细
     *
     * @param recipeDetail
     * @return
     */
    boolean saveRecipeDetails(List<RecipeDetail> recipeDetail);

    /**
     * 根据就诊流水号查询处方明细
     *
     * @param receptionNo
     * @param itemCategory
     * @return
     */
    List<RecipeDetail> getRecipeDetailByReceptionNo(Long receptionNo, Integer itemCategory);

    /**
     * 根据检查流水号查询处方明细
     *
     * @param examineNo    检查流水号
     * @param hospitalCode 医院编码
     * @return
     */
    List<RecipeDetail> getRecipeDetailByExamineNo(Integer examineNo, Integer hospitalCode);

    /**
     * @param examineNo
     * @param hospitalCode
     * @return
     */
    List<RecipeDetail> getRecipeDetailByExamineNos(List<Integer> examineNo, Integer hospitalCode);


    Integer deleteRecipeDetailByDetailIdList(List<Long> detailIdList, Integer hospitalCode);


    /**
     * 根据处方明细id 删除处方明细
     *
     * @param id           处方id
     * @param hospitalCode 医院编码
     * @return Boolean
     */
    Boolean deleteRecipeDetailByRecipeId(Long id, Integer hospitalCode);

    /**
     * 根据检查流水号集合删除处方明细
     *
     * @param applyIds
     * @param hospitalCode
     * @return
     */
    Boolean deleteRecipeDetailByApplyId(List<Integer> applyIds, Integer hospitalCode);

    /**
     * 删除套餐主项目
     *
     * @param packageIdList
     * @param hospitalCode
     * @return
     */
    boolean deleteRecipeDetailPackageMainItem(List<Long> packageIdList, Integer hospitalCode);



    /**
     * 根据ID获取处方明细
     *
     * @param detailNo
     * @param hospitalCode
     * @return
     */
    RecipeDetail getRecipeDetailById(Long detailNo, Integer hospitalCode);

    RecipeDetail tyMdcGetRecipeDetailById(Long detailNo, Integer hospitalCode);

    List<RecipeDetail> getRecipeDetailByIdList(List<Long> detailNoList, Integer hospitalCode);

    /**
     * 根据处方流水号、状态查询处方明细
     * 查询状态 0 或者 3 的记录
     *
     * @param recipeNos    处方流水号
     * @param hospitalCode 医院编码
     * @return List<RecipeDetail>
     */
    List<RecipeDetail> getRecipeDetailByRecipeNosAndStatus(List<Long> recipeNos, Integer hospitalCode);

    /**
     * 根据处方流水号、状态查询处方明细
     *
     * @param recipeNos    处方流水号
     * @param hospitalCode 医院编码
     * @return List<RecipeDetail>
     */
    List<RecipeDetail> getRecipeDetailByRecipeNos(List<Long> recipeNos, Integer hospitalCode);

    /**
     * 获取处方明细ID 集合获取处方明细
     *
     * @param ids
     * @param hospitalCode
     * @return
     */
    List<RecipeDetail> getRecipeDetailByIds(List<Long> ids, Integer hospitalCode);

    /**
     * 根据就诊流水号获取药品处方数量
     *
     * @param receptionNo
     * @param hospital
     * @return
     */
    Integer getDrugRecipeCountById(Long receptionNo, Integer hospital);


    /**
     * 根据就诊流水号判断是否处方
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    boolean hasRecipeDetailByReceptionNo(Long receptionNo, Integer hospitalCode);

    /**
     * 根据处方流水号和费用类别获取处方明细
     *
     * @param receptionNo  处方流水号
     * @param hospitalCode 医院编码
     * @param feeCategory  费用类别
     * @param dispensing
     * @return List<RecipeDetail>
     */
    List<RecipeDetail> getRecipeByReceptionNoAndFeeCategory(Long receptionNo, Integer hospitalCode, List<Integer> feeCategory, Integer dispensing);


    /**
     * 根据挂号流水号查询互联网药品处方(历史处方接口使用)
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    List<HistoryRecipeDetailDto> getInternetDrugRecipeByRegNo(String regNo, Integer hospitalCode);


    /**
     * 根据挂号流水号查询药品处方(历史处方接口使用)
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    List<RecipeDetail> getDrugRecipeByRegNo(Long regNo, Integer hospitalCode);


    /**
     * 根据挂号流水号查询处方明细
     *
     * @param regNo
     * @param itemCategory
     * @return
     */
    List<RecipeDetail> getRecipeDetailByRegNo(Long regNo, Integer itemCategory);

    /**
     * 根据处方id查询处方明细
     *
     * @param recipeId 处方id
     * @return
     */
    List<RecipeDetail> getRecipeDetailsByRecipeId(Long recipeId);

    /**
     * 查询处方明细
     *
     * @param receptionNo  就诊流水号
     * @param itemCategory 收费类型
     * @return List<RecipeDetail>
     */
    List<RecipeDetail> getRecipeDetailsByRecipeId(Long receptionNo, List<Integer> itemCategory);

    /**
     * 查询处方明细
     *
     * @param receptionNo  就诊流水号
     * @param itemCategory 收费类型
     * @return List<RecipeDetail>
     */
    List<RecipeDetail> getRecipeDetailsByReceptionNo(Long receptionNo, List<Integer> itemCategory);

    List<RecipeDetail> getRecipeDetailsByReceptionNoAndNoCategory(Long receptionNo);
    /**
     * 根据 处方id and 收费类型 查询处方明细
     *
     * @param receptionNo  就诊流水号
     * @param hospitalCode 医院编码
     * @return List<RecipeDetail>
     */
    List<RecipeDetail> getDrugRecipeDetailByRecipeId(Long receptionNo, Integer hospitalCode);


    /**
     * 根据挂号流水号获取药物信息
     *
     * @param regNo 挂号流水号
     * @return
     */
    List<RecipeDetail> getRecipeDetailByRegNo(Long regNo);



    /**
     * 根据就诊流水号、项目编码，查询处方明细
     *
     * @param receptionNo
     * @param itemCode
     * @param hospitalCode
     * @param columns
     * @return
     */
    List<RecipeDetail> getRecipeDetailsByItemCode(Long receptionNo, List<Integer> itemCode, Integer hospitalCode, String... columns);

    /**
     * 获取处方明细(根据预存流水号)
     *
     * @param preSaveNo
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    List<RecipeDetail> getRecipeDetailsBySaveNo(Long preSaveNo, Long recipeDetailId, Integer doctorId, Integer hospitalCode);


    List<DrugInfo> getDrugInfo(Integer itemCode, String dose, Integer frequency);

    List<DrugInfo> getDrugInfoTwo(Integer itemCode, String dose, Integer frequency);

    /**
     * 根据就诊流水号，项目编码，医院id查询处方明细信息
     *
     * @param jzlsh
     * @param mxbm
     * @param hospitalCode
     * @return
     */
    RecipeDetail getMzCfMx(Long jzlsh, Integer mxbm, Integer hospitalCode);


    /**
     * 根据处方流水号
     *
     * @param RecipeNo
     * @return
     */
    List<AuditMedicine> getRecipeDetailByRecipeNo(String RecipeNo, String hospitalCode);

    /**
     * 统计药品处方明细
     * 排除指定 {@code itemCode} 列
     *
     * @param receptionNo  就诊流水号
     * @param itemCategory 收费类型
     * @param dispensing   配药途径
     * @param hospitalCode 医院编码
     * @param itemCode
     * @return 统计数量
     */
    Integer getRecipeDetailCount(Long receptionNo, List<Integer> itemCategory, Integer dispensing, Integer hospitalCode, Integer itemCode);

    List<RecipeDetail> getRecipeDetailByLastDoctorId(Integer lastDoctorId, Long receptionNo, Integer itemCode, Integer hospitalCode, Long recipeDetailNo);

    List<RecipeDetail> getRecipeDetailsByLastDoctorId(Integer lastDoctorId, Long receptionNo, Integer hospitalCode);

    /**
     * WHERE (xmbm = ? and hospitalCode = ? and jzlsh in (?, ?) and cfmxlsh <> ? and zhkfys <> ?)
     * 查询其他医生在其他就诊中有无开立过 {@code itemCode}
     *
     * @param lastDoctorId   当前开发医生
     * @param receptionNo
     * @param itemCode       当前收费项目编码
     * @param hospitalCode   医院编码
     * @param recipeDetailNo 当前处方明细流水号
     * @return 符合条件的处方明细列表
     */
    List<RecipeDetail> getRecipeDetailByLastDoctorIdAndReceptionNos(Integer lastDoctorId, List<Long> receptionNo, Integer itemCode, Integer hospitalCode, Long recipeDetailNo);

    List<RecipeDetail> getRecipeDetailsByLastDoctorIdAndReceptionNos(Integer lastDoctorId, List<Long> receptionNo, Integer hospitalCode);

    /**
     * 查询处方明细
     *
     * @param weekend
     * @return
     */
    List<RecipeDetail> drugsSpeciesCount(Weekend<RecipeDetail> weekend);


    /**
     * 查询处方明细
     *
     * @param itemCategory 收费类型
     * @param dispensing   配药途径
     * @param receptionNo  就诊流水号
     * @param hospitalCode 医院编码
     * @return
     */
    Integer drugsSpeciesCount(List<Integer> itemCategory, Integer dispensing, List<Long> receptionNo, Integer hospitalCode);


    /**
     * 根据组号查询
     *
     * @param groupNo      组号
     * @param hospitalCode 医院编码
     * @param receptionNo
     * @return
     */
    List<RecipeDetail> getRecipeDetailByGroupNo(Integer groupNo, Integer hospitalCode, Long receptionNo);

    /**
     * 通过套餐 id 查询
     *
     * @param packageId    套餐 id
     * @param hospitalCode 医院编码
     * @return
     */
    List<RecipeDetail> getRecipeDetailByPackageId(Long receptionNo, Long packageId, Integer hospitalCode);

    List<RecipeDetail> getRecipeDetailByPackageIdList(Long receptionNoList, List<Long> packageId, Integer hospitalCode);


    /**
     * 更新处方审方状态
     *
     * @param receptionNo
     * @param recipeNo
     * @param recipeStatus
     * @param hospitalCode
     * @return
     */
    boolean updateRecipeStatus(Long receptionNo, Integer recipeNo, Integer recipeStatus, Integer hospitalCode);

    /**
     * 检查审方状态
     *
     * @param receptionNo
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    List<RecipeDetail> checkRecipeStatus(Long receptionNo, Integer recipeNo, Integer hospitalCode);

    /**
     * 根据就诊流水号和处方明细状态获取处方明细
     *
     * @param recipeNo
     */
    List<RecipeDetail> getRecipeDetailByStatus(Long recipeNo);

    /**
     * 根据就诊流水号从mzys_new获取处方明细信息
     *
     * @param receptionNo
     * @return
     */
    List<ReceptionDrugDTO> getRecipeByReceptionFromMzysnew(Long receptionNo);


    /**
     * 根据就诊流水号从总院获取处方明细
     *
     * @param receptionNo
     * @return
     */
    List<ReceptionDrugDTO> getRecipeFromZy(String receptionNo);

    /**
     * 根据就诊流水号从分院获取处方明细
     *
     * @param receptionNo
     * @return
     */
    List<ReceptionDrugDTO> getRecipeFromFy(String receptionNo);


    /**
     * 查询治疗单列表
     *
     * @param regNo
     * @param type
     * @param hospitalCode
     */
    List<TreatDto> getTreatDto(Long regNo, Integer type, Integer hospitalCode);


    /**
     * 更新处方状态
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    boolean updateRecipeTollStatus(Long receptionNo, Integer hospitalCode);

    /**
     * 根据就诊流水号获取处方明细信息[只获取申请单数据]
     *
     * @param receptionNo
     * @param hospitalCode
     */
    List<RecipeDetail> getRecipeDetailsByReceptionNo(Long receptionNo, Integer hospitalCode);

    /**
     * 根据就诊流水号和医院编码获取未审核通过的门诊处方明细记录
     *
     * @param receptionNo
     * @param hospitalCode
     */
    List<RecipeDetail> getNoCheckedRecipeStatus(Integer operateType, Long receptionNo, Integer firstDoctorId, Integer hospitalCode);

    /**
     * 删除草药代煎时的附加项目
     *
     * @param recipeNo
     * @param hospitalCode
     * @return
     */
    boolean deleteHerbsMakeInfo(Long recipeNo, Integer hospitalCode, Integer itemCode);

    List<RecipeDetail> getPsychotherapyDrugDeptRecipeDetail(Integer hospitalCode,Long receptionNo,Integer drugDeptId);

}
