package com.rjsoft.outPatient.infrastructure.repository.service;


import com.rjsoft.outPatient.infrastructure.repository.entity.RegTbSpecialsourcerelation;

import java.util.List;

/**
 * 特需号挂号
 *
 * <AUTHOR>
public interface RegTbSpecialsourcerelationRepository {
    RegTbSpecialsourcerelation getRegTbSpecialsourcerelation(Long regNo, Integer hospitalCode);
    List<RegTbSpecialsourcerelation> getRegTbSpecialsourcerelationList(List<Long> regNos, Integer hospitalCode);
    RegTbSpecialsourcerelation getRegTbSpecialsourcerelationBySpecial(long regNo, int hospitalCode);
}
