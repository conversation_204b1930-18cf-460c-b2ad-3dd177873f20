package com.rjsoft.outPatient.infrastructure.repository.entity;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* 
* @TableName System_Tv_ItemInfoDept
*/
@Data
@Table(name = "System_Tv_ItemInfoDept")
public class SystemTvIteminfodept implements Serializable {

    /**
    * 
    */
    @Column(name = "globalId")
    private Integer globalId;
    /**
    * 
    */
    @Column(name = "drugId")
    private Integer drugId;
    /**
    * 
    */
    @Column(name = "usedeptrange")
    private Integer useDeptRange;
    /**
    * 
    */
    @Column(name = "hospitalid")
    private Integer hospitalId;
    /**
    * 
    */
    @Column(name = "deptid")
    private Integer deptId;
    /**
    * 
    */
    @Column(name = "stopped")
    private Integer stopped;

    @Transient
    private String deptName;

}
