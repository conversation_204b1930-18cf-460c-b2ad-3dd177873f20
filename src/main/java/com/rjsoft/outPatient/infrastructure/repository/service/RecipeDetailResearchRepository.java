package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetailResearch;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/18 - 15:25
 */
public interface RecipeDetailResearchRepository {

    /**
     * 通过就诊流水号查询
     *
     * @param recipeDetailNo 就诊流水号
     * @param hospitalCode   医院编码
     * @return {@link RecipeDetailResearch}
     */
    RecipeDetailResearch getByRecipeDetailNo(Long recipeDetailNo, Integer hospitalCode);

    /**
     * 通过就诊流水号查询列表
     *
     * @param recipeDetailNoList 就诊流水号列表
     * @param hospitalCode   医院编码
     * @return {@link RecipeDetailResearch}
     */
    List<RecipeDetailResearch> getByRecipeDetailNoList(List<Long> recipeDetailNoList, Integer hospitalCode);

    /**
     * 通过就诊流水号查询
     *
     * @param itemCodeList
     * @param patId
     * @return {@link RecipeDetailResearch}
     */
    List<RecipeDetailResearch> getByItemCodeAndPatId(List<String> itemCodeList, Integer patId, String projectId);

    /**
     * 通过就诊流水号查询
     *
     * @param recipeDetailResearch 就诊流水号
     * @return {@link RecipeDetailResearch}
     */
    boolean insert(RecipeDetailResearch recipeDetailResearch);

    /**
     * 通过就诊流水号、项目编号查询
     *
     * @param receptionNo 就诊流水号
     * @param hospitalCode   医院编码
     * @return {@link RecipeDetailResearch}
     */
    List<RecipeDetailResearch> getByReceptionNoAndItemCodes(Long receptionNo, List<String> itemCodeList ,Integer hospitalCode);

    /**
     * 通过就诊流水号、处方明细ID、医院编码更新科研医嘱状态
     *
     * @param receptionNo 就诊流水号
     * @param recipeDetailIds 医嘱明细Ids
     * @param hospitalCode   医院编码
     *
     */
    int updateRecipeDetailResearch(Long receptionNo, List<String> recipeDetailIds, Integer hospitalCode);
}
