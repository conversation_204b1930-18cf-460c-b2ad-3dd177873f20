package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationControl;
import com.rjsoft.outPatient.infrastructure.repository.entity.CaseTempFileIndex;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface CaseTempFileIndexMapper extends BaseMapper<CaseTempFileIndex> , ExampleMapper <CaseTempFileIndex>{





}
