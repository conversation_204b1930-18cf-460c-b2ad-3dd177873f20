package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationContent;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * 申请单内容
 *
 * <AUTHOR>
public interface ApplicationContentMapper extends BaseMapper<ApplicationContent> {

    /**
     * 根据Id查询申请单
     *
     * @param id
     * @param hospitalCode
     * @return
     */
    default ApplicationContent getApplicationContentById(Integer id, Integer hospitalCode) {
        ApplicationContent entity = new ApplicationContent();
        entity.setId(id);
        entity.setHospitalCode(hospitalCode);
        return selectOne(entity);
    }

    /**
     * 根据表单id + 就诊流水号查询 MZYS_TB_JCSQDNR 内容id
     *
     * @param formId       表单id
     * @param receptionNo  就诊流水号
     * @param hospitalCode 医院编码
     * @return {@link ApplicationContent}
     */
    @DatabaseAnnotation
    default List<ApplicationContent> getOneByFormIdAndReceptionNoAndHospitalCode(Integer formId, Integer receptionNo, Integer hospitalCode) {
        ApplicationContent entity = new ApplicationContent(formId, receptionNo, hospitalCode);
        return select(entity);
    }

    /**
     * 删除
     *
     * @param formId       表单id
     * @param receptionNo  就诊流水号
     * @param hospitalCode 医院编码
     * @return 收影响行数
     */
    @DatabaseAnnotation
    default int deleteByFormIdAndReceptionNoAndHospitalCode(Integer formId, Integer receptionNo, Integer hospitalCode) {
        ApplicationContent entity = new ApplicationContent(formId, receptionNo, hospitalCode);
        return delete(entity);
    }

}
