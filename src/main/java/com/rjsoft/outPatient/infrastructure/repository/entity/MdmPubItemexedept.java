package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
* MDM_Pub_ItemExeDept  收费项目执行科室维护表
* @TableName MDM_Pub_ItemExeDept
*/
@Data
@Table( name ="MDM_Pub_ItemExeDept" )
public class MdmPubItemexedept implements Serializable {

    /**
    * id
    */
    @Id
    @Column(name = "id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;
    /**
    * 项目编码
    */
    @Column(name = "itemcode")
    private Integer itemCode;
    /**
    * 项目名称
    */
    @Column(name = "itemname")
    private String itemName;
    /**
    * 执行科室
    */
    @Column(name = "execdept")
    private Integer execDept;
    /**
    * 执行科室名称
    */
    @Column(name = "execdeptname")
    private String execDeptName;
    /**
    * 执行院区
    */
    @Column(name = "exechospitalid")
    private Integer execHospitalId;
    /**
    * 费用类型
    */
    @Column(name = "feecategorycode")
    private Integer feeCategoryCode;
    /**
    * 医院编码
    */
    @Column(name = "hospitalId")
    private Integer hospitalId;
    /**
    * 开始时间
    */
    @Column(name = "starttime")
    private String startTime;
    /**
    * 结束时间
    */
    @Column(name = "endtime")
    private String endTime;
    /**
    * 
    */
    @Column(name = "updatetime")
    private Date updateTime;
    /**
    * 
    */
    @Column(name = "updateuserid")
    private Integer updateUserId;
    /**
    * 
    */
    @Column(name = "createtime")
    private Date createTime;
    /**
    * 
    */
    @Column(name = "createuserid")
    private Integer createUserId;
    /**
    * 开立科室
    */
    @Column(name = "usedept")
    private Integer useDept;
    /**
    * 开立科室名称
    */
    @Column(name = "usedeptname")
    private String useDeptName;
    /**
    * 1：门诊 2：住院
    */
    @Column(name = "type")
    private Integer type;

    @Transient
    private String execHospitalName;

    @Transient
    private String execDeptHospitalName;

    @Transient
    private String execDeptId;
}
