package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.AreaInfo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface AreaInfoMapper extends BaseMapper<AreaInfo>, ExampleMapper<AreaInfo> {

    default List<AreaInfo> getAreaInfo(String parentCode, Integer level) {
        Weekend<AreaInfo> weekend = new Weekend<>(AreaInfo.class);
        WeekendCriteria<AreaInfo, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(AreaInfo::getParentCode, parentCode);
        weekendCriteria.andEqualTo(AreaInfo::getLevel, level);
        weekend.setOrderByClause("code asc");
        return selectByExample(weekend);
    }

    default AreaInfo getAreaInfoByCode(String parentCode, Integer level, String code) {
        Weekend<AreaInfo> weekend = new Weekend<>(AreaInfo.class);
        WeekendCriteria<AreaInfo, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(AreaInfo::getParentCode, parentCode);
        weekendCriteria.andEqualTo(AreaInfo::getLevel, level);
        weekendCriteria.andEqualTo(AreaInfo::getCode, code);
        return selectOneByExample(weekend);
    }
}
