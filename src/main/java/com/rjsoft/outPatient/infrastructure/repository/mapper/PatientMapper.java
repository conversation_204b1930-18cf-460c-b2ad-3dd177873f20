package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo;
import com.rjsoft.outPatient.domain.diseaseReport.dto.ReportContentResult;
import com.rjsoft.outPatient.domain.patient.dto.GetPatientByRegNoDto;
import com.rjsoft.outPatient.domain.recipe.dto.RecipeInfoResponse;
import com.rjsoft.outPatient.domain.recipe.dto.WeiNingDto;
import com.rjsoft.outPatient.domain.reserve.dto.ReserveRegisterResponse;
import com.rjsoft.outPatient.domain.thridParty.vo.TreatPatientVO;
import com.rjsoft.outPatient.infrastructure.repository.entity.PatientDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.PatientList;
import com.rjsoft.outPatient.infrastructure.repository.entity.PubPatientInfoOther;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.List;


public interface PatientMapper extends BaseMapper<PatientList>, ExampleMapper<PatientList> {


    /**
     * 查询患者基本信息
     *
     * @param patId
     * @param hospitalCode
     * @return
     */
    default PatientList getPatientList(Integer patId, Integer hospitalCode) {
        PatientList patientList = new PatientList();
        patientList.setPatID(patId);
//        patientList.setHospitalCode(hospitalCode);
        return selectOne(patientList);
    }


    /**
     * 根据患者编号和医院编码获取疾病报告中患者基本信息
     *
     * @param patId    患者编号
     * @param hospCode 医院编码
     * @return
     */
    ReportContentResult getPatInfo(@Param("patId") String patId, @Param("hospCode") String hospCode);

    /**
     * 根据身份证号+医院编码获取患者信息
     *
     * @param certificateNo 身份证
     * @param hospCode      医院编码
     * @return
     */
    default List<PatientList> getPatInfoByIdCard(String certificateNo, Integer hospCode) {
        Weekend<PatientList> weekend = new Weekend<>(PatientList.class);
        weekend.weekendCriteria().andEqualTo(PatientList::getCertificateNo, certificateNo);
//                .andEqualTo(PatientList::getHospitalCode, hospCode);
        return selectByExample(weekend);
    }

    /**
     * 根据挂号流水号获取普通医生编码
     *
     * @param regNo
     * @return
     */
    int getCommonDoctorNo(@Param("regNo") Long regNo);

    /**
     * 根据挂号流水号获取患者信息
     *
     * @param regNo
     * @return
     */
    GetPatientByRegNoDto getPatientByRegNo(@Param("regNo") String regNo);

    /**
     * 根据挂号流水号获取患者信息
     *
     * @param regNo
     * @return
     */
    GetPatientByRegNoDto getAllPatientByRegNo(@Param("regNo") String regNo);

    /**
     * 根据就诊信息的患者编号和就诊流水号，匹配卫宁推送数据
     *
     * @param patId
     * @param regNo
     * @return
     */
    WeiNingDto getWeiNingDto(@Param("patId") Long patId, @Param("regNo") Long regNo);

    /**
     * 获取患者信息
     *
     * @param regNo
     * @param hospitalCode
     * @param workerId
     * @return
     */
    RecipeInfoResponse getPatInfoByRegNo(@Param("regNo") Long regNo, @Param("hospitalCode") Integer hospitalCode, @Param("workerId") Integer workerId);


    /**
     * 根据挂号流水号和医院编号获取就诊类型
     *
     * @param regNo
     * @param hospitalCode
     */
    Integer getRegisterType(Integer regNo, Integer hospitalCode);

    /**
     * 根据患者身份证号获取患者patNo
     *
     * @return
     */
    @Select("select top 1 PatNo from Tbt_PubPatientInfo where PatSfz = #{patNo} ")
    Integer getPatNo(String patIdNo);


    PubPatientInfoOther getPubPatientInfoOther(@Param("patNo") Integer patNo);

    /**
     * 根据患者身份证号获取患者全部信息
     *
     * @param certificateNo
     * @param hospitalId
     */
    default List<PatientList> getPatientInfos(String certificateNo, Integer hospitalId) {
        if (StringUtils.isEmpty(certificateNo) || hospitalId == null) {
            return new ArrayList<>();
        }
        PatientList info = new PatientList();
        info.setCertificateNo(certificateNo);
        info.setIsDelete(0);
//        info.setHospitalCode(hospitalId);
        return select(info);
    }

    /**
     * 根据患者身份证号获取患者年龄和性别
     *
     * @param list
     * @param hospitalId
     */
    List<ReserveRegisterResponse> getPatientAgeAndSex(@Param("list") List<String> list, @Param("hospitalId") Integer hospitalId);

    /**
     * 根据患者编号获取患者信息【新系统】
     *
     * @param list
     * @param hospitalCode
     */
    List<DiagnoseRecordInfo> getNewDiseasePatNos(@Param("list") List<DiagnoseRecordInfo> list, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据患者编号获取患者信息【老系统】
     *
     * @param list
     * @param hospitalCode
     */
    List<DiagnoseRecordInfo> getOldDiseasePatNos(@Param("list") List<DiagnoseRecordInfo> list, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据患者编号列表、医院编码 获取患者基本信息
     *
     * @param patIds
     * @param hospitalCode
     */
    default List<PatientList> getPatInfoByPatIds(List<String> patIds, Integer hospitalCode) {
        Weekend<PatientList> weekend = new Weekend<>(PatientList.class);
        WeekendCriteria<PatientList, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(PatientList::getPatID, patIds);
//                .andEqualTo(PatientList::getHospitalCode, hospitalCode);
        return selectByExample(weekend);
    }

    /**
     * 获取老系统GUID患者编号
     *
     * @param certificateNo
     * @return
     */
    List<String> getGuidPatientByCertificateNo(@Param("certificateNo") String certificateNo);


    /**
     * 获取老系统GUID患者编号
     *
     * @param patNo
     * @return
     */
    List<String> getGuidPatientByPatNo(@Param("patNo") Integer patNo);

    /**
     * 新增患者时获取patId
     *
     * @return
     */
    Long getPatId();

    /**
     * 获取患者失信标签
     *
     * @return
     */
    String getPatCreeditTags(@Param("idCardNo") String idCardNo);

    /**
     * 舒辅心理治疗获取his复诊患者信息
     *
     */
    List<TreatPatientVO> queryTreatmentPatientList(@Param("certificateNos") List<String> certificateNos);

    /**
     * 根据患者编号列表、医院编码 获取患者基本信息
     *
     * @param patIds
     */
    List<PatientList> queryPatInfoByPatIds(@Param("patIds") List<Long> patIds);

    /**
     * 查询患者补充信息
     *
     * @param patIds
     * @return
     */
    List<PatientDetail> queryPatientDetailByIds(@Param("patIds") List<Long> patIds);

}
