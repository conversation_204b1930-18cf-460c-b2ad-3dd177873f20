<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DrugInfoHospitalMapper">

    <select id="getDrugInfoHospital" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.DrugInfoHospital">
        select b.ID,b.GlobalId,b.DrugId,b.HospitalId,b.IsStop,b.UseDeptRange from
        Drug_Tb_DrugInfoHospital a
        inner join Drug_Tb_DrugInfoHospital b
        on a.GlobalId = b.GlobalId and b.UseDeptRange = #{useDeptRange}
        where a.isStop = 0 and a.HospitalId = #{hospitalId} and a.UseDeptRange = #{useDeptRange} and a.DrugId = #{drugId}
    </select>

</mapper>