package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * 注射费用对照表
 *
 * <AUTHOR>
 * @since 2021/11/24-4:15 下午
 */
@Data
@Table(name = "MZYS_TB_ZSXMDZ")
public class InjectionFeeAssociative implements Serializable {

    private static final long serialVersionUID = 8058408414968773871L;

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "id", insertable = false, updatable = false)
    private Integer id;
    /**
     * 就诊流水号
     */
    @Column(name = "jzlsh")
    private Integer receptionNo;
    /**
     * 处方明细流水号
     */
    @Column(name = "cfmxlsh")
    private Integer recipeDetailNo;
    /**
     * 组号
     */
    @Column(name = "zh")
    private Integer groupNo;
    /**
     * 注射处方明细流水号
     */
    @Column(name = "zscfmxlsh")
    private Integer injectionRecipeDetailNo;
    /**
     * 注射项目编码
     */
    @Column(name = "zsxmbm")
    private Integer injectionItemCode;
    /**
     * 创建日期
     */
    @Column(name = "cjrq")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public InjectionFeeAssociative() {
    }


    public InjectionFeeAssociative(RecipeDetail appendRecipeDetail, RecipeDetail targetRecipeDetail) {
        this.receptionNo = Math.toIntExact(targetRecipeDetail.getReceptionNo());
        this.recipeDetailNo = Math.toIntExact(targetRecipeDetail.getRecipeDetailNo());
        this.injectionRecipeDetailNo = Math.toIntExact(appendRecipeDetail.getRecipeDetailNo());
        this.injectionItemCode = appendRecipeDetail.getItemCode();
        this.createTime = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
    }
}
