package com.rjsoft.outPatient.infrastructure.repository.mapper;

import cn.hutool.core.collection.CollUtil;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbMedicalFee;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Collections;
import java.util.List;

public interface MzysTbMedicalFeeMapper extends Mapper<MzysTbMedicalFee> {

    default MzysTbMedicalFee getMedicalFeeByRegNo(Long regNo, Integer hospitalCode) {
        Weekend<MzysTbMedicalFee> weekend = Weekend.of(MzysTbMedicalFee.class);
        WeekendCriteria<MzysTbMedicalFee, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andEqualTo(MzysTbMedicalFee::getRegNo, regNo).andEqualTo(MzysTbMedicalFee::getHospitalId, hospitalCode);
        return this.selectOneByExample(weekend);
    }


    default List<MzysTbMedicalFee> queryMedicalFeeByRegNoList(List<Long> regNoList, Integer hospitalCode) {
        if (CollUtil.isEmpty(regNoList)) {
            return Collections.emptyList();
        }
        Weekend<MzysTbMedicalFee> weekend = Weekend.of(MzysTbMedicalFee.class);
        WeekendCriteria<MzysTbMedicalFee, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andIn(MzysTbMedicalFee::getRegNo, regNoList).andEqualTo(MzysTbMedicalFee::getHospitalId, hospitalCode);
        return this.selectByExample(weekend);
    }

    default boolean deleteByRegNo(Long regNo, Integer hospitalCode) {
        Weekend<MzysTbMedicalFee> weekend = Weekend.of(MzysTbMedicalFee.class);
        WeekendCriteria<MzysTbMedicalFee, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andEqualTo(MzysTbMedicalFee::getRegNo, regNo).andEqualTo(MzysTbMedicalFee::getHospitalId, hospitalCode);
        return this.deleteByExample(weekend) > 0;
    }

    default boolean deleteByRegNoList(List<Long> regNoList, Integer hospitalCode) {
        Weekend<MzysTbMedicalFee> weekend = Weekend.of(MzysTbMedicalFee.class);
        WeekendCriteria<MzysTbMedicalFee, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andIn(MzysTbMedicalFee::getRegNo, regNoList).andEqualTo(MzysTbMedicalFee::getHospitalId, hospitalCode);
        return this.deleteByExample(weekend) > 0;
    }

}