package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 药品超量科室
 * MZYS_NEW..MZYS_TB_YPCLKS
 *
 * <AUTHOR>
 * @since 2021/9/27-3:33 下午
 */
@Data
@Table(name = "MZYS_TB_YPCLKS")
public class OverdoseDrugsDept implements Serializable {

    /**
     * id
     */
    @Id
    @Column(name = "id")
    private Integer id;

    /**
     * 科室编码
     */
    @Column(name = "ksbm")
    private Integer deptId;

    /**
     * 医院编码
     */
    @Column(name = "yybm")
    private Integer hospitalCode;

}
