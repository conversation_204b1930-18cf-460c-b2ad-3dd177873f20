<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DiagnoseTypeDicMapper">

    <select id="getDiagnoseListByType"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.DiagnoseTypeDic">
        SELECT rtrim(DiagnosisCode) diagnoseCode, rtrim(DiagnosisName) diagnoseName, inputcode inputCode,
        CASE WHEN SUBSTRING(DiagnosisCode,1, 1)= 'F' then 1 else 0 end indes, 1 diagnoseType
        FROM System_Tb_Diagnosis(nolock)
        WHERE UseRange IN (0, 1)
        AND IsStop = 0
        <if test="inputType==0 or inputType=='' ">
            AND (InputCode LIKE '%' + #{inputCode} + '%'
            OR DiagnosisCode LIKE '%' + #{inputCode} + '%'
            OR DiagnosisName LIKE '%' + #{inputCode} + '%')
        </if>
        <if test="inputType==1 ">
            AND (InputCode LIKE '' + #{inputCode} + '%'
            OR DiagnosisCode LIKE '' + #{inputCode} + '%'
            OR DiagnosisName LIKE '' + #{inputCode} + '%')
        </if>
        union all
        SELECT rtrim(DiagnosisCode) diagnoseCode, rtrim(DiagnosisName) diagnoseName, inputcode,
        0 indes, 2 diagnoseType
        FROM System_Tb_ChineseDiagnose (nolock)
        WHERE UseRange IN (0, 1)
        AND IsStop = 0
        <if test="inputType==0 or inputType=='' ">
            AND (InputCode LIKE '%' + #{inputCode} + '%'
            OR DiagnosisCode LIKE '%' + #{inputCode} + '%'
            OR DiagnosisName LIKE '%' + #{inputCode} + '%')
        </if>
        <if test="inputType==1 ">
            AND (InputCode LIKE '' + #{inputCode} + '%'
            OR DiagnosisCode LIKE '' + #{inputCode} + '%'
            OR DiagnosisName LIKE '' + #{inputCode} + '%')
        </if>
    </select>

    <select id="getOldDiagnoseTypeName" resultType="com.rjsoft.outPatient.domain.diagnose.dto.DiagnoseCodeName">
        select rtrim(code) code ,rtrim([name]) name from TBT_SYSCURECODE where code in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryDiagnoseListByCodes" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.DiagnoseTypeDic">
        SELECT rtrim(DiagnosisCode) diagnoseCode, rtrim(DiagnosisName) diagnoseName, inputcode inputCode, useRange, IsStop, diagKindType, 1 diagnoseType
        FROM System_Tb_Diagnosis(nolock)
        WHERE rtrim(DiagnosisCode) in
        <foreach collection="diagCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        union all
        SELECT rtrim(DiagnosisCode) diagnoseCode, rtrim(DiagnosisName) diagnoseName, inputcode, useRange, IsStop, null diagKindType, 2 diagnoseType
        FROM System_Tb_ChineseDiagnose (nolock)
        WHERE rtrim(DiagnosisCode) in
        <foreach collection="diagCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>