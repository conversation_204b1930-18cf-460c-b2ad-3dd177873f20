package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.HerbPatientAddress;

public interface HerbPatientAddressRepository {
    /**
     * 根据患者id查询草药处方联系地址
     * @param patId
     * @param hospitalCode
     * @return
     */
    HerbPatientAddress getContactAddress(Integer patId,Integer hospitalCode);

    /**
     * 新增草药处方联系地址
     * @param herbPatientAddress
     * @return
     */
    boolean addContactAddress(HerbPatientAddress herbPatientAddress);
}
