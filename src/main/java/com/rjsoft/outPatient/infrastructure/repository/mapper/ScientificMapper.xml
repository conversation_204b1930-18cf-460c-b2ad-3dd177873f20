<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ScientificMapper">


    <select id="getProjectsList" resultType="java.util.HashMap">
        exec UP_DoctorManagerProjects
      @UserCode=#{doctorId},
        @Result=''
    </select>

    <select id="patJoinProject" resultType="java.lang.String">
        exec UP_HISInputPatient
        @InputNo=#{projectId},
        @PatientNo=#{hisCardNo},
        @PatientName=#{patName},
        @ProjectId=#{projectId},
        @Birthday='',
        @Sex=#{sex},
        @IdCard=#{patSfz},
        @OperatorCode=#{workerId}
    </select>

</mapper>
