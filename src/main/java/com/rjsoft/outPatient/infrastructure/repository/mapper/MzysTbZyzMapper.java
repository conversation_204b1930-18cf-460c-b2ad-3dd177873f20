package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.hospitalProve.pojo.MzysTbZyz;
import com.rjsoft.outPatient.domain.hospitalProve.pojo.MzysTbZyzExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MzysTbZyzMapper {
    int countByExample(MzysTbZyzExample example);

    int deleteByExample(MzysTbZyzExample example);

    int deleteByPrimaryKey(String jzlsh);

    int insert(MzysTbZyz record);

    int insertSelective(MzysTbZyz record);

    List<MzysTbZyz> selectByExample(MzysTbZyzExample example);

    MzysTbZyz selectByPrimaryKey(String jzlsh);

    int updateByExampleSelective(@Param("record") MzysTbZyz record, @Param("example") MzysTbZyzExample example);

    int updateByExample(@Param("record") MzysTbZyz record, @Param("example") MzysTbZyzExample example);

    int updateByPrimaryKeySelective(MzysTbZyz record);

    int updateByPrimaryKey(MzysTbZyz record);
}