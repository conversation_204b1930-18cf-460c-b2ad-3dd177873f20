package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.DoctorConfig;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

/**
 * 医生配置
 * <AUTHOR>
public interface DoctorConfigMapper extends BaseMapper<DoctorConfig>, ExampleMapper<DoctorConfig> {

    /**
     * 保存配置
     * @param entity
     * @return
     */
    default boolean save(DoctorConfig entity) {
        if (existsWithPrimaryKey(entity)) {
            return updateByPrimaryKeySelective(entity) > 0;
        } else {
            return insert(entity) > 0;
        }
    }

    /**
     * 根据ID获取医生配置
     * @param configKey
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    default DoctorConfig getDoctorConfigById(String configKey,Integer doctorId,Integer hospitalCode){
        DoctorConfig entity=new DoctorConfig();
        entity.setConfigKey(configKey);
        entity.setWorkerId(doctorId);
        entity.setHospitalCode(hospitalCode);
        return  selectByPrimaryKey(entity);
    }

    /**
     * 根据医生加载所有配置
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    default   List<DoctorConfig> getConfigByDoctor(Integer doctorId,Integer hospitalCode) {
        DoctorConfig entity = new DoctorConfig();
        entity.setWorkerId(doctorId);
        entity.setHospitalCode(hospitalCode);
        return select(entity);
    }

    /**
     * 删除医生配置
     * @param configKey
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    default boolean deleteDoctorConfig(String configKey,Integer doctorId,Integer hospitalCode) {
        DoctorConfig entity = new DoctorConfig();
        entity.setConfigKey(configKey);
        entity.setWorkerId(doctorId);
        entity.setHospitalCode(hospitalCode);
        return deleteByPrimaryKey(entity) > 0;
    }

}
