package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 心理治疗项目
 */
@Data
@Table(name = "MZYS_TB_XLZLXM")
public class PsychotherapyItem implements Serializable {

    @Column(name = "id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    @Column(name = "xmbm")
    private Integer itemCode;
    
    @Column(name = "xmmc")
    private String itemName;

    @Column(name = "zt")
    private Integer state;

    @Column(name = "cjr")
    private Integer opCode;

    @Column(name = "cjsj")
    private Date opDate;

    @Column(name = "yybm")
    private Integer hospitalCode;


}