package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.item.dto.ApplyItemExeDeptDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplyItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

/**
 * 申请单项目
 * <AUTHOR>
@Mapper
public interface ApplyItemMapper extends BaseMapper<ApplyItem>, ExampleMapper<ApplyItem> {


    List<ApplyItemExeDeptDto> getApplyExeDeptByItemCode(@Param("itemCode") String itemCode,
                                                        @Param("examineItemCode") String examineItemCode,
                                                        @Param("examineNo") String examineNo,
                                                        @Param("applyDetailId") Integer applyDetailId);
}
