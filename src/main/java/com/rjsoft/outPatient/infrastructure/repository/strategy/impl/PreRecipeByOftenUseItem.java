package com.rjsoft.outPatient.infrastructure.repository.strategy.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.enums.RecipeStatusEnum;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.recipe.RecipeDomain;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.OftenUseItem;
import com.rjsoft.outPatient.infrastructure.repository.entity.OftenUseItemUsage;
import com.rjsoft.outPatient.infrastructure.repository.entity.PreRecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.SystemTbPubItems;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.*;
import com.rjsoft.outPatient.infrastructure.repository.strategy.PreRecipeStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component("PreRecipeByOftenUseItem")
@AllArgsConstructor
public class PreRecipeByOftenUseItem implements PreRecipeStrategy {

    @Resource
    RecipeRepository recipeRepository;
    SystemTbPubItemsRepository systemTbPubItemsRepository	;
    SystemTvIteminfodeptRepository systemTvIteminfodeptRepository	;
    VirtualInventoryRepository virtualInventoryRepository	;
    ChargeItemRepository chargeItemRepository	;
    DrugFrequencyRepository drugFrequencyRepository;

    PreRecipeDetailMapper preRecipeDetailMapper	;
    RecipeDetailMapper recipeDetailMapper	;
    OldRecipeDetailMapper oldRecipeDetailMapper	;
    DrugToHospitalMapper drugToHospitalMapper	;
    SysFunctionMapper sysFunctionMapper	;
    OftenUseItemMapper oftenUseItemMapper;
    OftenUseItemUsageMapper oftenUseItemUsageMapper;

    @Override
    @DatabaseAnnotation(name = "HISDB")

    public List<PreRecipeDetail> getPreRecipeDetails(CheckRecipeDto checkDto, Map errInfo) {
        List<PreRecipeDetail> details = getPreRecipeDetailsByOftenUseItem(Converter.toInt32(checkDto.getOperatingId()),
                checkDto.getDoctorId(), checkDto.getHospitalCode());
        return details;
    }


    /**
     * 获取处方预存明细(根据常用项目)
     *
     * @param id
     * @param hospitalCode
     * @return
     */
    @DatabaseAnnotation(name = "HISDB")
    private List<PreRecipeDetail> getPreRecipeDetailsByOftenUseItem(Integer id, Integer doctorId, Integer hospitalCode) {
        List<PreRecipeDetail> res = new ArrayList<>();
        //查询常用项目
        OftenUseItem oftenUseItem = oftenUseItemMapper.getOftenUseItemById(id, doctorId, hospitalCode);
        OftenUseItemUsage oftenUseItemUsage = oftenUseItemUsageMapper.getOftenUseItemUsageByItemCode(id, doctorId, hospitalCode);

        PreRecipeDetail preRecipeDetail = new PreRecipeDetail();
        preRecipeDetail.setPreSaveNo(sysFunctionMapper.getGetSequences(SequenceEnum.PRE_SAVE_NO));
        preRecipeDetail.setRecipeDetailNo(sysFunctionMapper.getGetSequences(SequenceEnum.RECIPE_DETAIL_NO));

        DataSourceSwitchAspect.changeDataSource("HISDB");
        SystemTbPubItems chargeItem = systemTbPubItemsRepository.getChargeItemById(oftenUseItem.getItemCode(), hospitalCode, null);
//        ChargeItem chargeItem = chargeItemRepository.getChargeItemById(oftenUseItem.getItemCode(), hospitalCode);
        preRecipeDetail.ChangeChargeItem(chargeItem);
        preRecipeDetail.setUsage(oftenUseItemUsage.getUsage());
        preRecipeDetail.setFrequency(oftenUseItemUsage.getFrequency());
        preRecipeDetail.setDose(oftenUseItemUsage.getDose());
        preRecipeDetail.setDoseUnit(oftenUseItemUsage.getDoseUnit());
        preRecipeDetail.setDays(oftenUseItemUsage.getDays());
        preRecipeDetail.setQuantity(oftenUseItemUsage.getQuantity());
        preRecipeDetail.setOpFlag(0);
        preRecipeDetail.setRecipeStatus(RecipeStatusEnum.UNREVIEWED.getCode());
        res.add(preRecipeDetail);
        return res;
    }

}
