package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.*;

import java.util.List;

/**
 * 封装检查申请所有操作（MZYS,ZXHIS,RIS库共六张表数据）
 *
 * <AUTHOR>
public interface ExamineRequestRepository {

    /**
     * 删除 检查申请单主表 根据参数
     *
     * @param examineNo
     * @param recipeDetailId
     * @param receptionNo
     * @return
     */
    int deleteByExamineNoAndRecipeDetailIdAndReceptionNo(Integer examineNo, Long recipeDetailId, Long receptionNo);

    /**
     * 检查处方明细是否需要保存申请单（如果需要，返回申请类型，申请名称）
     *
     * @param recipeDetail
     * @return
     */
    RequestType checkNeedRequest(RecipeDetail recipeDetail);

    /**
     * 根据处方生成检查申请
     *
     * @param applyDetail
     * @param applyList
     * @param registerList
     * @return
     */
    ExamineRequest getExamineRequestByRecipe(ApplyDetail applyDetail, ApplyList applyList, RegisterList registerList);

    /**
     * 根据检查流水号获取申请单
     *
     * @param examineNos
     * @param hospitalCode
     * @return
     */
    List<ExamineRequest> getExamineRequestByIds(List<Integer> examineNos, Integer hospitalCode);

    /**
     * 根据检查明细ID获取检查检查申请单集合
     *
     * @param examineDetailNos
     * @param hospitalCode
     * @return
     */
    List<ExamineRequest> getExamineRequestByDetailIds(List<Integer> examineDetailNos, Integer hospitalCode);

    /**
     * 根据表单ID集合获取申请单明细（一次提交保存的申请单为一个GroupId）
     *
     * @param groupIds
     * @param hospitalCode
     * @return
     */
    List<ExamineRequest> getExamineRequestByGroupIds(List<Integer> groupIds, Integer hospitalCode);

    /**
     * 获取检查申请明细
     *
     * @param examineDetailNo
     * @param hospitalCode
     * @return
     */
    List<ExamineRequestDetail> getExamineRequestDetailNos(Integer examineDetailNo, Integer hospitalCode);

    /**
     * 获取检查申请明细
     *
     * @param recipeDetail
     * @param request
     * @return
     */
    ExamineRequestDetail getExamineRequestDetail(RecipeDetail recipeDetail, ExamineRequest request);

    /**
     * 根据处方明细生成检查申请
     *
     * @param recipeDetail
     * @param requestType
     * @param opCode
     * @return
     */
    ExamineRequestZxHis createExamineRequestZxHisByRecipe(RecipeDetail recipeDetail, RequestType requestType, Integer opCode);

    /**
     * 根据处方明细生成检查申请明细，如果数据不存在，则创建数据
     *
     * @param recipeDetail
     * @param risId
     * @param opCode
     * @return
     */
    ExamineRequestDetailZxHis getExamineRequestDetailZxHis(RecipeDetail recipeDetail, Integer risId, Integer opCode);

    /**
     * 根据处方明细生成检查申请
     *
     * @param recipeDetails
     * @return
     */
    List<ExamineRequestRis> createExamineRequestRisByMzRequest(List<RecipeDetail> recipeDetails);

    /**
     * 保存检查申请
     *
     * @param request
     * @return
     */
    boolean saveExamineRequest(List<ExamineRequest> request);

    /**
     * 保存检查申请
     *
     * @param request
     * @param hospitalCode
     * @return
     */
    boolean saveExamineRequestZxHis(ExamineRequestZxHis request, Integer hospitalCode);

    /**
     * 保存检查申请
     *
     * @param request
     * @return
     */
    boolean saveExamineRequestRis(ExamineRequestRis request);

    /**
     * 根据检查流水号和医院编码，修改检查申请单主表收费状态
     *
     * @param examineNos
     * @param hospitalCode
     */
    void updateExamineChargeStatus(List<String> examineNos, Integer hospitalCode);

}
