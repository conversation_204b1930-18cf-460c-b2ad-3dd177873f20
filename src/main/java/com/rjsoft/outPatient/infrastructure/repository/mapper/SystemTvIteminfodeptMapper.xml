<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.SystemTvIteminfodeptMapper">


    <select id="getSystemTbItemexecdept" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.SystemTvIteminfodept">
        SELECT a.*,b.DeptName deptName
        FROM System_Tv_ItemInfoDept a
        left join System_Tb_Department b on a.DeptId = b.DeptId and a.HospitalId = b.HospitalId
        WHERE a.usedeptrange = 1 AND a.stopped =0
        <if test="hospitalId != null">
            AND a.hospitalid = #{hospitalId}
        </if>
        <if test="itemCode != null">
            AND a.drugId = #{itemCode}
        </if>
    </select>

    <select id="getItemsExecDept" resultType="com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDeptDto">
        SELECT d.DeptId execDept,b.DeptName execDeptName,d.HospitalId execHospitalId,c.simpleName execHospitalName
        FROM System_Tv_PubItems a
        inner join System_Tb_ItemExecDept d on a.itemCode = d.itemCode
        left join System_Tb_Department b on d.DeptId = b.DeptId and d.HospitalId = b.HospitalId
        left join Tb_Hospital c on d.HospitalId = c.HospitalCode
        WHERE a.userange = 1 AND a.stopped =0 AND a.exeDept != 0
        <if test="itemCode != null">
            AND a.itemCode = #{itemCode}
        </if>
        group by d.DeptId,b.DeptName,d.HospitalId,c.simpleName
        union
        SELECT b.DeptId execDept,c.DeptName execDeptName,b.HospitalId execHospitalId,d.simpleName execHospitalName
        FROM System_Tv_PubItems	a
        inner join Drug_Tb_LocalDeptDrug b on a.ItemCode = b.drugId and a.exeHospitalId = b.HospitalId
        left join System_Tb_Department c on b.DeptId = c.DeptId	and b.HospitalId = c.HospitalId
        left join Tb_Hospital d on b.HospitalId = d.HospitalCode
        where  a.userange = 1 AND a.stopped =0 AND b.IsUsed = 1
        <if test="itemCode != null">
            AND a.itemCode = #{itemCode}
        </if>
        group by b.DeptId,c.DeptName,b.HospitalId,d.simpleName
    </select>
    <select id="getNotDrugsExecDept" resultType="com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDeptDto">
        SELECT d.DeptId execDept,b.DeptName execDeptName,d.HospitalId execHospitalId,c.simpleName execHospitalName
        FROM System_Tv_not_Drugs a
        inner join System_Tb_ItemExecDept d on a.itemCode = d.itemCode
        left join System_Tb_Department b on d.DeptId = b.DeptId and d.HospitalId = b.HospitalId
        left join Tb_Hospital c on d.HospitalId = c.HospitalCode
        WHERE a.userange = 1 AND a.stopped =0 AND a.exeDept != 0
        <if test="itemCode != null">
            AND a.itemCode = #{itemCode}
        </if>
        group by d.DeptId,b.DeptName,d.HospitalId,c.simpleName
    </select>
    <select id="getDrugsExecDept" resultType="com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDeptDto">
        SELECT b.DeptId execDept,c.DeptName execDeptName,b.HospitalId execHospitalId,d.simpleName execHospitalName
        FROM System_Tv_Drugs	a
        inner join Drug_Tb_LocalDeptDrug b on a.ItemCode = b.drugId and a.exeHospitalId = b.HospitalId
        left join System_Tb_Department c on b.DeptId = c.DeptId	and b.HospitalId = c.HospitalId
        left join Tb_Hospital d on b.HospitalId = d.HospitalCode
        where  a.userange = 1 AND a.stopped =0 AND b.IsUsed = 1
        <if test="itemCode != null">
            AND a.itemCode = #{itemCode}
        </if>
        group by b.DeptId,c.DeptName,b.HospitalId,d.simpleName
    </select>

    <select id="getItemsExecDeptByItemCodeList" resultType="com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDeptDtoWithItemCode">
        SELECT d.DeptId execDept,b.DeptName execDeptName,d.HospitalId execHospitalId,c.simpleName execHospitalName,a.itemCode
        FROM System_Tv_PubItems a
        inner join System_Tb_ItemExecDept d on a.itemCode = d.itemCode
        left join System_Tb_Department b on d.DeptId = b.DeptId and d.HospitalId = b.HospitalId
        left join Tb_Hospital c on d.HospitalId = c.HospitalCode
        WHERE a.userange = 1 AND a.stopped =0 AND a.exeDept != 0
        <if test="itemCodeList != null and itemCodeList.size() > 0">
            AND a.itemCode in
            <foreach item="itemCode" index="index" collection="itemCodeList" open="(" separator="," close=")">
                #{itemCode}
            </foreach>
        </if>
        group by d.DeptId,b.DeptName,d.HospitalId,c.simpleName,a.itemCode
        union
        SELECT b.DeptId execDept,c.DeptName execDeptName,b.HospitalId execHospitalId,d.simpleName execHospitalName,a.itemCode
        FROM System_Tv_PubItems a
        inner join Drug_Tb_LocalDeptDrug b on a.ItemCode = b.drugId and a.exeHospitalId = b.HospitalId
        left join System_Tb_Department c on b.DeptId = c.DeptId and b.HospitalId = c.HospitalId
        left join Tb_Hospital d on b.HospitalId = d.HospitalCode
        where a.userange = 1 AND a.stopped =0 AND b.IsUsed = 1
        <if test="itemCodeList != null and itemCodeList.size() > 0">
            AND a.itemCode in
            <foreach item="itemCode" index="index" collection="itemCodeList" open="(" separator="," close=")">
                #{itemCode}
            </foreach>
        </if>
        group by b.DeptId,c.DeptName,b.HospitalId,d.simpleName,a.itemCode
    </select>
</mapper>