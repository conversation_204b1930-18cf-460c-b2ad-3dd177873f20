package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.OftenUseItem;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.Mapper;

/**
 * 常用项目
 * <AUTHOR>
public interface OftenUseItemMapper extends Mapper<OftenUseItem> {

    /**
     * 保存常用项目
     * @param oftenUseItem
     * @return
     */
    default  boolean saveOftenUseItem(OftenUseItem oftenUseItem) {
        if (0 == oftenUseItem.getDataFlag()) {
            return insert(oftenUseItem) > 0;
        } else if (1 == oftenUseItem.getDataFlag()) {
            return updateByPrimaryKeySelective(oftenUseItem) > 0;
        } else {
            return false;
        }
    }

    /**
     * 根据id获取常用项目
     * @param id
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    default  OftenUseItem getOftenUseItemById(Integer id, Integer doctorId,Integer hospitalCode){
        OftenUseItem entity=new OftenUseItem();
        entity.setId(id);
        entity.setDoctorId(doctorId);
        entity.setHospitalCode(hospitalCode);
       return selectOne(entity);
    }
}
