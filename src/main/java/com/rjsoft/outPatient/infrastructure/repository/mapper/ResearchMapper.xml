<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ResearchMapper">

    <resultMap id="PatientListMap" type="com.rjsoft.outPatient.domain.research.dto.PatientListDto">
        <id column="patId" property="patId"/>
        <result column="hisCardNo" property="hisCardNo"/>
        <result column="cardNo" property="cardNo"/>
        <result column="patName" property="patName"/>
        <result column="patAge" property="patAge"/>
        <result column="patSex" property="patSex"/>
        <result column="patSexName" property="patSexName"/>
        <result column="patPhone" property="patPhone"/>
        <result column="birthday" property="birthday"/>
        <result column="certificateNo" property="certificateNo"/>
        <result column="liveProv" property="liveProv"/>
        <result column="liveProvName" property="liveProvName"/>
        <result column="liveCity" property="liveCity"/>
        <result column="liveCityName" property="liveCityName"/>
        <result column="liveCounty" property="liveCounty"/>
        <result column="liveCountyName" property="liveCountyName"/>
        <result column="liveAddr" property="liveAddr"/>
        <result column="hospitalCode" property="hospitalCode"/>
        <result column="createTime" property="createTime"/>
        <collection property="researchProjectList"  ofType="com.rjsoft.outPatient.domain.research.dto.ResearchProjectDto">
            <id column="id" property="id" />
            <result column="projectId" property="projectId"/>
            <result column="projectName" property="projectName"/>
        </collection>
    </resultMap>


    <!--  查询科研患者列表 -->
    <select id="patientList" parameterType="com.rjsoft.outPatient.domain.research.dto.PatientListSearchDto" resultMap="PatientListMap">
        SELECT
            a.HospNo hisCardNo,
            a.patId patId,
            a.patName patName,
            a.sex patSex,
            isnull(datediff(year, a.Birthday, getdate()), '') patAge,
            a.Birthday birthday,
            a.CertificateNo certificateNo,
            d.id id,
            d.projectId projectId,
            d.projectName projectName,
            b.LiveProv liveProv,
            (select top 1 name from Tb_Dic_Area where code = b.LiveProv) liveProvName,
            b.LiveCity liveCity,
            (select top 1 name from Tb_Dic_Area where code = b.LiveCity) liveCityName,
            b.LiveCounty liveCounty,
            (select top 1 name from Tb_Dic_Area where code = b.LiveCounty) liveCountyName,
            b.LiveAddr liveAddr,
            b.PatPhone patPhone,
            a.hospitalCode hospitalCode,
            a.createTime createTime
        FROM
            Reg_Tb_PatientList a
            INNER JOIN Reg_Tb_PatientDetl b ON a.patId = b.patId
            LEFT JOIN Sci_Patient_Project d ON a.patId = d.patId and d.hospitalCode = #{patientListSearchDto.hospitalCode}
        <where>
            <if test="patientListSearchDto.hisCardNo != null and patientListSearchDto.hisCardNo != ''">
                AND a.HospNo = #{patientListSearchDto.hisCardNo}
            </if>
            <if test="patientListSearchDto.patName != null and patientListSearchDto.patName != ''">
                <bind name="pattern" value="patientListSearchDto.patName+'%'"/>
                and a.patName like #{pattern}
            </if>
            <if test="patientListSearchDto.certificateNo != null and patientListSearchDto.certificateNo != ''">
                AND a.CertificateNo = #{patientListSearchDto.certificateNo}
            </if>
            <if test="patientListSearchDto.cardNo != null and patientListSearchDto.cardNo != ''">
                AND EXISTS (select 1 from Reg_Tb_PatientCard c where c.patId = b.patId and c.cardNo = #{patientListSearchDto.cardNo} )
            </if>
        </where>
    </select>

    <!--  查询科研患者列表 -->
    <select id="recipePatientList"  parameterType="com.rjsoft.outPatient.domain.research.dto.PatientListSearchDto"  resultType="com.rjsoft.outPatient.domain.research.dto.RecipePatientListDto">
        SELECT
            distinct
            a.RegNo regNo,
            a.CardNo cardNo,
            a.OutPatientNo hisCardNo,
            a.PatId patId,
            a.PatName patName,
            e.ProjectId projectId,
            e.ProjectName projectName,
            b.GroupNo groupNo,
            a.RegistTime registTime,
            b.Remarks remarks,
            c.Sex patSex,
            convert(int,isnull( datediff( YEAR, c.Birthday, getdate( ) ), 0 )) patAge,
            c.CertificateNo certificateNo,
            d.LiveProv liveProv,
            ( SELECT TOP 1 name FROM Tb_Dic_Area WHERE code = d.LiveProv ) liveProvName,
            d.LiveCity liveCity,
            ( SELECT TOP 1 name FROM Tb_Dic_Area WHERE code = d.LiveCity ) liveCityName,
            d.LiveCounty liveCounty,
            ( SELECT TOP 1 name FROM Tb_Dic_Area WHERE code = d.LiveCounty ) liveCountyName,
            d.LiveAddr liveAddr,
            a.hospitalCode hospitalCode,
               e.followup,
               e.followupTitle
        FROM
            Reg_Tv_RegisterList a
            INNER JOIN Sci_RegisterList_Project e ON a.RegNo = e.RegNo AND a.hospitalCode = e.hospitalCode
            INNER JOIN Sci_Patient_Project b ON e.ProjectId = b.ProjectId
            AND a.PatId = b.PatId
            AND a.DoctorID = b.creator
            INNER JOIN Reg_Tb_PatientList c ON a.PatId = c.PatId
            INNER JOIN Reg_Tb_PatientDetl d ON a.PatId = d.PatId
        <where>
            AND a.DoctorID = #{patientListSearchDto.workId}
            <if test="patientListSearchDto.cardNo != null and patientListSearchDto.cardNo != ''">
                AND a.CardNo = #{patientListSearchDto.cardNo}
            </if>
            <if test="patientListSearchDto.hisCardNo != null and patientListSearchDto.hisCardNo != ''">
                AND a.OutPatientNo = #{patientListSearchDto.hisCardNo}
            </if>
            <if test="patientListSearchDto.patName != null and patientListSearchDto.patName != ''">
                AND a.PatName = #{patientListSearchDto.patName}
            </if>
            <if test="patientListSearchDto.certificateNo != null and patientListSearchDto.certificateNo != ''">
                AND c.CertificateNo = #{patientListSearchDto.certificateNo}
            </if>
            AND a.RegistTime between #{patientListSearchDto.startTime} and #{patientListSearchDto.endTime}
        </where>
        Order by ${patientListSearchDto.sortField}
    </select>

    <!--  获取病历卡号  -->
    <select id="getHisCardNo" resultType="java.lang.String">
        EXEC GetMaxHisCardNo
        #{type}
    </select>

    <!--  获取病历卡号  -->
    <select id="getRegNo" resultType="java.lang.Long">
        EXEC ups_GetSequences #{tabName},#{columnName}
    </select>

</mapper>
