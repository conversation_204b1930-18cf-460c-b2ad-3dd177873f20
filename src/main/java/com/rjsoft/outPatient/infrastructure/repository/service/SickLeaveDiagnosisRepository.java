package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.DictionaryCondition;
import com.rjsoft.outPatient.infrastructure.repository.entity.SickLeaveDiagnosis;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/30 - 13:52
 */
public interface SickLeaveDiagnosisRepository {

    /**
     * 根据病假单id及诊断编码查询诊断信息
     *
     * @param mainId       病假单id
     * @param diagnoseCode 诊断编码
     * @return 诊断信息
     */
    boolean existsWithPrimaryKey(Integer mainId, String diagnoseCode);

    /**
     * 根据病假单id删除诊断
     *
     * @param mainId 病假单id
     */
    void deleteSickLeaveDiagnosisByMainId(Integer mainId);

    /**
     * 保存
     *
     * @param entity
     */
    void saveSickLeaveDiagnosis(SickLeaveDiagnosis entity);

    /**
     * 查询状态类诊断信息
     *
     * @param hospitalCode
     * @return
     */
    List<DictionaryCondition> getDictionaryCondition(Integer hospitalCode);

}
