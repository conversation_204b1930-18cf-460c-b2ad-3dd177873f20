package com.rjsoft.outPatient.infrastructure.repository.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import org.apache.ibatis.annotations.Param;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * MZYS_TB_Diagnosticopinion
 * <AUTHOR>
@Data
@Table(name = "MZYS_TB_Diagnosticopinion")
public class DiagnosticOpinion  implements Serializable{
    /**
     *  
     */
    @Id
    @Column(name = "Id")
    private Integer id;

    /**
     * 时间
     */
    @Column(name = "RecDate")
    private Date recDate;

    /**
     * 患者编号
     */
    @Column(name = "PatNo")
    private String patId;

    /**
     * 患者姓名
     */
    @Column(name = "PatName")
    private String patName;

    /**
     * 性别
     */
    @Column(name = "PatSex")
    private String patSex;

    /**
     * 年龄
     */
    @Column(name = "PatAge")
    private String patAge;

    /**
     * 病历卡号
     */
    @Column(name = "HisCardNo")
    private String hisCardNo;

    /**
     * 初诊时间
     */
    @Column(name = "FirstDate")
    private Date firstDate;

    /**
     * 确诊时间
     */
    @Column(name = "ConfirmDate")
    private Date confirmDate;

    /**
     * 入院时间
     */
    @Column(name = "InTime")
    private Date inTime;

    /**
     * 诊断
     */
    @Column(name = "Diagnosis")
    private String diagnosis;

    /**
     * 备注
     */
    @Column(name = "Notes")
    private String notes;

    /**
     * 医生ＩＤ
     */
    @Column(name = "DoctorId")
    private Integer doctorId;

    /**
     * 医生名称
     */
    @Column(name = "DoctorName")
    private String doctorName;

    /**
     * 就诊流水号
     */
    @Column(name = "Jzlsh")
    private String medicalNum;

    /**
     * 创建时间
     */
    @Column(name = "CreateTime")
    private Date createTime;

    /**
     *  
     */
    @Column(name = "Duse")
    private String duse;


}