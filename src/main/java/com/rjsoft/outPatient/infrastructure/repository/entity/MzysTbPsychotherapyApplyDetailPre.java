package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.rjsoft.outPatient.domain.thridParty.dto.PreTreatApplyDetailDTO;
import com.ruijing.code.util.SnowflakeIdWorker;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
    * 舒辅临时治疗申请单项目明细
    */
@Data
@Table(name = "MZYS_TB_PsychotherapyApplyDetailPre")
public class MzysTbPsychotherapyApplyDetailPre implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 申请单ID
     */
    @Column(name = "apply_id")
    private Long applyId;

    /**
     * 治疗类型
     */
    @Column(name = "type_code")
    private String typeCode;

    /**
     * 治疗名称
     */
    @Column(name = "type_name")
    private String typeName;

    /**
     * 项目编码
     */
    @Column(name = "item_code")
    private Integer itemCode;

    /**
     * 项目名称
     */
    @Column(name = "item_name")
    private String itemName;

    /**
     * 项目数量
     */
    @Column(name = "quantity")
    private Integer quantity;

    private static final long serialVersionUID = 1L;

    public void saveFieldFromEntity(PreTreatApplyDetailDTO dto) {
        this.id = SnowflakeIdWorker.generateId();
        this.typeCode = dto.getTypeCode();
        this.typeName = dto.getTypeName();
        this.itemCode = dto.getItemCode();
        this.itemName = dto.getItemName();
        this.quantity = dto.getQuantity();
    }
}