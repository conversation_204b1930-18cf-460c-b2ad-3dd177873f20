package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.domain.herbAgreeRecipe.dto.AddHerbAgreeRecipeDTO;
import com.rjsoft.outPatient.domain.herbAgreeRecipe.vo.*;
import com.rjsoft.outPatient.infrastructure.repository.entity.PackageDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.PackagePrimary;
import com.rjsoft.outPatient.infrastructure.repository.mapper.HerbAgreeRecipeMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.HerbAgreeRecipeRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class HerbAgreeRecipeRepositoryImpl implements HerbAgreeRecipeRepository {

    @Autowired
    private HerbAgreeRecipeMapper herbAgreeRecipeMapper;

    //协定处方标识
    private static final Integer AGREE_RECIPE = 1;


    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public List<PackagePrimary> selectList(HerbAgreeRecipeListReqVO herbAgreeRecipeListReqVO) {
        return herbAgreeRecipeMapper.selectList(herbAgreeRecipeListReqVO);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public List<PackageDetail> selectDetailList(HerbAgreeRecipeDetailReqVO herbAgreeRecipeDetailReqVO) {
        return herbAgreeRecipeMapper.selectDetailList(herbAgreeRecipeDetailReqVO);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public Integer addHerbAgreeRecipe(AddHerbAgreeRecipeReqVO addHerbAgreeRecipeReqVO) {
        AddHerbAgreeRecipeDTO addHerbAgreeRecipeDTO = new AddHerbAgreeRecipeDTO();
        BeanUtils.copyProperties(addHerbAgreeRecipeReqVO, addHerbAgreeRecipeDTO);
        addHerbAgreeRecipeDTO.setIsAgreeRecipe(AGREE_RECIPE);
        addHerbAgreeRecipeDTO.setCreateTime(new Date());
        addHerbAgreeRecipeDTO.setUpdateTime(new Date());
        herbAgreeRecipeMapper.addHerbAgreeRecipe(addHerbAgreeRecipeDTO);
        return addHerbAgreeRecipeDTO.getId();
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public void addHerbAgreeRecipeDetail(AddHerbAgreeRecipeDetailReqVO detailReqVO) {

    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public void batchInsertAgreeRecipeDetail(Integer agreeRecipeId, Integer hospitalCode, List<HerbAgreeRecipeDetailVO> agreeRecipeList) {
        herbAgreeRecipeMapper.batchInsertAgreeRecipeDetail(agreeRecipeId, hospitalCode, agreeRecipeList);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public List<PackagePrimary> selectListByName(String agreeRecipeName, Integer shareFlag, Integer hospitalCode, Integer deptId, Integer doctorId) {
        return herbAgreeRecipeMapper.selectListByName(agreeRecipeName, shareFlag, hospitalCode, deptId, doctorId);
    }
}
