package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.dto.DpMdmCodeSystemVo;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DpMdmCodeSystemMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DpMdmCodeSystemRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class DpMdmCodeSystemRepositoryImpl implements DpMdmCodeSystemRepository {
    private DpMdmCodeSystemMapper dpMdmCodeSystemMapper;

    @DatabaseAnnotation(name = DatasourceName.HISDB)
    @Override
    public List<DpMdmCodeSystemVo> getDpMdmCodeSystemList(List<String> codeList, Integer hospitalId) {
        if(codeList==null||codeList.size()==0||hospitalId==null){
            return new ArrayList<DpMdmCodeSystemVo>();
        }
        return dpMdmCodeSystemMapper.getDpMdmCodeSystemList(codeList,hospitalId);
    }
}
