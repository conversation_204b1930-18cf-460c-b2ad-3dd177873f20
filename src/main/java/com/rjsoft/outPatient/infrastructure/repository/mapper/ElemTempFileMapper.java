package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.ElemTempAlterParam;
import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.ElemTempFileAddDto;
import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QuerySonParam;
import com.rjsoft.outPatient.infrastructure.repository.entity.ElemTempFile;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.mapping.FetchType;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

public interface ElemTempFileMapper extends BaseMapper<ElemTempFile> {

    @Select("select wjmc as fileName,wjlx as fileClass,qy as area, wjnr as fileContent, hosp_code as hospCode " +
            "from MZYS_TB_MBWJ " +
            "where wjbh = #{fileCode} and status = 0")
    ElemTempFileAddDto getTempFile(@Param("fileCode")Integer fileCode);

}
