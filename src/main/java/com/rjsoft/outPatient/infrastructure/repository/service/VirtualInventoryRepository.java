package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.DeptItempStock;
import com.rjsoft.outPatient.infrastructure.repository.entity.Recipe;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.VirtualInventory;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/8-11:15 上午
 */
public interface VirtualInventoryRepository {

    /**
     * 根据参数查询
     *
     * @param drugId     药品编码
     * @param deptId     科室编码
     * @param hospitalId 医院编码
     * @return {@link VirtualInventory}
     */
    List<VirtualInventory> getAllByDrugIdAndDeptId(Integer drugId, Integer deptId, Integer hospitalId);

    /**
     * 查询药库包装库存
     *
     * @param drugId     药品编码
     * @param deptId     药房编码
     * @param hospitalId 医院编码
     * @return 药品库存
     */
    BigDecimal getStorePackFactorInventoryByDrugIdAndDeptId(Integer drugId, Integer deptId, Integer hospitalId);

    /**
     * 查询门诊包装库存
     *
     * @param drugId     药品编码
     * @param deptId     药房编码
     * @param hospitalId 医院编码
     * @return 药品库存
     */
    BigDecimal getClinicPackFactorInventoryByDrugIdAndDeptId(Integer drugId, Integer deptId, Integer hospitalId);

    List<DeptItempStock> getClinicPackFactorInventoryByDrugIdAndDeptId(List<Integer> drugIdList, List<Integer> deptIdList);

    /**
     * 查询住院库存
     * @param drugId
     * @param deptId
     * @param hospitalId
     * @return
     */
    BigDecimal getInHospPackFactorInventoryByDrugIdAndDeptId(Integer drugId, Integer deptId, Integer hospitalId);

    /**
     * 出入库
     *
     * @param recipeList
     * @param changeType 去库存 15、加库存 16
     * @return 受影响行数
     */
    Integer inOrOutOfWarehouse(List<Recipe> recipeList, Integer changeType);

    /**
     * 出入库
     *
     * @param recipeDetailList
     * @param changeType 去库存 15、加库存 16
     * @return 受影响行数
     */
    Integer inOrOutOfWarehouseByRecipeDetail(RecipeDetail recipeDetailList, Integer changeType);

    /**
     * 录入虚拟库存
     *
     * @param recipeList
     * @return 受影响行数
     */
    Integer inOfWarehouse(List<Recipe> recipeList);

    /**
     * 录入虚拟库存
     *
     * @param recipeList
     * @return 受影响行数
     */
    Integer outOfWarehouse(List<Recipe> recipeList);

    void deleteRecord(Integer drugId, Integer deptId, Integer hospitalId, Integer sourceDetailId);
}
