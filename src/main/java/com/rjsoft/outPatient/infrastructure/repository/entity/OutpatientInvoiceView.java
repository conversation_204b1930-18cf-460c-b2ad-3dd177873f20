package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "Reg_TV_OutpatientInvoice")
public class OutpatientInvoiceView implements Serializable {
    @Column(name = "InvoiceID")
    private Long invoiceid;

    @Column(name = "ReturnInvoiceID")
    private Long returninvoiceid;

    @Column(name = "RegNo")
    private Long regno;

    @Column(name = "CardType")
    private String cardtype;

    @Column(name = "CardNo")
    private String cardno;

    @Column(name = "OutpatientNo")
    private String outpatientno;

    @Column(name = "InvoiceNo")
    private Long invoiceno;

    @Column(name = "PatID")
    private Long patid;

    @Column(name = "NewPatID")
    private Long newpatid;

    @Column(name = "PatName")
    private String patname;

    @Column(name = "ChargeType")
    private Integer chargetype;

    @Column(name = "InvoicePrefix")
    private String invoiceprefix;

    @Column(name = "InvoiceLabel")
    private String invoicelabel;

    @Column(name = "InvoiceInfo")
    private String invoiceinfo;

    @Column(name = "OpCode")
    private Integer opcode;

    @Column(name = "OpTime")
    private Date optime;

    @Column(name = "IsPrint")
    private Integer isprint;

    @Column(name = "Printer")
    private Integer printer;

    @Column(name = "PrintTime")
    private Date printtime;

    @Column(name = "TotalAmount")
    private BigDecimal totalamount;

    @Column(name = "CapitalAmount")
    private String capitalamount;

    @Column(name = "OwnFee")
    private BigDecimal ownfee;

    @Column(name = "InsuranceTotal")
    private BigDecimal insurancetotal;

    @Column(name = "InsuranceCashTotal")
    private BigDecimal insurancecashtotal;

    @Column(name = "CostTotal")
    private BigDecimal costtotal;

    @Column(name = "FLPay")
    private BigDecimal flpay;

    @Column(name = "PubPay")
    private BigDecimal pubpay;

    @Column(name = "AppendPay")
    private BigDecimal appendpay;

    @Column(name = "CurrAccountPay")
    private BigDecimal curraccountpay;

    @Column(name = "LastAccountPay")
    private BigDecimal lastaccountpay;

    @Column(name = "CurrAccountBalance")
    private BigDecimal curraccountbalance;

    @Column(name = "LastAccountBalance")
    private BigDecimal lastaccountbalance;

    @Column(name = "HisCardFee")
    private BigDecimal hiscardfee;

    @Column(name = "PayFee")
    private BigDecimal payfee;

    @Column(name = "CashFee")
    private BigDecimal cashfee;

    @Column(name = "ReceiveAmount")
    private BigDecimal receiveamount;

    @Column(name = "ChangeAmount")
    private BigDecimal changeamount;

    @Column(name = "ErrorCents")
    private BigDecimal errorcents;

    @Column(name = "[Status]")
    private Integer status;

    @Column(name = "UseFlag")
    private Integer useflag;

    @Column(name = "BussEvent")
    private Integer bussevent;

    @Column(name = "BillNo")
    private Integer billno;

    @Column(name = "Flag")
    private Integer flag;

    @Column(name = "TradeSerialNo")
    private String tradeserialno;

    @Column(name = "PrepaidRemind")
    private BigDecimal prepaidremind;

    @Column(name = "PrepaidPay")
    private BigDecimal prepaidpay;

    @Column(name = "PrepaidId")
    private Integer prepaidid;

    @Column(name = "DataFrom")
    private Integer datafrom;

    @Column(name = "FromFlag")
    private Integer fromflag;

    @Column(name = "Rate")
    private BigDecimal rate;

    @Column(name = "DiscountAmount")
    private BigDecimal discountamount;

    @Column(name = "ChargeNo")
    private Long chargeno;

    @Column(name = "OriginalInvoiceId")
    private Long originalinvoiceid;

    @Column(name = "ComputerNo")
    private String computerno;

    @Column(name = "TerminalType")
    private Integer terminaltype;

    @Column(name = "PayOrderNo")
    private String payorderno;

    @Column(name = "ReturnOrderNo")
    private String returnorderno;

    @Column(name = "DispensingWindow")
    private String dispensingwindow;

    @Column(name = "IsDelete")
    private Boolean isdelete;

    @Column(name = "CreatedBy")
    private Integer createdby;

    @Column(name = "CreatedDate")
    private Date createddate;

    @Column(name = "UpdateBy")
    private Integer updateby;

    @Column(name = "UpdateDate")
    private Date updatedate;

    @Column(name = "ReturnOpCode")
    private Integer returnopcode;

    @Column(name = "ReturnOpTime")
    private Date returnoptime;

    @Column(name = "DrugDept")
    private Integer drugdept;

    @Column(name = "HospitalCode")
    private Integer hospitalcode;

    @Column(name = "SerialNo")
    private Long serialno;

    @Column(name = "MzNo")
    private Long mzno;

    @Column(name = "DeptKind")
    private Short deptkind;

    @Column(name = "JzPay")
    private BigDecimal jzpay;

    @Column(name = "RecipeDoctor")
    private Integer recipedoctor;

    @Column(name = "RecipeDept")
    private Integer recipedept;

    @Column(name = "AccountFlag")
    private String accountflag;

    @Column(name = "DrugInfo")
    private String druginfo;

    @Column(name = "PayType")
    private Integer paytype;

    @Column(name = "PrintCount")
    private Integer printcount;

    @Column(name = "MoveFlag")
    private Integer moveflag;

    private static final long serialVersionUID = 1L;
}