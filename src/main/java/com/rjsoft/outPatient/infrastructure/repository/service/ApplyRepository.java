package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.item.dto.ApplyItemExeDeptDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplyBigForm;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplyList;

import java.util.List;

/**
 * 申请单
 *
 * <AUTHOR>
public interface ApplyRepository {

    /**
     * 根据申请单ID，加载申请单所有信息，包括下面明细
     *
     * @param applyId
     * @return
     */
    ApplyList getApplyListById(Integer applyId);

    /**
     * 根据就诊流水号，申请单类型，查询申请单
     *
     * @param receptionNo
     * @param type
     * @return
     */
    List<ApplyList> getApplyListByReceptionNo(Integer receptionNo, Integer type);

    /**
     * 根据formCode获取对应申请单配置项目总数
     *
     * @param formCode
     * @param hospitalCode
     * @return
     */
    Integer getApplyItemCount(String formCode, Integer hospitalCode);

    /**
     * 根据检查流水号修改申请单项目收费类型
     *
     * @param examineNos
     * @param hospitalCode
     */
    void updateApplyDetailStatus(List<Integer> examineNos, Integer hospitalCode);

    List<ApplyItemExeDeptDto> getApplyExeDeptByItemCode(String itemCode, String examineItemCode, String examineNo, Integer applyDetailId);

    /**
     * 根据申请单ID获取大类id
     *
     * @param applyId
     * @return
     */
    ApplyBigForm getApplyBigFormType(Integer applyId, Integer hospitalCode);

    /**
     * 根据申请单ID，加载申请单所有信息，包括下面明细
     *
     * @param applyIds
     * @return
     */
    List<ApplyList> queryApplyListById(List<Integer> applyIds, Integer hospitalCode);

    /**
     * 获取大类信息
     *
     * @param bigFormIds
     * @return
     */
    List<ApplyBigForm> queryBigFormListByIds(List<Integer> bigFormIds);

    /**
     * 根据申请单Id,加载申请单信息
     */
    List<ApplyList> getApplyListByIds(List<Integer> applyIds, Integer hospitalCode);


}
