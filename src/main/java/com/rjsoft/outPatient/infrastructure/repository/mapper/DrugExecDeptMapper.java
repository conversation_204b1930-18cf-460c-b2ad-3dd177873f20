package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.DrugExecDept;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 药品执行科室
 * <AUTHOR>
public interface DrugExecDeptMapper extends BaseMapper<DrugExecDept> {

    /**
     * 根据时间类型，星期获取药品执行科室
     * @param timeType 时间类型，1早上，2上午，3下午，4晚上
     * @param weekday 内部星期序号，周一0，周二1，周三2，周四3，周五4，周六5，周日6
     * @return
     */
    default List<Integer> getExecDeptByTime(Integer timeType, Integer weekday) {
        List<Integer> res = new ArrayList<>();
        DrugExecDept entity = new DrugExecDept();
        entity.setWeekDay(weekday);
        switch (timeType) {
            case 1:
                entity.setMorning(1);
                break;
            case 2:
                entity.setForenoon(1);
                break;
            case 3:
                entity.setAfternoon(1);
                break;
            case 4:
                entity.setNight(1);
                break;
            default:
                return res;
        }
        entity.setStatus(0);
        List<DrugExecDept> list = select(entity);
        res = list.stream().map(DrugExecDept::getDeptId).collect(Collectors.toList());
        return res;
    }
}
