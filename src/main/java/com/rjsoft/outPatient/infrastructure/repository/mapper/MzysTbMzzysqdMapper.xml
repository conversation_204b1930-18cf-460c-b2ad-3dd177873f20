<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.MzysTbMzzysqdMapper" >
  <resultMap id="BaseResultMap" type="com.rjsoft.outPatient.domain.admissionApplicationForm.pojo.MzysTbMzzysqd" >
    <result column="JZLSH" property="JZLSH" jdbcType="CHAR" />
    <result column="XM" property="XM" jdbcType="NVARCHAR" />
    <result column="XB" property="XB" jdbcType="INTEGER" />
    <result column="MZH" property="MZH" jdbcType="NVARCHAR" />
    <result column="KH" property="KH" jdbcType="NVARCHAR" />
    <result column="ZJH" property="ZJH" jdbcType="NVARCHAR" />
    <result column="ZJLX" property="ZJLX" jdbcType="NVARCHAR" />
    <result column="CWYS" property="CWYS" jdbcType="INTEGER" />
    <result column="CZR" property="CZR" jdbcType="INTEGER" />
    <result column="CZSJ" property="CZSJ" jdbcType="NVARCHAR" />
    <result column="SQDXML" property="SQDXML" jdbcType="LONGNVARCHAR" />
    <result column="DeptId" property="deptId" jdbcType="INTEGER" />
    <result column="DeptName" property="deptName" jdbcType="NVARCHAR" />
    <result column="ZDBM" property="ZDBM" jdbcType="NVARCHAR" />
    <result column="ZDMC" property="ZDMC" jdbcType="NVARCHAR" />
    <result column="InHospTime" property="inHospTime" jdbcType="TIMESTAMP" />
    <result column="SFZH" property="SFZH" jdbcType="NVARCHAR" />
    <result column="SJHM" property="SJHM" jdbcType="NVARCHAR" />
    <result column="Status" property="status" jdbcType="INTEGER" />
    <result column="SJRYSJ" property="SJRYSJ" jdbcType="TIMESTAMP" />
    <result column="Bed" property="bed" jdbcType="VARCHAR" />
    <result column="ReviewDoctor" property="reviewDoctor" jdbcType="INTEGER" />
    <result column="ReviewTime" property="reviewTime" jdbcType="TIMESTAMP" />
    <result column="CreateId" property="createId" jdbcType="INTEGER" />
    <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    JZLSH, XM, XB, MZH, KH, ZJH, ZJLX, CWYS, CZR, CZSJ, SQDXML, DeptId, DeptName, ZDBM, 
    ZDMC, InHospTime, SFZH, SJHM, Status, SJRYSJ, Bed, ReviewDoctor, ReviewTime, CreateId, 
    CreateTime
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.rjsoft.outPatient.domain.admissionApplicationForm.pojo.MzysTbMzzysqdExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from MZYS_TB_MZZYSQD
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.rjsoft.outPatient.domain.admissionApplicationForm.pojo.MzysTbMzzysqdExample" >
    delete from MZYS_TB_MZZYSQD
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.rjsoft.outPatient.domain.admissionApplicationForm.pojo.MzysTbMzzysqd" >
    insert into MZYS_TB_MZZYSQD (JZLSH, XM, XB, 
      MZH, KH, ZJH, ZJLX, 
      CWYS, CZR, CZSJ, SQDXML, 
      DeptId, DeptName, ZDBM, 
      ZDMC, InHospTime, SFZH, 
      SJHM, Status, SJRYSJ, 
      Bed, ReviewDoctor, ReviewTime, 
      CreateId, CreateTime)
    values (#{JZLSH,jdbcType=CHAR}, #{XM,jdbcType=NVARCHAR}, #{XB,jdbcType=INTEGER}, 
      #{MZH,jdbcType=NVARCHAR}, #{KH,jdbcType=NVARCHAR}, #{ZJH,jdbcType=NVARCHAR}, #{ZJLX,jdbcType=NVARCHAR}, 
      #{CWYS,jdbcType=INTEGER}, #{CZR,jdbcType=INTEGER}, #{CZSJ,jdbcType=NVARCHAR}, #{SQDXML,jdbcType=LONGNVARCHAR}, 
      #{deptId,jdbcType=INTEGER}, #{deptName,jdbcType=NVARCHAR}, #{ZDBM,jdbcType=NVARCHAR}, 
      #{ZDMC,jdbcType=NVARCHAR}, #{inHospTime,jdbcType=TIMESTAMP}, #{SFZH,jdbcType=NVARCHAR}, 
      #{SJHM,jdbcType=NVARCHAR}, #{status,jdbcType=INTEGER}, #{SJRYSJ,jdbcType=TIMESTAMP}, 
      #{bed,jdbcType=VARCHAR}, #{reviewDoctor,jdbcType=INTEGER}, #{reviewTime,jdbcType=TIMESTAMP}, 
      #{createId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.rjsoft.outPatient.domain.admissionApplicationForm.pojo.MzysTbMzzysqd" >
    insert into MZYS_TB_MZZYSQD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="JZLSH != null" >
        JZLSH,
      </if>
      <if test="XM != null" >
        XM,
      </if>
      <if test="XB != null" >
        XB,
      </if>
      <if test="MZH != null" >
        MZH,
      </if>
      <if test="KH != null" >
        KH,
      </if>
      <if test="ZJH != null" >
        ZJH,
      </if>
      <if test="ZJLX != null" >
        ZJLX,
      </if>
      <if test="CWYS != null" >
        CWYS,
      </if>
      <if test="CZR != null" >
        CZR,
      </if>
      <if test="CZSJ != null" >
        CZSJ,
      </if>
      <if test="SQDXML != null" >
        SQDXML,
      </if>
      <if test="deptId != null" >
        DeptId,
      </if>
      <if test="deptName != null" >
        DeptName,
      </if>
      <if test="ZDBM != null" >
        ZDBM,
      </if>
      <if test="ZDMC != null" >
        ZDMC,
      </if>
      <if test="inHospTime != null" >
        InHospTime,
      </if>
      <if test="SFZH != null" >
        SFZH,
      </if>
      <if test="SJHM != null" >
        SJHM,
      </if>
      <if test="status != null" >
        Status,
      </if>
      <if test="SJRYSJ != null" >
        SJRYSJ,
      </if>
      <if test="bed != null" >
        Bed,
      </if>
      <if test="reviewDoctor != null" >
        ReviewDoctor,
      </if>
      <if test="reviewTime != null" >
        ReviewTime,
      </if>
      <if test="createId != null" >
        CreateId,
      </if>
      <if test="createTime != null" >
        CreateTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="JZLSH != null" >
        #{JZLSH,jdbcType=CHAR},
      </if>
      <if test="XM != null" >
        #{XM,jdbcType=NVARCHAR},
      </if>
      <if test="XB != null" >
        #{XB,jdbcType=INTEGER},
      </if>
      <if test="MZH != null" >
        #{MZH,jdbcType=NVARCHAR},
      </if>
      <if test="KH != null" >
        #{KH,jdbcType=NVARCHAR},
      </if>
      <if test="ZJH != null" >
        #{ZJH,jdbcType=NVARCHAR},
      </if>
      <if test="ZJLX != null" >
        #{ZJLX,jdbcType=NVARCHAR},
      </if>
      <if test="CWYS != null" >
        #{CWYS,jdbcType=INTEGER},
      </if>
      <if test="CZR != null" >
        #{CZR,jdbcType=INTEGER},
      </if>
      <if test="CZSJ != null" >
        #{CZSJ,jdbcType=NVARCHAR},
      </if>
      <if test="SQDXML != null" >
        #{SQDXML,jdbcType=LONGNVARCHAR},
      </if>
      <if test="deptId != null" >
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="deptName != null" >
        #{deptName,jdbcType=NVARCHAR},
      </if>
      <if test="ZDBM != null" >
        #{ZDBM,jdbcType=NVARCHAR},
      </if>
      <if test="ZDMC != null" >
        #{ZDMC,jdbcType=NVARCHAR},
      </if>
      <if test="inHospTime != null" >
        #{inHospTime,jdbcType=TIMESTAMP},
      </if>
      <if test="SFZH != null" >
        #{SFZH,jdbcType=NVARCHAR},
      </if>
      <if test="SJHM != null" >
        #{SJHM,jdbcType=NVARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="SJRYSJ != null" >
        #{SJRYSJ,jdbcType=TIMESTAMP},
      </if>
      <if test="bed != null" >
        #{bed,jdbcType=VARCHAR},
      </if>
      <if test="reviewDoctor != null" >
        #{reviewDoctor,jdbcType=INTEGER},
      </if>
      <if test="reviewTime != null" >
        #{reviewTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null" >
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.rjsoft.outPatient.domain.admissionApplicationForm.pojo.MzysTbMzzysqdExample" resultType="java.lang.Integer" >
    select count(*) from MZYS_TB_MZZYSQD
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update MZYS_TB_MZZYSQD
    <set >
      <if test="record.JZLSH != null" >
        JZLSH = #{record.JZLSH,jdbcType=CHAR},
      </if>
      <if test="record.XM != null" >
        XM = #{record.XM,jdbcType=NVARCHAR},
      </if>
      <if test="record.XB != null" >
        XB = #{record.XB,jdbcType=INTEGER},
      </if>
      <if test="record.MZH != null" >
        MZH = #{record.MZH,jdbcType=NVARCHAR},
      </if>
      <if test="record.KH != null" >
        KH = #{record.KH,jdbcType=NVARCHAR},
      </if>
      <if test="record.ZJH != null" >
        ZJH = #{record.ZJH,jdbcType=NVARCHAR},
      </if>
      <if test="record.ZJLX != null" >
        ZJLX = #{record.ZJLX,jdbcType=NVARCHAR},
      </if>
      <if test="record.CWYS != null" >
        CWYS = #{record.CWYS,jdbcType=INTEGER},
      </if>
      <if test="record.CZR != null" >
        CZR = #{record.CZR,jdbcType=INTEGER},
      </if>
      <if test="record.CZSJ != null" >
        CZSJ = #{record.CZSJ,jdbcType=NVARCHAR},
      </if>
      <if test="record.SQDXML != null" >
        SQDXML = #{record.SQDXML,jdbcType=LONGNVARCHAR},
      </if>
      <if test="record.deptId != null" >
        DeptId = #{record.deptId,jdbcType=INTEGER},
      </if>
      <if test="record.deptName != null" >
        DeptName = #{record.deptName,jdbcType=NVARCHAR},
      </if>
      <if test="record.ZDBM != null" >
        ZDBM = #{record.ZDBM,jdbcType=NVARCHAR},
      </if>
      <if test="record.ZDMC != null" >
        ZDMC = #{record.ZDMC,jdbcType=NVARCHAR},
      </if>
      <if test="record.inHospTime != null" >
        InHospTime = #{record.inHospTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.SFZH != null" >
        SFZH = #{record.SFZH,jdbcType=NVARCHAR},
      </if>
      <if test="record.SJHM != null" >
        SJHM = #{record.SJHM,jdbcType=NVARCHAR},
      </if>
      <if test="record.status != null" >
        Status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.SJRYSJ != null" >
        SJRYSJ = #{record.SJRYSJ,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bed != null" >
        Bed = #{record.bed,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewDoctor != null" >
        ReviewDoctor = #{record.reviewDoctor,jdbcType=INTEGER},
      </if>
      <if test="record.reviewTime != null" >
        ReviewTime = #{record.reviewTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createId != null" >
        CreateId = #{record.createId,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null" >
        CreateTime = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update MZYS_TB_MZZYSQD
    set JZLSH = #{record.JZLSH,jdbcType=CHAR},
      XM = #{record.XM,jdbcType=NVARCHAR},
      XB = #{record.XB,jdbcType=INTEGER},
      MZH = #{record.MZH,jdbcType=NVARCHAR},
      KH = #{record.KH,jdbcType=NVARCHAR},
      ZJH = #{record.ZJH,jdbcType=NVARCHAR},
      ZJLX = #{record.ZJLX,jdbcType=NVARCHAR},
      CWYS = #{record.CWYS,jdbcType=INTEGER},
      CZR = #{record.CZR,jdbcType=INTEGER},
      CZSJ = #{record.CZSJ,jdbcType=NVARCHAR},
      SQDXML = #{record.SQDXML,jdbcType=LONGNVARCHAR},
      DeptId = #{record.deptId,jdbcType=INTEGER},
      DeptName = #{record.deptName,jdbcType=NVARCHAR},
      ZDBM = #{record.ZDBM,jdbcType=NVARCHAR},
      ZDMC = #{record.ZDMC,jdbcType=NVARCHAR},
      InHospTime = #{record.inHospTime,jdbcType=TIMESTAMP},
      SFZH = #{record.SFZH,jdbcType=NVARCHAR},
      SJHM = #{record.SJHM,jdbcType=NVARCHAR},
      Status = #{record.status,jdbcType=INTEGER},
      SJRYSJ = #{record.SJRYSJ,jdbcType=TIMESTAMP},
      Bed = #{record.bed,jdbcType=VARCHAR},
      ReviewDoctor = #{record.reviewDoctor,jdbcType=INTEGER},
      ReviewTime = #{record.reviewTime,jdbcType=TIMESTAMP},
      CreateId = #{record.createId,jdbcType=INTEGER},
      CreateTime = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>