package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;
import net.sf.jsqlparser.expression.DateTimeLiteralExpression;
import org.joda.time.DateTime;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
* MDM_Recipe_Whitelist  处方白名单表
* @TableName MDM_Recipe_Whitelist
*/
@Data
@Table( name ="MDM_Recipe_Whitelist" )
public class MdmRecipeWhitelist implements Serializable {

    /**
    * id
    */
    @Id
    @Column(name = "id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;
    /**
    * 医生id
    */
    @Column(name = "WorkerId")
    private Integer workerId;

    /**
     * 院区id
     */
    @Column(name = "HospitalCode")
    private Integer hospitalCode;
    /**
    * 项目名称
    */
    @Column(name = "Name")
    private String name;
    /**
    * 操作时间
    */
    @Column(name = "OperTime")
    private DateTime operTime;
    /**
    * 操作人
    */
    @Column(name = "Oper")
    private String Oper;

}
