package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.utils.TyMapUtil;
import com.rjsoft.outPatient.domain.config.dto.DoctorInfoResponse;
import com.rjsoft.outPatient.domain.doctorElemMain.dto.WorkerInfoDTO;
import com.rjsoft.outPatient.domain.doctorElemMain.dto.WorkerInfoQueryDTO;
import com.rjsoft.outPatient.domain.doctorElemMain.dto.WorkerInfoResponse;
import com.rjsoft.outPatient.domain.recipe.dto.HlyyDoctor;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.ItemCount;
import com.rjsoft.outPatient.infrastructure.repository.entity.MdmRecipeWhitelist;
import com.rjsoft.outPatient.infrastructure.repository.entity.WorkerContrast;
import com.rjsoft.outPatient.infrastructure.repository.entity.Worker;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SystemTbDepartmentMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.WorkerContrastMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.WorkerMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.WorkerRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 职工
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class WorkerRepositoryImpl implements WorkerRepository {

    WorkerMapper workerMapper;
    WorkerContrastMapper workerContrastMapper;
    SystemTbDepartmentMapper systemTbDepartmentMapper;

    Map<String, ItemCount> spDiseaseMap = null;

    /**
     * 根据ID获取所有与当前职工关联的职工信息
     *
     * @param workerId
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<Worker> getAllWorkerById(Integer workerId, Integer hospitalCode) {
        List<Worker> list = new ArrayList<>();
        Worker WorkerPOJO = workerMapper.getWorkerById(workerId, hospitalCode);
        if (WorkerPOJO != null) {
            list.add(WorkerPOJO);
        }

        List<WorkerContrast> workers = workerContrastMapper.getWorkerContrastById(workerId, hospitalCode);
        for (WorkerContrast contrast : workers) {
            WorkerPOJO = workerMapper.getWorkerById(contrast.getToWorkerId(), contrast.getHospitalId());
            if (WorkerPOJO != null) {
                list.add(WorkerPOJO);
            }
        }
        return list;
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public int judgeSpecialDisease(Integer deptId, String hospitalCode) {
//        // XXX: yutao 2024/6/17  refresh
////        return systemTbDepartmentMapper.judgeSpecialDisease(deptId, hospitalCode);
//        if(spDiseaseMap == null||spDiseaseMap.size()==0){
//            spDiseaseMap = new HashMap<>();
//            List<ItemCount> itemCountList = systemTbDepartmentMapper.speciaDiseaseCount();
//            spDiseaseMap = TyMapUtil.listToMapMul(itemCountList, "getPId", "getId");
//        }
//        ItemCount itemCount = spDiseaseMap.get("" + hospitalCode + "_" + deptId + "_");
//        if(itemCount == null){
//            return 0;
//        }
//        return itemCount.getCount();
        return systemTbDepartmentMapper.judgeSpecialDisease(deptId, hospitalCode);
    }


    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<Worker> getAllWorkerByIds(List<Integer> workerIds) {
        Weekend<Worker> weekend = new Weekend<>(Worker.class);
        weekend.weekendCriteria().andIn(Worker::getWorkerId,workerIds);
        return workerMapper.selectByExample(weekend);
    }

    /**
     * 根据医生id获取医生相关信息
     *
     * @param doctorId
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public HlyyDoctor getHlyyDoctor(Integer doctorId, Integer hospitalCode) {
        return workerMapper.getHlyyDoctor(doctorId, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Worker getWorkerById(Integer workerId, String hospCode) {
        return workerMapper.getWorkerById(workerId, Integer.valueOf(hospCode));
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Worker getWorkerByIdAll(Integer workerId, Integer hospitalCode) {
        return workerMapper.getWorkerByIdAll(workerId, hospitalCode);
    }


    /**
     * 模糊搜索医生信息
     *
     * @param inputCode
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<DoctorInfoResponse> getDoctorInfos(String inputCode, Integer hospitalCode) {
        return workerMapper.getDoctorInfos(inputCode, hospitalCode);
    }

    /**
     * 根据医生ids,hospitalCode，模糊查找医生信息
     *
     * @param inputCode
     * @param hospitalCode
     * @param list
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<DoctorInfoResponse> getDoctorInfosByList(String inputCode, Integer hospitalCode, List<Integer> list) {
        return workerMapper.getDoctorInfosByList(inputCode, hospitalCode, list);
    }

    /**
     * 根据总分院入参，查询是否有对应的记录
     *
     * @param param
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<WorkerContrast> getWorkerContrast(WorkerInfoDTO param) {
        return workerContrastMapper.getWorkerContrast(param);
    }

    /**
     * 添加职工对照信息
     *
     * @param param
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public int addWorker(WorkerInfoDTO param) {
        return workerContrastMapper.addWorker(param);
    }

    /**
     * 删除职工信息
     *
     * @param id
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public void delWorker(Integer id) {
        workerContrastMapper.delWorker(id);
    }

    /**
     * 查询总院医生工号信息
     *
     * @param workerId
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public int queryGeneralWorker(Integer workerId) {
        return workerContrastMapper.queryGeneralWorker(workerId);
    }

    /**
     * 查询分院医生工号信息
     *
     * @param workerId
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public int queryBranchWorker(Integer workerId) {
        return workerContrastMapper.queryBranchWorker(workerId);

    }

    /**
     * 根据主键Id查询实体信息
     *
     * @param id
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public WorkerContrast queryEntityById(Integer id) {
        return workerContrastMapper.queryEntityById(id);
    }

    /**
     * 修改分院职工对照记录
     *
     * @param id
     * @param workerId
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public int updateWorkerInfo(Integer id, Integer workerId) {
        return workerContrastMapper.updateWorkerInfo(id, workerId);
    }

    /**
     * 查询总分院职工对照列表
     *
     * @param param
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<WorkerInfoResponse> queryWorker(WorkerInfoQueryDTO param) {
        return workerContrastMapper.queryWorker(param);
    }

    /**
     * 通过医生工号查询医生信息
     *
     * @param workerNo
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Worker> getDoctorInfo(String workerNo, Integer hospitalCode) {
        return workerMapper.getDoctorInfoByWorkerNo(workerNo, hospitalCode);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Worker> getAllWorkerByInput(String input, Integer hospitalCode) {
        return workerMapper.getAllWorkerByInput(input, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public MdmRecipeWhitelist getIsWhiteDoc(Integer doctorId, Integer hospitalCode) {
        return workerMapper.getIsWhiteDoc(doctorId, hospitalCode);
    }

    /**
     * 根据多学科门诊挂号记录获取副主任级别以上医生人数
     *
     * <AUTHOR>
     * @param regNo 挂号流水号
     * @param hospitalCode 医院编码
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Integer getMDTWorkNum(Integer regNo, Integer hospitalCode) {
        return workerMapper.getMDTWorkNum(regNo, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Worker> getRelationWorker(Integer doctorId, Integer hospitalCode) {
        return workerMapper.getRelationWorker(doctorId, hospitalCode);
    }


}
