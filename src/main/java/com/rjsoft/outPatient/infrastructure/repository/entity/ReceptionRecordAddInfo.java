package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 看诊记录补充信息
 *
 * <AUTHOR>
 * @since 2021/8/9 - 15:08
 */
@Data
@Table(name = "MZYS_TB_KZJLBCXX")
public class ReceptionRecordAddInfo implements Serializable {

    /**
     * 就诊流水号
     */
    @Id
    @Column(name = "jzlsh")
    private Long receptionNo;
    /**
     * 待配药
     */
    @Column(name = "dpy")
    private Integer prescription;
    /**
     * 复核诊断
     */
    @Column(name = "fhzd")
    private Integer reviewDiagnosis;
    /**
     * 明细 备注
     */
    @Column(name = "mxb")
    private Integer detailsRemarks;
    /**
     * 资料室
     */
    @Column(name = "zls")
    private Integer zls;

    /**
     * 医院编码
     */
    @Column(name = "hospitalCode")
    private Integer hospitalCode;

}
