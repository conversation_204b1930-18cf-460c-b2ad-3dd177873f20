package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.CaseHistoryLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.common.Mapper;

import java.util.Date;
import java.util.List;

@Repository
public interface CaseHistoryLogMapper extends Mapper<CaseHistoryLog> {


    /**
     * 查询病历操作日志
     *
     * @return
     */
    List<CaseHistoryLog> getCaseHistoryLogControl(@Param("startTime") Date startTime,
                                                  @Param("endTime") Date endTime,
                                                  @Param("hospitalCode") Integer hospitalCode,
                                                  @Param("hisCardNo") String hisCardNo,
                                                  @Param("regNo") Integer regNo,
                                                  @Param("patName") String patName,
                                                  @Param("workerId") Integer workerId,
                                                  @Param("state") Integer state,
                                                  @Param("sortField") String sortField);


}
