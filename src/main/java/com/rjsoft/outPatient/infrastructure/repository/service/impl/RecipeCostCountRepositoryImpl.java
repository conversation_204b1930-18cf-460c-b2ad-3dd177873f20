package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeCostCountMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeCostCountRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2021/7/28 - 9:22
 */
@Service
@AllArgsConstructor
public class RecipeCostCountRepositoryImpl implements RecipeCostCountRepository {

    private final RecipeCostCountMapper recipeCostCountMapper;

    @Override
    public BigDecimal getZfPerCapitaCost(int doctorId, LocalDate start, LocalDate end) {
        return recipeCostCountMapper.getZfPerCapitaCost(doctorId, start, end);
    }
}
