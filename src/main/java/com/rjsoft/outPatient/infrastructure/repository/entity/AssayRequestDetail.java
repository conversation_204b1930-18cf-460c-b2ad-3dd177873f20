package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;

/**
 * 系统套餐申请单明细预存
 * <AUTHOR>
@Data
@Table(name = "MZYS_TB_HYSQMX")
public class AssayRequestDetail  implements Serializable {

    /**
     * 申请流水号
     */
    @Column(name = "sqlsh")
    private Long requestNo;
    /**
     * 申请明细流水号
     */
    @Column(name = "sqmxlsh")
    @Id
    private Long requestDetailNo;

    /**
     * 就诊流水号
     */
    @Column(name = "jzlsh")
    private Long receptionNo;

    /**
     * 挂号流水号
     */
    @Column(name = "ghlsh")
    private Long regNo;

    /**
     * 项目编码
     */
    @Column(name = "xmbm")
    private Integer itemCode;

    /**
     * 项目名称
     */
    @Column(name = "xmmc")
    private String itemName;

    /**
     * 申请医生
     */
    @Column(name = "sqys")
    private Integer applyDoctor;

    /**
     * 申请明细
     */
    @Column(name = "zxks")
    private Integer execDept;

    /**
     * 医院编码
     */
    @Id
    @Column(name = "yybm")
    private Integer hospitalCode;

    /**
     * 操作标识
     * -1 删除 0 新增 1 修改
     */
    @Transient
    private Integer opFlag;

    public AssayRequestDetail() {
    }

    public AssayRequestDetail(Long assayRequestDetailNo, AssayRequest assayRequest) {
        this.opFlag = 0;
        this.regNo = assayRequest.getRegNo();
        this.requestDetailNo = assayRequestDetailNo;
        this.requestNo = assayRequest.getRequestNo();
        this.itemCode = assayRequest.getRequestCode();
        this.itemName = assayRequest.getRequestName();
        this.receptionNo = assayRequest.getReceptionNo();
        this.applyDoctor = assayRequest.getApplyDoctor();
        this.hospitalCode = assayRequest.getHospitalCode();
    }
}
