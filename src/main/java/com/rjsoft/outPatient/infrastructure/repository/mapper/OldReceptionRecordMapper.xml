<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.OldReceptionRecordMapper">
    <select id="getReceptionByRegNo" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.OldReceptionRecord">
        select a.jzlsh receptionNo,a.ghlsh regNo,a.ksbm deptId,a.ysbm doctorId,a.sckzrq firstDate
        from MZYS_TV_KZJL a
        inner join MZYS_TV_MZCFMX b on a.ghlsh = b.ghlsh
        where b.sflx in (12, 13)
          and b.ghlsh in
          <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
              #{item}
          </foreach>
          and ISNULL(b.sqd, '') != 'MECT'
    </select>
</mapper>