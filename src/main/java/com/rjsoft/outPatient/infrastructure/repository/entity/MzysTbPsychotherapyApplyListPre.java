package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.rjsoft.outPatient.domain.thridParty.dto.PreTreatApplyDTO;
import com.ruijing.code.util.SnowflakeIdWorker;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
    * 舒辅临时治疗申请单表
    */
@Data
@Table(name = "MZYS_TB_PsychotherapyApplyListPre")
public class MzysTbPsychotherapyApplyListPre implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 患者ID
     */
    @Column(name = "patient_id")
    private Integer patientId;

    /**
     * 患者姓名
     */
    @Column(name = "patient_name")
    private String patientName;

    /**
     * 申请医生ID
     */
    @Column(name = "apply_doctor_id")
    private String applyDoctorId;

    /**
     * 申请时间
     */
    @Column(name = "apply_time")
    private Date applyTime;

    /**
     * 就诊流水号
     */
    @Column(name = "reception_no")
    private Integer receptionNo;

    /**
     * 就诊医生ID
     */
    @Column(name = "reception_doctor_id")
    private Integer receptionDoctorId;

    /**
     * 就诊时间
     */
    @Column(name = "reception_time")
    private Date receptionTime;

    /**
     * 院区ID
     */
    @Column(name = "hospital_id")
    private Integer hospitalId;

    /**
     * 申请单状态 -1已作废 0未就诊 1已就诊
     */
    @Column(name = "apply_status")
    private Integer applyStatus;

    /**
     * 无需提醒的医生ID
     */
    @Column(name = "filter_doctor")
    private String filterDoctor;

    /**
     * 创建人
     */
    @Column(name = "create_by")
    private Integer createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @Column(name = "update_by")
    private Integer updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public void saveFieldFromEntity(PreTreatApplyDTO dto) {
        this.id = SnowflakeIdWorker.generateId();
        this.patientId = dto.getPatientId();
        this.patientName = dto.getPatientName();
        this.applyDoctorId = dto.getApplyDoctorId();
        this.applyTime = dto.getApplyTime();
        this.hospitalId = dto.getHospitalCode();
        this.applyStatus = 0;
        this.createBy = 999;
        this.createTime = new Date();
    }
}