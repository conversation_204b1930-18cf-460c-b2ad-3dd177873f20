<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DrugInfomationMapper">


    <select id="getDrugUnitInfo"
            resultType="com.rjsoft.outPatient.domain.prescriptionAudit.dto.DrugUnitInfoDto">
        select a.DrugId                           as drugId,
               a.Dose                             as dose,
               rtrim(c.HisDictionaryName)                as doseUnit,
               rtrim(d.HisDictionaryName)                as smallUnit,
               rtrim(e.HisDictionaryName)                as packUnit,
               a.ClinicPackFactor                 as packCount,
               a.RetailPrice * a.ClinicPackFactor as retailPrice
        from Drug_Tb_DrugInfomation a (nolock)
                 inner join System_Tb_PubItems b(nolock) on a.DrugId = b.ItemCode and b.HospitalId = #{hospitalCode} and b.Stopped = 0
                 left join TB_Dic_HisDictionary c (nolock) on a.DoseUnit = c.HisDictionaryCode and c.DictionaryTypeID = 66 and  c.HospitalId = #{hospitalCode}
                 inner join TB_Dic_HisDictionary d (nolock) on a.WardPackUnit = d.HisDictionaryCode and d.DictionaryTypeID = 67 and d.HospitalId = #{hospitalCode}
                 inner join TB_Dic_HisDictionary e (nolock)  on a.ClinicPackUnit = e.HisDictionaryCode and e.DictionaryTypeID = 67 and  e.HospitalId = #{hospitalCode}
        where a.DrugId = #{drugId}
          and a.HospitalId = #{hospitalCode}

    </select>

    <select id="checkDrugIsYYY" resultType="java.lang.Boolean">
        select KeyValue from System_Tb_TemplateExtension where KeyCode='YYY' and ClassId=#{itemId} and HospitalId=#{hospId}
    </select>

    <select id="ListDrugAllYYY" resultType="com.rjsoft.outPatient.domain.recipe.dto.DrugYYYDto">
        select KeyValue,ClassId from System_Tb_TemplateExtension where KeyCode='YYY'and HospitalId=#{hospId}
    </select>

    <select id="checkYYYAuthority" resultType="java.lang.Integer">
        select COUNT(*)
        from System_Pub_DoctorAuthority a
        inner join System_Pub_DoctorAuthorityRelation b on a.Id=b.AuthorityId and a.HospitalId=b.HospitalId
        where a.WorkerId=#{doctorId} and a.HospitalId=#{hospId} and a.authorityType=1 and b.UseRange=1 and (a.DelFlag=0 or a.DelFlag is null)
    </select>

</mapper>