package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.ItemCategory;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ItemCategoryMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ItemCategoryRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

/**
 * 收费项目收费类型字典
 */
@Service
@AllArgsConstructor
public class ItemCategoryRepositoryImpl implements ItemCategoryRepository {

    ItemCategoryMapper itemCategoryMapper;


    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<ItemCategory> getItemCategoryList(Integer hospitalCode) {
        ItemCategory itemCategory = new ItemCategory();
        itemCategory.setStopped(1);
        itemCategory.setHospitalCode(Converter.toString(hospitalCode));
        return itemCategoryMapper.select(itemCategory);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<ItemCategory> getItemCategoryByhospitalCode(Integer hospitalCode) {
        Weekend<ItemCategory> weekend = new Weekend<>(ItemCategory.class);
        weekend.weekendCriteria().andEqualTo(ItemCategory::getStopped,1).andEqualTo(ItemCategory::getMzUse,1).andEqualTo(ItemCategory::getHospitalCode,hospitalCode);
        return itemCategoryMapper.selectByExample(weekend);
    }
}
