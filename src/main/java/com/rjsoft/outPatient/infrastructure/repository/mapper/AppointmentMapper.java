package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Appointment;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Arrays;
import java.util.List;

public interface AppointmentMapper extends Mapper<Appointment> {

    /**
     * 患者当天是否已存在预约信息
     *
     * @param workDate
     * @param certificateNo
     * @param subjectId
     */
    default List<Appointment> isReserved(String workDate, String certificateNo, Integer subjectId) {
        List<Integer> list = Arrays.asList(2, 3, 7); //新增停用状态也不提示 edit by will 20221220
        Weekend<Appointment> weekend = new Weekend<Appointment>(Appointment.class);
        WeekendCriteria<Appointment, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(Appointment::getCheckDate, workDate)
                .andEqualTo(Appointment::getCertificateNo, certificateNo)
                .andEqualTo(Appointment::getSubjectId, subjectId)
                .andNotIn(Appointment::getAppointmentStatus, list);
        return selectByExample(weekend);
    }


    /**
     * 根据身份证号和科目Id判断患者当天是否已经预约占号
     *
     * @param certificateNo
     * @param subjectId
     * @param hospitalCode
     * @return
     */
    int getAppointmentCount(@Param("certificateNo") String certificateNo,
                            @Param("subjectId") Integer subjectId,
                            @Param("hospitalCode") Integer hospitalCode);


    /**
     * 根据患者初复诊状态查询预约表中已经预约的数量
     *
     * @param isReview
     * @param subjectId
     * @param deptId
     * @param doctorId
     * @param timeMs
     * @param hospitalCode
     * @return
     */
    Integer isReviewCount(@Param("isReview") Integer isReview,
                          @Param("subjectId") Integer subjectId,
                          @Param("deptId") Integer deptId,
                          @Param("doctorId") Integer doctorId,
                          @Param("timeMs") String timeMs,
                          @Param("hospitalCode") Integer hospitalCode
    );


    /**
     * 查询当日预约表中预约信息列表
     *
     * @param doctorId
     * @param deptId
     * @param hospitalCode
     * @return
     */
    List<Appointment> getTodayAppointment(Long doctorId,Long deptId,String hospitalCode);


    /**
     * 查询当天医生号源数量
     *
     * @param doctorId
     * @param deptId
     * @param hospitalCode
     * @return
     */
    int getTodayTotalNum(Long doctorId,Long deptId,String hospitalCode);

    /**
     * 查询当天医生不同号源类型数量
     *
     * @param doctorId
     * @param deptId
     * @param hospitalCode
     * @param type
     * @return
     */
    int getTodayNum(Integer doctorId,Integer deptId,String hospitalCode,Integer type);
}
