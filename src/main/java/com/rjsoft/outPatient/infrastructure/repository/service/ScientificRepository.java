package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.scientific.dto.ProjectItemsDto;
import com.rjsoft.outPatient.domain.scientific.vo.PatJoinProjectReqVO;

import java.util.HashMap;
import java.util.List;

/**
 * 科研
 *
 * <AUTHOR>
public interface ScientificRepository {

    /**
     * 查询当前医生科研课题信息
     */
    List<HashMap> getProjectsList(String doctorId, Integer hospitalCode);

    void patJoinProject(PatJoinProjectReqVO patJoinProjectReqVO);
}
