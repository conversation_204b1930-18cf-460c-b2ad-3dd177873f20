package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.herbAgreeRecipe.vo.*;
import com.rjsoft.outPatient.infrastructure.repository.entity.PackageDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.PackagePrimary;

import java.util.List;

public interface HerbAgreeRecipeRepository {
    List<PackagePrimary> selectList(HerbAgreeRecipeListReqVO herbAgreeRecipeListReqVO);

    List<PackageDetail> selectDetailList(HerbAgreeRecipeDetailReqVO herbAgreeRecipeDetailReqVO);

    Integer addHerbAgreeRecipe(AddHerbAgreeRecipeReqVO addHerbAgreeRecipeReqVO);

    void addHerbAgreeRecipeDetail(AddHerbAgreeRecipeDetailReqVO detailReqVO);

    void batchInsertAgreeRecipeDetail(Integer agreeRecipeId, Integer hospitalCode,List<HerbAgreeRecipeDetailVO> agreeRecipeList);

    List<PackagePrimary> selectListByName(String agreeRecipeName, Integer shareFlag, Integer hospitalCode, Integer deptId, Integer doctorId);
}
