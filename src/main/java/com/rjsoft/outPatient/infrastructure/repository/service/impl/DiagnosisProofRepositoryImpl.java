package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.RegistrationFormStatusEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.DiagnosisProof;
import com.rjsoft.outPatient.infrastructure.repository.entity.DiagnosisProofDisease;
import com.rjsoft.outPatient.infrastructure.repository.entity.DiagnosisProofOld;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DiagnosisProofDiseaseMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DiagnosisProofMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DiagnosisProofOldMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DiagnosisProofRepository;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/11-5:43 下午
 */
@Service
@AllArgsConstructor
public class DiagnosisProofRepositoryImpl implements DiagnosisProofRepository {

    private final DiagnosisProofMapper diagnosisProofMapper;
    private final DiagnosisProofOldMapper diagnosisProofOldMapper;
    private final DiagnosisProofDiseaseMapper diagnosisProofDiseaseMapper;

    @Override
    @DatabaseAnnotation
    public List<DiagnosisProof> findByCardNoAndBlCardNumAndNameAndStatus(String cardNo, String blCardNum, String name, Integer status) {
        final Weekend<DiagnosisProof> weekend = new Weekend<>(DiagnosisProof.class);
        final WeekendCriteria<DiagnosisProof, Object> weekendCriteria = weekend.weekendCriteria();
        if (StringUtils.isNotBlank(cardNo)) {
            weekendCriteria.andEqualTo(DiagnosisProof::getCardNo, cardNo);
        }
        if (StringUtils.isNotBlank(blCardNum)) {
            weekendCriteria.andEqualTo(DiagnosisProof::getBlCardNum, blCardNum);
        }
        if (StringUtils.isNotBlank(name)) {
            weekendCriteria.andEqualTo(DiagnosisProof::getName, name);
        }
        if (status != -1) {
            weekendCriteria.andEqualTo(DiagnosisProof::getState, status);
        }
        weekendCriteria.andEqualTo(DiagnosisProof::getDelFlag, false);
        return diagnosisProofMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public List<DiagnosisProofOld> findOldByCardNoAndBlCardNumAndNameAndStatus(String cardNo, String blCardNum, String name, Integer status) {
        final Weekend<DiagnosisProofOld> weekend = new Weekend<>(DiagnosisProofOld.class);
        final WeekendCriteria<DiagnosisProofOld, Object> weekendCriteria = weekend.weekendCriteria();
//        if (StringUtils.isNotBlank(cardNo)) {
//            weekendCriteria.andEqualTo(DiagnosisProof::getCardNo, cardNo);
//        }
        if (StringUtils.isNotBlank(blCardNum)) {
            weekendCriteria.andEqualTo(DiagnosisProofOld::getBlCardNum, blCardNum);
        }
        if (StringUtils.isNotBlank(name)) {
            weekendCriteria.andEqualTo(DiagnosisProofOld::getName, name);
        }
        if (status != -1) {
            weekendCriteria.andEqualTo(DiagnosisProofOld::getState, status);
        }
        weekendCriteria.andEqualTo(DiagnosisProofOld::getDelFlag, false);
        return diagnosisProofOldMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<DiagnosisProof> getDiseaseProofByIdAndRegNo(Integer id, Integer regNo, Integer hospitalCode) {
        final Weekend<DiagnosisProof> weekend = new Weekend<>(DiagnosisProof.class);
        if (regNo == null || hospitalCode == null) {
            return new ArrayList<>(0);
        }
        weekend.weekendCriteria()
                .andEqualTo(DiagnosisProof::getId, id)
                .andEqualTo(DiagnosisProof::getRegNo, regNo)
                .andEqualTo(DiagnosisProof::getHospitalCode, hospitalCode)
                .andEqualTo(DiagnosisProof::getDelFlag, false);
        return diagnosisProofMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public DiagnosisProof getDiseaseProofById(Integer id) {
        if (id == null) {
            return null;
        }
        final Weekend<DiagnosisProof> weekend = new Weekend<>(DiagnosisProof.class);
        weekend.weekendCriteria().andEqualTo(DiagnosisProof::getId, id).andEqualTo(DiagnosisProof::getDelFlag, false);
        return diagnosisProofMapper.selectOneByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public void save(DiagnosisProof addEntity) {
        diagnosisProofMapper.insertSelective(addEntity);
        final Integer mainId = addEntity.getId();
        final List<DiagnosisProofDisease> diagnosisList = addEntity.getDiagnosisProofDiseaseList();
        for (DiagnosisProofDisease disease : diagnosisList) {
            disease.setMainId(mainId);
            diagnosisProofDiseaseMapper.insertSelective(disease);
        }
    }

    @Override
    @DatabaseAnnotation
    public void update(DiagnosisProof updateEntity) {
        diagnosisProofMapper.updateByPrimaryKey(updateEntity);
        if (updateEntity.getDelFlag() || RegistrationFormStatusEnum.unalterable(updateEntity.getState())) {
            // 如果是删除或者不可变更的状态，就不需要修改诊断信息
            return;
        }
        final List<DiagnosisProofDisease> diagnosisList = updateEntity.getDiagnosisProofDiseaseList();
        this.deleteDiseaseProofDiagnosisByMainId(updateEntity.getId());
        for (DiagnosisProofDisease diagnosis : diagnosisList) {
            this.saveDiseaseProofDiagnosis(diagnosis);
        }
    }

    @Override
    @DatabaseAnnotation
    public Boolean existsWithDiseaseProofIdAndDiagnosticCode(Integer diseaseProofId, String diagnosticCode) {
        final Weekend<DiagnosisProofDisease> weekend = new Weekend<>(DiagnosisProofDisease.class);
        weekend.setCountProperty("id");
        weekend.weekendCriteria().andEqualTo(DiagnosisProofDisease::getMainId, diseaseProofId)
                .andEqualTo(DiagnosisProofDisease::getDiagnoseCode, diagnosticCode);
        return diagnosisProofDiseaseMapper.selectCountByExample(weekend) > 0;
    }

    @Override
    @DatabaseAnnotation
    public Integer saveDiseaseProofDiagnosis(DiagnosisProofDisease diagnosisProofDisease) {
        return diagnosisProofDiseaseMapper.insertSelective(diagnosisProofDisease);
    }

    @Override
    @DatabaseAnnotation
    public Integer deleteDiseaseProofDiagnosisByMainId(Integer mainId) {
        if (ObjectUtils.isEmpty(mainId)) {
            return 0;
        }
        final Weekend<DiagnosisProofDisease> weekend = new Weekend<>(DiagnosisProofDisease.class);
        weekend.weekendCriteria().andEqualTo(DiagnosisProofDisease::getMainId, mainId);
        return diagnosisProofDiseaseMapper.deleteByExample(weekend);
    }

}
