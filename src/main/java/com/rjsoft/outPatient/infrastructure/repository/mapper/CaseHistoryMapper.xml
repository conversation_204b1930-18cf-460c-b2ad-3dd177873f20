<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.CaseHistoryMapper">
    <select id="getCaseHistoryById" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.CaseHistoryVO">
        select id as blId, bl as blXml, opCode, opTime
        from MZYS_TB_DZBL
        where id = #{blId}
    </select>

    <select id="queryDzblFromMzys" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.CaseHistoryVO">
        select id    as blId,
               jzlsh as medicalNum,
               bl    as blXml
        from MZYS_TB_DZBL (nolock)
        where jzlsh = #{jzlsh}
    </select>

    <select id="queryPatChief" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.PatChiefDTO">
        SELECT hzbh as patId,
               zz   as symptom,
               zzsj as allTime,
               zbc  as courseDisease
        FROM MZYS_TB_MZZSXX (nolock)
        WHERE hzbh = #{patId}
          and yybm = #{hospitalCode}
    </select>

    <select id="getDeptName" resultType="java.lang.String">
        SELECT DeptName
        FROM System_Tb_Department
        where DeptId = #{dept}
          and hospitalId = #{hospitalCode}
    </select>

    <select id="GetMedicalHistory" resultType="java.lang.String">
        select top(1)  '症状:' + ISNULL(bl.query('XTextDocument/XElements/Element/XElements/Element[ID=**********]').value(
        '(/Element/TextValue)[1]', 'nvarchar(max)'),'')
		+ ';时间:' + ISNULL( bl.query('XTextDocument/XElements/Element/XElements/Element[ID=''时间'']').value(
        '(/Element/TextValue)[1]', 'nvarchar(max)'),'')
		+ ';总病程:' + ISNULL(bl.query('XTextDocument/XElements/Element/XElements/Element[ID=''总病程'']').value(
        '(/Element/TextValue)[1]', 'nvarchar(max)'),'')
		+ ';现病史:' + ISNULL( bl.query('XTextDocument/XElements/Element/XElements/Element[ID=**********]').value(
        '(/Element/TextValue)[1]', 'nvarchar(max)'),'')
		+ ';发病时间:' + ISNULL(bl.query('XTextDocument/XElements/Element/XElements/Element/XElements/Element[ID=**********]').value(
        '(/Element/TextValue)[1]', 'nvarchar(max)'),'')
		+ ';发病诱因:' +  ISNULL(bl.query('XTextDocument/XElements/Element/XElements/Element/XElements/Element[ID=8888880767]').value(
        '(/Element/TextValue)[1]', 'nvarchar(max)'),'')
		+ ';主要表现:' + ISNULL(bl.query('XTextDocument/XElements/Element/XElements/Element/XElements/Element[ID=8888880773]').value(
        '(/Element/TextValue)[1]', 'nvarchar(max)'),'')
		+ ';' + ISNULL(bl.query('XTextDocument/XElements/Element/XElements/Element/XElements/Element[ID=8888880774]').value(
        '(/Element/TextValue)[1]', 'nvarchar(max)'),'')
		+ ';' + ISNULL(bl.query('XTextDocument/XElements/Element/XElements/Element/XElements/Element[ID=8888880775]').value(
        '(/Element/TextValue)[1]', 'nvarchar(max)'),'')
		+ ';' + ISNULL(bl.query('XTextDocument/XElements/Element/XElements/Element/XElements/Element[ID=8888880776]').value(
        '(/Element/TextValue)[1]', 'nvarchar(max)'),'')
		+ ';' + ISNULL(bl.query('XTextDocument/XElements/Element/XElements/Element/XElements/Element[ID=**********]').value(
        '(/Element/TextValue)[1]', 'nvarchar(max)'),'')
		+ ';' + ISNULL(bl.query('XTextDocument/XElements/Element/XElements/Element[ID=''bqybqk'']').value(
        '(/Element/TextValue)[1]', 'nvarchar(max)'),'')
        from MZYS_TB_DZBL (nolock)
        where jzlsh = #{receptionNo}
          and hospitalCode = #{hospitalCode}
    </select>
    <select id="getCaseHistoryByGuidRegNo"
            resultType="com.rjsoft.outPatient.domain.caseHistory.dto.QueryHistoryCaseInfoResult">
        select d.id blId,d.ksmc deptName,d.ysxm doctorName
        from
        MZYS_TB_KZJL c (nolock)
        inner join MZYS_TB_DZBL d (nolock) on c.jzlsh=d.jzlsh
        where c.ghlsh = #{guidRegNo}
        <if test="startDate!=null and endDate!=null">
            and (c.sckzrq between #{startDate} and #{endDate})
        </if>
    </select>
    <select id="getCaseHistoryByGuidRegNoOffset"
            resultType="com.rjsoft.outPatient.domain.caseHistory.dto.QueryHistoryCaseInfoResult">
        select d.id blId,d.ksmc deptName,d.ysxm doctorName
        from
        MZYS_TB_KZJL_DATA c (nolock)
        inner join MZYS_TB_DZBL_DATA d (nolock) on c.jzlsh=d.jzlsh
        where c.ghlsh = #{guidRegNo}
        <if test="startDate!=null and endDate!=null">
            and (c.sckzrq between #{startDate} and #{endDate})
        </if>
    </select>
    <select id="queryBlHistoryByBlId"
            resultType="com.rjsoft.outPatient.domain.caseHistory.dto.CaseHistoryByIdResponse">
        select bl                                                                          as blXml,
               bl.query('XTextDocument/BodyText').value('(/BodyText)[1]', 'nvarchar(max)') as blHtml
        from MZYS_TB_DZBL (nolock)
        where id = #{blId}
    </select>


    <select id="queryBlHistoryFromInternet"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.InternetCaseHistoryDetail">
        select Complaint, MedicalHistory, HandlingSuggestion, DiagnoseName
        from Tbt_Internet_VisitRecords (nolock)
        where VisitId = #{blId}
    </select>

    <select id="queryBlHistoryFromZyOffset"
            resultType="com.rjsoft.outPatient.domain.caseHistory.dto.CaseHistoryByIdResponse">
        select bl                                                                          as blXml,
               bl.query('XTextDocument/BodyText').value('(/BodyText)[1]', 'nvarchar(max)') as blHtml
        from MZYS_TB_DZBL_DATA (nolock)
        where id = #{blId}
    </select>
    <select id="caAuthorizedData" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.CAAuthorizedData">
        SELECT
        cjrq dateTime,
        CASE

        WHEN Authorized = 1 THEN
        '认证成功' ELSE '未认证'
        END result,
        COUNT ( jzlsh ) receptionCount
        FROM
        (
        SELECT
        a.jzlsh,
        a.ysbm,
        CONVERT ( VARCHAR ( 10 ), a.cjrq, 120 ) cjrq,
        B.upload Authorized
        FROM
        MZYS_TB_DZBL a (nolock)
        INNER JOIN MZYS_TB_CALastUploadTime c (nolock) ON a.ysbm= c.DoctorId
        INNER JOIN MZYS_TB_CABL b (nolock) ON b.jzlsh= a.jzlsh
        WHERE
        a.cjrq> c.LastTime
        AND a.cjrq &lt; DATEADD( MINUTE,- 10, GETDATE( ) )
        AND a.ysbm = #{doctorId}
        <if test="hospitalCode != null">
            AND a.hospitalCode=#{hospitalCode}
        </if>
        ) t
        GROUP BY
        t.cjrq,
        t.Authorized
        ORDER BY
        t.Authorized
    </select>
    <select id="getCaseHistory" resultType="com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo">
        select jzlsh reception
        from MZYS_TB_DZBL (nolock)
        where cast(jzlsh as varchar(50)) = #{reception}
          and tjbj = 1
    </select>
    <select id="queryNewPatBl" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.BLContentDto">
        SELECT id   blId,
               bl   blXml,
               dybj printStatus,
               tjbj submitStatus,
               ysbm doctorId
        FROM MZYS_TB_DZBL (nolock)
        where jzlsh = #{receptionNo}
          and hospitalCode = #{hospitalCode}
    </select>
    <select id="queryOldPatBl" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.BLContentDto">
        SELECT id   blId,
               bl   blXml,
               dybj printStatus,
               tjbj submitStatus,
               ysbm doctorId
        FROM MZYS_TB_DZBL (nolock)
        where jzlsh = #{receptionNo}
    </select>
    <select id="queryCheckBls" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.CheckBlDto">
        select id,
        blkh hisCardNo,
        hzbh patId,
        jzlsh receptionNo,
        hzxm patName,
        bl blXml,
        isnull(shcs, 0) auditNum,
        ysbm creDoctorId,
        dybj printStatus,
        tjbj submitStatus,
        hospitalCode hospitalCode,
        isnull(czbj,0) visitFlag,
        regno regNo,
        ksbm deptId
        from MZYS_TB_DZBL (nolock)
        WHERE tjbj = 1
        and convert(varchar (10), tjsj, 120) between #{startTime} and #{endTime}
        and ysbm = #{doctorId}
        and hospitalCode = #{hospitalCode}
        <if test="patName != null">
            and hzxm like '%' + #{patName} + '%'
        </if>
        <if test="auditNum != null">
            and isnull(shcs, 0) = #{auditNum}
        </if>
        <if test="auditFlag != null">
            and isnull(shbj, 0) = #{auditFlag}
        </if>
        <if test="completeFlag != null">
            and isnull(wcbj, 0) = #{completeFlag}
        </if>
        <if test="dealFlag != null">
            and isnull(clbj, 0) = #{dealFlag}
        </if>
        <if test="firstAuditFlag != null">
            and isnull(csbj, 0) = #{firstAuditFlag}
        </if>
        <if test="firstDiagnosisFlag != null">
            and isnull(czbj, 0) = #{firstDiagnosisFlag}
        </if>
    </select>
    <select id="queryOldCheckBls" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.CheckBlDto">
        select a.id id,
        a.blkh hisCardNo,
        a.hzbh patId,
        a.jzlsh receptionNo,
        a.hzxm patName,
        c.sfzh certificateNo,
        a.bl blXml,
        isnull(a.shcs, 0) auditNum,
        a.ysbm creDoctorId,
        a.dybj printStatus,
        a.tjbj submitStatus,
        b.ghlsh regNo,
        a.ksbm deptId,
        a.czbj visitFlag
        from MZYS_TB_DZBL a (nolock)
        inner join MZYS_TB_KZJL b (nolock) on a.jzlsh = b.jzlsh
        inner join GHSF_TB_HZJBXX c (nolock) on b.hzbh = c.hzbh
        WHERE a.tjbj = 1
        and convert(varchar (10), tjsj, 120) between #{startTime} and #{endTime}
        and a.ysbm = #{doctorId}
        <if test="patName != null">
            and a.hzxm like '%' + #{patName} + '%'
        </if>
        <if test="auditNum != null">
            and isnull(a.shcs, 0) = #{auditNum}
        </if>
        <if test="auditFlag != null">
            and isnull(a.shbj, 0) = #{auditFlag}
        </if>
        <if test="completeFlag != null">
            and isnull(a.wcbj, 0) = #{completeFlag}
        </if>
        <if test="dealFlag != null">
            and isnull(a.clbj, 0) = #{dealFlag}
        </if>
        <if test="firstAuditFlag != null">
            and isnull(a.csbj, 0) = #{firstAuditFlag}
        </if>
        <if test="firstDiagnosisFlag != null">
            and isnull(a.czbj, 0) = #{firstDiagnosisFlag}
        </if>
    </select>
    <select id="getFirstVisitDate" resultType="java.lang.String">
        select convert(varchar (10), isnull(a.初诊日期, ''), 23)
        from Tbv_MzPatinfoBa a
        where a.院号 = #{hisCardNo}
    </select>
    <update id="updateNewHandleState">
        update MZYS_TB_DZBL
        set clbj = 1
        where id = #{id}
    </update>
    <select id="selectById" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.CaseHistory"
            useCache="false">
        select isnull(tjbj, 0) submitFlag,
               isnull(wcbj, 0) completeFlag,
               isnull(dybj, 0) printFlag,
               isnull(shbj, 0) auditFlag
        from MZYS_TB_DZBL (nolock)
        where id = #{blId}
    </select>

    <select id="getCaseHistoryByTime"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.CaseHistory">
        select a.id blId,
        a.blkh blCardNo,
        a.hzbh patientId,
        a.hzxm patientName,
        a.cjrq createTime,
        a.xgrq updateTime,
        a.ksbm deptId,
        a.ksmc deptName,
        a.ysbm doctorId,
        a.ysxm doctorName,
        a.jzlsh receptionNo,
        a.regno regNo,
        a.tempCode tempCode,
        a.dybj printFlag,
        a.tjbj submitFlag,
        a.tjsj submitDate,
        a.wcbj completeFlag,
        a.wcsj completeTime,
        a.czbj firstDiagnosisFlag,
        a.shcs auditNum,
        a.csbj firstAuditFlag,
        a.shbj auditFlag,
        a.clbj dealFlag,
        a.opCode opCode,
        a.opTime opTime,
        a.hospitalCode hospitalCode
        from MZYS_TB_DZBL a (nolock)
        inner join MZYS_TB_KZJL b (nolock) on a.jzlsh = b.jzlsh and a.hospitalCode = b.hospitalCode
        left join MZYS_TB_DZBLSH c (nolock) on a.id = c.Blid and a.hospitalCode = c.HospitalCode
        where b.sckzrq between #{startTime} and #{endTime}
        and a.HospitalCode = #{hospitalCode}
        and a.tjbj=1
        and isnull(c.CheckSort,1) !=2
        <if test="deptId != null">
            and a.ksbm = #{deptId}
        </if>
    </select>

    <select id="getInternetCaseHistoryIdByRegNos"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.InternetCaseHistory">
        select visitId as blId,regNo
        from Tbt_Internet_VisitRecords (nolock)
        <where>
            <if test="startTime != null">
                <![CDATA[opTime >= #{startTime,jdbcType=TIMESTAMP}]]>
            </if>
            <if test="endTime != null">
                <![CDATA[AND opTime <= #{endTime,jdbcType=TIMESTAMP}]]>
            </if>
            AND regNo in
            <foreach collection="regNos" index="index" item="regNo" separator="," close=")" open="(">
                #{regNo}
            </foreach>
        </where>
    </select>

    <select id="getInternetCaseHistoryByRegNos"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.InternetCaseHistory">
        select h.RegNo, deptName, z.Name as doctorName, h.hospitalCode
        from Reg_Tv_RegisterList h (nolock)
        inner join Reg_Tb_PatientList p (nolock) on p.PatID=h.PatID and h.Status=0
        left join System_Tb_Department f on f.DeptId=h.DeptId and h.HospitalCode=f.HospitalId
        left join System_Tb_Worker z on h.DoctorId=z.WorkerId and h.HospitalCode=z.HospitalId
        where h.Status=0
        AND h.hospitalCode = #{hospitalCode}
        AND h.regNo in
        <foreach collection="internetRegNos" index="index" item="regNo" separator="," close=")" open="(">
            #{regNo}
        </foreach>
    </select>

    <insert id="saveCaRecord">
        exec Usp_CA_SaveRecord
        #{receptionNo},
        #{caId}
    </insert>

    <select id="getCaRecordId" resultType="java.lang.Integer">
        select isnull(MAX(serialno), 0)
        from MZYS_TB_CABL (nolock)
        where caId = #{caId}
    </select>

    <select id="getCaseHistoryByPatientGuid"
            resultType="com.rjsoft.outPatient.domain.caseHistory.dto.QueryHistoryCaseInfoResult" useCache="false">
        select d.cjrq creDate,
        d.id blId,
        case when d.ksbm = '999' then '药理基地' else d.ksmc end deptName,
        d.ysxm doctorName
        from
        MZYS_TB_KZJL c (nolock)
        inner join MZYS_TB_DZBL d (nolock) on c.jzlsh=d.jzlsh
        where c.hzbh = #{patientGuid}
        and c.bz='未挂号就诊'
        <if test="startDate!=null and endDate!=null">
            and (c.sckzrq between #{startDate} and #{endDate})
        </if>
    </select>


    <select id="getOldestCaseHistory"
            resultType="com.rjsoft.outPatient.domain.caseHistory.dto.QueryHistoryCaseInfoResult" useCache="true">
        select
        b.RegistDate creDate,
        convert(varchar(10),a.lsh) blId,
        a.DeptName deptName,
        a.DoctorName doctorName
        from mzemr.dbo.tbt_OutPatientEmr a (nolock)
        inner join dbo.Tbv_MdBl c (nolock) on c.BlId = a.BlId
        inner join dbo.Tbt_GhRegList_Time b (nolock) on b.RegNo=c.RegNo
        inner join dbo.Tbt_PubPatientInfo d (nolock) on d.PatNo=b.PatNo
        where d.PatSfz=#{certificateNo} and b.Status=0
        <if test="startDate != null  ">
            and b.RegistDate &gt;= #{startDate}
        </if>
        <if test="endDate != null  ">
            and b.RegistDate &lt;= #{endDate}
        </if>
        union all
        select
        b2.RegistDate creDate,
        convert(varchar(10),a2.lsh) blId,
        a2.DeptName deptName,
        a2.DoctorName doctorName
        from mzemr.dbo.tbt_OutPatientEmr a2 (nolock)
        inner join dbo.Tbv_MdBl c2 (nolock) on c2.BlId = a2.BlId
        inner join dbo.Tbt_GhRegList_Move b2 (nolock) on b2.RegNo=c2.RegNo
        inner join dbo.Tbt_PubPatientInfo d2 (nolock) on d2.PatNo=b2.PatNo
        where d2.PatSfz=#{certificateNo} and b2.Status=0
        <if test="startDate != null  ">
            and b2.RegistDate &gt;= #{startDate}
        </if>
        <if test="endDate != null  ">
            and b2.RegistDate &lt;= #{endDate}
        </if>
        union all
        select
        b3.RegistDate creDate,
        convert(varchar(10),a3.lsh) blId,
        a3.DeptName deptName,
        a3.DoctorName doctorName
        from mzemr.dbo.tbt_OutPatientEmr a3 (nolock)
        inner join dbo.Tbv_MdBl c3 (nolock) on c3.BlId = a3.BlId
        inner join dbo.Tbt_GhRegList_Time b3 (nolock) on b3.RegNo=c3.RegNo
        inner join dbo.Tbt_PubPatientInfo d3 (nolock) on d3.PatNo=b3.PatNo
        where d3.PatSfz=#{certificateNo} and b3.Status=0
        <if test="startDate != null  ">
            and b3.RegistDate &gt;= #{startDate}
        </if>
        <if test="endDate != null  ">
            and b3.RegistDate &lt;= #{endDate}
        </if>
    </select>

    <select id="getOldestCaseHistoryDetail"
            resultType="com.rjsoft.outPatient.domain.caseHistory.dto.CaseHistoryByIdResponse" useCache="false">
        select EMR blXml
        from tbt_OutPatientEmr (nolock)
        where lsh = #{blId}
    </select>

    <select id="getOldestCaseHistoryByPatNo"
            resultType="com.rjsoft.outPatient.domain.caseHistory.dto.QueryHistoryCaseInfoResult" useCache="true">
        select
        b.RegistDate creDate,
        convert(varchar(10),a.lsh) blId,
        a.DeptName deptName,
        a.DoctorName doctorName
        from mzemr.dbo.tbt_OutPatientEmr a (nolock)
        inner join dbo.Tbv_MdBl c (nolock) on c.BlId = a.BlId
        inner join dbo.Tbt_GhRegList_Time b (nolock) on b.RegNo=c.RegNo
        inner join dbo.Tbt_PubPatientInfo d (nolock) on d.PatNo=b.PatNo
        where d.PatId=#{patNo} and b.Status=0
        <if test="startDate != null  ">
            and b.RegistDate &gt;= #{startDate}
        </if>
        <if test="endDate != null  ">
            and b.RegistDate &lt;= #{endDate}
        </if>
        union all
        select
        b2.RegistDate creDate,
        convert(varchar(10),a2.lsh) blId,
        a2.DeptName deptName,
        a2.DoctorName doctorName
        from mzemr.dbo.tbt_OutPatientEmr a2 (nolock)
        inner join dbo.Tbv_MdBl c2 (nolock) on c2.BlId = a2.BlId
        inner join dbo.Tbt_GhRegList_Move b2 (nolock) on b2.RegNo=c2.RegNo
        inner join dbo.Tbt_PubPatientInfo d2 (nolock) on d2.PatNo=b2.PatNo
        where d2.PatId=#{patNo} and b2.Status=0
        <if test="startDate != null  ">
            and b2.RegistDate &gt;= #{startDate}
        </if>
        <if test="endDate != null  ">
            and b2.RegistDate &lt;= #{endDate}
        </if>
        union all
        select
        b3.RegistDate creDate,
        convert(varchar(10),a3.lsh) blId,
        a3.DeptName deptName,
        a3.DoctorName doctorName
        from mzemr.dbo.tbt_OutPatientEmr a3 (nolock)
        inner join dbo.Tbv_MdBl c3 (nolock) on c3.BlId = a3.BlId
        inner join dbo.Tbt_GhRegList_Time (nolock) b3 on b3.RegNo=c3.RegNo
        inner join dbo.Tbt_PubPatientInfo d3 (nolock) on d3.PatNo=b3.PatNo
        where d3.PatId=#{patNo} and b3.Status=0
        <if test="startDate != null  ">
            and b3.RegistDate &gt;= #{startDate}
        </if>
        <if test="endDate != null  ">
            and b3.RegistDate &lt;= #{endDate}
        </if>
    </select>

    <select id="getCaseHistoryFromZYProc"
            resultType="com.rjsoft.outPatient.domain.caseHistory.dto.QueryHistoryCaseInfoResult">
        exec usp_Mz_GetHistoryDZBL
        @patid=#{patId},
        @startTime=#{startDate},
        @endTime=#{endDate}
    </select>
</mapper>