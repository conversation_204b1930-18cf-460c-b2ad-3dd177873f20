package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.hospitalProve.pojo.MzysTbZyzlog;
import com.rjsoft.outPatient.domain.hospitalProve.pojo.MzysTbZyzlogExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MzysTbZyzlogMapper {
    int countByExample(MzysTbZyzlogExample example);

    int deleteByExample(MzysTbZyzlogExample example);

    int deleteByPrimaryKey(Long logId);

    int insert(MzysTbZyzlog record);

    int insertSelective(MzysTbZyzlog record);

    List<MzysTbZyzlog> selectByExample(MzysTbZyzlogExample example);

    MzysTbZyzlog selectByPrimaryKey(Long logId);

    int updateByExampleSelective(@Param("record") MzysTbZyzlog record, @Param("example") MzysTbZyzlogExample example);

    int updateByExample(@Param("record") MzysTbZyzlog record, @Param("example") MzysTbZyzlogExample example);

    int updateByPrimaryKeySelective(MzysTbZyzlog record);

    int updateByPrimaryKey(MzysTbZyzlog record);
}