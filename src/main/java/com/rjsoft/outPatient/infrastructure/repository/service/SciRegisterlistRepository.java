package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList;
import com.rjsoft.outPatient.infrastructure.repository.entity.SciRegisterlist;

public interface SciRegisterlistRepository {
    boolean saveSciRegisterlist(SciRegisterlist sciRegisterlist);

    boolean saveRegRegisterlist(RegisterList registerList);

    SciRegisterlist getSciRegisterlist(Integer regNo);
}
