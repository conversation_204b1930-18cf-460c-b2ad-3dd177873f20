package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.YesOrNoEnum;
import com.rjsoft.outPatient.domain.enterHospitalRequisition.dto.ProcessScheduleResult;
import com.rjsoft.outPatient.infrastructure.repository.entity.EnterHospitalRequisition;
import com.rjsoft.outPatient.infrastructure.repository.entity.NotificationDetails;
import com.rjsoft.outPatient.infrastructure.repository.mapper.EnterHospitalRequisitionMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.NotificationDetailsMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.NotificationDetailsRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/19 - 14:16
 */
@Service
@AllArgsConstructor
public class NotificationDetailsRepositoryImpl implements NotificationDetailsRepository {

    private final NotificationDetailsMapper notificationDetailsMapper;
    @Autowired
    private EnterHospitalRequisitionMapper enterHospitalRequisitionMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Integer findCountNotificationDetailsByRgNoAndGzdName(String rgNo, Integer hospitalCode) {
        Weekend<NotificationDetails> weekend = new Weekend<>(NotificationDetails.class);
        weekend.weekendCriteria()
                .andEqualTo(NotificationDetails::getHospitalId, hospitalCode)
                .andEqualTo(NotificationDetails::getIsDelete, YesOrNoEnum.NO.getCode())
                .andEqualTo(NotificationDetails::getRgNo, rgNo);
        final WeekendCriteria<NotificationDetails, Object> criteria = notificationDetailsMapper.commonConditionsByGzdName();
        weekend.and(criteria);
        return notificationDetailsMapper.selectCountByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public NotificationDetails findNotificationDetailsByRgNoAndGzdName(String rgNo, String gzdName, Integer hospitalCode) {
        Weekend<NotificationDetails> weekend = new Weekend<>(NotificationDetails.class);
        weekend.weekendCriteria()
                .andEqualTo(NotificationDetails::getHospitalId, hospitalCode)
                .andEqualTo(NotificationDetails::getRgNo, rgNo)
                .andEqualTo(NotificationDetails::getGZDName, gzdName)
                .andNotEqualTo(NotificationDetails::getFinishStatue,99)
                .andEqualTo(NotificationDetails::getIsDelete, YesOrNoEnum.NO.getCode());
        List<NotificationDetails> notificationDetails = notificationDetailsMapper.selectByExample(weekend);
        if(CollUtil.isEmpty(notificationDetails)){
            return null;
        }
        return notificationDetails.get(0);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<String> getNotificationDetailsByRgNosAndGzdName(List<String> rgNos, String gzdName) {
        return enterHospitalRequisitionMapper.getNotificationDetailsByRgNosAndGzdName(rgNos,gzdName);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<EnterHospitalRequisition> getSq(List<Long> regNos1) {
       List<EnterHospitalRequisition>  sq =enterHospitalRequisitionMapper.getSq(regNos1);
       return sq;
    }

    /**
     * 查询报告结果
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public ProcessScheduleResult getInspect(String receptionNo, Integer hospitalCode) {
        return enterHospitalRequisitionMapper.getInspect(receptionNo,hospitalCode);
    }
}
