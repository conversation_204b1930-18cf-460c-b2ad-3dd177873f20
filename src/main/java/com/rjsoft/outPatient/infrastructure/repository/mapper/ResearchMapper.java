package com.rjsoft.outPatient.infrastructure.repository.mapper;


import com.rjsoft.outPatient.domain.research.dto.PatientListDto;
import com.rjsoft.outPatient.domain.research.dto.PatientListSearchDto;
import com.rjsoft.outPatient.domain.research.dto.RecipePatientListDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ResearchMapper {
    /**
     * 查询科研患者列表
     * @param patientListSearchDto
     * @return
     */
    List<PatientListDto> patientList(@Param("patientListSearchDto") PatientListSearchDto patientListSearchDto);

    /**
     * 查询科研已就诊患者列表
     * @param patientListSearchDto
     * @return
     */
    List<RecipePatientListDto> recipePatientList(@Param("patientListSearchDto") PatientListSearchDto patientListSearchDto);

    /**
     * 获取病历卡号
     * @param type
     * @return
     */
    String getHisCardNo(Integer type);

    /**
     * 获取就诊流水号
     * @param tabName,columnName
     * @return
     */
    Long getRegNo(String tabName, String columnName);
}
