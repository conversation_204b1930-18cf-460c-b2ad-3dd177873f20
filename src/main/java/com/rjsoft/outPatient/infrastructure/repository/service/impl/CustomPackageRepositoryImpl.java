package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.enums.YesOrNoEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.PackagePrimary;
import com.rjsoft.outPatient.infrastructure.repository.mapper.CustomPackageMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SysFunctionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.CustomPackageRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/7/6 - 10:07
 */
@Service
@AllArgsConstructor
public class CustomPackageRepositoryImpl implements CustomPackageRepository {

    CustomPackageMapper customPackageMapper;
    SysFunctionMapper sysFunctionMapper;

    @Override
    @DatabaseAnnotation
    public List<PackagePrimary> getCustomPackage(Integer packageCategory, Integer hospitalCode, Integer packageRange, Integer doctorId) {
        final PackagePrimary entity = new PackagePrimary();
        entity.setPackageCategory(packageCategory);
        entity.setHospitalCode(hospitalCode);
        // 0 正常 1 删除
        entity.setStatus(YesOrNoEnum.NO.getCode());
        entity.setPackageRange(packageRange);
        entity.setCreateId(1 == packageRange ? doctorId : null);
        return customPackageMapper.select(entity);
    }

    @Override
    @DatabaseAnnotation
    public void saveCustomPackage(PackagePrimary packagePrimary) {
        packagePrimary.setCreateTime(sysFunctionMapper.getDate());
        customPackageMapper.insertSelective(packagePrimary);
    }

    @Override
    @DatabaseAnnotation
    public void updateCustomPackage(PackagePrimary packagePrimary, Example example) {
        packagePrimary.setCreateTime(sysFunctionMapper.getDate());
        if (example == null) {
            customPackageMapper.updateByPrimaryKeySelective(packagePrimary);
        } else {
            customPackageMapper.updateByExampleSelective(packagePrimary, example);
        }
    }

    @Override
    @DatabaseAnnotation
    public String getCustomPackageNameById(Integer id, Integer hospitalCode) {
        final Weekend<PackagePrimary> weekend = new Weekend<>(PackagePrimary.class);
        weekend.selectProperties("packageName");
        weekend.weekendCriteria().andEqualTo(PackagePrimary::getPackageId, id).andEqualTo(PackagePrimary::getHospitalCode, hospitalCode);
        final PackagePrimary packagePrimary = customPackageMapper.selectOneByExample(weekend);
        if (packagePrimary != null) {
            return Optional.ofNullable(packagePrimary.getPackageName()).orElse("");
        }
        return "";
    }
}
