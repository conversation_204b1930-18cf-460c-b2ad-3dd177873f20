package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.report.dto.XdtReportResponse;
import com.rjsoft.outPatient.infrastructure.repository.entity.ReportXdt;
import com.sun.org.apache.bcel.internal.generic.NEW;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

/**
 * 心电图
 * <AUTHOR>
public interface ReportXdtMapper extends Mapper<ReportXdt> {
    /**
     * 根据挂号流水号获取心电图报告
     * @param regNo
     * @param cardNo
     */
   List<ReportXdt> getXdtReport(@Param("regNo") Long regNo, @Param("cardNo")String cardNo);

   /**
    * 根据报告id获取心电图详情
    * @param id
    */
   default ReportXdt getXdtReportDetail(Long id){
       ReportXdt report = new ReportXdt();
       report.setId(id);
       return selectByPrimaryKey(report);
   }



}
