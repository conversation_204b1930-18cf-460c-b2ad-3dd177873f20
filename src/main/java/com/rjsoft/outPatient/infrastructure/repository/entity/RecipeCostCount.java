package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 处方费用统计 视图
 *
 * <AUTHOR>
 * @since 2021/7/27 - 17:48
 */
@Data
@Table(name = "MZYS_TB_CFFYTJ")
public class RecipeCostCount implements Serializable {

    /**
     * 医生编码
     */
    @Column(name = "ysbm")
    private Integer doctorId;
    /**
     * 挂号人次
     */
    @Column(name = "ghrc")
    private Integer ghrc;
    @Column(name = "yb")
    private Short yb;
    @Column(name = "isyb")
    private Short isyb;
    @Column(name = "isqtyb")
    private Short isqtyb;
    @Column(name = "isyp")
    private Short isyp;
    @Column(name = "cfzje")
    private BigDecimal totamt;
    @Column(name = "ypzje")
    private BigDecimal drugamount;
    @Column(name = "fypzje")
    private BigDecimal ndrugamount;
    @Column(name = "ybhj")
    private BigDecimal ybtot;
    @Column(name = "ybje")
    private BigDecimal ybamount;
    @Column(name = "qtybje")
    private BigDecimal qtybamount;
    /**
     * 自费药品金额
     */
    @Column(name = "zfypje")
    private BigDecimal zfdrugamount;
    @Column(name = "ybfyphj")
    private BigDecimal ybndrugtot;
    @Column(name = "ybfypje")
    private BigDecimal ybndrugamount;
    @Column(name = "qtybfypje")
    private BigDecimal qtybndrugamount;
    @Column(name = "dzzje")
    private BigDecimal dztotamt;
    @Column(name = "dzypze")
    private BigDecimal dzdrugamount;
    @Column(name = "dzybypze")
    private BigDecimal dzybdrugamount;
    @Column(name = "dzqtypze")
    private BigDecimal dzqtybdrugamount;
    @Column(name = "dzqtfypze")
    private BigDecimal dzqtybndrugamount;
    @Column(name = "sgzje")
    private BigDecimal sgtotamt;
    @Column(name = "sgypze")
    private BigDecimal sgdrugamount;
    @Column(name = "sgybypze")
    private BigDecimal sgybdrugamount;
    @Column(name = "sgqtypze")
    private BigDecimal sgqtybdrugamount;
    @Column(name = "sgybfypze")
    private BigDecimal sgybndrugamount;
    @Column(name = "sgqtfypze")
    private BigDecimal sgqtybndrugamount;
    @Column(name = "Zrc")
    private Integer zrc;
    @Column(name = "YbRcTot")
    private Integer ybrctot;
    @Column(name = "YbRc")
    private Integer ybrc;
    @Column(name = "QtYbRc")
    private Integer qtybrc;
    @Column(name = "FYbrc")
    private Integer fybrc;
    @Column(name = "Optime")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp createTime;

}
