package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.report.dto.*;
import com.rjsoft.outPatient.infrastructure.repository.entity.ReportXdt;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/9/11 10:52
 * @description
 **/
public interface ReportRepository {
    /**
     * 获取危机值
     *
     * @param hisCardNo
     * @return
     */
    List<CrisisValueDto> getCrisisValue(String hisCardNo);

    /**
     * 放射报告
     *
     * @param hisCardNo
     * @return
     */
    RadiationReportDto getRadiationReport(String hisCardNo);

    /**
     * 化验报告明细
     *
     * @param newSqdh
     * @param mid
     * @param endTime
     * @param hospitalCode
     * @return
     */
    List<TestReportDetailDto> getTestReportDetail(String newSqdh, Integer mid, Date endTime, Integer hospitalCode);


    /**
     * 化验报告列表
     *
     * @param patName
     * @param patSfz
     * @param startTime
     * @param endTime
     * @return
     */
    List<TestReportDto> getTestReportList(String patName, String patSfz, Date startTime, Date endTime);

    /**
     * 模糊搜索化验项目列表
     *
     * @param code
     */
    List<TestTypeResponse> getTestType(String code, Integer hospId);

    /**
     * 根据化验列表匹配单价和单位
     */
    List<TestItemResponse> getTestItemList(List<TestTypeResponse> testTypeResponses, Integer hospId);

    /**
     * 根据挂号流水号获取心电图报告
     *
     * @param regNo
     * @param cardNo
     */
    List<ReportXdt> getXdtReport(Long regNo, String cardNo);

    /**
     * 根据报告id获取心电图详情
     *
     * @param id
     */
    ReportXdt getXdtReportDetail(Long id);

    /**
     * 检查报告
     *
     * @param hisCardNo
     * @param startTime
     * @return
     */
    List<CheckReportDto> getCheckReportList(String hisCardNo,Date startTime);

}
