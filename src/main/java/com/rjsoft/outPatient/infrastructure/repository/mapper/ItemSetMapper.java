package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.packages.dto.ItemSetDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.ItemSet;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

/**
 * 系统套餐
 * <AUTHOR>
public interface ItemSetMapper extends BaseMapper<ItemSet>, ExampleMapper<ItemSet> {

    List<ItemSetDto> getItemSetDtoByHospitalId(Integer itemCategory, Integer hospitalId,String itemNameOrInputCode);
}
