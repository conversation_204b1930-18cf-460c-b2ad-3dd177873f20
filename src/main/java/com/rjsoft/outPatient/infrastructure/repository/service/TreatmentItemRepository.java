package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.TreatmentItem;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/11/14-1:56 下午
 */
public interface TreatmentItemRepository {

    /**
     * 查询所有治疗项目
     *
     * @param hospitalCode 医院编码
     * @return {@link TreatmentItem} for List
     */
    List<TreatmentItem> findAll(Integer hospitalCode);


    /**
     * 查询所有治疗项目
     *
     * @param hospitalCode 医院编码
     * @return {@link TreatmentItem} for Map
     */
    Map<String, String> findAllAsMap(Integer hospitalCode);

}
