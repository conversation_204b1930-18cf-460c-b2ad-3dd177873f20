package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.infrastructure.repository.entity.Appointment;
import com.rjsoft.outPatient.infrastructure.repository.entity.AppointmentPatient;
import com.rjsoft.outPatient.infrastructure.repository.mapper.AppointmentMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.AppointmentPatientMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.AppointmentRepository;
import com.ruijing.code.util.DateUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

/**
 * 预约表
 *
 * <AUTHOR>
@AllArgsConstructor
@Service
public class AppointmentRepositoryImpl implements AppointmentRepository {

    private final AppointmentMapper appointmentMapper;

    AppointmentPatientMapper appointmentPatientMapper;

    /**
     * 患者当天是否已存在预约信息
     *
     * @param workDate
     * @param certificateNo
     * @param subjectId
     * @param hospitalCode
     */
    @Override
    public List<Appointment> isReserved(String workDate, String certificateNo, Integer subjectId, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER3);
        }
        return appointmentMapper.isReserved(workDate, certificateNo, subjectId);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public int getAppointmentCount(String certificateNo, Integer subjectId, Integer hospitalCode) {
        return appointmentMapper.getAppointmentCount(certificateNo, subjectId, hospitalCode);
    }


    @Override
    public List<Appointment> isAppointment(List<Integer> subjectId, String certificateNo, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER3);
        }
        Weekend<Appointment> weekend = new Weekend<>(Appointment.class);
        weekend.weekendCriteria()
                .andEqualTo(Appointment::getCertificateNo, certificateNo)
                .andEqualTo(Appointment::getHospitalCode, hospitalCode)
                .andIn(Appointment::getSubjectId, subjectId);
        return appointmentMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.RJCONFIGER)
    public Integer isReviewCount(Integer isReview, Integer subjectId, Integer deptId, Integer doctorId, String timeMs, Integer hospitalCode) {
        return appointmentMapper.isReviewCount(isReview, subjectId, deptId, doctorId, timeMs, hospitalCode);
    }

    @Override
    public List<Appointment> getAppointmentBySQHs(List<Long> appointmentSQHs, String hospitalCode) {
        if (hospitalCode.equals("1")) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER3);
        }
        Weekend<Appointment> weekend = new Weekend<>(Appointment.class);
        weekend.weekendCriteria().andIn(Appointment::getSqh,appointmentSQHs)
//                .andEqualTo(Appointment::getRemarkA1,"GTYY")
                .andEqualTo(Appointment::getHospitalCode,hospitalCode);
        return appointmentMapper.selectByExample(weekend);
    }

    @Override
    public List<Appointment> getTodayAppointment(Long doctorId,Long deptId, String hospitalCode) {
        if (hospitalCode.equals("1")) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER3);
        }
        return appointmentMapper.getTodayAppointment(doctorId,deptId,hospitalCode);
    }

    @Override
    public List<Appointment> getTodayAppointmentList(Long doctorId, Long deptId, String hospitalCode) {
        if (hospitalCode.equals("1")) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER3);
        }
        Weekend<Appointment> weekend = new Weekend<>(Appointment.class);
        weekend.weekendCriteria().andEqualTo(Appointment::getHospitalCode,hospitalCode)
                .andEqualTo(Appointment::getDoctorId,doctorId)
                .andEqualTo(Appointment::getDeptId,deptId)
                .andEqualTo(Appointment::getIsDelete,0)
                .andCondition("DATEDIFF(DAY,CheckDate, getdate()) = 0");
        return appointmentMapper.selectByExample(weekend);
    }

    @Override
    public int getTodayTotalNum(Long doctorId, Long deptId, String hospitalCode) {
        if (hospitalCode.equals("1")) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER3);
        }
        return appointmentMapper.getTodayTotalNum(doctorId,deptId,hospitalCode);
    }

    @Override
    public int countBySubjectIdAndVipNum(Integer subjectId, Integer hospitalCode, String vipNum) {
        String dataName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode)? DatasourceName.RJCONFIGER:DatasourceName.RJCONFIGER3;
        DataSourceSwitchAspect.changeDataSource(dataName);
        Weekend<Appointment> weekend = new Weekend<>(Appointment.class);
        weekend.weekendCriteria().andEqualTo(Appointment::getHospitalCode,hospitalCode)
                .andEqualTo(Appointment::getSubjectId,subjectId)
                .andEqualTo(Appointment::getVipAppointmentNum,vipNum)
                .andGreaterThan(Appointment::getCreatedDate, DateUtil.getCurDateTime(DateUtil.DAY_PATTERN))
                .andEqualTo(Appointment::getIsDelete,0);
        return appointmentMapper.selectCountByExample(weekend);
    }

    @Override
    public Appointment getBySqh(Long appointmentNum, Integer hospitalCode) {
        String dataName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode)? DatasourceName.RJCONFIGER:DatasourceName.RJCONFIGER3;
        DataSourceSwitchAspect.changeDataSource(dataName);
        Weekend<Appointment> weekend = new Weekend<>(Appointment.class);
        weekend.weekendCriteria().andEqualTo(Appointment::getHospitalCode,hospitalCode).andEqualTo(Appointment::getSqh,appointmentNum);
        return appointmentMapper.selectOneByExample(weekend);
    }

    @Override
    public Boolean updateVipNumBySqh(Integer hospitalCode, String vipNum, Long appointmentNum) {
        String dataName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode)? DatasourceName.RJCONFIGER:DatasourceName.RJCONFIGER3;
        DataSourceSwitchAspect.changeDataSource(dataName);
        if(appointmentNum!=null){
            Weekend<Appointment> weekend = new Weekend<>(Appointment.class);
            weekend.weekendCriteria().andEqualTo(Appointment::getHospitalCode,hospitalCode).andEqualTo(Appointment::getSqh,appointmentNum);
            Appointment update = new Appointment();
            update.setVipAppointmentNum(vipNum);
            return appointmentMapper.updateByExampleSelective(update,weekend)>0;
        }else{
            return false;
        }
    }


    /**
     * 根据电话和身份证号获取患者预约信息
     *
     * @param phone
     * @param certificate
     */
    @Override
    @DatabaseAnnotation(name = "RJCONFIGER")
    public AppointmentPatient getPatientAppoint(String phone, String certificate) {
        return appointmentPatientMapper.getPatientAppoint(phone, certificate);
    }

    /**
     * 根据电话和身份证号获取患者预约信息，判断患者有没有认证
     *
     * @param phone
     * @param certificate
     */
    @Override
    @DatabaseAnnotation(name = "RJCONFIGER")
    public AppointmentPatient getPatientAppointment(String phone, String certificate) {
        return appointmentPatientMapper.getPatientAppointment(phone, certificate);
    }

}
