package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.config.dto.AddDoctorParams;
import com.rjsoft.outPatient.infrastructure.repository.entity.DoctorRight;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.List;

/**
 * 医生权限
 *
 * <AUTHOR>
public interface DoctorRightMapper extends Mapper<DoctorRight> {

    /**
     * 根据搜索类型获取医院权限列表
     *
     * @param searchType   1 职工 2 权限
     * @param id           职工ID或权限ID
     * @param hospitalCode
     * @return
     */
    default List<DoctorRight> getDoctorRightByType(Integer searchType, Integer id, Integer hospitalCode) {
        DoctorRight entity = new DoctorRight();
        if (searchType == 1) {
            entity.setWorkerId(id);
        } else if (searchType == 2) {
            entity.setRightType(id);
        } else {
            return new ArrayList<DoctorRight>();
        }
        entity.setHospitalCode(hospitalCode);
        return select(entity);
    }


    /**
     * 根据权限类型和操作员获取信息
     *
     * @param type   权限类型
     * @param opCode 操作员
     * @return
     */
    default List<DoctorRight> getInfoByTypeWorkerId(int type, int opCode, Integer hospCode) {
        Weekend<DoctorRight> weekend = new Weekend<>(DoctorRight.class);
        WeekendCriteria<DoctorRight, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(DoctorRight::getRightType, type);
        weekendCriteria.andEqualTo(DoctorRight::getWorkerId, opCode);
        weekendCriteria.andEqualTo(DoctorRight::getHospitalCode, hospCode);
        return selectByExample(weekend);
    }

    /**
     * 检查职工是否存在此权限
     *
     * @param type
     * @param workerId
     * @param hospitalCode
     * @return
     */
    default boolean checkRight(Integer type, Integer workerId, Integer hospitalCode) {
        DoctorRight entity = new DoctorRight();
        entity.setRightType(type);
        entity.setWorkerId(workerId);
        entity.setHospitalCode(hospitalCode);
        return this.select(entity).size() > 0 ? true : false;
    }

    /**
     * 添加门诊医生对应权限
     *
     * @param doctorRight
     */
    default void addDoctorRight(List<DoctorRight> doctorRight) {
        for (DoctorRight right : doctorRight) {
            insertSelective(right);
        }
    }

    /**
     * 根据权限类型和医生Id查询医生
     *
     * @param typeId
     * @param hospitalCode
     */
    default List<DoctorRight> queryDoctorInfoByRight(Integer typeId, Integer hospitalCode) {
        DoctorRight doctorRight = new DoctorRight();
        doctorRight.setRightType(typeId);
        doctorRight.setHospitalCode(hospitalCode);
        return select(doctorRight);
    }

    /**
     * 根据传入添加权限列表获取已经存在列表
     *
     * @param params
     */
    List<DoctorRight> getDoctorRightList(@Param("list") List<AddDoctorParams> params);


    /**
     * 删除医生权限信息
     */
    void delDoctorRight(@Param("list") List<Integer> id, @Param("typeId") Integer typeId, @Param("hospitalCode") Integer hospitalCode);


}
