package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Agent;
import com.rjsoft.outPatient.infrastructure.repository.entity.TbUser;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;

public interface TbUserMapper extends BaseMapper<TbUser>, ExampleMapper<TbUser> {

    /**
     * 根据医生编码+医院编码获取用户信息
     *
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    default TbUser getUserByIdAndHosp(String doctorId, String hospitalCode) {
        Weekend<TbUser> weekend = new Weekend<>(TbUser.class);
        weekend.weekendCriteria().andEqualTo(TbUser::getWorkerId, doctorId)
                .andEqualTo(TbUser::getHospitalCode, hospitalCode)
                .andEqualTo(TbUser::getIsDeleted,"0");
        return selectOneByExample(weekend);
    }

}
