package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.utils.ObjectUtils;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.domain.item.dto.ApplyItemExeDeptDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplyBigForm;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplyDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplyDetailCost;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplyList;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.ApplyRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 申请单
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class ApplyRepositoryImpl implements ApplyRepository {

    @Autowired
    private ApplyItemMapper applyItemMapper;

    ApplyListMapper applyListMapper;
    ApplyDetailMapper applyDetailMapper;
    ApplyDetailCostMapper applyDetailCostMapper;
    ApplyBigFormMapper applyBigFormMapper;

    /**
     * 根据申请单ID，加载申请单所有信息，包括下面明细
     *
     * @param applyId
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "Apply")
    public ApplyList getApplyListById(Integer applyId) {
        ApplyList applyList = applyListMapper.getApplyListById(applyId);
        if (applyList == null) {
            return null;
        }
        ApplyBigForm bigForm = applyBigFormMapper.getBigFormById(applyList.getBigformId());
        applyList.setBigForm(bigForm);
        List<ApplyDetail> detailList = applyDetailMapper.getApplyDetailsByListId(applyList.getId());
        for (ApplyDetail applyDetail : detailList) {
            List<ApplyDetailCost> costList = applyDetailCostMapper.getApplyDetailsByDetailId(applyDetail.getListId(), applyDetail.getId());
            applyDetail.setApplyDetailCostList(costList);
        }
        applyList.setApplyDetailList(detailList);
        return applyList;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.APPLY)
    public List<ApplyList> getApplyListByReceptionNo(Integer receptionNo, Integer type) {
        List<ApplyList> applyLists = new ArrayList<>();
        List<ApplyBigForm> applyBigFormList = applyBigFormMapper.getBigFormByType(type);
        if (applyBigFormList.size() <= 0) {
            return applyLists;
        }

        List<Integer> bigFormIds = applyBigFormList.stream().map(ApplyBigForm::getId).collect(Collectors.toList());
        applyLists = applyListMapper.getApplyListByVisitId(receptionNo, bigFormIds);
        if (applyLists.size() == 0) {
            return applyLists;
        }
        //填充申请单信息申请类型
        applyLists = applyLists.stream().peek(p -> p.setBigForm(applyBigFormList.stream().filter(a -> ObjectUtils.compare(a.getId(), p.getBigformId())).findFirst().orElse(null))).collect(Collectors.toList());
        List<Integer> applyIds = applyLists.stream().map(ApplyList::getId).collect(Collectors.toList());
        List<ApplyDetail> applyDetails = applyDetailMapper.getApplyDetailsByListId(applyIds);
        List<ApplyDetailCost> detailCosts = applyDetailCostMapper.getApplyDetailsByListId(applyIds);
        Map<Integer, List<ApplyDetail>> collect = applyDetails.stream().collect(Collectors.groupingBy(ApplyDetail::getListId));
        Map<Integer, List<ApplyDetailCost>> collect1 = detailCosts.stream().collect(Collectors.groupingBy(ApplyDetailCost::getDetlId));
        for (ApplyList applyList : applyLists) {
            if (!collect.containsKey(applyList.getId())) {
                continue;
            }
            List<ApplyDetail> tmpDetails = collect.get(applyList.getId());
            for (ApplyDetail applyDetail : tmpDetails) {
                if (!collect1.containsKey(applyDetail.getId())) {
                    continue;
                }
                applyDetail.setApplyDetailCostList(collect1.get(applyDetail.getId()));
            }
            applyList.setApplyDetailList(tmpDetails);
        }
        return applyLists;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.APPLY)
    public Integer getApplyItemCount(String formCode, Integer hospitalCode) {
        return applyListMapper.getApplyItemCount(formCode, hospitalCode);
    }

    /**
     * 根据检查流水号修改申请单项目收费类型
     *
     * @param examineNos
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.APPLY)
    public void updateApplyDetailStatus(List<Integer> examineNos, Integer hospitalCode) {
        applyDetailMapper.updateApplyDetailStatus(examineNos, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.APPLY)
    public List<ApplyItemExeDeptDto> getApplyExeDeptByItemCode(String itemCode, String examineItemCode, String examineNo, Integer applyDetailId) {
        return applyItemMapper.getApplyExeDeptByItemCode(itemCode, examineItemCode, examineNo, applyDetailId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.APPLY)
    public ApplyBigForm getApplyBigFormType(Integer applyId, Integer hospitalCode) {
        ApplyList applyList = applyListMapper.getApplyListById(applyId);
        if (applyList == null) {
            return null;
        }
        ApplyBigForm bigForm = applyBigFormMapper.getBigFormById(applyList.getBigformId());
        return bigForm;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.APPLY)
    public List<ApplyList> queryApplyListById(List<Integer> applyIds, Integer hospitalCode) {
        if (CollUtil.isEmpty(applyIds)) {
            return Collections.emptyList();
        }
        return applyListMapper.queryApplyListById(applyIds, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.APPLY)
    public List<ApplyBigForm> queryBigFormListByIds(List<Integer> bigFormIds) {
        if (CollUtil.isEmpty(bigFormIds)) {
            return Collections.emptyList();
        }
        return applyBigFormMapper.queryBigFormListByIds(bigFormIds);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.APPLY)
    public List<ApplyList> getApplyListByIds(List<Integer> applyIds, Integer hospitalCode) {
        List<ApplyList> applyLists = applyListMapper.queryApplyListById(applyIds, hospitalCode);
        if (applyLists.isEmpty()) {
            return applyLists;
        }
        //填充申请单信息申请类型
        List<Integer> bigFormIds = applyLists.stream().map(ApplyList::getBigformId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<ApplyBigForm> bigForms = applyBigFormMapper.queryBigFormListByIds(bigFormIds);
        List<ApplyDetail> applyDetails = applyDetailMapper.getApplyDetailsByListId(applyIds);
        List<ApplyDetailCost> detailCosts = applyDetailCostMapper.getApplyDetailsByListId(applyIds);

        Map<Integer, ApplyBigForm> bigFormMap = bigForms.stream().collect(Collectors.toMap(ApplyBigForm::getId, Function.identity()));
        Map<Integer, List<ApplyDetail>> applyDetailMap = applyDetails.stream().collect(Collectors.groupingBy(ApplyDetail::getListId));
        Map<Integer, List<ApplyDetailCost>> applyDetailCostMap = detailCosts.stream().collect(Collectors.groupingBy(ApplyDetailCost::getDetlId));
        for (ApplyList applyList : applyLists) {
            //设置 bigForm
            Optional.ofNullable(bigFormMap.get(applyList.getBigformId())).ifPresent(applyList::setBigForm);
            // 提前获取 tmpDetails，避免嵌套判断
            List<ApplyDetail> tmpDetails = applyDetailMap.get(applyList.getId());
            if (CollUtil.isEmpty(tmpDetails)) {
                continue;
            }
            // 处理 ApplyDetailCost
            tmpDetails.forEach(applyDetail -> {
                List<ApplyDetailCost> costs = applyDetailCostMap.get(applyDetail.getId());
                if (CollUtil.isNotEmpty(costs)) {
                    applyDetail.setApplyDetailCostList(costs);
                }
            });

            applyList.setApplyDetailList(tmpDetails);
        }
        return applyLists;
    }

}
