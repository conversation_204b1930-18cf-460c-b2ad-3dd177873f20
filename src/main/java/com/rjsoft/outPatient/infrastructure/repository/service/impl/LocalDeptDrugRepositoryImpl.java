package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.LocalDeptDrug;
import com.rjsoft.outPatient.infrastructure.repository.mapper.LocalDeptDrugMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.LocalDeptDrugRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/8/20-9:32 上午
 */
@Service
@AllArgsConstructor
public class LocalDeptDrugRepositoryImpl implements LocalDeptDrugRepository {

    private final LocalDeptDrugMapper localDeptDrugMapper;

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<Integer> getDrugIdsByDeptId(Integer deptId, Integer hospitalCode) {
        Weekend<LocalDeptDrug> weekend = new Weekend<>(LocalDeptDrug.class);
        weekend.selectProperties("drugId");
        weekend.weekendCriteria().andEqualTo(LocalDeptDrug::getDeptId, deptId)
                .andEqualTo(LocalDeptDrug::getHospitalId, hospitalCode);
        return localDeptDrugMapper.selectByExample(weekend)
                .parallelStream().map(LocalDeptDrug::getDrugId).collect(Collectors.toList());
    }
}
