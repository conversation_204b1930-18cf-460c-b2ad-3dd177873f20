<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.MdmPubFeecategoryexedeptMapper">

    <select id="getFeeCategoryExecDept" resultType="java.util.HashMap">
        select
            (CONVERT ( CHAR ( 10 ), getdate( ), 120) + ' ' + startTime) startTime,
            (CONVERT ( CHAR ( 10 ), getdate( ), 120 ) + ' ' + endTime ) endTime
        from MDM_Pub_FeeCategoryExeDept
        where FeeCategoryCode = #{FeeCategoryCode}
          and useDept = #{useDeptId}
          and hospitalId = #{curHospitalId}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <select id="getFeeCategoryExecDeptByTime" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubFeecategoryexedept">
        SELECT id, feeCategoryCode, feeCategoryName, execdept, execdeptname, exechospitalid, starttime, endtime,
        hospitalId, usedept, usedeptname, updatetime, updateuserid, createtime, createuserid, type
        FROM MDM_Pub_FeeCategoryExeDept
        where feeCategoryCode = #{feeCategory}
        and usedept = #{curDeptId}
        and hospitalId = #{curHospitalId}
        and CONVERT(time,startTime) &lt;= #{curTime}
        and CONVERT(time,endtime) &gt;= #{curTime}
    </select>

    <select id="getExecDeptList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubFeecategoryexedept">
        SELECT id, feeCategoryCode, feeCategoryName, execdept, execdeptname, exechospitalid, starttime, endtime,
               hospitalId, usedept, usedeptname, updatetime, updateuserid, createtime, createuserid, type
        FROM MDM_Pub_FeeCategoryExeDept
        where usedept = #{curDeptId}
          and hospitalId = #{curHospitalId}
          and CONVERT(time,startTime) &lt;= #{curTime}
          and CONVERT(time,endtime) &gt;= #{curTime}
    </select>
</mapper>