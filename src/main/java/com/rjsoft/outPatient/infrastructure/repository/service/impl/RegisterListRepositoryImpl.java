package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.rjsoft.common.SystemNo;
import com.rjsoft.common.configuration.SysConfig;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.dictionary.AutoTranslate;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.param.SearchParam;
import com.rjsoft.common.tools.threads.TyFuture;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.AppointmentEnum;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.common.enums.IoInPatientStatusEnum;
import com.rjsoft.outPatient.config.HisConfig;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatBlParam;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatientDTO;
import com.rjsoft.outPatient.domain.config.dto.DischargeDrugDoctorDTO;
import com.rjsoft.outPatient.domain.diagnose.dto.GeneralDiagnoseDto;
import com.rjsoft.outPatient.domain.diagnose.dto.MajorIoDiagnoseDto;
import com.rjsoft.outPatient.domain.diagnose.dto.MajorRegNoDto;
import com.rjsoft.outPatient.domain.doctorElemMain.constant.HospitalIdEnum;
import com.rjsoft.outPatient.domain.reception.dto.ReceptionRegisterDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.Appointment;
import com.rjsoft.outPatient.infrastructure.repository.entity.IoTbInpatient;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzcfmxGzdEntity;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList;
import com.rjsoft.outPatient.infrastructure.repository.mapper.AppointmentMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.IoTbInpatientMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.MzcfmxGzdMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RegisterListMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RegisterListRepository;
import com.ruijing.code.api.HeadInfo;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
@AllArgsConstructor
@Service
public class RegisterListRepositoryImpl implements RegisterListRepository {

    RegisterListMapper registerListMapper;
    MzcfmxGzdMapper mzcfmxGzdMapper;
    AppointmentMapper appointmentMapper;
    HisConfig hisConfig;
    IoTbInpatientMapper inpatientMapper;

    @Autowired
    private SysConfig sysConfig;


    @Override
    public boolean saveRegisterList(RegisterList registerList) {
        DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
        //int newCount = registerListMapper.updateByPrimaryKeySelective(registerList);
        Log.info("待接诊挂号记录:" + JSON.toJSONString(registerList));
        Weekend<RegisterList> weekendReg = new Weekend<>(RegisterList.class);
        WeekendCriteria<RegisterList, Object> weekendCriteria = weekendReg.weekendCriteria();
        weekendCriteria.andEqualTo(RegisterList::getRegNo, registerList.getRegNo());
        weekendCriteria.andEqualTo(RegisterList::getHospitalCode, registerList.getHospitalCode());

        WeekendCriteria<RegisterList, Object> weekendCriteriaEx = weekendReg.weekendCriteria();
        if (registerList.getFzFlag().equals(4)) {
            weekendCriteriaEx.andIn(RegisterList::getFzFlag, Arrays.asList("0", "1"));
            weekendCriteriaEx.orIsNull(RegisterList::getFzFlag);
        }
        if (registerList.getFzFlag().equals(0)) {
            weekendCriteriaEx.andNotIn(RegisterList::getFzFlag, Arrays.asList("0", "1"));
        }

        //并联查询
        weekendReg.and(weekendCriteriaEx);

        RegisterList registerListNew = new RegisterList();
        registerListNew.setFzFlag(registerList.getFzFlag());
        registerListNew.setDoctorID(registerList.getDoctorID());
        int newCount = registerListMapper.updateByExampleSelective(registerListNew, weekendReg);
        Log.info("更新新系统挂号表接诊状态 " + String.valueOf(newCount));

        if(newCount > 0) {
            //FIXME 因继承调用saveReception方法中的事务，为保证HISDB数据的一致性，所以此处切换数据源需要异步处理，否则会导致数据源切换失败
            TyFuture tyFuture = new TyFuture(2, true);
            tyFuture.addJob("updateAppointment", () -> {
                //更新号源信息
                if (registerList.getAppointmentNo() != null && !"".equals(registerList.getAppointmentNo())) {
                    if (HospitalIdEnum.GENERAL.getCode().equals(registerList.getHospitalCode())) {
                        DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER);
                    } else {
                        DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER3);
                    }

                    Appointment aentity = new Appointment();
                    aentity.setAppointmentStatus(AppointmentEnum.COMPLETE_APPOINTMENT.getCode().longValue());
                    Weekend<Appointment> weekend = new Weekend<>(Appointment.class);
                    weekend.weekendCriteria().
                            andEqualTo(Appointment::getSqh, registerList.getAppointmentNo())
                            .andEqualTo(Appointment::getHospitalCode, registerList.getHospitalCode().toString());
                    int appointmentCount = appointmentMapper.updateByExampleSelective(aentity, weekend);
                }
                return true;
            });
            tyFuture.addJob("updateOldRegisterList", () -> {
                if (!hisConfig.getIsHistoryVersion().equals("true")) {
                    if (HospitalIdEnum.GENERAL.getCode().equals(registerList.getHospitalCode())) {
                        DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
                    } else {
                        DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
                    }
                    // 修改老表挂号表接诊医生和复诊标记
                    int oldCount = registerListMapper.updateOldRegisterList(registerList.getDoctorID(), registerList.getFzFlag(), registerList.getRegNo());
                    if (newCount > 0 && oldCount >= 0) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return newCount > 0;
                }
            });
            List<Object> futureRuleList = tyFuture.runAll();
            tyFuture.printRunInfo();
            tyFuture.close();
            return (boolean) futureRuleList.get(1);
        }else{
            return false;
        }

    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Long> getRegisterListRegNosByPatId(Integer patId) {
        List<RegisterList> recordList = new ArrayList<>();
        Weekend<RegisterList> weekend = new Weekend<>(RegisterList.class);
        weekend.selectProperties("regNo");
        weekend.weekendCriteria().andEqualTo(RegisterList::getPatID, patId);
        recordList = registerListMapper.selectByExample(weekend);
        if (recordList.size() <= 0) {
            weekend.setTableName("Reg_Tb_RegisterList");
            recordList = registerListMapper.selectByExample(weekend);
        }
        if (recordList.size() <= 0) {
            return new ArrayList<>(2);
        }
        return recordList.stream().map(RegisterList::getRegNo).collect(Collectors.toList());
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<RegisterList> getPageRegisterListByDoctor(SearchParam param) {
        Integer hospitalCode = param.getHospitalCode();
        if (ObjectUtil.isEmpty(hospitalCode)) {
            hospitalCode = HeadInfo.getHospId();
        }
        String workerId = Converter.toString(param.getKeys().get("workerId"));
        String deptId = Converter.toString(param.getKeys().get("deptId"));
        String cardNo = Converter.toString(param.getKeys().get("cardNo"));
        Integer fzFlag = Converter.toInt32(param.getKeys().get("fzFlag"));
        Integer selectRange = Converter.toInt32(param.getKeys().get("selectRange"));
        String sortField = Converter.toString(param.getKeys().get("sortField"));

        String cloudDeptId = sysConfig.getConfig(SystemNo.OUTPATIENT, "CloudDeptId", String.valueOf(hospitalCode));
        List<Integer> cloudDeptIds = new ArrayList<Integer>();
        if (cloudDeptId != null && !"".equals(cloudDeptId)) {
            for (String clouddeptid : cloudDeptId.split(",")) {
                cloudDeptIds.add(Integer.parseInt(clouddeptid));
            }
        }

        if (!StringUtils.isEmpty(sortField)) {
            //如果排序字段不为空，根据排序字段信息关联特殊处理
            if (sortField.startsWith("doctorName")) {
                sortField = sortField.replace("doctorName", "zg.Name");
            }
            if (sortField.startsWith("registerTime")) {
                sortField = sortField.replace("registerTime", "a.RegistTime");
            }
            if (sortField.startsWith("hisCardNo")) {
                sortField = sortField.replace("hisCardNo", "a.OutPatientNo");
            }
            if (sortField.startsWith("patName")) {
                sortField = sortField.replace("patName", "a.PatName");
            }
            if (sortField.startsWith("cardNo")) {
                sortField = sortField.replace("cardNo", "a.CardNo");
            }
        }

        Calendar calendar = new GregorianCalendar();
        calendar.setTime(Optional.ofNullable(param.getEndTime()).orElseThrow(() -> new IllegalArgumentException("请输入结束时间!")));
        calendar.add(Calendar.DATE, 1);
        Date endTime = calendar.getTime();

        //PageHelper.startPage(param.getPageNum(), param.getPageSize(), sortField);
        Date startTime = Optional.ofNullable(param.getStartTime()).orElseThrow(() -> new IllegalArgumentException("请输入开始时间!"));


        List<RegisterList> list = new ArrayList<RegisterList>();
        LocalDate nowLocaldate = LocalDate.now();
        LocalDate beginLocaldate = startTime.toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalDate();
        long dayUntil = beginLocaldate.until(nowLocaldate, ChronoUnit.DAYS);
        if (dayUntil > 3) {
            //判断是否要去迁移表查数据
            Log.info("查询" + dayUntil + "天前记录，进入迁移表查询");
            list = registerListMapper.getRegisterAllListPageByDoctor(startTime
                    , endTime
                    , hospitalCode
                    , workerId, deptId
                    , cardNo
                    , fzFlag, selectRange, sortField, cloudDeptIds,param.getPageNum(),param.getPageSize());
        } else {
            list = registerListMapper.getRegisterListPageByDoctor(startTime, endTime, hospitalCode, workerId, deptId, cardNo, fzFlag, selectRange, sortField, cloudDeptIds,param.getPageNum(),param.getPageSize());
        }

        //PageInfo pageInfo = new PageInfo<>(list);
        //param.setTotalCount(Converter.toInt32(pageInfo.getTotal()));
        return list;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<RegisterList> getRegister(Integer hospitalCode, String workerId, String deptId, Long regNo) {
        String cloudDeptId = sysConfig.getConfig(SystemNo.OUTPATIENT, "CloudDeptId", String.valueOf(hospitalCode));
        List<Integer> cloudDeptIds = new ArrayList<Integer>();
        if (cloudDeptId != null && !"".equals(cloudDeptId)) {
            for (String clouddeptid : cloudDeptId.split(",")) {
                cloudDeptIds.add(Integer.parseInt(clouddeptid));
            }
        }

        List<RegisterList> list = new ArrayList<RegisterList>();
            //判断是否要去迁移表查数据
        list = registerListMapper.getRegisterListByDoctor(hospitalCode, workerId, deptId, 1,regNo, cloudDeptIds);

        if(list.size()==0){
            list = registerListMapper.getRegisterAllListByDoctor(hospitalCode, workerId, deptId, 1,regNo, cloudDeptIds);
        }
        return list;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public HashMap<String, Integer> getRegisterCountByDoctor(SearchParam param) {
        Integer hospitalCode = param.getHospitalCode();
        if (ObjectUtil.isEmpty(hospitalCode)) {
            hospitalCode = HeadInfo.getHospId();
        }
        String workerId = Converter.toString(param.getKeys().get("workerId"));
        String deptId = Converter.toString(param.getKeys().get("deptId"));
        String cardNo = Converter.toString(param.getKeys().get("cardNo"));
        Integer fzFlag = Converter.toInt32(param.getKeys().get("fzFlag"));
        Integer selectRange = Converter.toInt32(param.getKeys().get("selectRange"));
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(Optional.ofNullable(param.getEndTime()).orElseThrow(() -> new IllegalArgumentException("请输入结束时间!")));
        calendar.add(Calendar.DATE, 1);
        Date endTime = calendar.getTime();
        final Date startTime = Optional.ofNullable(param.getStartTime()).orElseThrow(() -> new IllegalArgumentException("请输入开始时间!"));
        LocalDate nowLocaldate = LocalDate.now();
        LocalDate beginLocaldate = startTime.toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalDate();
        long dayUntil = beginLocaldate.until(nowLocaldate, ChronoUnit.DAYS);

        String cloudDeptId = sysConfig.getConfig(SystemNo.OUTPATIENT, "CloudDeptId", String.valueOf(hospitalCode));
        List<Integer> cloudDeptIds = new ArrayList<Integer>();
        if (cloudDeptId != null && !"".equals(cloudDeptId)) {
            for (String clouddeptid : cloudDeptId.split(",")) {
                cloudDeptIds.add(Integer.parseInt(clouddeptid));
            }
        }

        HashMap<String, Integer> result = null;
        if (dayUntil > 3) {
            Log.info("查询" + dayUntil + "天前记录，进入迁移表查询");
            result = registerListMapper.getRegisterAllCountByDoctor(startTime, endTime, hospitalCode, workerId, deptId, cardNo, fzFlag, selectRange,cloudDeptIds);
        } else {
            result = registerListMapper.getRegisterCountByDoctor(startTime, endTime, hospitalCode, workerId, deptId, cardNo, fzFlag, selectRange,cloudDeptIds);
        }
        return result;
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public RegisterList getRegisterByHisCardNo(String hisCardNo, Integer hospitalCode) {
        return registerListMapper.getRegisterByHisCardNo(hisCardNo, hospitalCode);
    }


    @Override
    @AutoTranslate
    @DatabaseAnnotation(name = "HISDB")
    public RegisterList getRegisterDtoByRegNo(Long regNo, Integer hospitalCode) {
        RegisterList registerList = registerListMapper.getRegisterByRegNo(regNo, hospitalCode, null);
        if (ObjectUtil.isEmpty(registerList)) {
            registerList = registerListMapper.getRegisterByRegNo(regNo, hospitalCode, "Reg_Tb_RegisterList");
        }
        return registerList;
    }

    @Override
    @AutoTranslate
    @DatabaseAnnotation(name = "HISDB")
    public List<RegisterList> getRegisterDtoByRegNoList(List<Long> regNoList, Integer hospitalCode) {
        List<RegisterList> registerList = registerListMapper.getRegisterByRegNoList(regNoList, hospitalCode, null);
        if (CollUtil.isEmpty(registerList)) {
            registerList = registerListMapper.getRegisterByRegNoList(regNoList, hospitalCode, "Reg_Tb_RegisterList");
        }
        return registerList;
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<RegisterList> getRegisterByRegNos(List<Long> regNo, Integer hospitalCode) {
        if(regNo!=null&&regNo.size()>0){
            Weekend weekend = new Weekend(RegisterList.class);
            WeekendCriteria<RegisterList, Object> weekendCriteria = weekend.weekendCriteria();
            weekendCriteria.andIn(RegisterList::getRegNo, regNo);
            weekendCriteria.andEqualTo(RegisterList::getHospitalCode, hospitalCode);
            return registerListMapper.selectByExample(weekend);
        }else{
            return null;
        }
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    // FIXME: yutao 2024/6/17 等王玮改挂号时异步查出RegNo
    public RegisterList getRegisterByRegNo(Long regNo, Integer hospitalCode) {
        RegisterList registerList = registerListMapper.getRegisterByRegNo(regNo, hospitalCode, null);
        if (ObjectUtil.isEmpty(registerList)) {
            registerList = registerListMapper.getRegisterByRegNo(regNo, hospitalCode, "Reg_Tb_RegisterList");
        }
        return registerList;
    }

     /*
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public RegisterList getRegisterDtoByRegNo(Integer doctorId, Integer hospitalCode) {
        final RegisterList entity = new RegisterList();
        entity.setDoctorID(doctorId);
        entity.setHospitalCode(hospitalCode);
        final List<RegisterList> result = registerListMapper.select(entity);
        return result.size() > 0 ? result.get(0) : null;
    }
    */

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    //@Cacheable(cacheNames = "getRegisterByPatId1", unless = "#result == null")
    public List<RegisterList> getRegisterByPatId(List<Integer> patIds, Date startTime, Date endDate, Integer hospitalCode) {
        // XXX: yutao 2024/6/20  这里提前查，endDate以医生接诊时间来算，而不是当前时间，问题不大。
        TyFuture tyFuture = new TyFuture(2,true);
        tyFuture.addJob("",()->{
            List<RegisterList>  records = registerListMapper.getRegisterByPatIds(patIds, startTime, endDate, hospitalCode, "");
            return records;
        });
        tyFuture.addJob("",()->{
            List<RegisterList> recordsMove = registerListMapper.getRegisterByPatIds(patIds, startTime, endDate, hospitalCode, "Reg_Tb_RegisterList");
            return recordsMove;
        });
        List<Object> objects = tyFuture.runAll();
        List<RegisterList> records = objects.get(0)==null?null:(List<RegisterList>) objects.get(0);
        List<RegisterList> recordsMove = objects.get(1)==null?null:(List<RegisterList>) objects.get(1);
        records.addAll(recordsMove);
        tyFuture.printRunInfo("GetRegisterByPatId");
        tyFuture.close();
        return records;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    @Cacheable(cacheNames = "getIOInPatientByPatId", unless = "#result == null")
    public List<IoTbInpatient> getIOInPatientByPatIds(List<Integer> patIds, String startTime, String endDate, Integer hospitalCode) {
        List<IoTbInpatient> ioTbInpatientList = inpatientMapper.getInpatientListByPatids(patIds, IoInPatientStatusEnum.OUT.getCode(), startTime, endDate);
        if (CollUtil.isEmpty(ioTbInpatientList)) {
            return Collections.emptyList();
        }
        List<IoTbInpatient> result = new ArrayList<>();
        for (IoTbInpatient ioTbInpatient : ioTbInpatientList) {
            //查询出院带药医生 无出院带药不展示
            DischargeDrugDoctorDTO dischargeDrugDoctor = inpatientMapper.getDischargeDrugDoctor(ioTbInpatient.getRegno());
            if (ObjectUtil.isEmpty(dischargeDrugDoctor)) {
                continue;
            }
            ioTbInpatient.setDischargeDrugDoctorId(dischargeDrugDoctor.getDoctorId());
            ioTbInpatient.setDischargeDrugDoctorName(dischargeDrugDoctor.getDoctorName());
            result.add(ioTbInpatient);
        }
        return result;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    @Cacheable(cacheNames = "gwRegisterByCardNo1", unless = "#result == null")
    public List<RegisterList> getRegisterByCardNo(String cardNo, Date startTime, Date endDate, Integer hospitalCode) {
        List<RegisterList> records = registerListMapper.getRegisterByCardNo(cardNo, startTime, endDate, hospitalCode, "");
        if (records.isEmpty()) {
            List<RegisterList> recordsMove = registerListMapper.getRegisterByCardNo(cardNo, startTime, endDate, hospitalCode, "Reg_Tb_RegisterList");
            records.addAll(recordsMove);
        }
        return records;
    }

    /**
     * 门诊处方明细告知单保存
     *
     * @param mzcfmxGzdEntity
     * @return
     */
    @Override
    @DatabaseAnnotation
    public int saveMzcfmxGzd(MzcfmxGzdEntity mzcfmxGzdEntity) {
        return mzcfmxGzdMapper.insertSelective(mzcfmxGzdEntity);
    }

    /**
     * 修改门诊处方明细告知单
     *
     * @param mzcfmxGzdEntity
     * @return
     */
    @Override
    @DatabaseAnnotation
    public int updateMzcfmxGzd(MzcfmxGzdEntity mzcfmxGzdEntity) {
        return mzcfmxGzdMapper.updateByPrimaryKeySelective(mzcfmxGzdEntity);
    }

    /**
     * 根据处方流水号和项目编码查询是否存在告知单信息
     *
     * @param cflsh
     * @param itemId
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public MzcfmxGzdEntity getMzCfMxGzd(Long cflsh, Integer itemId, Integer hospitalCode) {
        Weekend<MzcfmxGzdEntity> weekend = new Weekend<>(MzcfmxGzdEntity.class);
        weekend.weekendCriteria().andEqualTo(MzcfmxGzdEntity::getRecipeNo, cflsh)
                .andEqualTo(MzcfmxGzdEntity::getItemid, itemId)
                .andEqualTo(MzcfmxGzdEntity::getHospitalCode, hospitalCode);
        weekend.setOrderByClause("opTime desc");
        List<MzcfmxGzdEntity> list = mzcfmxGzdMapper.selectByExample(weekend);
        return list.size() > 0 ? list.get(0) : null;
    }

    /**
     * 根据处方流水号和项目编码查询是否存在告知单信息
     *
     * @param cflsh
     * @param itemId
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<MzcfmxGzdEntity> getMzCfMxGzdList(List<Long> cflshList, List<Integer> itemIdList, Integer hospitalCode) {
        Weekend<MzcfmxGzdEntity> weekend = new Weekend<>(MzcfmxGzdEntity.class);
        weekend.weekendCriteria().andEqualTo(MzcfmxGzdEntity::getRecipeNo, cflshList)
                .andEqualTo(MzcfmxGzdEntity::getItemid, itemIdList)
                .andEqualTo(MzcfmxGzdEntity::getHospitalCode, hospitalCode);
        weekend.setOrderByClause("opTime desc");
        List<MzcfmxGzdEntity> list = mzcfmxGzdMapper.selectByExample(weekend);
        return list;
    }

    /**
     * 删除门诊处方明细告知单
     *
     * @param id
     * @return
     */
    @Override
    @DatabaseAnnotation
    public int deleteMzCfMxGzd(Long id, Long cflsh) {
        Map<String, Long> key = new HashMap<>();
        key.put("id", id);
        key.put("recipeNo", cflsh);
        return mzcfmxGzdMapper.deleteByPrimaryKey(key);
    }

    /**
     * 删除门诊处方明细告知单
     *
     * @param id
     * @return
     */
    @Override
    @DatabaseAnnotation
    public int deleteMzCfMxGzdList(Long id, Long cflsh) {
        Map<String, Long> key = new HashMap<>();
        key.put("id", id);
        key.put("recipeNo", cflsh);
        return mzcfmxGzdMapper.deleteByPrimaryKey(key);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public RegisterList getRegisterListByRegNoAndDocId(Long regNo, Integer doctorId, Integer hospCode) {
        return registerListMapper.getRegisterListByRegNoAndDocId(regNo, doctorId, hospCode);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<RegisterList> getDayRegisterInfo(Date registTime, Integer doctorId, Integer hospCode) {
        return registerListMapper.getDayRegisterInfo(registTime, doctorId, hospCode);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<RegisterList> getRegisterListByDay(Date registTime, Integer patId, Integer doctorId, Integer hospCode, String tbName) {

        List<RegisterList> lregs = registerListMapper.getRegisterListByDay(registTime, patId, doctorId, hospCode, "");
        if (!StringUtils.isEmpty(tbName) && lregs.size() < 1) {
            lregs = registerListMapper.getRegisterListByDay(registTime, patId, doctorId, hospCode, tbName);
        }

        return lregs;
    }

    @Override
    public List<RegisterList> getRegisterListByDay(Date minDate, Date maxDate, List<Long> patId, List<Integer> doctorId, Integer hospitalCode) {
        return registerListMapper.getRegisterListByDay(minDate, maxDate, patId, doctorId, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public RegisterList getPatIdByRegNoAndChargeType(Long regNo, Integer hospCode) {
        final Weekend<RegisterList> weekend = new Weekend<>(RegisterList.class);
        final WeekendCriteria<RegisterList, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(RegisterList::getRegNo, regNo)
                .andEqualTo(RegisterList::getHospitalCode, hospCode);
        RegisterList registerList = registerListMapper.selectOneByExample(weekend);
        if (ObjectUtil.isEmpty(registerList)) {
            weekend.setTableName("Reg_Tb_RegisterList");
            return registerListMapper.selectOneByExample(weekend);
        }
        return registerList;
    }

    /**
     * 根据患者编号列表获取所有患者信息
     *
     * @param patIds
     * @param hospitalId
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<RegisterList> getRegisterListByPatIds(List<Integer> patIds, Integer hospitalId) {
        List<RegisterList> registerLists = registerListMapper.getRegisterListByPatIds(patIds, hospitalId, null);
        registerLists.addAll(registerListMapper.getRegisterListByPatIds(patIds, hospitalId, "Reg_Tb_RegisterList"));
        return registerLists;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Long> getAllRegNoByPatId(Integer patId) {
        return registerListMapper.getAllRegNoByPatId(patId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Long> queryAllRegNoByPatIds(List<Integer> patIds) {
        return registerListMapper.queryAllRegNoByPatIds(patIds);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Long> getAllRegNoByCertificateNo(String certificateNo) {
        return registerListMapper.getAllRegNoByCertificateNo(certificateNo);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public Date getAllRegistTimeByCertificateNo(String certificateNo, Integer hospitalCode) {
        if (HospitalClassify.BRANCH.getHospitalCode().equals(hospitalCode)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
        }
        return registerListMapper.getAllRegistTimeByCertificateNo(certificateNo);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<MajorRegNoDto> getMajorAllRegNoByPatId(Integer patId) {
        return registerListMapper.getMajorAllRegNoByPatId(patId);
    }

    @Override
    public List<MajorRegNoDto> getMajorAllRegNoFromOldData(Integer patNo, int hospitalCode) {
        if(patNo!=null&&patNo!=0){
            String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.ZXHIS : DatasourceName.ZXHIS3;
            DataSourceSwitchAspect.changeDataSource(dataSourceName);
            return registerListMapper.getMajorAllRegNoFromOldData(patNo);
        }else{
            return new ArrayList<>();
        }
    }

    @Override
    public List<MajorRegNoDto> queryMajorAllRegNoFromOldData(List<Integer> patNos, int hospitalCode) {
        if(CollUtil.isNotEmpty(patNos)){
            String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.ZXHIS : DatasourceName.ZXHIS3;
            DataSourceSwitchAspect.changeDataSource(dataSourceName);
            return registerListMapper.queryMajorAllRegNoFromOldData(patNos);
        }else{
            return new ArrayList<>();
        }
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Long> getIOAllRegNosByPatId(Integer patId) {
        return registerListMapper.getIOAllRegNosByPatId(patId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public HashMap<String, Integer> getIOCheckInfoByRegNo(Long regNo) {
        return registerListMapper.getIOCheckInfoByRegNo(regNo);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<MajorIoDiagnoseDto> getIODiagnoseInfoByRegNo(Long regNo) {
        return registerListMapper.getIODiagnoseInfoByRegNo(regNo);
    }

    @Override
    public List<String> getAllRegNoFromOldData(String certificateNo, int hospitalCode) {
        String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.ZXHIS : DatasourceName.ZXHIS3;
        DataSourceSwitchAspect.changeDataSource(dataSourceName);
        return registerListMapper.getAllRegNoFromOldData(certificateNo);
    }

    @Override
    public List<String> getAllRegNoFromOldDataByPatId(Integer patId, int hospitalCode) {
        if(patId!=null&&patId!=0){
            String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.ZXHIS : DatasourceName.ZXHIS3;
            DataSourceSwitchAspect.changeDataSource(dataSourceName);
            return registerListMapper.getAllRegNoFromOldDataByPatId(patId);
        }else{
            return null;
        }
    }

    @Override
    public List<String> getAllRegNoFromOldDataByPatIds(List<Integer> patIds, int hospitalCode,Date startTime, Date endTime) {
        String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.ZXHIS : DatasourceName.ZXHIS3;
        DataSourceSwitchAspect.changeDataSource(dataSourceName);
        return registerListMapper.getAllRegNoFromOldDataByPatIds(patIds, startTime, endTime);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public ReceptionRegisterDto getRegTvRegisterList(Long regNo) {
        return registerListMapper.getRegTvRegisterList(regNo);
    }

    /**
     * 根据挂号流水号列表，查询是否有已删除挂号信息
     *
     * @param regNos
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Long> selectInvalidRegNos(Set<Long> regNos) {
        return registerListMapper.selectInvalidRegNos(regNos);
    }

    /**
     * 根据身份证号获取患者诊断记录[总院 106 ]
     *
     * @param certificateNo
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public List<GeneralDiagnoseDto> queryDiagnoses(String certificateNo) {
        return registerListMapper.queryDiagnoses(certificateNo);
    }

    /**
     * 根据身份证号获取患者诊断记录[分院 107 ]
     *
     * @param certificateNo
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS3)
    public List<GeneralDiagnoseDto> queryDiagnosesBranch(String certificateNo) {
        return registerListMapper.queryDiagnoses(certificateNo);
    }

    /**
     * 根据挂号流水号获取相关信息
     * 【病历补写】
     *
     * @param regNos
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<PatBlDto> getPatBlInfoByRegNos(List<String> regNos, String hisCardNo, String patName) {
        return registerListMapper.getPatBlInfoByRegNos(regNos, hisCardNo, patName);
    }

    /**
     * 获取
     * 【病历补写】
     *
     * @param param
     */
    @Override
    public List<PatBlDto> getNoFilledPatBlInfo(PatBlParam param) {
        if (param.getHospitalCode().equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
        }
        return registerListMapper.getNoFilledPatBlInfo(param);
    }

    /**
     * 获取未提交患者病历列表
     * 【病历补写】
     *
     * @param param
     */
    @Override
    public List<PatBlDto> getNoCommittedPatBlInfo(PatBlParam param) {
        if (param.getHospitalCode().equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }
        return registerListMapper.getNoCommittedPatBlInfo(param);
    }

    /**
     * 获取未提交患者病历列表
     * 【病历补写】
     *
     * @param param
     */
    @Override
    public List<PatBlDto> getNoSignedPatBlInfo(PatBlParam param) {
        if (param.getHospitalCode().equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }
        return registerListMapper.getNoSignedPatBlInfo(param);
    }

    /**
     * 根据挂号流水号和医院编码查询挂号信息
     *
     * @param regNo
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public RegisterList getRegisterInfoByRegNo(Long regNo, Integer hospitalCode) {
        return registerListMapper.getRegisterInfoByRegNo(regNo, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public RegisterList getAllRegisterInfoByRegNo(Long regNo, Integer hospitalCode) {
        return registerListMapper.getAllRegisterInfoByRegNo(regNo,hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public PatientDTO getRegisterInfoByRegNo(String regNo) {
        return registerListMapper.queryPatientInfo(regNo);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<RegisterList> getPageRegisterListByPatid(SearchParam param) {
        Integer hospitalCode = Converter.toInt32(param.getHospitalCode());
        String patId = Converter.toString(param.getKeys().get("patId"));
        String regNo = Converter.toString(param.getKeys().get("regNo"));

        //设置当天
        Calendar calendar = new GregorianCalendar(TimeZone.getTimeZone("GMT+8"));
        calendar.setTime(new Date());

        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(calendar.getTime().getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        final Date startTime = Date.from(startOfDay.atZone(TimeZone.getTimeZone("GMT+8").toZoneId()).toInstant());
        calendar.setTime(startTime);
        calendar.add(Calendar.DATE, 1);
        final Date endTime = calendar.getTime();


        List<RegisterList> list = new ArrayList<RegisterList>();
        list = registerListMapper.getPageRegisterListByPatid(startTime, endTime, hospitalCode, patId);

        return list;
    }

    @Override
    @DatabaseAnnotation
    public MzcfmxGzdEntity getMzCfMxGzdByMxlsh(Long recipeDetailNo, Integer hospitalCode) {
        Weekend<MzcfmxGzdEntity> weekend = new Weekend<>(MzcfmxGzdEntity.class);
        weekend.weekendCriteria().andEqualTo(MzcfmxGzdEntity::getRecipeDetailNo, recipeDetailNo)
                .andEqualTo(MzcfmxGzdEntity::getHospitalCode, hospitalCode);
        weekend.setOrderByClause("opTime desc");
        List<MzcfmxGzdEntity> list = mzcfmxGzdMapper.selectByExample(weekend);
        return list.size() > 0 ? list.get(0) : null;
    }


    @Override
    @DatabaseAnnotation
    @Cacheable(cacheNames = "getRegisterListFromOldData1", unless = "#result == null")
    public List<RegisterList> getRegisterListFromOldData(String certificateNo, Integer hospitalCode, Date startTime, Date endTime) {
        String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.ZXHIS : DatasourceName.ZXHIS3;
        DataSourceSwitchAspect.changeDataSource(dataSourceName);
        return registerListMapper.getRegisterListFromOldData(certificateNo, startTime, endTime);
    }

    @Override
    @DatabaseAnnotation
    @Cacheable(cacheNames = "getRegisterListOldDataByPatNo1", unless = "#result == null")
    public List<RegisterList> getRegisterListOldDataByPatNo(Integer patNo, Integer hospitalCode, Date startTime, Date endTime) {
        String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.ZXHIS : DatasourceName.ZXHIS3;
        DataSourceSwitchAspect.changeDataSource(dataSourceName);
        return registerListMapper.getRegisterListOldDataByPatNo(patNo, startTime, endTime);
    }

    @Override
    @DatabaseAnnotation
    public Boolean updateRegistOrderByRegNo(Long regNo, Integer hospitalCode, Integer registOrder) {
        if(regNo==null){
            return false;
        }else{
            return registerListMapper.updateRegistOrderByRegNo(regNo,hospitalCode,registOrder)>0;
        }
    }

    @Override
    public Boolean updateRegistVipFlag(Long regNo, Integer hospitalCode) {
        if(regNo==null){
            return false;
        }else{
            return registerListMapper.updateRegistVipFlag(regNo,hospitalCode)>0;
        }
    }

    /**
     * 根据患者编号列表获取指定时间段内所有患者信息
     *
     * @param patIds 患者Id集合
     * @param hospitalId 医院编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<RegisterList> getRegisterListByPatIds(List<Integer> patIds, Integer hospitalId, Date startTime, Date endTime) {
        List<RegisterList> registerLists = registerListMapper.getRegisterListByPatIds(patIds, hospitalId, startTime, endTime,null);
        registerLists.addAll(registerListMapper.getRegisterListByPatIds(patIds, hospitalId, startTime, endTime,"Reg_Tb_RegisterList"));
        return registerLists;
    }

    /**
     * 根据患者编号列表获取指定时间段内所有患者信息
     *
     * <AUTHOR>
     * @param regNo 挂号流水号
     * @param hospitalCode 医院编码
     * @param insuType 险种类型
     * @return
     */
    public Integer verifyIsJMYB(Integer regNo, Integer hospitalCode, String insuType)
    {
        Integer isflag = registerListMapper.verifyIsJMYB(regNo, hospitalCode, insuType);
        return isflag;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<RegisterList> getByOutPatientNo(Integer hospitalCode, Integer doctorId, Integer deptId, String outPatientNo) {
        return registerListMapper.getByOutPatientNo(hospitalCode,doctorId,deptId,outPatientNo);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<RegisterList> listRegisterByRegNo(Integer hospitalCode, List<Long> regNoList) {
        List<RegisterList> list = registerListMapper.listRegisterByRegNo(hospitalCode, regNoList);
        return list;
    }

}
