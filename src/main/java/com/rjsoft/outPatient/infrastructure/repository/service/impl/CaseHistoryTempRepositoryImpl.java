package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.*;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.CaseHistoryTempRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/6/12 13:41
 * @description
 **/
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class CaseHistoryTempRepositoryImpl implements CaseHistoryTempRepository {
    ElemMainMapper elemMainMapper;
    ElemTempMapper elemTempMapper;
    ElemTempFileMapper elemTempFileMapper;
    CaseTempFileIndexMapper caseTempFileIndexMapper;
    CaseTempFileMapper caseTempFileMapper;
    DeptCaseTempMapper deptCaseTempMapper;
    CaseTempIndexMapper caseTempIndexMapper;
    CaseTempMapper caseTempMapper;
    ScientificResearchTempMapper scientificResearchTempMapper;
    SystemTbDepartmentMapper systemTbDepartmentMapper;


    @Override
    @DatabaseAnnotation
    public boolean judgeTempCode(String tempCode, Integer hospCode, Integer tempId) {
        return caseTempMapper.judgeTempCode(tempCode, hospCode, tempId) > 0;
    }

    @Override
    @DatabaseAnnotation
    public List<QueryDefaultTempResult> queryDeptTemp(Integer deptId, Integer hospCode, Integer searchType, String tempName, Integer dataSources) {
        //兼容老数据，切换数据源
        if (dataSources.equals(0)) {
            String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospCode) ? DatasourceName.MZYS : DatasourceName.MZYS3;
            DataSourceSwitchAspect.changeDataSource(dataSourceName);
        }
        return deptCaseTempMapper.queryDeptTemp(deptId, hospCode, searchType, tempName);
    }

    @Override
    @DatabaseAnnotation
    public List<QueryDefaultTempResult> queryHospTemp(Integer hospCode, String tempName, Integer dataSources) {
        //兼容老数据，切换数据源
        if (dataSources.equals(0)) {
            String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospCode) ? DatasourceName.MZYS : DatasourceName.MZYS3;
            DataSourceSwitchAspect.changeDataSource(dataSourceName);
        }
        return deptCaseTempMapper.queryHospTemp(hospCode, tempName);
    }

    @Override
    @DatabaseAnnotation
    public List<QueryDefaultTempResult> queryResearchTemp(String projectId, Integer hospCode, String tempName) {
        return scientificResearchTempMapper.queryResearchTemp(projectId, hospCode, tempName);
    }

    @Override
    @DatabaseAnnotation
    public QueryDefaultTempResult queryDeptDefaultTemp(Integer deptId, Integer visitFlag, Integer sexFlag, Integer hospitalCode) {
        if (sexFlag == null) {
            sexFlag = 3;
        }
        return deptCaseTempMapper.queryDeptDefaultTemp(deptId, visitFlag, sexFlag, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public QueryDefaultTempResult queryResearchDefaultTemp(String projectId, Integer visitFlag, Integer hospitalCode) {
        return scientificResearchTempMapper.queryResearchDefaultTemp(projectId, visitFlag, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public QueryDefaultTempResult getResearchDefaultTemp(String projectId, Integer sexFlag, Integer hospitalCode) {
        return scientificResearchTempMapper.getResearchDefaultTemp(projectId, sexFlag, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public boolean saveTempInfo(SaveTempInfoParam dataParam) {

        if (dataParam.getTempId() != null) {
            //判断模板代码是否重复
            boolean flag = judgeTempCode(dataParam.getTempCode(), dataParam.getHospCode(), Integer.valueOf(dataParam.getTempId()));
            Assert.isTrue(!flag, "模板代码{" + dataParam.getTempCode() + "}已存在");
            CaseTemp caseTemp = caseTempMapper.selectByPrimaryKey(dataParam.getTempId());
            Assert.notNull(caseTemp, "未找到对应病历模板信息");
            CaseTemp updateCaseTemp = dataParam.toCaseTemp();
            return caseTempMapper.updateByPrimaryKeySelective(updateCaseTemp) > 0;
        }

        boolean flag = judgeTempCode(dataParam.getTempCode(), dataParam.getHospCode(), null);
        Assert.isTrue(!flag, "模板代码{" + dataParam.getTempCode() + "}已存在");

        CaseTempIndex caseTempIndex = new CaseTempIndex();
        caseTempIndex.setValue(1);
        boolean flag1 = caseTempIndexMapper.insertSelective(caseTempIndex) > 0;
        CaseTemp addCaseTemp = dataParam.toCaseTemp();
        addCaseTemp.setTempId(caseTempIndex.getElemNo());
        boolean flag2 = caseTempMapper.insertSelective(addCaseTemp) > 0;
        return flag1 && flag2;

    }

    @Override
    @DatabaseAnnotation
    public boolean delTempInfo(Integer tempId, Integer hospCode) {
        CaseTemp caseTemp = caseTempMapper.selectByPrimaryKey(tempId);
        Assert.notNull(caseTemp, "未找到对应病历模板信息");
        Weekend<CaseTemp> weekend = new Weekend<>(CaseTemp.class);
        weekend.weekendCriteria().andEqualTo(CaseTemp::getTempId, tempId)
                .andEqualTo(CaseTemp::getHospCode, hospCode);
        CaseTemp updateCaseTemp = new CaseTemp();
        updateCaseTemp.setDelStatus(1);
        return caseTempMapper.updateByExampleSelective(updateCaseTemp, weekend) > 0;
    }

    @Override
    @DatabaseAnnotation
    public boolean saveTempXML(SaveTempXMLParam dataParam) {
        if (dataParam.getFileNo() != null) {
            CaseTempFile tempFile = caseTempFileMapper.selectByPrimaryKey(dataParam.getFileNo());
            Assert.notNull(tempFile, "未找到对应模板内容");
            CaseTempFile updateCaseTempFile = dataParam.toCaseTempFile();
            return caseTempFileMapper.updateByPrimaryKeySelective(updateCaseTempFile) > 0;
        } else {
            CaseTempFileIndex caseTempFileIndex = new CaseTempFileIndex();
            caseTempFileIndex.setValue(1);
            boolean flag1 = caseTempFileIndexMapper.insertSelective(caseTempFileIndex) > 0;
            CaseTempFile tempFile = dataParam.toCaseTempFile();
            tempFile.setFileNo(String.valueOf(caseTempFileIndex.getFileNo()));
            boolean flag2 = caseTempFileMapper.insertSelective(tempFile) > 0;
            CaseTemp caseTemp = new CaseTemp();
            caseTemp.setTempId(dataParam.getTempId());
            caseTemp.setFileNo(String.valueOf(caseTempFileIndex.getFileNo()));
            boolean flag3 = caseTempMapper.updateByPrimaryKeySelective(caseTemp) > 0;
            return flag1 && flag2 && flag3;
        }
    }

    @Override
    @DatabaseAnnotation
    @Transactional
    public boolean saveBaseTempContrast(SaveBaseTempContrastParam dataParam) {
        List<Integer> tempIdList = dataParam.getTempIdList();
        tempIdList.forEach(elem -> {
            DeptCaseTemp addDeptCaseTemp = new DeptCaseTemp();
            addDeptCaseTemp.setDeptId(dataParam.getDeptId());
            addDeptCaseTemp.setTempId(elem);
            addDeptCaseTemp.setCreator(dataParam.getCreator());
            addDeptCaseTemp.setHospCode(dataParam.getHospCode());
            deptCaseTempMapper.insertSelective(addDeptCaseTemp);
        });
        return true;
    }


    @Override
    @DatabaseAnnotation
    public List<QueryBaseTempContrastResult> queryBaseTempContrast(Integer deptId, Integer hospCode, String tempName) {
        return deptCaseTempMapper.queryBaseTempContrast(deptId, hospCode, tempName);
    }

    @Override
    @DatabaseAnnotation
    public boolean delBaseTempContrast(Integer deptTempId) {
        DeptCaseTemp updateCaseTemp = new DeptCaseTemp();
        updateCaseTemp.setDeptTempId(deptTempId);
        updateCaseTemp.setDelStatus(1);
        return deptCaseTempMapper.updateByPrimaryKeySelective(updateCaseTemp) > 0;
    }

    @Override
    @DatabaseAnnotation
    public int batchDelBaseTempContrast(Integer deptId, List<Integer> deptTempIdList) {
        DeptCaseTemp deptCaseTemp = new DeptCaseTemp();
        deptCaseTemp.setDelStatus(1);
        Weekend<DeptCaseTemp> weekend = new Weekend<>(DeptCaseTemp.class);
        weekend.weekendCriteria().andEqualTo(DeptCaseTemp::getDeptId, deptId)
                .andIn(DeptCaseTemp::getTempId, deptTempIdList);
        return deptCaseTempMapper.updateByExampleSelective(deptCaseTemp, weekend);
    }

    @Override
    @DatabaseAnnotation
    @Transactional
    public boolean saveResearchTempContrast(SaveResearchTempContrastParam dataParam) {
        List<Integer> tempIdList = dataParam.getTempIdList();
        tempIdList.forEach(elem -> {
            ScientificResearchTemp researchTemp = new ScientificResearchTemp();
            researchTemp.setTempId(elem);
            researchTemp.setProjectId(dataParam.getProjectId());
            researchTemp.setProjectName(dataParam.getProjectName());
            researchTemp.setCreDoctor(dataParam.getCreator());
            researchTemp.setHospCode(dataParam.getHospCode());
            scientificResearchTempMapper.insertSelective(researchTemp);
        });
        return true;
    }

    @Override
    @DatabaseAnnotation
    public List<QueryResearchTempContrastResult> queryResearchTempContrast(Integer hospCode, String projectId, String tempName) {
        return scientificResearchTempMapper.queryResearchTempContrast(hospCode, projectId, tempName);
    }

    @Override
    @DatabaseAnnotation
    public List<QueryDefaultTempResult> queryProjectTemp(Integer hospCode, String projectId, String tempName) {
        return scientificResearchTempMapper.queryResearchTemp(projectId,hospCode,tempName);
    }

    @Override
    @DatabaseAnnotation
    public boolean delResearchTempContrast(Integer researchTempId) {
        ScientificResearchTemp updateResearchTemp = new ScientificResearchTemp();
        updateResearchTemp.setResearchTempId(researchTempId);
        updateResearchTemp.setStatus(1);
        return scientificResearchTempMapper.updateByPrimaryKeySelective(updateResearchTemp) > 0;
    }

    @Override
    @DatabaseAnnotation
    public int batchDelResearchTempContrast(String projectId, List<Integer> tempIdList) {
        ScientificResearchTemp researchTemp = new ScientificResearchTemp();
        researchTemp.setStatus(1);
        Weekend<ScientificResearchTemp> weekend = new Weekend<>(ScientificResearchTemp.class);
        weekend.weekendCriteria().andEqualTo(ScientificResearchTemp::getProjectId, projectId)
                .andIn(ScientificResearchTemp::getTempId, tempIdList);
        return scientificResearchTempMapper.updateByExampleSelective(researchTemp, weekend);
    }

    @Override
    @DatabaseAnnotation
    public PageInfo<CaseTemp> queryTempInfoList(Integer hospCode, String tempName, Integer tempType, Integer type, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        List<CaseTemp> caseTempList = caseTempMapper.queryTempInfoList(hospCode, tempType, type, tempName);
        return new PageInfo<>(caseTempList);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<DeptDto> getDeptList(Integer hospCode) {
        return systemTbDepartmentMapper.getDeptList(hospCode);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<DeptDto> getZyDeptList(Integer hospCode) {
        return systemTbDepartmentMapper.getZyDeptList(hospCode);
    }

    @Override
    @DatabaseAnnotation
    public List<CaseTemp> getTempListByTempId(List<Integer> tempIdList, Integer hospCode) {
        Weekend<CaseTemp> weekend = new Weekend<>(CaseTemp.class);
        weekend.weekendCriteria().andIn(CaseTemp::getTempId, tempIdList)
                .andEqualTo(CaseTemp::getHospCode, hospCode)
                .andEqualTo(CaseTemp::getDelStatus, 0)
                .andEqualTo(CaseTemp::getStatus, 0);
        return caseTempMapper.selectByExample(weekend);
    }
}
