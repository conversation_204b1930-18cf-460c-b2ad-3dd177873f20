package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.ItemSet;
import com.rjsoft.outPatient.infrastructure.repository.entity.ItemSetDetail;

import java.util.List;

/**
 * 查询套餐明细
 * <AUTHOR>
public interface ItemSetDetailRepository {

    /**
     * 根据大类加载系统套餐
     * @param itemCategory
     * @param hospitalCode
     * @return
     */
    List<ItemSet> getItemSetByCategory(Integer itemCategory, Integer hospitalCode);

    /**
     * 根据套餐ID查询套餐明细
     *
     * @param setId
     * @param hospitalCode
     * @return
     */
    List<ItemSetDetail> getItemSetDetailBySetId(Integer setId, Integer hospitalCode);

    /**
     * 系统套餐是否含有项目明细
     *
     * @param setId        套餐 id
     * @param hospitalCode 医院编码
     * @return
     */
    boolean hasItemSetDetail(Integer setId, Integer hospitalCode);

    /**
     * 根据套餐ID集合获取套餐明细
     *
     * @param hospitalCode
     * @return
     */
    List<ItemSetDetail> getItemSetDetailBySetIds(List<Integer> setIdList,Integer deptId,Integer hospitalCode);


    /**
     * 根据套餐ID查询套餐明细Ids
     *
     * @param setId        套餐 id
     * @param hospitalCode 医院编码
     * @return 套餐明细Ids
     */
    List<Integer> getItemIdsBySetId(Integer setId, Integer hospitalCode);

    /**
     * 通过主键查询套餐名称，如果查无记录或者记录中套餐名称是 NULL 或是 Empty，则返回入参{@code defaultValue}
     *
     * @param packageId    套餐 id
     * @param hospitalCode 医院编码
     * @param defaultValue 默认值
     * @return 套餐名称
     */
    String getItemSetNameByIdOrDefault(Long packageId, Integer hospitalCode, String defaultValue);
}
