package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.DictionaryType;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

/**
 * 字典类型
 * <AUTHOR>
public interface DictionaryTypeMapper extends BaseMapper<DictionaryType>, ExampleMapper<DictionaryType> {

    /**
     * 根据类型集合查询系统字典类型
     * @param types
     * @return
     */
    default List<DictionaryType> getDictionaryTypeByIds(List<Integer> types){
        Weekend weekend=new Weekend(DictionaryType.class);
       WeekendCriteria<DictionaryType,Object>weekendCriteria= weekend.weekendCriteria();
        weekendCriteria.andIn(DictionaryType::getDictionaryType,types);
        return selectByExample(weekend);
    }

}
