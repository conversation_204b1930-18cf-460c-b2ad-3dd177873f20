<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.MultidisciplinaryMapper">

    <select id="getMultidisciplinaryList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubMultidisciplinary">
        select
        typeName,
        remark,
        pinyinCode1,
        pinyinCode2,
        wubiCode,
        num,
        hospitalId,
        state,
        CAST(uniqueID AS VARCHAR(32)) uniqueIDStr
        from MDM_Pub_Multidisciplinary
        <where>
            <if test="name != null and name !='' ">
                and (typeName like concat(concat('%',#{name}),'%') or pinyinCode1 like concat(concat('%',#{name}),'%') or pinyinCode2 like concat(concat('%',#{name}),'%')  or wubiCode like concat(concat('%',#{name}),'%'))
            </if>
            <if test="hospitalId != null and hospitalId != 0">
                and hospitalId = #{hospitalId}
            </if>
            <if test="state != null and state !=''">
                and state = #{state}
            </if>
        </where>
        order by state,pinyinCode1
    </select>

</mapper>