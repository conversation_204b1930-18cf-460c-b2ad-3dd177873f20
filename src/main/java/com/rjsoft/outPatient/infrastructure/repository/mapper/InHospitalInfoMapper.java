package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.InHospitalInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 住院信息
 */
public interface InHospitalInfoMapper extends BaseMapper<InHospitalInfo>, ExampleMapper<InHospitalInfo> {

    /**
     * 根据患者身份证号获取住院信息【流水号】
     *
     * @param certificateNo
     * @return
     */
    default List<InHospitalInfo> queryRegNoByCertificateNo(String certificateNo) {
        InHospitalInfo entity = new InHospitalInfo();
        entity.setPatSfz(certificateNo);
        return select(entity);
    }

}
