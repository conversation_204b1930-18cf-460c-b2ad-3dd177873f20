<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.WorkerMapper">

    <select id="getDoctorInfo" resultType="com.rjsoft.outPatient.domain.reserve.dto.DoctorInfoResponse">
        SELECT a.WorkerId workerId,
        rtrim(a.Name) name,
        a.DeptId deptId
        FROM System_Tb_Worker a
        where a.Status = 0
        and a.HospitalId = #{hospId}
        and a.WorkerId in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="getHlyyDoctor" resultType="com.rjsoft.outPatient.domain.recipe.dto.HlyyDoctor">
        SELECT a.WorkerId DoctCode,
               a.Name     DoctName,
               b.DeptId   DeptCode,
               b.DeptName DeptName
        FROM System_Tb_Worker a
                 LEFT JOIN System_Tb_Department b ON a.HospitalId = b.HospitalId
            AND b.DeptId = a.DeptId
        WHERE a.WorkerId = #{doctorId}
          and a.HospitalId = #{hospitalCode}
          and a.Status = 0
    </select>
    <select id="getDoctorInfos" resultType="com.rjsoft.outPatient.domain.config.dto.DoctorInfoResponse">
        select a.WorkerId          workerId,
               RTRIM(a.workerNo)   workerNo,
               rtrim(a.Name)       name,
               rtrim(a.InputCode2) abName,
               rtrim(b.DeptName)   deptName,
               a.DeptId            deptId
        from System_Tb_Worker a
                 left join System_Tb_Department b on a.HospitalId = b.HospitalId and a.DeptId = b.DeptId
        where a.HospitalId = #{hospitalCode}
          and a.Status = 0
          and (a.WorkerId like '%' + #{inputCode} + '%' OR a.Name like '%' + #{inputCode} + '%' or
               a.InputCode2 like '%' + #{inputCode} + '%' or b.DeptName like '%' + #{inputCode} + '%')
    </select>
    <select id="getDoctorInfosByList" resultType="com.rjsoft.outPatient.domain.config.dto.DoctorInfoResponse">
        select a.WorkerId workerId,
        rtrim(a.WorkerNo) workerNo,
        rtrim(a.Name) name,
        rtrim(a.InputCode2) abName,
        rtrim(b.DeptName) deptName,
        a.DeptId deptId
        from System_Tb_Worker a
        left join System_Tb_Department b on a.HospitalId = b.HospitalId and a.DeptId=b.DeptId
        where a.Status=0 and a.WorkerId in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and a.HospitalId = #{hospitalCode}
        and (a.WorkerId like '%' + #{inputCode} + '%' OR a.Name like '%' + #{inputCode} + '%' or
        a.InputCode2 like '%' + #{inputCode} + '%' or b.DeptName like '%' + #{inputCode} + '%')
    </select>
    <select id="getDoctorLevelByWorkerId" resultType="java.lang.Integer">
        select distinct b.HisDictionaryCode
        from TB_Cinfiger_SubjectItem a
                 inner join TB_Dic_HisDictionaryExt b on a.Grade = b.HisDictionaryID and a.HOSPITALCODE = b.HospitalCode
        where a.DctCode = #{workerId}
          and a.HOSPITALCODE = #{hospitalCode}
          and a.IsDelete = 0
          and a.IsUse = 1
          and b.IsDelete = 0
          and b.IsUse = 1
    </select>
    <select id="getAllWorkerByInput" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.Worker">
        select a.*
        from System_Tb_Worker a
                 inner join HISDB..TB_Dic_HisDictionary b
                            on a.StaffType = b.HisDictionaryCode and a.HospitalId = b.HospitalId
        where b.DictionaryTypeID = 60
          and b.HisDictionaryCode = 1
          and b.IsDelete = 0
          and a.Status = 0
          and a.HospitalId = #{hospitalCode}
          and (a.Name like '%' + #{input} + '%' or a.InputCode2 like '%' + #{input} + '%')
    </select>
    <select id="getIsWhiteDoc" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.MdmRecipeWhitelist">
        SELECT a.WorkerId,
        a.Name,
        a.HospitalCode,
        a.Oper,
        a.OperTime
        FROM MDM_Recipe_Whitelist a
        WHERE a.WorkerId = #{doctorId}
        and a.HospitalCode = #{hospitalCode}
    </select>
    <select id="getMDTWorkNum" resultType="java.lang.Integer">
        select count(1)
        from System_Tb_Worker wr
        where wr.Title in(2,3)
          and exists
            (
                select 1 from Reg_Tb_RegisterDoctor_Time mdt
                where wr.WorkerId = mdt.Doctor
                  and wr.HospitalId = mdt.HospitalCode
                  and mdt.RegNo = #{regNo})
          and wr.HospitalId = #{hospitalCode}
    </select>

    <select id="getRelationWorker" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.Worker">
        select a.*
        from System_Tb_Worker a
        where a.Status = 0
            and exists
                       (
                           select 1 from System_Tb_Worker b
                           where b.WorkerId = #{doctorId}
                             and b.HospitalId = #{hospitalCode}
                             and b.IdentityCard = a.IdentityCard
                             and LEN(TRIM(b.IdentityCard)) >0
                       )
    </select>

</mapper>