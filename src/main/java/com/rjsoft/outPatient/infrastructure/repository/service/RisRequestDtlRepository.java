package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.RisRequestDtl;

/**
 * <AUTHOR>
 * @since 2021/10/13-2:13 下午
 */
public interface RisRequestDtlRepository {

    /**
     * 根据处方明细 id 查询
     *
     * @param recipeDetailId 处方明细 id
     * @param hospitalCode   医院编码区分总院分院数据库
     * @return {@link RisRequestDtl}
     */
    RisRequestDtl getRisRequestDtlByRecipeDetailId(Integer recipeDetailId, Integer hospitalCode);

}
