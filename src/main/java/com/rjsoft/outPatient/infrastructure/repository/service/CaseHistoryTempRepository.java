package com.rjsoft.outPatient.infrastructure.repository.service;

import com.github.pagehelper.PageInfo;
import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.*;
import com.rjsoft.outPatient.domain.doctorElemMain.dto.ElemParams;
import com.rjsoft.outPatient.infrastructure.repository.entity.CaseTemp;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/6/12 13:41
 * @description
 **/
public interface CaseHistoryTempRepository {


    /**
     * 判断MZYS_TB_MBINFO表中模板代码是否已存在
     *
     * @param tempCode 模板代码
     * @return
     */
    boolean judgeTempCode(String tempCode, Integer hospCode, Integer tempId);


    /**
     * 查询科室模板
     *
     * @param deptId
     * @param hospCode
     * @param searchType
     * @param tempName
     * @return
     */
    List<QueryDefaultTempResult> queryDeptTemp(Integer deptId, Integer hospCode, Integer searchType, String tempName, Integer dataSources);

    /**
     * 获取全院模板
     *
     * @param hospCode
     * @param tempName
     * @param dataSources
     * @return
     */
    List<QueryDefaultTempResult> queryHospTemp(Integer hospCode, String tempName, Integer dataSources);

    /**
     * 根据科研课题id喝医院编码获取模板
     *
     * @param projectId
     * @param hospCode
     * @return
     */
    List<QueryDefaultTempResult> queryResearchTemp(String projectId, Integer hospCode, String tempName);

    /**
     * 获取默认模板（科室、科研）
     *
     * @param deptId
     * @param visitFlag
     * @param sexFlag
     * @param hospitalCode
     * @return
     */
    QueryDefaultTempResult queryDeptDefaultTemp(Integer deptId, Integer visitFlag, Integer sexFlag, Integer hospitalCode);


    /**
     * 根据科研课题id+初诊标记+医院编码获取科研默认模板
     *
     * @param projectId
     * @param visitFlag
     * @param hospitalCode
     * @return
     */
    QueryDefaultTempResult queryResearchDefaultTemp(String projectId, Integer visitFlag, Integer hospitalCode);

    /**
     * 根据科研课题id+性别+医院编码获取科研默认模板
     *
     * @param projectId
     * @param sexFlag
     * @param hospitalCode
     * @return
     */
    QueryDefaultTempResult getResearchDefaultTemp(String projectId, Integer sexFlag, Integer hospitalCode);

    /**
     * 病历模板维护（保存修改）
     *
     * @param dataParam
     * @return
     */
    boolean saveTempInfo(SaveTempInfoParam dataParam);

    /**
     * 删除病历模板维护
     *
     * @param tempId
     * @param hospCode
     * @return
     */
    boolean delTempInfo(Integer tempId, Integer hospCode);

    /**
     * 病历模板内容保存修改
     *
     * @param dataParam
     * @return
     */
    boolean saveTempXML(SaveTempXMLParam dataParam);

    /**
     * 科室模板对照保存
     *
     * @param dataParam
     * @return
     */
    boolean saveBaseTempContrast(SaveBaseTempContrastParam dataParam);

    /**
     * 查询基础模板对照列表
     *
     * @param deptId
     * @param hospCode
     * @param tempName
     * @return
     */
    List<QueryBaseTempContrastResult> queryBaseTempContrast(Integer deptId, Integer hospCode, String tempName);

    /**
     * 科室模板对照删除
     *
     * @param deptTempId
     * @return
     */
    boolean delBaseTempContrast(Integer deptTempId);

    /**
     * 科研模板对照保存
     *
     * @param dataParam
     * @return
     */
    boolean saveResearchTempContrast(SaveResearchTempContrastParam dataParam);

    /**
     * 查询科研模板对照信息
     *
     * @param hospCode
     * @param projectId
     * @param tempName
     * @return
     */
    List<QueryResearchTempContrastResult> queryResearchTempContrast(Integer hospCode, String projectId, String tempName);

    /**
     * 查询课题下模板
     *
     * @param hospCode
     * @param projectId
     * @param tempName
     * @return
     */
    List<QueryDefaultTempResult> queryProjectTemp(Integer hospCode, String projectId, String tempName);

    /**
     * 科研模板对照删除
     *
     * @param researchTempId
     * @return
     */
    boolean delResearchTempContrast(Integer researchTempId);


    /**
     * 获取病历维护列表
     *
     * @param hospCode
     * @param tempName
     * @param tempType
     * @param pageSize
     * @param pageNum
     * @return
     */
    PageInfo<CaseTemp> queryTempInfoList(Integer hospCode, String tempName, Integer tempType, Integer type, Integer pageSize, Integer pageNum);

    /**
     * 获取科室列表
     *
     * @param hospCode
     * @return
     */
    List<DeptDto> getDeptList(Integer hospCode);

    /**
     * 获取住院科室列表
     *
     * @param hospCode
     * @return
     */
    List<DeptDto> getZyDeptList(Integer hospCode);

    /**
     * @param deptId
     * @param deptTempIdList
     * @return
     */
    int batchDelBaseTempContrast(Integer deptId, List<Integer> deptTempIdList);

    /**
     * @param tempIdList
     * @param hospCode
     * @return
     */
    List<CaseTemp> getTempListByTempId(List<Integer> tempIdList, Integer hospCode);

    /**
     * @param projectId
     * @param tempIdList
     * @return
     */
    int batchDelResearchTempContrast(String projectId, List<Integer> tempIdList);

}
