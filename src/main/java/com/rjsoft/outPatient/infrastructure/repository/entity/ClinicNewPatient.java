package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.rjsoft.outPatient.domain.diseaseReport.Enum.ReportStatus;
import com.rjsoft.outPatient.domain.diseaseReport.dto.ReportContentResult;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Data
@Table(name = "Tbt_ZyEMR_Qpmzxbr")
public class ClinicNewPatient implements Serializable {

    @Id
    @Column(name = "Id", updatable = false)
    private Integer id;

    @Column(name = "diagnoseNo")
    private Integer diagnoseNo;

    /**
     * 疾病上报类型，区分HIS上报与第三方上报
     */
    @Column(name = "ReportType")
    private String reportType;

    /**
     * 疾病上报类型
     */
    @Column(name = "UploadTypeId")
    private Integer uploadTypeId;

    /**
     * 疾病上报大类ID
     */
    @Column(name = "ReportDiseaseId")
    private Integer reportDiseaseId;

    /**
     * 就诊流水号
     */
    @Column(name = "jzlsh", updatable = false)
    private String receptionNo;
    /**
     * 患者姓名
     */
    @Column(name = "Name")
    private String patName;
    /**
     * 患者性别
     */
    @Column(name = "Gender")
    private Integer gender;
    /**
     * 患者出生日期
     */
    @Column(name = "Birthday")
    private String birthday;
    /**
     * 民族
     */
    @Column(name = "Nation")
    private String nation;
    /**
     * 文化程度
     */
    @Column(name = "Education")
    private String education;
    /**
     * 职业
     */
    @Column(name = "Occupaton")
    private String occupation;
    /**
     * 工作地点
     */
    @Column(name = "WorkPlace")
    private String workPlace;
    /**
     * 身份证号
     */
    @Column(name = "IdNo")
    private String idNo;
    /**
     * 联系人姓名
     */
    @Column(name = "ContractName")
    private String contractName;
    /**
     * 联系人电话
     */
    @Column(name = "ContractPhone")
    private String contractPhone;
    /**
     * 与患者关系
     */
    @Column(name = "ContractRelation")
    private String contractRelation;

    /**
     * 患者电话
     */
    @Column(name = "Phone")
    private String phone;
    /**
     * 户别
     */
    @Column(name = "RegType")
    private String regType;
    /**
     * 婚姻状况
     */
    @Column(name = "MaritalStatus")
    private String maritalStatus;
    /**
     * 户籍地  省（自治区、直辖市）
     */
    @Column(name = "hj1")
    private String domicile1;

    /**
     * 户籍地  市（帝、州、盟）
     */
    @Column(name = "hj2")
    private String domicile2;
    /**
     * 户籍地  县(市区、旗)
     */
    @Column(name = "hj3")
    private String domicile3;
    /**
     * 户籍地  乡
     */
    @Column(name = "hj4")
    private String domicile4;
    /**
     * hj4code
     */
    @Column(name = "hj4code")
    private String domicile4Code;
    /**
     * 户籍地  村（居委会）
     * domicile
     */
    @Column(name = "hj5")
    private String domicile5;
    /**
     * 户籍地  详细至门牌号
     * domicile
     */
    @Column(name = "hj6")
    private String domicile6;
    /**
     * 现居地 省（自治区、直辖市）
     */
    @Column(name = "xj1")
    private String presentAddr1;
    /**
     * 现居地 市（地、州、盟）
     */
    @Column(name = "xj2")
    private String presentAddr2;
    /**
     * 现居地 县（市区、旗）
     */
    @Column(name = "xj3")
    private String presentAddr3;
    /**
     * 现居地 乡（镇，街道）
     */
    @Column(name = "xj4")
    private String presentAddr4;
    /**
     * xj4code
     */
    @Column(name = "xj4code")
    private String presentAddr4Code;
    /**
     * 现居地 村（居委会）
     */
    @Column(name = "xj5")
    private String presentAddr5;
    /**
     * 现居地 门牌号
     */
    @Column(name = "xj6")
    private String presentAddr6;

    /**
     * 精神症状幻觉
     */
    @Column(name = "Illusion")
    private String illusion;
    /**
     * 精神症状情感
     */
    @Column(name = "Emotion")
    private String emotion;
    /**
     * 精神症状思维
     */
    @Column(name = "Thought")
    private String thought;
    /**
     * 精神症状行为
     */
    @Column(name = "Action")
    private String action;
    /**
     * 精神症状意志
     */
    @Column(name = "Volition")
    private String volition;
    /**
     * 精神症状其他
     */
    @Column(name = "Other")
    private String other;

    /**
     * 诊断编码
     */
    @Id
    @Column(name = "diagnoseCode", updatable = false)
    private String diagnoseCode;
    /**
     * 诊断医生姓名
     */
    @Column(name = "DoctorName")
    private String doctorName;
    /**
     * 诊断日期
     */
    @Column(name = "DiagnoseDate")
    private Date diagnoseDate;
    /**
     * 诊断复核编码
     */
    @Column(name = "DiagnoseCode2")
    private String diagnoseCode2;
    /**
     * 诊断复核医生名称
     */
    @Column(name = "DoctorName2")
    private String doctorName2;
    /**
     * 诊断复核日期
     */
    @Column(name = "DiagnoseDate2")
    private Date diagnoseDate2;
    /**
     * 初发病日期
     */
    @Column(name = "FirstAppearDate")
    private Date firstAppearDate;
    /**
     * 初诊日期
     */
    @Column(name = "FirstConfirmDate")
    private Date firstConfirmDate;


    /**
     * 既往住院情况
     */
    @Column(name = "HospitalStateHistory")
    private String hospitalStateHistory;
    /**
     * 送检主体   多选 英文逗号隔开
     */
    @Column(name = "SendDiagnosis")
    private String sendDiagnosis;
    /**
     * 送检主体 其他
     */
    @Column(name = "SendDiagnosisOther")
    private String sendDiagnosisOther;
    /**
     * 两系三代精神疾病家族史
     */
    @Column(name = "FamilyHistory")
    private String familyHistory;
    /**
     * 是否已进行抗精神病药物治疗
     */
    @Column(name = "IfCure")
    private String ifCure;
    /**
     * 首次抗精神病药物治疗时间
     */
    @Column(name = "FirstCureTime")
    private String firstCureTime;
    /**
     * 既往关锁情况
     */
    @Column(name = "ShutStatusHistory")
    private String shutStatusHistory;
    /**
     * 知情同意
     */
    @Column(name = "AgreeType")
    private String agreeType;
    /**
     * 既往危险行为
     */
    @Column(name = "PastRiskhave")
    private String pastRiskhave;
    /**
     * 目前危险性评估
     */
    @Column(name = "RiskPast")
    private String riskPast;
    /**
     * 药品信息
     */
    @Column(name = "DrugInfos")
    private String drugInfos;


    /**
     * 诊断机构编码
     */
    @Column(name = "DiagnoseOrgId")
    private String diagnoseOrgId;

    /**
     * 诊断机构名称
     */

    @Column(name = "DiagnoseOrgName")
    private String diagnoseOrgName;

    /**
     *
     */
    @Column(name = "Status")
    private String status;

    /**
     * 创建人
     */
    @Column(name = "Creator")
    private String creator;

    /**
     * 创建时间
     */
    @Column(name = "CreateTime")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "Updator")
    private String updator;

    /**
     * 修改时间
     */
    @Column(name = "UpdateTime")
    private Date updateTime;

    /**
     *
     */
    @Column(name = "ComeFlag")
    private String comeFlag;

    @Column(name = "HospitalCode")
    private Integer hospitalCode;

    public ReportContentResult toReportContentResult() {
        ReportContentResult result = new ReportContentResult();
        BeanUtils.copyProperties(this, result);
        List<Integer> checked = getChecked();
        // 定稿状态： 0定稿   1撤销定稿
        if (checked.contains(Integer.parseInt(this.status.trim()))) {
            result.setStatus("1");
        } else {
            result.setStatus("0");
        }
        result.setGender(String.valueOf(gender == null ? null : gender));
        if ("-1".equals(this.education)) {
            result.setEducation("8");
        }
        return result;
    }

    /**
     * 获取疾病上报定稿的状态范围
     */
    public List<Integer> getChecked() {
        ArrayList<Integer> list = new ArrayList<>();
        list.add(Integer.parseInt(ReportStatus.FINAL.getStatus()));
        list.add(Integer.parseInt(ReportStatus.CHECKED.getStatus()));
        list.add(Integer.parseInt(ReportStatus.UPLOADED.getStatus()));
        return list;
    }


}
