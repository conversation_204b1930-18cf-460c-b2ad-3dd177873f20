<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ItemSetDetailMapper">


    <select id="getItemSetDetailBySetId"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ItemSetDetail">
        select distinct a.setId,
               a.itemId,
               a.qty,
               a.useRange,
               a.hospitalId,
               a.itemName,
               a.unit,
               ISNULL((b.ClinicExpensePrice + b.ClinicNonExpensePrice), 0) * ISNULL(s.ClinicQuantity, b.ClinicQty) price,
               b.ItemName                                     realItemName,
               b.ClinicUnit                                   realUnit
        from System_tb_ItemSetDetl a
        inner join System_Tv_PubItems b on a.ItemId = b.ItemCode and a.hospitalid = b.hospitalid
        LEFT JOIN System_Tb_SpecialItems s ON s.ItemCode = b.ItemCode AND b.hospitalid = s.HospitalCode
        where b.Stopped = 0
          and b.UseRange in (0, 1)
          and a.hospitalId = #{hospitalCode}
        order by a.itemId
    </select>

    <select id="getItemSetDetailByDeptId"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ItemSetDetail">
        select distinct a.setId,
                        a.itemId,
                        a.qty,
                        a.useRange,
                        a.hospitalId,
                        a.itemName,
                        a.unit,
                        ISNULL((b.ClinicExpensePrice + b.ClinicNonExpensePrice), 0) * ISNULL(s.ClinicQuantity, b.ClinicQty) price,
                        b.ItemName                                     realItemName,
                        b.ClinicUnit                                   realUnit,
            (select count(0) from System_Tb_ItemInfoDept c where c.drugId = a.itemId and c.hospitalid = a.hospitalId and c.useDeptRange in (0,1) and (c.deptid = #{deptId} or c.deptid = -1) ) as useDeptNum
        from System_tb_ItemSetDetl a
        inner join System_Tv_PubItems b on a.ItemId = b.ItemCode and a.hospitalid = b.hospitalid
        LEFT JOIN System_Tb_SpecialItems s ON s.ItemCode = b.ItemCode AND b.hospitalid = s.HospitalCode
        where b.Stopped = 0
          and a.UseRange in (0, 1)
          and a.hospitalId = #{hospitalCode}
        <if test="setIdList!= null and setIdList.size() >0">
           and a.SetId IN
            <foreach collection="setIdList" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
        </if>
        order by a.itemId
    </select>


</mapper>