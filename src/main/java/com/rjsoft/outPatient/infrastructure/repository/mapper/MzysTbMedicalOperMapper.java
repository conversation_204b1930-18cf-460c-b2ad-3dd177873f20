package com.rjsoft.outPatient.infrastructure.repository.mapper;

import cn.hutool.core.collection.CollUtil;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbMedicalOper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Collections;
import java.util.List;

public interface MzysTbMedicalOperMapper extends Mapper<MzysTbMedicalOper> {

    default List<MzysTbMedicalOper> getMedicalOperByRegNo(Long regNo, Integer hospitalCode) {
        Weekend<MzysTbMedicalOper> weekend = Weekend.of(MzysTbMedicalOper.class);
        WeekendCriteria<MzysTbMedicalOper, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andEqualTo(MzysTbMedicalOper::getRegNo, regNo).andEqualTo(MzysTbMedicalOper::getHospitalId, hospitalCode);
        return this.selectByExample(weekend);
    }

    default List<MzysTbMedicalOper> queryMedicalOperByRegNoList(List<Long> regNoList, Integer hospitalCode) {
        if (CollUtil.isEmpty(regNoList)) {
            return Collections.emptyList();
        }
        Weekend<MzysTbMedicalOper> weekend = Weekend.of(MzysTbMedicalOper.class);
        WeekendCriteria<MzysTbMedicalOper, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andIn(MzysTbMedicalOper::getRegNo, regNoList).andEqualTo(MzysTbMedicalOper::getHospitalId, hospitalCode);
        return this.selectByExample(weekend);
    }

    default boolean deleteByRegNo(Long regNo, Integer hospitalCode) {
        Weekend<MzysTbMedicalOper> weekend = Weekend.of(MzysTbMedicalOper.class);
        WeekendCriteria<MzysTbMedicalOper, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andEqualTo(MzysTbMedicalOper::getRegNo, regNo).andEqualTo(MzysTbMedicalOper::getHospitalId, hospitalCode);
        return this.deleteByExample(weekend) > 0;
    }

    default boolean deleteByRegNoList(List<Long> regNoList, Integer hospitalCode) {
        Weekend<MzysTbMedicalOper> weekend = Weekend.of(MzysTbMedicalOper.class);
        WeekendCriteria<MzysTbMedicalOper, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andIn(MzysTbMedicalOper::getRegNo, regNoList).andEqualTo(MzysTbMedicalOper::getHospitalId, hospitalCode);
        return this.deleteByExample(weekend) > 0;
    }

}