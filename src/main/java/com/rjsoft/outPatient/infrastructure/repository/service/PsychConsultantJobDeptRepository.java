package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.PsychConsultantJobDept;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/25-2:56 下午
 */
public interface PsychConsultantJobDeptRepository {

    /**
     * 根据医生 id 查询
     *
     * @param doctorId     医生 id
     * @param hospitalCode 医院编码
     * @return {@link PsychConsultantJobDept}
     */
    List<PsychConsultantJobDept> getByDoctorId(Integer doctorId, Integer hospitalCode);

}
