package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.CaseHistory;
import com.rjsoft.outPatient.infrastructure.repository.entity.Consul;
import com.rjsoft.outPatient.infrastructure.repository.entity.ConsulDept;
import com.rjsoft.outPatient.infrastructure.repository.entity.ConsulType;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ConsulDeptMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ConsulMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ConsulTypeMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ConsulRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/10 - 9:49
 */
@Service
@AllArgsConstructor
public class ConsulRepositoryImpl implements ConsulRepository {

    private final ConsulMapper consulMapper;
    private final ConsulDeptMapper consulDeptMapper;
    private final ConsulTypeMapper consulTypeMapper;


    @Override
    @DatabaseAnnotation
    public Consul queryConsulList(Long receptionNo, Integer hospitalCode) {
        final Consul entity = new Consul();
        entity.setReceptionNo(receptionNo);
        entity.setHospitalCode(hospitalCode);
        return consulMapper.selectOne(entity);
    }

    @Override
    @DatabaseAnnotation
    public Consul queryConsul(Long regNo, Long patId, Integer hospitalCode) {
        final Consul entity = new Consul();
        entity.setRegNo(regNo);
        entity.setPatId(patId);
        entity.setHospitalCode(hospitalCode);
        return consulMapper.selectOne(entity);
    }

    @Override
    @DatabaseAnnotation
    public Consul queryConsulByConsulId(Long consulId, Integer hospitalCode) {
        final Consul entity = new Consul(consulId);
        entity.setHospitalCode(hospitalCode);
        return consulMapper.selectOne(entity);
    }

    @Override
    @DatabaseAnnotation
    public Consul queryConsulByRegNo(Long regNo, Integer hospitalCode) {
        final Consul entity = new Consul();
        entity.setRegNo(regNo);
        entity.setHospitalCode(hospitalCode);
        return consulMapper.selectOne(entity);
    }

    @Override
    @DatabaseAnnotation
    public List<Consul> queryConsulByRegNos(List<Long> regNos, Integer hospitalCode) {
        if (regNos == null || regNos.size() == 0) {
            return new ArrayList<>();
        }
        ArrayList<Consul> list = new ArrayList<>();
        if (regNos.size() > 2000) {
            int count = 2000;
            int batch = regNos.size() / count;
            if (regNos.size() % count != 0) {
                batch++;
            }
            for (int i = 0; i < batch; i++) {
                int start = i * count;
                int end = (i + 1) * count;
                if(end >= regNos.size()){
                    end = regNos.size();
                }
                List<Long> extraRegNo = regNos.subList(start, end);
                Weekend<Consul> weekend = new Weekend(Consul.class);
                weekend.weekendCriteria()
                        .andIn(Consul::getRegNo, extraRegNo)
                        .andEqualTo(Consul::getHospitalCode, hospitalCode);
                list.addAll(consulMapper.selectByExample(weekend));
            }

        } else {
            Weekend<Consul> weekend = new Weekend(Consul.class);
            weekend.weekendCriteria()
                    .andIn(Consul::getRegNo, regNos)
                    .andEqualTo(Consul::getHospitalCode, hospitalCode);
            list.addAll(consulMapper.selectByExample(weekend));
        }
        return list;
    }

    @Override
    @DatabaseAnnotation
    public Consul queryConsulByDeptId(Long regNo, Integer hospitalCode, Integer deptId) {
        final Weekend<Consul> weekend = new Weekend<>(Consul.class);
        weekend.weekendCriteria().andEqualTo(Consul::getRegNo, regNo).andEqualTo(Consul::getHospitalCode, hospitalCode);
        final WeekendCriteria<Consul, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(Consul::getConsulDept, deptId).orEqualTo(Consul::getDeptId, deptId);
        weekend.and(weekendCriteria);
        return consulMapper.selectOneByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public Consul queryConsulByWorkerId(Long regNo, Integer hospitalCode, Integer workerId) {
        final Consul entity = new Consul();
        entity.setRegNo(regNo);
        entity.setConsulDoctor(workerId);
        entity.setHospitalCode(hospitalCode);
        return consulMapper.selectOne(entity);
    }

    @Override
    @DatabaseAnnotation
    public Boolean saveConsul(Consul consul) {
        return consulMapper.insertSelective(consul) > 0;
    }

    @Override
    @DatabaseAnnotation
    public Boolean updateConsul(Consul consul) {
        return consulMapper.updateByPrimaryKeySelective(consul) > 0;
    }

    @Override
    @DatabaseAnnotation
    public Boolean deleteConsul(Consul consul) {
        return consulMapper.deleteByPrimaryKey(consul) > 0;
    }

    @Override
    @DatabaseAnnotation
    public List<ConsulDept> queryConsulDeptList(Integer deptId, Integer hospitalCode) {
        final Weekend<ConsulDept> weekend = new Weekend<>(ConsulDept.class);
        final WeekendCriteria<ConsulDept, Object> weekendCriteria = weekend.weekendCriteria();
        if (deptId != null && deptId != 0) {
            weekendCriteria.andEqualTo(ConsulDept::getDeptId, deptId);
        }
        weekendCriteria.andEqualTo(ConsulDept::getHospitalCode, hospitalCode);
        return consulDeptMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<ConsulType> queryConsulTypeList(Integer id, Integer hospitalCode) {
        ConsulType entity = new ConsulType(id, hospitalCode);
        return consulTypeMapper.select(entity);
    }
}
