<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ClinicNewPatientMapper">

    <select id="getInfoByIdNoSbname" resultType="java.lang.Integer">
        SELECT count(*)
        FROM Tbt_ZyEMR_Qpmzxbr a (nolock)
        WHERE ReportType = 'HIS'
          and a.idNo = #{idNo}
          and a.jzlsh = #{receptionNo}
          and a.HospitalCode = #{hospitalCode}
    </select>

    <select id="getFinalizedInfoByIdNoSbname" resultType="java.lang.Integer">
        SELECT count(*)
        FROM Tbt_ZyEMR_Qpmzxbr a (nolock)
        WHERE a.idNo = #{idNo}
          AND a.reportDiseaseId = #{reportDiseaseId}
          AND a.HospitalCode = #{hospitalCode}
          AND a.Status > 0
    </select>

    <select id="getInfoByIdNoSbname2"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ClinicNewPatient">

        SELECT top 1
        a.jzlsh receptionNo, a.Name patName,
               a.Gender           gender,
               a.Birthday         birthday,
               a.Nation           nation,
               a.Education        education,
               a.Occupaton        occupation,
               a.WorkPlace        workPlace,
               a.IdNo             idNo,
               a.ContractName     contractName,
               a.ContractPhone    contractPhone,
               a.ContractRelation contractRelation,
               a.Phone            phone,
               a.RegType          regType,
               a.MaritalStatus    maritalStatus,
               a.hj1              domicile1,
               a.hj2              domicile2,
               a.hj3              domicile3,
               a.hj4              domicile4,
               a.hj4code          domicile4Code,
               a.hj5              domicile5,
               a.hj6              domicile6,
               a.xj1              presentAddr1,
               a.xj2              presentAddr2,
               a.xj3              presentAddr3,
               a.xj4              presentAddr4,
               a.xj4code          presentAddr4Code,
               a.xj5              presentAddr5,
               a.xj6              presentAddr6,
               a.Illusion         illusion,
               a.Emotion          emotion,
               a.Thought          thought,
               a.Action action,
        a.Volition volition,
        a.Other other,
        a.diagnoseCode diagnoseCode,
        a.DoctorName doctorName,
        a.DiagnoseDate diagnoseDate,
        a.DiagnoseCode2 diagnoseCode2,
        a.DoctorName2 doctorName2,
        a.DiagnoseDate2 diagnoseDate2,
        a.FirstAppearDate firstAppearDate,
        a.FirstConfirmDate firstConfirmDate,
        a.HospitalStateHistory hospitalStateHistory,
        a.SendDiagnosis sendDiagnosis,
        a.SendDiagnosisOther sendDiagnosisOther,
        a.FamilyHistory familyHistory,
        a.IfCure ifCure,
        a.FirstCureTime firstCureTime,
        a.ShutStatusHistory shutStatusHistory,
        a.AgreeType agreeType,
        a.PastRiskhave pastRiskhave,
        a.RiskPast riskPast,
        a.DrugInfos drugInfos,
        a.DiagnoseOrgId diagnoseOrgId,
        a.DiagnoseOrgName diagnoseOrgName,
        a.Status status,
        a.Creator creator,
        a.CreateTime createTime,
        a.Updator updator,
        a.UpdateTime updateTime,
        a.ComeFlag comeFlag
        FROM Tbt_ZyEMR_Qpmzxbr a (nolock)
        WHERE ReportType = 'HIS'
          and a.idNo = #{idNo}
          and a.jzlsh = #{receptionNo}
          and a.HospitalCode=#{hospitalCode}
    </select>

</mapper>
