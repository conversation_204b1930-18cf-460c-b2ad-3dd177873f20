package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.enterHospitalRequisition.dto.ProcessScheduleResult;
import com.rjsoft.outPatient.infrastructure.repository.entity.EnterHospitalRequisition;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.HashMap;
import java.util.List;

/**
 * 住院申请单
 *
 * <AUTHOR>
 * @since 2021/7/19 - 14:13
 */
public interface EnterHospitalRequisitionMapper extends BaseMapper<EnterHospitalRequisition>, ExampleMapper<EnterHospitalRequisition> {


    String importReport(String patName, String hisCardNo, String receptionNo);

    ProcessScheduleResult getInspect(@Param("receptionNo") String receptionNo, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 查询风险评估单完成状态
     *
     * @param receptionNo
     * @return
     */
    Integer getFxpgStatus(@Param("receptionNo") String receptionNo, @Param("hospitalCode") Integer hospitalCode);


    List<String> getNotificationDetailsByRgNosAndGzdName(@Param("rgNos")List<String> rgNos, @Param("gzdName")String gzdName);

    List<EnterHospitalRequisition> getSq(@Param("rgNos")List<Long> regNos1);

    HashMap getSqhInfo(@Param("id")Integer id);

    boolean updateByRecipeNo(@Param("id")String id,@Param("status") Boolean status);

    HashMap getpatPhone(@Param("patID")Long patID);

    boolean updateRiskReportStatusByRecipeNo(@Param("id")String id
            ,@Param("riskStatus") Boolean riskStatus
            ,@Param("riskSignStatus") Boolean riskSignStatus);
}
