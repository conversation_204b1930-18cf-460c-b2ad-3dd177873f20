package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rjsoft.outPatient.common.enums.YesOrNoEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.OldSeriousIllnessRegister;
import com.rjsoft.outPatient.infrastructure.repository.entity.SeriousIllnessRegister;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;
import java.util.Optional;

/**
 * 大病登记
 *
 * <AUTHOR>
public interface OldSeriousIllnessRegisterMapper extends BaseMapper<OldSeriousIllnessRegister>, ExampleMapper<OldSeriousIllnessRegister> {

    /**
     * 根据卡号查询大病登记记录
     * @param sfz      身份证
     * @param patName      患者姓名
     * @param status       状态
     * @param hospitalCode 医院编码
     * @return {@link List<SeriousIllnessRegister>}
     */
    default List<OldSeriousIllnessRegister> getSeriousIllnessRegister(Integer pageNum,String sfz, String patName, Integer status, Integer hospitalCode) {
        Weekend<OldSeriousIllnessRegister> weekend = new Weekend<>(OldSeriousIllnessRegister.class);
        WeekendCriteria<OldSeriousIllnessRegister, Object> weekendCriteria = weekend.weekendCriteria();
        if (!StringUtils.isEmpty(patName)) {
            weekendCriteria.andLike(OldSeriousIllnessRegister::getPatName, "%" + patName + "%");
        }
        if (!StringUtils.isEmpty(sfz)) {
            weekendCriteria.andEqualTo(OldSeriousIllnessRegister::getPatSfz, sfz);
        }
        Optional.ofNullable(status).ifPresent(s -> weekendCriteria.andEqualTo(OldSeriousIllnessRegister::getState, s));
        weekendCriteria.andEqualTo(OldSeriousIllnessRegister::getDelFlag, YesOrNoEnum.NO.getCode());
        PageHelper.startPage(pageNum, 10);
        List<OldSeriousIllnessRegister> oldSeriousIllnessRegisterList = selectByExample(weekend);
        PageInfo<OldSeriousIllnessRegister> pageinfo = new PageInfo<>(oldSeriousIllnessRegisterList);
        return pageinfo.getList();
    }

    String getSFZ(String cardNo, String hisCardNo, Integer hospitalId);
}
