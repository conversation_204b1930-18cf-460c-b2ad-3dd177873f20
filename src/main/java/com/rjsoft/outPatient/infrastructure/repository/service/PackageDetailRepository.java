package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.PackageDetail;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/6 - 17:07
 */
public interface PackageDetailRepository {

    /**
     * 根据套餐id查询套餐明细
     *
     * @param packageId 套餐id
     * @return
     */
    List<PackageDetail> getPackageDetailByPackageId(int packageId);

    /**
     * 保存套餐明细
     *
     * @param packageDetail 套餐明细
     */
    void savePackageDetail(PackageDetail packageDetail);

    /**
     * 删除套餐明细
     *
     * @param packageDetail 套餐明细
     */
    void updatePackageDetail(PackageDetail packageDetail, Example example);

    /**
     * 根据套餐id查询套餐明细id列表
     *
     * @param packageId 套餐id
     * @return 套餐明细ids
     */
    List<Integer> getPackageDetailIdsByPackageId(Integer packageId);

}
