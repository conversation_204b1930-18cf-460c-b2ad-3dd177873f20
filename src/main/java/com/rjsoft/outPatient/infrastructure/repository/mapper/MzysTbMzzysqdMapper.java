package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.admissionApplicationForm.pojo.MzysTbMzzysqd;
import com.rjsoft.outPatient.domain.admissionApplicationForm.pojo.MzysTbMzzysqdExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MzysTbMzzysqdMapper {
    int countByExample(MzysTbMzzysqdExample example);

    int deleteByExample(MzysTbMzzysqdExample example);

    int insert(MzysTbMzzysqd record);

    int insertSelective(MzysTbMzzysqd record);

    List<MzysTbMzzysqd> selectByExample(MzysTbMzzysqdExample example);

    int updateByExampleSelective(@Param("record") MzysTbMzzysqd record, @Param("example") MzysTbMzzysqdExample example);

    int updateByExample(@Param("record") MzysTbMzzysqd record, @Param("example") MzysTbMzzysqdExample example);
}