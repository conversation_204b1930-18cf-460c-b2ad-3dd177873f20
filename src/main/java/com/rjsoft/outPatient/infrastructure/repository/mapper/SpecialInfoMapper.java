package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ChiefComplaint;
import com.rjsoft.outPatient.infrastructure.repository.entity.SpecialInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.SpecialItems;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

public interface SpecialInfoMapper extends BaseMapper<SpecialInfo>, ExampleMapper<SpecialInfo> {

    /**
     * 根据ID查询患者特殊信息
     *
     * @param id
     * @param hospitalCode
     * @return
     */
    default SpecialInfo getPatientSpecialInfoOne(Integer id, Integer hospitalCode) {
        SpecialInfo specialInfo = new SpecialInfo();
        specialInfo.setId(id);
        specialInfo.setHospitalCode(hospitalCode);
        return selectByPrimaryKey(specialInfo);
    }

}
