package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.prescriptionAudit.dto.DrugUnitInfoDto;
import com.rjsoft.outPatient.domain.recipe.dto.DrugYYYDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugInfomation;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

/**
 * 药品信息
 *
 * <AUTHOR>
public interface DrugInfomationMapper extends BaseMapper<DrugInfomation>, ExampleMapper<DrugInfomation> {

    /**
     * 根据ID获取药品信息
     *
     * @param drugId
     * @param hospitalCode
     * @return
     */
    default DrugInfomation getDrugInfoById(Integer drugId, Integer hospitalCode) {
        DrugInfomation entity = new DrugInfomation();
        entity.setDrugId(drugId);
        entity.setHospitalId(hospitalCode);
        return selectOne(entity);
    }


    /**
     * 根据ID集合查询药典信息
     *
     * @param ids
     * @param hospitalCode
     * @return
     */
    default List<DrugInfomation> getDrugInfoByIds(Iterable<Integer> ids, Integer hospitalCode) {
        Weekend<DrugInfomation> weekend = new Weekend<>(DrugInfomation.class);
        WeekendCriteria<DrugInfomation, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(DrugInfomation::getDrugId, ids);
        weekendCriteria.andEqualTo(DrugInfomation::getHospitalId, hospitalCode);
        return selectByExample(weekend);
    }

    /**
     * 根据药品编码+医院编码查询药品单位信息
     *
     * @param drugId
     * @param hospitalCode
     * @return
     */
    DrugUnitInfoDto getDrugUnitInfo(@Param("drugId") String drugId, @Param("hospitalCode") String hospitalCode);

    Boolean checkDrugIsYYY(@Param("itemId")Integer itemId,@Param("hospId")Integer hospId);
    List<DrugYYYDto> ListDrugAllYYY(@Param("hospId")Integer hospId);

    Integer checkYYYAuthority(@Param("doctorId")Integer doctorId,@Param("hospId")Integer hospId);
}
