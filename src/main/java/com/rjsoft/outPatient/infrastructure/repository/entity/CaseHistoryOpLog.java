package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.rjsoft.common.request.RequestUtil;
import com.rjsoft.outPatient.domain.caseHistory.Enum.CaseHistoryOpTypeEnum;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2021/6/7 11:49
 * @description 电子病历操作日志
 **/
@Data
@Table(name = "MZYS_TB_DZBL_CZJL")
public class CaseHistoryOpLog implements Serializable {

    @Id
    @Column(name = "id", insertable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;
    /**
     * 病历id
     */
    @Column(name = "blId")
    private String blId;

    /**
     * 操作类型
     * 保存：save；
     * 提交：submmit；
     * 撤销提交：cancel submit；
     * 打印：print
     */
    @Column(name = "opType")
    private String opType;
    /**
     * 操作人id
     */
    @Column(name = "opCode")
    private String opCode;
    /**
     * 操作人姓名
     */
    @Column(name = "opName")
    private String opName;
    /**
     * 操作机ip
     */
    @Column(name = "ip")
    private String opIp;
    /**
     * 操作时间
     */
    @Column(name = "opTime")
    private Date opTime;
    /**
     * 删除状态  0否1是
     */
    @Column(name = "delStatus")
    private Integer delStatus;


    public CaseHistoryOpLog CreateEntity(CaseHistory addCaseHistory) {
        CaseHistoryOpLog entity = new CaseHistoryOpLog();
        entity.setBlId(String.valueOf(addCaseHistory.getBlId()));
        //entity.setOpType(CaseHistoryOpTypeEnum.ADD.getMsg());
        entity.setOpCode(String.valueOf(addCaseHistory.getOpCode()));
        entity.setOpName(addCaseHistory.getDoctorName());
        entity.setOpIp(RequestUtil.getRemoteAddr());
        entity.setOpTime(new Date());
        return entity;
    }

}
