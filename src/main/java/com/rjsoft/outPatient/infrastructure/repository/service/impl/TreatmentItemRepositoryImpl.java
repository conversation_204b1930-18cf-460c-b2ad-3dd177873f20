package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.domain.dictionary.Enum.DictionaryConfigEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.TreatmentItem;
import com.rjsoft.outPatient.infrastructure.repository.mapper.TreatmentItemMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.TreatmentItemRepository;
import lombok.AllArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/14-1:56 下午
 */
@Service
@AllArgsConstructor
public class TreatmentItemRepositoryImpl implements TreatmentItemRepository {

    private final TreatmentItemMapper treatmentItemMapper;

    @Override
    @DatabaseAnnotation
    @Cacheable(cacheNames = DictionaryConfigEnum.CACHE_NAME, key = "'treatmentItem'", unless = "#result == null || #result.isEmpty()")
    public List<TreatmentItem> findAll(Integer hospitalCode) {
        final Weekend<TreatmentItem> weekend = new Weekend<>(TreatmentItem.class);
        weekend.weekendCriteria().andEqualTo(TreatmentItem::getHospitalCode, hospitalCode);
        return treatmentItemMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public Map<String, String> findAllAsMap(Integer hospitalCode) {
        final List<TreatmentItem> treatmentItems = this.findAll(hospitalCode);
        return treatmentItems.stream().filter(i -> i.getName() != null).collect(
                Collectors.toMap(TreatmentItem::getCode, TreatmentItem::getName, (v1, v2) -> v2));
    }
}
