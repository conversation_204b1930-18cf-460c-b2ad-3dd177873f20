package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * 扩展模板
 * HISDB..System_Tb_TemplateExtension
 *
 * <AUTHOR>
 * @since 2021/9/30-9:13 上午
 */
@Data
@Table(name = "System_Tb_TemplateExtension")
public class TemplateExtension implements Serializable {

    /**
     * 可重复、不可为 null
     */
    @Column(name = "KeyId")
    private Integer keyId;
    /**
     * 药品 id
     */
    @Column(name = "ClassId")
    private String classId;
    /**
     * 编号
     */
    @Column(name = "TypeNo")
    private String typeNo;
    /**
     * 编码
     */
    @Column(name = "KeyCode")
    private String keyCode;
    /**
     * 值
     */
    @Column(name = "KeyValue")
    private String keyValue;
    /**
     * 类型
     */
    @Column(name = "KeyType")
    private Integer keyType;
    /**
     *
     */
    @Column(name = "KeySource")
    private String keySource;
    /**
     * 描述
     */
    @Column(name = "Description")
    private String description;
    /**
     *
     */
    @Column(name = "SID")
    private Integer sId;
    /**
     * 创建时间
     */
    @Column(name = "CreateOn")
    private Date createOn;
    /**
     * 创建人 id
     */
    @Column(name = "CreateUserId")
    private Integer createUserId;
    /**
     * 创建人姓名
     */
    @Column(name = "CreateUserName")
    private String createUserName;
    /**
     * 修改人 id
     */
    @Column(name = "UpdateUserId")
    private Integer updateUserId;
    /**
     * 修改人姓名
     */
    @Column(name = "UpdateUserName")
    private String updateUserName;
    /**
     * 修改时间
     */
    @Column(name = "UpdateOn")
    private Date updateOn;
    /**
     * 医院编码
     */
    @Column(name = "HospitalId")
    private Integer hospitalId;

    @Transient
    @Column(insertable = false, updatable = false)
    private Integer code;

    @Transient
    @Column(insertable = false, updatable = false)
    private String name;

}
