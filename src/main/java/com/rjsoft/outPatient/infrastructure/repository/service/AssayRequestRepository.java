package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.AssayRequest;
import com.rjsoft.outPatient.infrastructure.repository.entity.AssayRequestDetail;

/**
 * 化验申请单
 *
 * <AUTHOR>
public interface AssayRequestRepository {

    /**
     * 保存化验申请单
     *
     * @param assayRequest
     * @return
     */
    boolean saveAssayRequest(AssayRequest assayRequest);

    /**
     * 根据申请单流水号获取化验申请单
     *
     * @param applyId
     * @param hospitalCode
     * @return
     */
    AssayRequest getAssayRequestByApplyIId(Integer applyId, Integer hospitalCode);

    /**
     * 根据预存流水号获取化验申请单
     *
     * @param preSaveNo
     * @param hospitalCode
     * @return
     */
    AssayRequest getAssayRequestByPreNo(Long preSaveNo, Integer hospitalCode);

    /**
     * 删除化验申请单
     *
     * @param requestNo
     * @param hospitalCode
     * @return
     */
    boolean delAssayRequest(Long requestNo, Integer hospitalCode);

}
