package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ExamineRequestDetailZxHis;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * 检查申请明细（ZxHis库）
 * <AUTHOR>
public interface ExamineRequestDetailZxHisMapper extends BaseMapper<ExamineRequestDetailZxHis> {

    /**
     * 根据处方明细流水号，RisId获取检查申请明细
     * @param recipeId
     * @param recipeDetailId
     * @param risId
     * @return
     */
    default ExamineRequestDetailZxHis getExamineRequestDetailByRecipeDetailId(Long recipeId,Long recipeDetailId,Integer risId) {
        ExamineRequestDetailZxHis entity = new ExamineRequestDetailZxHis();
        entity.setRecipeId(recipeId);
        entity.setRecipeDetailId(recipeDetailId);
        entity.setRisId(risId);
        List<ExamineRequestDetailZxHis> list = select(entity);
        if (list.stream().count() == 0) {
            return null;
        }
        return list.get(0);
    }
}
