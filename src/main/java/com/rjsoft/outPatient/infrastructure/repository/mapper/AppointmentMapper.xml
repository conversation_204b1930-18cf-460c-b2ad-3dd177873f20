<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.AppointmentMapper">

    <select id="getAppointmentCount" resultType="java.lang.Integer">
        select count(1)
        from TB_Appointment (nolock)
        where CertificateNo = #{certificateNo}
          and SubjectID = #{subjectId}
          and HospitalCode = #{hospitalCode}
          and AppointmentStatus not in (2, 3)
          and datediff(day,CheckDate, getdate()) = 0;
    </select>


    <select id="isReviewCount" resultType="java.lang.Integer">
        select count(1)
        from TB_Appointment (nolock)
        where AppointmentType = #{isReview}
          and SubjectID = #{subjectId}
          AND DeptId = #{deptId}
          and DoctorId = #{doctorId}
          and TimsMs = #{timeMs}
          and DATEDIFF(DAY,CheckDate, getdate()) = 0
          and IsUse = 1
          and IsDelete = 0
          and AppointmentStatus not in (2, 3)
          and HospitalCode = #{hospitalCode}
    </select>

    <select id="getTodayAppointment" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.Appointment">
        select
            a.CardNo cardNo,
            a.HisCardNo hisCardNo,
            a.PatSfz patSfz,
            a.PatName patName,
            a.CheckDate checkDate,
            a.VisitDate visitDate,
            a.DeptId deptId,
            a.AppointmentStatus appointmentStatus
        from TB_Appointment a(nolock)
        where a.DoctorId = #{doctorId}
          and a.DeptId = #{deptId}
          and DATEDIFF(DAY,a.CheckDate, getdate()) = 0
          and a.IsDelete = 0
          and a.AppointmentStatus = 0
          and a.HospitalCode = #{hospitalCode}
    </select>

    <select id="getTodayTotalNum" resultType="java.lang.Integer">
        select isnull(SUM(b.quatity),0) + isnull(SUM(b.maxNum),0)
        from RJ_Configer.dbo.TB_Cinfiger_Scheduling a (nolock)
                 inner join RJ_Configer.dbo.TB_Cinfiger_SchedulingTimespanDetail b (nolock) on a.id = b.mainid and a.HOSPITALCODE = b.HOSPITALCODE
                 left join RJ_Configer.dbo.TB_Cinfiger_SubjectItem c (nolock) on a.Subjectid = c.SubjectID and a.HOSPITALCODE = c.HOSPITALCODE
                 left join RJ_Configer.dbo.TB_Dic_HisDictionaryExt e (nolock) on c.TimeType = e.[HisDictionaryID] and a.HOSPITALCODE = e.HOSPITALCODE and e.lang = 1 and e.isdelete = 0 and e.isuse = 1
        where a.HOSPITALCODE = #{hospitalCode}
          and c.DeptCode = #{deptId}
          and c.DctCode = #{doctorId}
          and DATEDIFF(DAY,a.DutyDate, getdate()) = 0
          and a.status = 0
          and a.IsDelete = 0
          and a.IsUse = 1
          and ISNULL(a.StopNumber,0) = 0
          and (e.HisDictionaryName = '上午'or e.HisDictionaryName = '下午')
    </select>

    <select id="getTodayNum" resultType="java.lang.Integer">
        select count(*) from TB_Appointment (nolock)
        where HOSPITALCODE = #{hospitalCode}
        and DeptId = #{deptId}
        and DoctorId = #{doctorId}
        and DATEDIFF(DAY,CheckDate, getdate()) = 0
        and AppointmentStatus in(5,9)
        <if test="type == 0">
            and Isnull(VipAppointmentNum,0)=0
        </if>
        <if test="type == 1">
            and Isnull(VipAppointmentNum,0)!=0
        </if>
    </select>

</mapper>