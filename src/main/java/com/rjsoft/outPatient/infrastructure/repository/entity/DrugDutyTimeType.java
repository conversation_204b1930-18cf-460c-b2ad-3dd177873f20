package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 药品值班时间类型
 * <AUTHOR>
@Data
@Table(name = "MZYS_TV_YPZBSJ")
public class DrugDutyTimeType  implements Serializable {

    @Column(name = "sjlx")
    private Integer timeType;

    @Column(name = "kssj")
    private String beginTime;

    @Column(name = "jssj")
    private String endTime;
}
