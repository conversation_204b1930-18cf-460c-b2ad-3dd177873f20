package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.OldRecipeDetail;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/4 - 9:23
 */
public interface OldRecipeDetailMapper extends BaseMapper<OldRecipeDetail>, ExampleMapper<OldRecipeDetail> {

    /**
     * 根据就诊流水号获取处方药品处方明细
     *
     * @param regNo     挂号流水号
     * @param firstDate
     * @return {@link List<OldRecipeDetail>}
     */
    default List<OldRecipeDetail> getDrugRecipeByRegNo(String regNo, Date firstDate, String tableName) {
        List<Integer> feeCategory = ItemCategoryEnum.getDrugTypeCodeList();

        Weekend<OldRecipeDetail> weekend = new Weekend<>(OldRecipeDetail.class);
        if (!StringUtils.isEmpty(tableName)) {
            weekend.setTableName(tableName);
        }

        weekend.weekendCriteria()
                .andEqualTo(OldRecipeDetail::getRegNo, regNo)
                //.andGreaterThanOrEqualTo(OldRecipeDetail::getFirstDate, firstDate)
                // .andLessThan(OldRecipeDetail::getFirstDate, DateUtils.addDays(firstDate, 1))
                .andIn(OldRecipeDetail::getFeeCategory, feeCategory);

        WeekendCriteria<OldRecipeDetail, Object> keywordCriteria1 = weekend.weekendCriteria();
        keywordCriteria1.andNotEqualTo(OldRecipeDetail::getApplicationForm, "MECT").orIsNull(OldRecipeDetail::getApplicationForm);

        weekend.and(keywordCriteria1);
        return selectByExample(weekend);
    }

    /**
     * 根据ID查询处方明细
     *
     * @param recipeDetailNo
     * @param tableName
     * @return
     */
    default OldRecipeDetail getOldRecipeDetailById(String recipeDetailNo, String tableName) {
        Weekend<OldRecipeDetail> weekend = new Weekend<>(OldRecipeDetail.class);
        weekend.setTableName(tableName);
        weekend.weekendCriteria().andEqualTo(OldRecipeDetail::getRecipeDetailNo, recipeDetailNo);
        List<OldRecipeDetail> list = selectByExample(weekend);
        if (list.size() == 0) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 根据ID查询处方明细
     *
     * @param receptionNo
     * @param category
     * @param tableName
     * @return
     */
    default List<OldRecipeDetail> getOldRecipeDetailByReceptionNo(String receptionNo, List<Integer> category, String tableName) {
        Weekend<OldRecipeDetail> weekend = new Weekend<>(OldRecipeDetail.class);
        weekend.setTableName(tableName);

        weekend.weekendCriteria().andEqualTo(OldRecipeDetail::getReceptionNo, receptionNo)
                .andIn(OldRecipeDetail::getFeeCategory, category);
        return selectByExample(weekend);
    }

}
