package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ApplyItemDetail;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.List;

/**
 * 申请单项目明细
 * <AUTHOR>
public interface ApplyItemDetailMapper extends BaseMapper<ApplyItemDetail>, ExampleMapper<ApplyItemDetail> {

    /**
     * 根据项目ID，查询收费申请单被合并明细
     * @param itemIds
     * @param hospitalCode
     * @return
     */
    default List<ApplyItemDetail> getApplyItemDetailByItemIds(List<Integer> itemIds, Integer hospitalCode) {
        if (itemIds == null || itemIds.size() == 0) {
            return new ArrayList<>();
        }
        Weekend weekend = new Weekend(ApplyItemDetail.class);
        WeekendCriteria<ApplyItemDetail, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ApplyItemDetail::getItemId, itemIds);
        weekendCriteria.andEqualTo(ApplyItemDetail::getHopsitalId, hospitalCode);
        weekendCriteria.andEqualTo(ApplyItemDetail::getDelFlag,0);
        weekendCriteria.andEqualTo(ApplyItemDetail::getEnableFlag,1);
        return selectByExample(weekend);
    }
}
