package com.rjsoft.outPatient.infrastructure.repository.mapper;
import com.rjsoft.outPatient.infrastructure.repository.entity.OldPatientInfo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

public interface OldPatientInfoMapper extends BaseMapper<OldPatientInfo>, ExampleMapper<OldPatientInfo> {
   String getHzbhByPatId(@Param("patNo") Integer patNo);

}
