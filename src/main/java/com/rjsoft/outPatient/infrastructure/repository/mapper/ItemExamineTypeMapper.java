package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ItemExamineType;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * 项目检查类型
 * <AUTHOR>
public interface ItemExamineTypeMapper extends BaseMapper<ItemExamineType> {

    /**
     * 根据项目ID，获取检查类型
     * @param itemCode
     * @return
     */
    default Integer getExamineTypeById(Integer itemCode) {
        ItemExamineType entity = new ItemExamineType();
        entity.setItemCode(itemCode);
        List<ItemExamineType> list = select(entity);
        if (list.stream().count() == 0) {
            return null;
        }
        return list.get(0).getRequestType();
    }
}
