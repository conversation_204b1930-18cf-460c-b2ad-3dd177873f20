package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationContentItems;
import com.rjsoft.outPatient.infrastructure.repository.entity.InfuseGroupDetail;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;
import java.util.Set;

public interface InfuseGroupDetailMapper extends Mapper<InfuseGroupDetail> {

    /**
     * 添加输液分组明细
     * @param params
     */
    default void insertInfuseGroup(List<InfuseGroupDetail> params){
        for (InfuseGroupDetail param : params) {
            insertSelective(param);
        }
    }

    /**
     * 根据处方明细id获取输液分组信息
     * @param recipeDetailNos
     */
    default List<InfuseGroupDetail> getInfuseGroup(Set<Long> recipeDetailNos, Integer hospitalCode){
        Weekend<InfuseGroupDetail> weekend = new Weekend<>(InfuseGroupDetail.class);
        WeekendCriteria<InfuseGroupDetail, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(InfuseGroupDetail::getCfmxid,recipeDetailNos)
        .andEqualTo(InfuseGroupDetail::getHospitalCode,hospitalCode);
        return selectByExample(weekend);
    }

}
