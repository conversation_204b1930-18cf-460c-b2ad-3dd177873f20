package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.doctorElemMain.dto.ElemParams;
import com.rjsoft.outPatient.infrastructure.repository.entity.ElemTemp;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface ElemTempMapper extends BaseMapper<ElemTemp>, ExampleMapper<ElemTemp> {


    default List<ElemTemp> queryElemTemp(ElemParams elemParams) {
        Weekend<ElemTemp> weekend = new Weekend<>(ElemTemp.class);
        WeekendCriteria<ElemTemp, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ElemTemp::getElemCode, elemParams.getElemCode());
        weekendCriteria.andEqualTo(ElemTemp::getCreateUser, elemParams.getCreateUser());
        weekendCriteria.andEqualTo(ElemTemp::getHospitalCode, elemParams.getHospitalCode());
        weekendCriteria.andEqualTo(ElemTemp::getStatus, 1);
        weekend.setOrderByClause("create_date asc");
        return selectByExample(weekend);
    }
}
