package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 心理治疗申请明细表
 */
@Data
@Table(name = "MZYS_TB_PsychotherapyApplyDetail")
public class PsychotherapyApplyDetail implements Serializable {

    @Id
    @Column(name = "ApplyDetailId", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    @Column(name = "ApplyId")
    private Integer applyId;

    @Column(name = "ItemId")
    private Integer itemId;

    @Column(name = "ItemName")
    private String itemName;

    @Column(name = "Quantity")
    private Integer quantity;

    @Column(name = "RetreatNum")
    private Integer retreatNum;

    @Column(name = "Cfmxid")
    private Long recipeDetailId;

    @Column(name = "SfFlag")
    private Integer sfFlag;

    @Column(name = "HospitalId")
    private Integer hospitalId;

    @Column(name = "ChargeStatus")
    private Integer chargeStatus;

    @Column(name = "ExecStatus")
    private Integer execStatus;

    @Column(name = "ApplyTime")
    private Date applyTime;

    @Column(name = "ApplyDoctor")
    private Integer applyDoctor;

    @Column(name = "isDelete")
    private Integer isDelete;
}