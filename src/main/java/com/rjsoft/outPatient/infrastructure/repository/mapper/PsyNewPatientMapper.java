package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.PsyNewPatient;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

public interface PsyNewPatientMapper extends BaseMapper<PsyNewPatient>, ExampleMapper<PsyNewPatient> {

    /**
     * 根据身份证号和大类名称判断是否已经上报过
     *
     * @param idNo
     * @param sbname
     * @return
     */
    int getInfoByIdNoSbname(@Param("idNo") String idNo, @Param("sbname") String sbname);
}
