package com.rjsoft.outPatient.infrastructure.repository.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
* MDM_Pub_FeeCategoryExeDept 收费类型执行科室维护表
* @TableName MDM_Pub_FeeCategoryExeDept
*/
@Data
@Table( name ="MDM_Pub_FeeCategoryExeDept" )
public class MdmPubFeecategoryexedept implements Serializable {

    /**
    * id
    */
    @Id
    @Column(name = "id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;
    /**
    * 费用类型编码
    */
    @Column(name = "feeCategoryCode")
    private Integer feeCategoryCode;
    /**
    * 费用类型名称
    */
    @Column(name = "feeCategoryName")
    private String feeCategoryName;
    /**
    * 执行科室
    */
    @Column(name = "execdept")
    private Integer execDept;
    /**
    * 执行科室名称
    */
    @Column(name = "execdeptname")
    private String execDeptName;
    /**
    * 执行院区
    */
    @Column(name = "exechospitalid")
    private Integer execHospitalId;
    /**
    * 开始时间
    */
    @Column(name = "starttime")
    private String startTime;
    /**
    * 结束时间
    */
    @Column(name = "endtime")
    private String endTime;
    /**
    * 医院编码
    */
    @Column(name = "hospitalId")
    private Integer hospitalId;
    /**
    * 开立科室
    */
    @Column(name = "usedept")
    private Integer useDept;
    /**
    * 开立科室名称
    */
    @Column(name = "usedeptname")
    private String useDeptName;
    /**
    * 
    */
    @Column(name = "updatetime")
    private Date updateTime;
    /**
    * 
    */
    @Column(name = "updateuserid")
    private Integer updateUserId;
    /**
    * 
    */
    @Column(name = "createtime")
    private Date createTime;
    /**
    * 
    */
    @Column(name = "createuserid")
    private Integer createUserId;
    /**
    * 1：门诊 2：住院
    */
    @Column(name = "type")
    private Integer type;

    @Transient
    private String execHospitalName;

    @Transient
    private String execDeptHospitalName;

    @Transient
    private String execDeptId;
}
