<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ReserveMapper">

    <!--  排班信息  -->
    <resultMap id="DoctorSchedulingInfoBase" type="com.rjsoft.outPatient.domain.reserve.dto.DoctorSchedulingInfoDto">
        <id column="id" property="id"/>
        <result column="Grade" property="grade"/>
        <result column="registeredType" property="registeredType"/>
        <result column="Dept_Code" property="deptId"/>
        <result column="Dept_Name" property="deptName"/>
        <result column="Doctor_Code" property="doctorCode"/>
        <result column="Doctor_Name" property="doctorName"/>
        <result column="Hb_Date" property="scheduleDate"/>
        <result column="Hb_Time" property="timeSpan"/>
        <result column="Am_Pm" property="amPm"/>
        <result column="Sum_Fee" property="sumFee"/>
        <result column="Appoint_Count" property="appointCount"/>
        <result column="Appoint_Count_Re" property="appointCountRe"/>
        <result column="Reg_Count" property="regCount"/>
        <result column="Sec_Dept_Code" property="secDeptCode"/>
        <result column="Dept_Type" property="deptType"/>
        <result column="Zbmz" property="specialDiseaseOutpatient"/>
        <result column="Sche_Type" property="scheType"/>
        <result column="TotalCzNum" property="totalCzNum"/>
        <result column="TotalCzLyNum" property="totalCzLyNum"/>
        <result column="TotalFzNum" property="totalFzNum"/>
        <result column="TotalFzLyNum" property="totalFzLyNum"/>
        <result column="AppointCzNum" property="appointCzNum"/>
        <result column="AppointFzNum" property="appointFzNum"/>
    </resultMap>


    <!--  查询医生排班信息  -->
    <select id="queryDoctorSchedulingInfoDto" resultMap="DoctorSchedulingInfoBase">
        EXEC UspNewMZYSDoctorScheduling
        #{startDate},
        #{endDate},
        #{doctorId},
        #{deptId},
        #{scheType},
        #{hospitalCode}
    </select>

    <!--  时段信息  -->
    <resultMap id="TimeSpanBase" type="com.rjsoft.outPatient.domain.reserve.dto.QueryTimeSpanDto">
        <id column="TimeSpanId" property="timeSpanId"/>
        <result column="StartTime" property="startTime"/>
        <result column="EndTime" property="endTime"/>
        <result column="CzSys" property="czSys"/>
        <result column="FzSys" property="fzSys"/>
    </resultMap>

    <!--  查询时段信息  -->
    <select id="queryTimeSpan" resultMap="TimeSpanBase">
        EXEC Usp_Hst_Get_Dic_TimeSpanExtNew
        #{timeMs},
        #{scheduleDate},
        #{deptId},
        #{doctorId}
    </select>

    <select id="getAdjustReserveView" resultType="com.rjsoft.outPatient.domain.reserve.dto.AdjustReserveTimeDTO">
        select
        l.ID AS scheduId,
        b.DEPTNAME AS deptName,
        s.Name AS doctorName,
        i.DctCode AS doctorCode,
        i.DeptCode AS deptCode,
        l.DutyDate AS dutyDate,
        t.StartTime + '-' + t.EndTime AS timeSpan,
        d.TimespanID AS timeSpanId,
        t.Name AS timeMs,
        l.HOSPITALCODE AS hospitalCode,
        totalCzLyNum = ISNULL((select sum(quatity)
        from TB_Cinfiger_SchedulingTimespanDetail
        where mainID = l.ID and TimeSpanID = d.TimespanID), 0),
        totalFzLyNum = ISNULL((select sum(maxNum)
        from TB_Cinfiger_SchedulingTimespanDetail
        where mainID = l.ID and TimeSpanID = d.TimespanID), 0),
        appointCzNum = ISNULL((select count(1)
        from TB_Appointment a inner join TB_Appointment_LockNum b on a.SQH = b.SQH
        where a.SubjectID = l.SubjectID and a.CheckDate = l.DutyDate
        and a.TimeSpanID = d.TimespanID and AppointmentStatus not in (2, 3)
        and (AppointmentType = 1 or b.IsCZF = 1)), 0),
        appointFzNum = ISNULL((select count(1)
        from dbo.TB_Appointment a inner join dbo.TB_Appointment_LockNum b on a.SQH = b.SQH
        where a.SubjectID = l.SubjectID and a.CheckDate = l.DutyDate
        and a.TimeSpanID = d.TimespanID and AppointmentStatus not in (2, 3)
        and (AppointmentType = 2 and b.IsCZF != 1)), 0),
        sumFee = ISNULL((select CONVERT(decimal (12, 2), sum(TotalFee))
        from TB_Cinfiger_SubjectFeeItem g
        where g.SubjectID = i.SubjectID),
        i.ghfee)
        from TB_Cinfiger_Scheduling l (nolock)
        inner join TB_Cinfiger_SubjectItem i (nolock) on l.Subjectid = i.SubjectID and l.HOSPITALCODE = i.HOSPITALCODE
        inner join TB_Cinfiger_SchedulingTimespanDetail d (nolock) on l.ID = d.mainID and d.IsUse = 1
        inner join TB_Dic_TimeSpanExt t (nolock) on t.ID = d.TimespanID and t.HOSPITALCODE = l.HOSPITALCODE
        left join TB_Dic_DeptExt b (nolock) on i.DeptCode = b.DEPTCODE and i.HOSPITALCODE = b.HOSPITALCODE
        left join TB_Dic_Staff s (nolock) on i.DctCode = s.Code and i.HOSPITALCODE = s.HOSPITALCODE
        where l.status = 0 and l.IsDelete = 0 and l.IsUse = 1
        and i.IsDelete = 0 and i.IsUse = 1
        and d.IsDelete = 0 and d.IsUse = 1 and d.quatity + d.maxNum > 0
        and t.IsDelete = 0 and t.IsUse = 1
        and b.ISDELETED = 0
        and s.IsDelete = 0 and s.IsUse = 1
        and l.HOSPITALCODE = #{hospitalCode}
        and CONVERT(varchar (10), l.DutyDate, 23) = CONVERT(varchar (10), #{dutyDate}, 23)
        <if test="timeMs != null and timeMs != ''">and t.Name = #{timeMs}</if>
        and i.DctCode = #{doctorCode} and i.DeptCode = #{deptCode}
        order by (t.StartTime + '-' + t.EndTime)
    </select>

    <select id="getReserveDctCode" resultType="java.lang.Integer">
        select DISTINCT DctCode
        from TB_Cinfiger_SubjectItem
        where IsDelete = 0
        and HOSPITALCODE = #{hospId}
        <if test="deptCode != 0">
            and DeptCode = #{deptCode}
        </if>
    </select>

    <select id="queryShiftDoctorList" resultType="com.rjsoft.outPatient.domain.reserve.dto.OnDutyDoctorDTO">
        select i.DeptCode, i.DctCode, l.Name, l.InputCode1, l.InputCode2, i.HOSPITALCODE
        from TB_Cinfiger_SubjectItem i
        left join TB_Dic_Staff l on i.DctCode = l.Code and i.HOSPITALCODE = l.HOSPITALCODE
        where
        <if test="deptCode != null and deptCode != ''">
            i.DeptCode = #{deptCode} and
        </if>
        i.HOSPITALCODE = #{hospitalCode}
        and i.IsDelete = 0
        and i.IsUse = 1
        <if test="inputCode != null and inputCode != ''">
            and (l.Name like '%' + #{inputCode} + '%'
            or l.InputCode1 like '%' + #{inputCode} + '%'
            or l.InputCode2 like '%' + #{inputCode} + '%')
        </if>
        ;
    </select>

    <select id="queryShiftDeptList" resultType="com.rjsoft.outPatient.domain.reserve.dto.OnDutyDeptDTO">
        select i.DeptCode deptId, i.DctCode, l.DEPTNAME, l.INPUTCODE1, l.INPUTCODE2, i.HOSPITALCODE
        from TB_Cinfiger_SubjectItem i
        inner join TB_Dic_DeptExt l on i.DeptCode = l.DEPTCODE and i.HOSPITALCODE = l.HOSPITALCODE
        inner join TB_Cinfiger_Scheduling s on i.SubjectID=s.Subjectid and i.HOSPITALCODE = s.HOSPITALCODE and
        i.IsDelete=s.IsDelete and i.IsUse=i.IsUse
        where
        <if test="doctorCode != null and doctorCode != ''">
            i.DctCode = #{doctorCode} and
        </if>
        i.HOSPITALCODE = #{hospitalCode}
        and i.IsDelete = 0
        and i.IsUse = 1
        and s.DutyDate between #{startTime} and #{endTime}
        <if test="inputCode != null and inputCode != ''">
            and (l.DEPTNAME like '%' + #{inputCode} + '%'
            or l.INPUTCODE1 like '%' + #{inputCode} + '%'
            or l.INPUTCODE2 like '%' + #{inputCode} + '%')
        </if>
        ;
    </select>
    <select id="findAppointmentInfo" resultType="com.rjsoft.outPatient.domain.reserve.dto.AppointmentInfo">
        select a.AppointmentID appointmentId,
        a.AppointmentStatus statusCode,
        b.Name status,
        a.VisitDate visitDate,
        a.CardNo cardNo,
        a.PatName patName,
        case a.Sex when '1' then '男' when '2' then '女' else '其他' end sex,
        a.MovePhone mobilePhone,
        case a.Cfz when 1 then '初诊' when 2 then '复诊' else '其他' end isReview,
        rtrim(e.DEPTNAME) dept,
        e.DEPTCODE deptCode,
        c.SubjectName subject,
        rtrim(f.Name) doctor,
        a.TotAmt money,
        d.Name source,
        a.AppointmentOrderID appointmentOrderID
        from TB_Appointment a
        left join Tb_AppointmentStatus b on a.AppointmentStatus = b.Code
        left join TB_Cinfiger_SubjectItem c on a.SubjectID = c.SubjectID and a.HospitalCode = c.HOSPITALCODE
        left join TB_Dic_SourceType d on a.Bmbh = d.EnName and a.HospitalCode = d.HOSPITALCODE
        left join TB_Dic_DeptExt e on a.DeptId = e.DEPTID AND a.HospitalCode = e.HOSPITALCODE
        left join TB_Dic_Staff f on a.DoctorId = f.ID AND a.HospitalCode = f.HOSPITALCODE
        where a.DoctorId = #{doctorId}
        <if test="deptId != 0">
            and a.DeptId = #{deptId}
        </if>
        and a.HospitalCode = #{hospitalId}
        and a.RemarkA1 = 'GTYY'
        and a.IsDelete = 0
        and a.CheckDate between #{startTime} and #{endTime}
        and c.IsDelete = 0
        and c.IsUse = 1
        and d.IsDelete = 0
        and d.IsUse = 1
        and e.ISDELETED = 0
        and f.IsDelete = 0
        and f.IsUse = 1;
    </select>

    <select id="medicalAppointment" resultType="java.util.LinkedHashMap">
        exec sp_Reg_MedAppointementSharing
        @startTime =
        #{time}
    </select>

    <select id="getReserve" statementType="CALLABLE" resultType="java.lang.String">
        declare @m_Ghxh integer
        declare @m_GhYyH varchar(500)
        set @m_Ghxh=#{regSerialNo}
        set @m_GhYyH= #{appointmentOrderId}
        exec[dbo].[uspMzghxh_YY_Arrange]@m_Ghxh, @m_GhYyH
        output
        select @m_GhYyH
    </select>

    <select id="getVipInfo" resultType="com.rjsoft.outPatient.domain.reception.dto.VipInfoDto">
        SELECT a.vipnum vipNum, b.SubjectID subjectID
        FROM
            TB_Cinfiger_Scheduling a
                INNER JOIN TB_Cinfiger_SubjectItem b ON a.Subjectid = b.SubjectID
                AND a.HOSPITALCODE = b.HOSPITALCODE
        WHERE
            a.IsDelete = 0
          AND a.IsUse = 1
          AND DATEDIFF(DAY, a.DutyDate, GETDATE())=0
          AND b.DctCode = #{doctID}
          AND b.DeptCode = #{deptId}
    </select>

    <select id="getIncreaseTypeById" resultType="java.lang.Integer">
        SELECT isnull(IncreaseType,0) FROM TB_Cinfiger_SubjectItem WHERE SubjectID = #{id}
    </select>

    <select id="getChedulingInfo" resultType="com.rjsoft.outPatient.domain.RegAppointmentLog.dto.ChedulingInfo">
        SELECT a.NormalMaxNum,Isnull(a.VipNum,0) vipNum,a.Subjectid,b.SubjectName,d.Name,d.StartTime,d.EndTime,b.DctCode
        FROM
            TB_Cinfiger_Scheduling a
                INNER JOIN TB_Cinfiger_SubjectItem b ON a.Subjectid = b.SubjectID
                AND a.HOSPITALCODE = b.HOSPITALCODE
                inner join TB_Cinfiger_SchedulingTimespanDetail c on a.ID=c.mainID and a.HOSPITALCODE = c.HOSPITALCODE
                inner join TB_Dic_TimeSpanExt d on c.TimespanID=d.ID and c.HOSPITALCODE = d.HOSPITALCODE
        WHERE
            a.IsDelete = 0
          AND a.IsUse = 1
          AND DATEDIFF( DAY, a.DutyDate, GETDATE( ) ) = 0
          AND b.DctCode = #{doctorId}
          AND b.DeptCode = #{deptId}
    </select>
</mapper>
