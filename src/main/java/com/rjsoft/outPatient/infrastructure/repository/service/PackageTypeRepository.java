package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.PackageType;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/30 - 16:03
 */
public interface PackageTypeRepository {

    /**
     * 根据套餐类型编号查询
     *
     * @param packageTypeNo 套餐类型编号
     * @param hospitalCode  医院编码
     * @return List<PackageType>
     */
    List<PackageType> getPackageTypesByPackageTypeNo(Integer packageTypeNo, Integer hospitalCode);

}
