package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.applicationForm.dto.ApplicationProjectDto;
import com.rjsoft.outPatient.domain.applicationForm.dto.ApplicationProjectListDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.ApplicationFormRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.HashMap;
import java.util.List;

/**
 * 电子申请单
 */
@AllArgsConstructor
@Service
public class ApplicationFormRepositoryImpl implements ApplicationFormRepository {

    SysFunctionMapper sysFunctionMapper;
    ApplicationTemplateMapper applicationTemplateMapper;
    ApplicationControlMapper applicationControlMapper;
    ApplicationControlItemMapper applicationControlItemMapper;
    ChargeItemMapper chargeItemMapper;
    ApplicationGroupMapper applicationGroupMapper;
    ApplicationLocationMapper applicationLocationMapper;
    ApplicationContentMapper applicationContentMapper;
    ApplicationContentLocaMapper applicationContentLocaMapper;
    ApplicationContentItemsMapper applicationContentItemsMapper;
    ReportDetailMapper reportDetailMapper;
    ExamineRequestMapper examineRequestMapper;


    @Override
    @DatabaseAnnotation
    public List<ApplicationTemplate> getApplicationTemplate(Integer templateType, String templateNo, Integer hospitalCode) {
        return applicationTemplateMapper.getApplicationTemplate(templateType, templateNo, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public List<ApplicationTemplate> getApplicationTemplateByTemplateNos(List<String> templateNo, Integer hospitalCode) {
        return applicationTemplateMapper.getApplicationTemplateByTemplateNos(templateNo, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public ApplicationTemplate getApplicationTemplateByTemplateNo(String templateNo, Integer hospitalCode) {
        final ApplicationTemplate record = new ApplicationTemplate();
        record.setTemplateNo(templateNo);
        record.setHospitalCode(hospitalCode);
        return applicationTemplateMapper.selectOne(record);
    }

    @Override
    @DatabaseAnnotation
    public boolean saveApplicationTemplate(ApplicationTemplate applicationTemplate) {
        boolean success;
        boolean hasData = applicationTemplateMapper.existsWithPrimaryKey(applicationTemplate);
        if (hasData) {
            applicationTemplate.setUptDoctor(applicationTemplate.getDoctorId());
            applicationTemplate.setUptTime(sysFunctionMapper.getDate());
            success = applicationTemplateMapper.updateByPrimaryKeySelective(applicationTemplate) > 0;
        } else {
            applicationTemplate.setTemplateNo(Converter.toString(sysFunctionMapper.getGetSequences(SequenceEnum.APPLICATION_TEMPLATENO)));
            applicationTemplate.setCreDoctor(applicationTemplate.getDoctorId());
            applicationTemplate.setCreTime(sysFunctionMapper.getDate());
            applicationTemplate.setStatus(1);
            success = applicationTemplateMapper.insert(applicationTemplate) > 0;
        }
        return success;
    }

    @Override
    @DatabaseAnnotation
    public boolean delApplicationTemplate(Integer id, Integer doctorId, Integer hospitalCode) {
        ApplicationTemplate applicationTemplate = new ApplicationTemplate();
        applicationTemplate.setId(id);
        applicationTemplate.setStatus(0);
        applicationTemplate.setHospitalCode(hospitalCode);
        applicationTemplateMapper.updateByPrimaryKeySelective(applicationTemplate);
        return true;
    }

    @Override
    @DatabaseAnnotation
    public List<ApplicationControl> getApplicationControl(String templateNo, Integer hospitalCode) {
        return applicationControlMapper.getApplicationControl(templateNo, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public List<ApplicationControl> getApplicationControlByIds(List<Integer> ids, Integer hospitalCode) {
        return applicationControlMapper.getApplicationControlByIds(ids, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public boolean saveApplicationControl(ApplicationControl applicationControl) {
        boolean success;
        boolean hasData = applicationControlMapper.existsWithPrimaryKey(applicationControl);
        if (hasData) {
            success = applicationControlMapper.updateByPrimaryKeySelective(applicationControl) > 0;
        } else {
            success = applicationControlMapper.insert(applicationControl) > 0;
        }
        return success;
    }

    @Override
    @DatabaseAnnotation
    public boolean delApplicationControl(Integer id, Integer doctorId, Integer hospitalCode) {
        ApplicationControl applicationControl = new ApplicationControl();
        applicationControl.setId(id);
        applicationControl.setHospitalCode(hospitalCode);
        applicationControlMapper.delete(applicationControl);
        return true;
    }


    @Override
    @DatabaseAnnotation
    public List<ApplicationControlItems> getApplicationControlItem(String templateNo, Integer hospitalCode) {
        return applicationControlItemMapper.getApplicationControlItem(templateNo, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public boolean saveApplicationControlItem(ApplicationControlItems applicationControlItems) {
        boolean success;
        success = applicationControlItemMapper.insert(applicationControlItems) > 0;
        return success;
    }

    @Override
    @DatabaseAnnotation
    public boolean delApplicationControlItem(Integer applicationConNo, Integer hospitalCode) {
        ApplicationControlItems applicationControlItems = new ApplicationControlItems();
        applicationControlItems.setApplicationConNo(applicationConNo);
        applicationControlItems.setHospitalCode(hospitalCode);
        applicationControlItemMapper.delete(applicationControlItems);
        return true;
    }


    @Override
    @DatabaseAnnotation
    public List<ApplicationControlItems> getApplicationControlItemByConNo(List<Integer> applicationConNos, Integer hospitalCode) {
        Weekend<ApplicationControlItems> weekend = new Weekend<>(ApplicationControlItems.class);
        WeekendCriteria<ApplicationControlItems, Object> weekendCriteria = weekend.weekendCriteria();
        if (applicationConNos != null && applicationConNos.size() > 0) {
            weekendCriteria.andIn(ApplicationControlItems::getApplicationConNo, applicationConNos);
        }
        weekendCriteria.andEqualTo(ApplicationControlItems::getHospitalCode, hospitalCode);
        return applicationControlItemMapper.selectByExample(weekend);
    }


    @Override
    @DatabaseAnnotation
    public List<ApplicationGroup> getApplicationGroup(String templateNo, Integer fromGroupNo, Integer hospitalCode) {
        ApplicationGroup applicationGroup = new ApplicationGroup();
        applicationGroup.setTemplateNo(templateNo);

        if (fromGroupNo != null && fromGroupNo != 0) {
            applicationGroup.setFromGroupNo(fromGroupNo);
        }
        applicationGroup.setHospitalCode(hospitalCode);
        return applicationGroupMapper.select(applicationGroup);
    }

    @Override
    @DatabaseAnnotation
    public List<ApplicationLocation> getApplicationLocation(List<Integer> GroupNos, Integer hospitalCode) {
        Weekend weekend = new Weekend(ApplicationLocation.class);
        WeekendCriteria<ApplicationLocation, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ApplicationLocation::getFromGroupNo, GroupNos);
        weekendCriteria.andEqualTo(ApplicationLocation::getHospitalCode, hospitalCode);
        return applicationLocationMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<ApplicationLocation> getApplicationLocationById(List<Integer> ids, Integer hospitalCode) {
        Weekend<ApplicationLocation> weekend = new Weekend<>(ApplicationLocation.class);
        WeekendCriteria<ApplicationLocation, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ApplicationLocation::getLocationId, ids);
        weekendCriteria.andEqualTo(ApplicationLocation::getHospitalCode, hospitalCode);
        return applicationLocationMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<ApplicationContent> getApplicationContent(Integer id, Integer hospitalCode) {
        ApplicationContent applicationContent = new ApplicationContent();
        applicationContent.setId(id);
        applicationContent.setHospitalCode(hospitalCode);
        applicationContent.setStatus(1);
        return applicationContentMapper.select(applicationContent);
    }

    @Override
    @DatabaseAnnotation
    public ApplicationContent getApplicationContentByFormId(Integer formId, Integer hospitalCode) {
        ApplicationContent applicationContent = new ApplicationContent();
        applicationContent.setFormId(formId);
        applicationContent.setHospitalCode(hospitalCode);
        applicationContent.setStatus(1);
        return applicationContentMapper.selectOne(applicationContent);
    }

    @Override
    @DatabaseAnnotation
    public boolean saveApplicationContent(ApplicationContent applicationContent) {
        boolean success;
        boolean hasData = applicationContentMapper.existsWithPrimaryKey(applicationContent);
        if (hasData) {
            applicationContent.setUptDoctor(applicationContent.getDoctorId());
            applicationContent.setUptTime(sysFunctionMapper.getDate());
            success = applicationContentMapper.updateByPrimaryKeySelective(applicationContent) > 0;
        } else {
            applicationContent.setStatus(1);
            applicationContent.setCreDoctor(applicationContent.getDoctorId());
            applicationContent.setCreTime(sysFunctionMapper.getDate());
            success = applicationContentMapper.insert(applicationContent) > 0;
        }
        return success;
    }

    @Override
    @DatabaseAnnotation
    public List<ApplicationContentLoca> getApplicationContentLocation(List<Integer> ids, Integer hospitalCode) {
        Weekend weekend = new Weekend(ApplicationContentLoca.class);
        WeekendCriteria<ApplicationContentLoca, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ApplicationContentLoca::getContentId, ids);
        weekendCriteria.andEqualTo(ApplicationContentLoca::getHospitalCode, hospitalCode);
        return applicationContentLocaMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public boolean deleteApplicationContentLocation(Integer contentId) {
        ApplicationContentLoca applicationContentLoca = new ApplicationContentLoca();
        applicationContentLoca.setContentId(contentId);
        applicationContentLocaMapper.delete(applicationContentLoca);
        return true;
    }

    @Override
    @DatabaseAnnotation
    public boolean saveApplicationContentLocation(ApplicationContentLoca applicationContentLoca) {
        boolean success;
        success = applicationContentLocaMapper.insert(applicationContentLoca) > 0;
        return success;

    }

    /**
     * 根据项目ID获取申请单
     *
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<ApplicationTemplate> getApplicationTemplateByItemCode(Integer itemCode, Integer hospitalCode) {
        return applicationTemplateMapper.getApplicationTemplateByItemCode(itemCode, hospitalCode);
    }


    @Override
    @DatabaseAnnotation
    public List<ApplicationControl> getApplicationControlByLoca(String templateNo, String fromGroupNo, String selectedPlace, Integer hospitalCode) {
        return applicationControlMapper.getApplicationControlByLoca(templateNo, fromGroupNo, selectedPlace, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public List<ApplicationControl> getApplicationControlByGroupNo(String fromGroup, Integer hospitalCode) {
        return applicationControlMapper.getApplicationControlByGroupNo(fromGroup, hospitalCode);
    }


    @Override
    @DatabaseAnnotation
    public boolean saveApplicationContentItems(Integer contentId, Integer itemCode) {
        boolean success;
        ApplicationContentItems applicationContentItems = new ApplicationContentItems();
        applicationContentItems.setContentId(contentId);
        applicationContentItems.setItemCode(itemCode);
        success = applicationContentItemsMapper.insert(applicationContentItems) > 0;
        return success;
    }

    @Override
    @DatabaseAnnotation
    public boolean deleteApplicationContentItems(Integer contentId) {
        ApplicationContentItems applicationContentItems = new ApplicationContentItems();
        applicationContentItems.setContentId(contentId);
        applicationContentItemsMapper.delete(applicationContentItems);
        return true;
    }


    @Override
    @DatabaseAnnotation
    public List<ApplicationContentItems> GetApplicationContentItems(Integer contentId, Integer hospitalCode) {
        return applicationContentItemsMapper.GetApplicationContentItems(contentId, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public List<ApplicationControlItems> getApplicationControlItemsBySfItem(Integer itemCode, Integer hospitalCode) {
        ApplicationControlItems entity = new ApplicationControlItems();
        entity.setItemCode(itemCode);
        entity.setHospitalCode(hospitalCode);
        return applicationControlItemMapper.select(entity);
    }

    @Override
    @DatabaseAnnotation(name = "MSIRS")
    public List<ReportDetail> getReportDetail(Integer formId, Integer hospitalCode) {
        return reportDetailMapper.getReportDetail(formId, hospitalCode);
    }


    @Override
    @DatabaseAnnotation
    public MzysTbXTPZ getXtpz(String gjz) {
        return sysFunctionMapper.getXtpz(gjz);
    }


    @Override
    @DatabaseAnnotation
    public List<ApplicationProjectDto> getApplicationProjectList(Integer receptionNo, Integer doctorId, Integer applyType, Integer hospitalCode) {
        return examineRequestMapper.getApplicationProjectList(receptionNo, doctorId, applyType, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public List<ApplicationProjectListDto> getApplicationProjectDetail(Integer examineNo) {
        return examineRequestMapper.getApplicationProjectDetail(examineNo);
    }

    @Override
    @DatabaseAnnotation
    public List<HashMap> getApplicationProjectDetailS(Integer examineNo) {
        return examineRequestMapper.getApplicationProjectDetailS(examineNo);
    }

    @Override
    @DatabaseAnnotation
    public ApplicationContent getApplicationContentById(Integer id, Integer hospitalCode) {
        ApplicationContent applicationContent = new ApplicationContent();
        applicationContent.setId(id);
        applicationContent.setHospitalCode(hospitalCode);
        return applicationContentMapper.selectOne(applicationContent);
    }

    @Override
    @DatabaseAnnotation
    public List<ApplicationProjectListDto> getApplicationItems(Integer formId, Integer hospitalCode) {
        return examineRequestMapper.getApplicationItems(formId, hospitalCode);
    }


}
