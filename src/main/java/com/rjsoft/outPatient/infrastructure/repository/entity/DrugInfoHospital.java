package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "Drug_Tb_DrugInfoHospital")
public class DrugInfoHospital {
    @Id
    @Column(name = "ID")
    private Integer id;

    /**
     * 药品ID
     */
    @Column(name = "DrugId")
    private Integer drugId;

    /**
     * 全局ID
     */
    @Column(name = "GlobalId")
    private Integer globalId;

    /**
     * 医院Id
     */
    @Column(name = "HospitalId")
    private Integer hospitalId;

    /**
     * 是否停用
     */
    @Column(name = "IsStop")
    private Integer isStop;

    /**
     * 使用范围
     */
    @Column(name = "UseDeptRange")
    private Integer useDeptRange;
}
