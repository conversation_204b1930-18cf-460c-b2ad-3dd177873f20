package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.rjsoft.outPatient.domain.dictionary.dto.CommonDicEntity;
import com.rjsoft.outPatient.domain.dictionary.vo.CommonDicVo;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 申请单对照性别条件
 */
@Data
@Table(name = "MZYS_TB_JCSQDXB")
public class SelectedSexCondition extends CommonDicEntity implements Serializable {

    @Column(name = "xbbm")
    private Integer sexCondition;

    @Column(name = "xbmc")
    private String sexConditionName;

    @Override
    public CommonDicVo conversionFill() {
        super.setCode(this.sexCondition);
        super.setName(this.sexConditionName);
        return new CommonDicVo(this);
    }
}