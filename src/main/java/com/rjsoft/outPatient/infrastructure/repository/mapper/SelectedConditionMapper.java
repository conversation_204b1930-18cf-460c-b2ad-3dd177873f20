package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.SelectedCondition;
import com.rjsoft.outPatient.infrastructure.repository.entity.SelectedType;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

public interface SelectedConditionMapper extends BaseMapper<SelectedCondition> {

    /**
     * 查询申请单对照条件
     *
     * @return
     */
    default List<SelectedCondition> getSelectedCondition() {
        SelectedCondition selectedCondition = new SelectedCondition();
        return select(selectedCondition);
    }
}
