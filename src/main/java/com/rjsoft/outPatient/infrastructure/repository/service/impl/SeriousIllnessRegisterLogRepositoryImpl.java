package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.domain.seriousIllness.dto.SeriousIllnessRegisterLogDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.SeriousIllnessRegisterLog;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SeriousIllnessRegisterLogMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.SeriousIllnessRegisterLogRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 大病登记
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class SeriousIllnessRegisterLogRepositoryImpl implements SeriousIllnessRegisterLogRepository {

    SeriousIllnessRegisterLogMapper seriousIllnessRegisterLogMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public Integer insertLog(SeriousIllnessRegisterLog seriousIllnessRegisterLog) {
        return seriousIllnessRegisterLogMapper.insert(seriousIllnessRegisterLog);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public List<SeriousIllnessRegisterLog> getSeriousIllnessRegisterLog(List<Integer> serIdList ,List<Integer> stateList,Integer oldFlag) {
        List<SeriousIllnessRegisterLog> logList = seriousIllnessRegisterLogMapper.getSeriousIllnessRegisterLog(serIdList,stateList,oldFlag);
        return logList;
    }
}
