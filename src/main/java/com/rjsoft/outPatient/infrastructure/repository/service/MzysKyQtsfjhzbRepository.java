package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.research.dto.VisitPlanDto;
import com.rjsoft.outPatient.domain.research.dto.VisitPlanInputDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysKyQtsfjhmx;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysKyQtsfjhzb;

import java.util.List;

public interface MzysKyQtsfjhzbRepository {
    List<VisitPlanDto> visitPlan(VisitPlanInputDto visitPlanInputDto);

    List<MzysKyQtsfjhzb> getMzysKyQtsfjhzbList(Integer patientId,Integer projectId, Long fplanId);

    Integer insertMzysKyQtsfjhzb(MzysKyQtsfjhzb mzysKyQtsfjhzb,List<MzysKyQtsfjhmx> mzysKyQtsfjhmxList,List<Integer> delIds);

    MzysKyQtsfjhzb getById(Integer id);

    String submitExtraVisitPlan(Integer id);

    boolean insertMzysKyQtsfjhzb(MzysKyQtsfjhzb mzysKyQtsfjhzb);
}
