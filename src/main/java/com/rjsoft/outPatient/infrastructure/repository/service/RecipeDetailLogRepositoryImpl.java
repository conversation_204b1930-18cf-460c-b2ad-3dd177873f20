package com.rjsoft.outPatient.infrastructure.repository.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.ChargeStatusEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbCfscjl;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.mapper.MzysTbCfscjlMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeDetailMapper;
import com.ruijing.code.api.HeadInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class RecipeDetailLogRepositoryImpl implements RecipeDetailLogRepository {
    @Autowired
    private RecipeDetailMapper recipeDetailMapper;
    @Autowired
    private MzysTbCfscjlMapper mzysTbCfscjlMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public void saveDeleteRecipeDetailLog(RecipeDetail recipeDetail) {
        if (ObjectUtil.isEmpty(recipeDetail)) {
            return;
        }
        MzysTbCfscjl mzysTbCfscjl = Convert.convert(MzysTbCfscjl.class, recipeDetail);
        mzysTbCfscjl.setDeleteBy(HeadInfo.getWorkerId());
        mzysTbCfscjl.setDeleteDate(new Date());
        mzysTbCfscjl.setDeleteSource(1);
        mzysTbCfscjlMapper.insert(mzysTbCfscjl);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public void saveDeleteRecipeDetailLogList(List<RecipeDetail> recipeDetailList) {
        if (CollUtil.isEmpty(recipeDetailList)) {
            return;
        }
        recipeDetailList.forEach(this::saveDeleteRecipeDetailLog);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public void saveDeleteRecipeDetailLogByIds(List<Long> recipeDetailIds) {
        if (CollUtil.isEmpty(recipeDetailIds)) {
            return;
        }
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria().andIn(RecipeDetail::getRecipeDetailId, recipeDetailIds)
                .andNotIn(RecipeDetail::getStatus, Arrays.asList(ChargeStatusEnum.CHARGED.getCode(),
                        ChargeStatusEnum.BEING_CHARGED.getCode(),
                        ChargeStatusEnum.REGISTERED.getCode(),
                        ChargeStatusEnum.DEREGISTER.getCode()));
        saveDeleteRecipeDetailLogList(recipeDetailMapper.selectByExample(weekend));
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public void saveDeleteRecipeDetailLogBApplyIds(List<Integer> applyIds) {
        if (CollUtil.isEmpty(applyIds)) {
            return;
        }
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria().andIn(RecipeDetail::getExamineNo, applyIds)
                .andNotIn(RecipeDetail::getStatus, Arrays.asList(ChargeStatusEnum.CHARGED.getCode(),
                        ChargeStatusEnum.BEING_CHARGED.getCode(),
                        ChargeStatusEnum.REGISTERED.getCode(),
                        ChargeStatusEnum.DEREGISTER.getCode()));
        saveDeleteRecipeDetailLogList(recipeDetailMapper.selectByExample(weekend));
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public void saveDeleteRecipeDetailLogByRecipeId(Long recipeId) {
        if (ObjectUtil.isEmpty(recipeId)) {
            return;
        }
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria().andEqualTo(RecipeDetail::getRecipeId, recipeId)
                .andNotIn(RecipeDetail::getStatus, Arrays.asList(ChargeStatusEnum.CHARGED.getCode(),
                        ChargeStatusEnum.BEING_CHARGED.getCode(),
                        ChargeStatusEnum.REGISTERED.getCode(),
                        ChargeStatusEnum.DEREGISTER.getCode()));
        saveDeleteRecipeDetailLogList(recipeDetailMapper.selectByExample(weekend));
    }

}
