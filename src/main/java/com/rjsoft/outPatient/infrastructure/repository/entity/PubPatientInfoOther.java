package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2021/11/3 11:07
 * @description
 **/
@Data
@Table(name = "Tbt_PubPatientInfo_Other")
public class PubPatientInfoOther implements Serializable {
    @Column(name = "PatNo")
    private Integer patNo;
    /**
     * 户别
     */
    @Column(name = "hb")
    private String regType;
    /**
     * 住院次数
     */
    @Column(name = "zycs")
    private Integer hospCount;
    /**
     * 是否已进行抗精神病药物治疗
     */
    @Column(name = "sfkjsbzl")
    private String ifCure;
    /**
     * 户籍地  省（自治区、直辖市）
     */
    @Column(name = "hj1")
    private String domicile1;

    /**
     * 户籍地  市（帝、州、盟）
     */
    @Column(name = "hj2")
    private String domicile2;
    /**
     * 户籍地  县(市区、旗)
     */
    @Column(name = "hj3")
    private String domicile3;
    /**
     * 户籍地  乡
     */
    @Column(name = "hj4")
    private String domicile4;
    /**
     * 户籍地  村（居委会）
     * domicile
     */
    @Column(name = "hj5")
    private String domicile5;
    /**
     * 户籍地  详细至门牌号
     * domicile
     */
    @Column(name = "hj6")
    private String domicile6;
    /**
     * 现居地 省（自治区、直辖市）
     */
    @Column(name = "xj1")
    private String presentAddr1;
    /**
     * 现居地 市（地、州、盟）
     */
    @Column(name = "xj2")
    private String presentAddr2;
    /**
     * 现居地 县（市区、旗）
     */
    @Column(name = "xj3")
    private String presentAddr3;
    /**
     * 现居地 乡（镇，街道）
     */
    @Column(name = "xj4")
    private String presentAddr4;
    /**
     * 现居地 村（居委会）
     */
    @Column(name = "xj5")
    private String presentAddr5;
    /**
     * 现居地 门牌号
     */
    @Column(name = "xj6")
    private String presentAddr6;


}
