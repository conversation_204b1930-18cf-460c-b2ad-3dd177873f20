package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.HisDictionary;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;
import java.util.Map;

/**
 * 医院字典
 * <AUTHOR>
public interface HisDictionaryMapper  extends BaseMapper<HisDictionary>, ExampleMapper<HisDictionary> {

    /**
     * 根据字典类型集合获取系统字典
     * @param types
     * @param hospitalCode
     * @return
     */
    default List<HisDictionary> getDictionaryByTypes(List<Integer> types,Integer hospitalCode) {
        Weekend weekend = new Weekend(HisDictionary.class);
        WeekendCriteria<HisDictionary, Object> keywordCriteria = weekend.weekendCriteria();
        if (types != null && types.stream().count() > 0) {
            keywordCriteria.andIn(HisDictionary::getDictionaryTypeID, types);
        }
        keywordCriteria.andEqualTo(HisDictionary::getHospitalId, hospitalCode);
        keywordCriteria.andEqualTo(HisDictionary::getIsDelete, 0);
        keywordCriteria.andEqualTo(HisDictionary::getIsUse, 1);
        weekend.setOrderByClause("dictionaryTypeID,[order]");
        return selectByExample(weekend);
    }
}
