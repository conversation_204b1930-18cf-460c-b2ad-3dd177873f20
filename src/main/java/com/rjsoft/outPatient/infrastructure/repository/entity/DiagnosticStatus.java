package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.rjsoft.outPatient.domain.dictionary.dto.CommonDicEntity;
import com.rjsoft.outPatient.domain.dictionary.vo.CommonDicVo;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 诊断状态
 *
 * <AUTHOR>
 * @since 2021/8/17 - 10:50
 */
@Data
@Table(name = "MZYS_TB_MZYSZDZT")
public class DiagnosticStatus extends CommonDicEntity  implements Serializable {

    /**
     * 诊断编码
     */
    @Column(name = "ztbm")
    private Integer diagnosticCode;
    /**
     * 诊断名称
     */
    @Column(name = "ztmc")
    private String diagnosticName;
    /**
     * 顺序号
     */
    @Column(name = "sxh")
    private Integer sort;
    /**
     * 状态
     */
    @Column(name = "state")
    private Integer state;
    /**
     * 医院编码
     */
    @Column(name = "yybm")
    private Integer hospitalCode;

    @Override
    public CommonDicVo conversionFill() {
        super.setCode(this.diagnosticCode);
        super.setName(this.diagnosticName);
        super.setHospitalCode(this.hospitalCode);
        return new CommonDicVo(this);
    }
}
