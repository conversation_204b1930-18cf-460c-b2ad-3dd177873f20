package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.config.dto.DoctorInfoResponse;
import com.rjsoft.outPatient.domain.doctorElemMain.dto.WorkerInfoDTO;
import com.rjsoft.outPatient.domain.doctorElemMain.dto.WorkerInfoQueryDTO;
import com.rjsoft.outPatient.domain.doctorElemMain.dto.WorkerInfoResponse;
import com.rjsoft.outPatient.domain.recipe.dto.HlyyDoctor;
import com.rjsoft.outPatient.infrastructure.repository.entity.MdmRecipeWhitelist;
import com.rjsoft.outPatient.infrastructure.repository.entity.Worker;
import com.rjsoft.outPatient.infrastructure.repository.entity.WorkerContrast;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 职工
 *
 * <AUTHOR>
public interface WorkerRepository {

    /**
     * 根据职工ID 获取与该职工关联的所有院区对应职工信息
     *
     * @param workerId
     * @param hospitalCode
     * @return
     */
    List<Worker> getAllWorkerById(Integer workerId, Integer hospitalCode);

    /**
     * 根据职工ID 获取职工信息
     *
     * @param workerIds
     * @return
     */
    List<Worker> getAllWorkerByIds(List<Integer> workerIds);

    /**
     * 根据科室编码+医院编码判断是否专病
     *
     * @param deptId
     * @param hospitalCode
     * @return
     */
    int judgeSpecialDisease(Integer deptId, String hospitalCode);

    /**
     * 根据医生id获取医生相关信息
     *
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    HlyyDoctor getHlyyDoctor(Integer doctorId, Integer hospitalCode);

    /**
     * 根据workerId+hospId获取医生信息
     *
     * @param creDoctor
     * @param hospCode
     * @return
     */
    Worker getWorkerById(Integer creDoctor, String hospCode);


    /**
     * 获取职工信息（过滤是否停用标识）
     *
     * @param creDoctor
     * @param hospitalCode
     * @return
     */
    Worker getWorkerByIdAll(Integer creDoctor, Integer hospitalCode);

    /**
     * 模糊搜索医生信息
     *
     * @param inputCode
     * @param hospitalCode
     */
    List<DoctorInfoResponse> getDoctorInfos(String inputCode, Integer hospitalCode);

    /**
     * 根据医生ids,hospitalCode，模糊查找医生信息
     *
     * @param inputCode
     * @param hospitalCode
     * @param list
     */
    List<com.rjsoft.outPatient.domain.config.dto.DoctorInfoResponse> getDoctorInfosByList(String inputCode, Integer hospitalCode, List<Integer> list);

    /**
     * 根据总分院入参，查询是否有对应的记录
     *
     * @param param
     */
    List<WorkerContrast> getWorkerContrast(WorkerInfoDTO param);

    /**
     * 添加职工对照信息
     *
     * @param param
     */
    int addWorker(WorkerInfoDTO param);

    /**
     * 删除职工信息
     */
    void delWorker(Integer id);

    /**
     * 查询总院医生工号信息
     */
    int queryGeneralWorker(Integer workerId);

    /**
     * 查询分院医生工号信息
     */
    int queryBranchWorker(Integer workerId);

    /**
     * 根据主键Id查询实体信息
     */
    WorkerContrast queryEntityById(Integer id);

    /**
     * 修改分院职工对照记录
     */
    int updateWorkerInfo(Integer id, Integer workerId);

    /**
     * 查询总分院职工对照列表
     *
     * @param param
     */
    List<WorkerInfoResponse> queryWorker(WorkerInfoQueryDTO param);

    /**
     * 通过医生工号查询医生信息
     *
     * @param workerNo
     * @param hospitalCode
     */
    List<Worker> getDoctorInfo(String workerNo, Integer hospitalCode);


    /**
     * 获取职工医生信息信息
     *
     * @param input
     * @param hospitalCode
     * @return
     */
    List<Worker> getAllWorkerByInput(String input, Integer hospitalCode);

    /**
     * 获取是否白名单职工
     *
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    MdmRecipeWhitelist getIsWhiteDoc(Integer doctorId, Integer hospitalCode);

    /**
     * 根据多学科门诊挂号记录获取副主任级别以上医生人数
     *
     * <AUTHOR>
     * @param regNo 挂号流水号
     * @param hospitalCode 医院编码
     * @return
     */
    Integer getMDTWorkNum(Integer regNo,Integer hospitalCode);

    /**
     * 获取总分院关联医生信息
     *
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    List<Worker> getRelationWorker(Integer doctorId, Integer hospitalCode);
}
