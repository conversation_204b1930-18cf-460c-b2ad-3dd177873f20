package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DrugInfo;
import com.rjsoft.outPatient.domain.diseaseReport.dto.ReceptionDrugDTO;
import com.rjsoft.outPatient.domain.item.vo.ItemExeDeptRespVO;
import com.rjsoft.outPatient.domain.prescriptionAudit.dto.AuditPrescriptionDto;
import com.rjsoft.outPatient.domain.prescriptionAudit.vo.AuditMedicine;
import com.rjsoft.outPatient.domain.recipe.dto.*;
import com.rjsoft.outPatient.domain.recipe.vo.DoctorFeeQuotaVo;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 门诊处方
 *
 * <AUTHOR>
public interface PreRecipeDetailRepository {
    /**
     * 获取处方预存明细(根据申请单ID)
     *
     * @param applyId
     * @param hospitalCode
     * @return
     */
    List<PreRecipeDetail> getPreRecipeDetailsByApplyId(Long applyId, Integer hospitalCode);

    /**
     * 获取处方预存明细(根据套餐ID)
     *
     * @param packageId
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    List<PreRecipeDetail> getPreRecipeDetailsByPackageId(Long packageId, Integer doctorId, Integer hospitalCode, Long preSaveNo, CheckRecipeDto inputData, Map errInfo);






    /**
     * 根据处方明细流水号查询处方预存信息
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    PreRecipeDetail getPreRecipeDetailsByRecipeDetailNo(Integer receptionNo, Integer hospitalCode);



    /**
     * 获取处方预存明细(根据预存流水号)
     *
     * @param preSaveNo
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    List<PreRecipeDetail> getPreRecipeDetailsBySaveNo(Long preSaveNo, Integer doctorId, Integer hospitalCode);


    /**
     * 根据预存流水号删除预存记录
     *
     * @param preSaveNo
     * @param hospitalCode
     */
    void deletePreRecipeDetailBySaveNo(Long preSaveNo, Integer hospitalCode);


    /**
     * 根据处方明细id 删除预存处方明细
     *
     * @param detailId     处方明细id
     * @param hospitalCode 医院编码
     * @return Boolean
     */
    Integer deletePreRecipeDetailByDetailId(Long detailId, Integer hospitalCode);

    Integer deletePreRecipeDetailByDetailIdList(List<Long> detailIdList, Integer hospitalCode);


    /**
     * 根据ID获取处方预存信息
     *
     * @param preSaveNo
     * @param detailNo
     * @param hospitalCode
     * @return
     */
    PreRecipeDetail getPreRecipeById(Long preSaveNo, Long detailNo, Integer hospitalCode);



    /**
     * 根据预存流水号删除预存记录
     *
     * @param preSaveNo
     * @param hospitalCode
     * @return
     */
    boolean delPreRecipeDetailByPreNo(Long preSaveNo, Integer hospitalCode);


    /**
     * 处方明细预存
     *
     * @param preRecipeDetail
     * @return
     */
    boolean savePreRecipeDetail(PreRecipeDetail preRecipeDetail);

    boolean savePreRecipeDetailList(List<PreRecipeDetail> preRecipeDetailList);

    /**
     * 处方明细预存
     *
     * @param preRecipeDetails
     * @return
     */
    boolean savePreRecipeDetails(List<PreRecipeDetail> preRecipeDetails);


    boolean delPreRecipeDetailList(List<PreRecipeDetail> insertList);
}
