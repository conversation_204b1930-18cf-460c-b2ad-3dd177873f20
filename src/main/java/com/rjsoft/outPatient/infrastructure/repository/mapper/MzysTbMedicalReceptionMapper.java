package com.rjsoft.outPatient.infrastructure.repository.mapper;

import cn.hutool.core.collection.CollUtil;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbMedicalReception;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Collections;
import java.util.List;

public interface MzysTbMedicalReceptionMapper extends Mapper<MzysTbMedicalReception> {

    default MzysTbMedicalReception getMedicalReceptionByRegNo(Long regNo, Integer hospitalCode) {
        Weekend<MzysTbMedicalReception> weekend = Weekend.of(MzysTbMedicalReception.class);
        WeekendCriteria<MzysTbMedicalReception, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andEqualTo(MzysTbMedicalReception::getRegNo, regNo).andEqualTo(MzysTbMedicalReception::getHospitalId, hospitalCode);
        return this.selectOneByExample(weekend);
    }

    default List<MzysTbMedicalReception> queryMedicalReceptionByRegNoList(List<Long> regNoList, Integer hospitalCode) {
        if (CollUtil.isEmpty(regNoList)) {
            return Collections.emptyList();
        }
        Weekend<MzysTbMedicalReception> weekend = Weekend.of(MzysTbMedicalReception.class);
        WeekendCriteria<MzysTbMedicalReception, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andIn(MzysTbMedicalReception::getRegNo, regNoList).andEqualTo(MzysTbMedicalReception::getHospitalId, hospitalCode);
        return this.selectByExample(weekend);
    }

    default boolean deleteByRegNo(Long regNo, Integer hospitalCode) {
        Weekend<MzysTbMedicalReception> weekend = Weekend.of(MzysTbMedicalReception.class);
        WeekendCriteria<MzysTbMedicalReception, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andEqualTo(MzysTbMedicalReception::getRegNo, regNo).andEqualTo(MzysTbMedicalReception::getHospitalId, hospitalCode);
        return this.deleteByExample(weekend) > 0;
    }

    default boolean deleteByRegNoList(List<Long> regNoList, Integer hospitalCode) {
        Weekend<MzysTbMedicalReception> weekend = Weekend.of(MzysTbMedicalReception.class);
        WeekendCriteria<MzysTbMedicalReception, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andIn(MzysTbMedicalReception::getRegNo, regNoList).andEqualTo(MzysTbMedicalReception::getHospitalId, hospitalCode);
        return this.deleteByExample(weekend) > 0;
    }

}