<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.WorkerContrastMapper">

    <select id="getWorkerContrast" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.WorkerContrast">
        select *
        from System_Tb_WorkerControl
        where (WorkerId = #{workerId} or ToWorkerId = #{toWorkerId})
          and IsDelete = 0
    </select>
    <select id="queryWorker" resultType="com.rjsoft.outPatient.domain.doctorElemMain.dto.WorkerInfoResponse">
        select distinct a.Id id, a.WorkerId workerId,rtrim(b.WorkerNo) workerNo, rtrim(b.Name) name , a.ToWorkerId toWorkerId, rtrim(c.WorkerNo) toWorkerNo, rtrim(c.Name) toName
        from System_Tb_WorkerControl a
        inner join System_Tb_Worker b on a.WorkerId = b.WorkerId and a.HospitalId = b.HospitalId
        inner join System_Tb_Worker c on a.ToWorkerId = c.WorkerId and a.ToHospitalId = c.HospitalId
        where a.IsDelete = 0 and b.Status=0 and c.Status=0
        <if test="hospitalCode != null">
            <if test="hospitalCode==1">
                and a.ToHospitalId = 1
            </if>
            <if test="hospitalCode==3">
                and a.HospitalId = 3
            </if>
        </if>
        <if test="code != null">
            and ( b.Name like '%' + #{code} + '%'
            or c.Name like '%' + #{code} + '%'
            or b.InputCode2 like '%' + #{code} + '%'
            or c.InputCode2 like '%' + #{code} + '%'
            or a.WorkerId like '%' + #{code} + '%'
            or a.ToWorkerId like '%' + #{code} + '%'
            )
        </if>
    </select>
</mapper>