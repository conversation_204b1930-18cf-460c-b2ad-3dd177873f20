<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DeptCaseTempMapper">

    <select id="queryDeptTemp"
            resultType="com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QueryDefaultTempResult">
        select b2.mbid as tempId,
        b2.mbysmc as tempElemName,
        b2.mbdm as tempCode,
        b2.mbmc as tempName,
        b3.wjbm as fileNo,
        b3.wjmc as fileName,
        b3.wjnr as fileContent,
        b2.yybm as hospCode
        from MZYS_TB_KSMB b1
        left join MZYS_TB_MBINFO b2 on b1.mbid = b2.mbid
        left join MZYS_TB_MBXML b3 on b2.wjbm = b3.wjbm and b2.yybm = b3.yybm and b3.del_status = 0
        where b2.yybm = #{hospCode}
        <if test="searchType!=null">
            and b1.ksdm = #{deptId}
        </if>
        <if test="tempName!=null and tempName!=''">
            and b2.mbmc like '%'+ #{tempName}+'%'
        </if>
        and b2.gxbj = 1
        and b1.del_status = 0
        and b2.zt = 0
        and b2.mbfl = 0
        order by b1.cjsj desc
    </select>

    <select id="queryHospTemp"
            resultType="com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QueryDefaultTempResult">
        select
        b2.mbid as tempId,
        b2.mbysmc as tempElemName,
        b2.mbdm as tempCode,
        b2.mbmc as tempName,
        b3.wjbm as fileNo,
        b3.wjmc as fileName,
        b3.wjnr as fileContent,
        b2.yybm as hospCode
        from
        MZYS_TB_MBINFO b2
        left join
        MZYS_TB_MBXML b3
        on b2.wjbm = b3.wjbm
        and b2.yybm = b3.yybm
        and b3.del_status = 0
        where
        b2.yybm = #{hospCode}
        <if test="tempName!=null and tempName!=''">
            and b2.mbmc like '%'+ #{tempName}+'%'
        </if>
        and b2.gxbj = 1
        and b2.zt = 0
        and b2.mbfl = 0
        order by
        b2.cjsj desc
    </select>
    <select id="queryDeptDefaultTemp"
            resultType="com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QueryDefaultTempResult">
        select top(1)
        b2.mbid as tempId,
        b2.mbysmc as tempElemName,
        b2.mbdm as tempCode,
        b2.mbmc as tempName,
        b3.wjbm as fileNo,
        b3.wjmc as fileName,
        b3.wjnr as fileContent,
        b2.yybm as hospCode
        from MZYS_TB_KSMB b1
        left join MZYS_TB_MBINFO b2 on b1.mbid = b2.mbid
        left join MZYS_TB_MBXML b3 on b2.wjbm = b3.wjbm and b2.yybm = b3.yybm
        where b1.del_status = 0
        and b2.zt = 0
        and b2.mbfl = 0
        and b2.mrmb = 1
        and b2.yybm =#{hospCode}
        and b2.czbj = #{visitFlag}
        and b1.ksdm = #{deptId}
        <if test="sexFlag !=0 ">
            and isnull(b2.xb,'3')=#{sexFlag}
        </if>
    </select>
    <select id="queryBaseTempContrast"
            resultType="com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QueryBaseTempContrastResult">
        select b1.Id AS deptTempId,
        b1.ksdm AS deptId,
        b2.mbid AS tempId,
        b2.mbysmc AS tempElemName,
        b2.mbdm AS tempCode,
        b2.mbmc AS tempName,
        b2.mbfl AS tempType,
        b2.czbj AS firstVisit,
        b2.mrmb AS defaultTempFlag,
        b2.yybm AS hospCode
        from MZYS_TB_KSMB b1
        left join MZYS_TB_MBINFO b2 on b1.mbid = b2.mbid
        where b1.ksdm = #{deptId}
        and b2.yybm = #{hospCode}
        <if test="tempName!=null and tempName!=''">
            and b2.mbmc like '%' + #{tempName} +'%'
        </if>
        and b1.del_status = 0
        and b2.del_status = 0
        and b2.mbfl = 0
    </select>

</mapper>