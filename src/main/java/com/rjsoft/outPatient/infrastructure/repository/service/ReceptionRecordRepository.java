package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatBlParam;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatRecipeInfoDTO;
import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.BLTimeAndDept;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo;
import com.rjsoft.outPatient.domain.reception.dto.CAResult;
import com.rjsoft.outPatient.domain.reception.dto.ReceptionDiagnose;
import com.rjsoft.outPatient.domain.recipe.dto.HistoryReceptionDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 接诊记录
 *
 * <AUTHOR>
public interface ReceptionRecordRepository {
    /**
     * 根据就诊流水号获取接诊记录
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    ReceptionRecord getReceptionById(Long receptionNo, Integer hospitalCode);

    /**
     * 根据就诊流水号获取接诊记录
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    List<ReceptionRecord> getReceptionByIds(List<Long> receptionNo, Integer hospitalCode);

    /**
     * 根据挂号流水号获取接诊记录
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    List<ReceptionRecord> getReceptionByRegNo(List<Long> regNo, Integer hospitalCode);

    /**
     * 根据挂号流水号获取接诊记录
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    List<ReceptionRecord> getReceptionByRegNoAndTable(List<Long> regNo, Integer hospitalCode,String tableName);

    /**
     * 根据挂号流水号获取新系统接诊记录
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    List<ReceptionRecord> getReceptionByRegNoList(List<Long> regNo, Integer hospitalCode, Date startTime, Date endTime);

    /**
     * 根据挂号流水号获取老系统接诊记录
     *
     * @param regNos
     * @param hospitalCode
     * @return
     */
    List<OldReceptionRecord> getOldReceptionByRegNo(List<String> regNos, Integer hospitalCode, Date startTime, Date endTime);

    /**
     * 根据挂号流水号和看诊医生获取接诊记录
     *
     * @param regNo
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    ReceptionRecord getReceptionByRegNoAndDoctor(Long regNo, Integer doctorId, Integer hospitalCode);

    /**
     * 保存就诊信息
     *
     * @param reception
     * @return
     */
    boolean saveReceptionRecord(ReceptionRecord reception);

    /**
     * 删除接诊记录
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    boolean delReceptionRecord(Long receptionNo, Integer hospitalCode);

    /**
     * 添加接诊日志
     *
     * @param registerList
     * @param receptionRecord
     * @param opType
     */
    void addReceptionLog(RegisterList registerList, ReceptionRecord receptionRecord, String opType);

    /**
     * 科研添加接诊日志
     *
     * @param receptionRecord
     * @param opType
     */
    void addSciReceptionLog(ReceptionRecord receptionRecord, String opType);

    /**
     * 根据挂号流水号加载接诊记录
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    List<ReceptionLogs> getReceptionLogByRegNo(Long regNo, Integer hospitalCode);

    /**
     * 根据患者编号、医生编号、科室编号、医院编码、首次看诊日期（年月日）
     *
     * @param reception
     * @return：
     */
    List<ReceptionRecord> getReceptionByParam(ReceptionRecord reception);

    /**
     * 根据就诊流水号获取患者处方信息
     *
     * @param receptionNoSet
     * @return
     */
    List<PatRecipeInfoDTO> getPatRecipe(Set<Long> receptionNoSet, Integer hospitalCode);


    /**
     * 根据患者编码集合+医院编码+看诊日期范围获取看诊数据+挂号数据
     *
     * @param patIdList
     * @param startDate
     * @param endDate
     * @return
     */
    List<ReceptionRecord> getReceptionListByPatIds(Set<Integer> patIdList, Date startDate, Date endDate);

    /**
     * 加载本次看诊中符合六大类的诊断
     *
     * @param receptionNo 就诊流水号
     * @param hospId      医院Id
     */
    List<ReceptionDiagnose> selectDiagnoseList(Long receptionNo, Integer hospId);

    /**
     * 加载本次看诊中符合六大类的诊断[根据诊断编码]
     *
     * @param receptionDiagnose
     */
    List<ReceptionDiagnose> selectDiagnoseListByDiagnoseNo(List<ReceptionDiagnose> receptionDiagnose);

    /**
     * 获取已经上报的疾病名称列表
     *
     * @param certificateNo
     * @param hospitalCode
     */
    List<String> getSbName(String certificateNo, Integer hospitalCode);

    /**
     * 电子病历验签结果
     *
     * @param doctorId 医生id
     */
    CAResult getCAResult(@Param("doctorId") Integer doctorId);

    /**
     * 根据患者编号获取患者历史就诊记录
     *
     * @param patIds
     */
    List<ReceptionRecord> getReceptionRecord(Set<Integer> patIds, Integer hospitalCode);

    /**
     * 根据就诊流水号获取看诊时间和科室
     *
     * @param hospitalCode
     * @param receptionNo
     */
    BLTimeAndDept getBlRecord(Integer hospitalCode, Integer receptionNo);

    /**
     * 根据就诊流水号获取总分院看诊记录
     *
     * @param receptions
     */
    List<DiagnoseRecordInfo> getReceptionRecordByReceptions(Set<String> receptions, Integer hospitalCode);

    /**
     * 获取新门诊医生未书写病历jzlsh
     *
     * @param param
     */
    List<PatBlDto> notFilledReceptionRecord(PatBlParam param);

    /**
     * 获取新门诊医生未提交病历jzlsh
     *
     * @param param
     */
    List<PatBlDto> notCommittedReceptionRecord(PatBlParam param);

    /**
     * 获取新门诊医生病历jzlsh
     *
     * @param param,status
     */
    List<PatBlDto> getCaseHistoryByDoctorId(PatBlParam param, Integer status);

    /**
     * 获取老门诊医生病历jzlsh
     *
     * @param param,status
     */
    List<PatBlDto> getOldCaseHistoryByDoctorId(PatBlParam param, Integer status);

    /**
     * 获取新门诊医生未签名病历jzlsh and regno
     *
     * @param param
     */
    List<PatBlDto> notSignedReceptionRecord(PatBlParam param);

    /**
     * 根据挂号流水号，加载医保类型
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    Integer getInsuranceByRegNo(Long regNo, Integer hospitalCode);

    Integer getInsuranceByChargeType(Integer chargeType, Integer hospitalCode);

    /**
     * 根据挂号流水号获取有药品处方明细的看诊记录
     *
     * @param regNos
     * @param hospitalCode
     */
    List<ReceptionRecord> getDrugReceptionRecord(List<Long> regNos, Integer hospitalCode);

    /**
     * 查询互联网处方挂号详情信息
     *
     * @param internetRegNos
     * @param hospitalCode
     * @return
     */
    List<HistoryReceptionDto> getInternetDrugReceptionRecord(List<String> internetRegNos, Integer hospitalCode);

    /**
     * 查询互联网处方数据
     *
     * @param regNos
     * @param hospitalCode
     * @return
     */
    List<HistoryReceptionDto> getInternetDrugReceptionTimeByRegNos(List<Long> regNos, Integer hospitalCode);

    /**
     * 查询医生最后一次看诊记录
     *
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    Long getLastReceptionByDoctorId(Integer doctorId, Integer deptId, Integer hospitalCode);


    /**
     * 根据就诊流水号获取看诊记录
     *
     * @param receptionNo
     * @param hospitalCode
     */
    ReceptionRecord getReceptionRecordByReceptionNo(Long receptionNo, Integer hospitalCode);


    /**
     * 根据就诊流水号获取看诊记录
     *
     * @param receptionNos
     * @param hospitalCode
     */
    List<ReceptionRecord> getRegNoByReceptions(Set<Integer> receptionNos, Integer hospitalCode);

}
