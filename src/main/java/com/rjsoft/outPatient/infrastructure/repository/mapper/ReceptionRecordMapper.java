package com.rjsoft.outPatient.infrastructure.repository.mapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatBlParam;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatRecipeInfoDTO;
import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.BLTimeAndDept;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo;
import com.rjsoft.outPatient.domain.reception.dto.CAResult;
import com.rjsoft.outPatient.domain.reception.dto.ReceptionDiagnose;
import com.rjsoft.outPatient.domain.recipe.dto.HistoryReceptionDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.ReceptionRecord;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 接诊记录
 *
 * <AUTHOR>
public interface ReceptionRecordMapper extends BaseMapper<ReceptionRecord>, ExampleMapper<ReceptionRecord> {

    /**
     * 根据就诊流水号获取接诊记录
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    default ReceptionRecord getReceptionById(Long receptionNo, Integer hospitalCode) {
        ReceptionRecord record = new ReceptionRecord();
        record.setReceptionNo(receptionNo);
        record.setHospitalCode(hospitalCode);
        return selectOne(record);
    }

    /**
     * 根据就诊流水号获取看诊记录
     *
     * @param receptionNos
     * @param hospitalCode
     */
    default List<ReceptionRecord> getRegNoByReceptions(Set<Integer> receptionNos, Integer hospitalCode) {
        Weekend<ReceptionRecord> weekend = new Weekend<>(ReceptionRecord.class);
        WeekendCriteria<ReceptionRecord, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(ReceptionRecord::getReceptionNo, receptionNos);
//                .andEqualTo(ReceptionRecord::getHospitalCode,hospitalCode);
        return selectByExample(weekend);
    }


    /**
     * 根据挂号流水号获取接诊记录
     *
     * @param regNos
     * @param hospitalCode
     * @return
     */
    default List<ReceptionRecord> getReceptionByRegNo(List<Long> regNos, Integer hospitalCode) {
        List<ReceptionRecord> records = new ArrayList<>();
        ReceptionRecord record = new ReceptionRecord();
        if (regNos == null || regNos.size() == 0) {
            return records;
        }
        Weekend<ReceptionRecord> weekend = new Weekend<>(ReceptionRecord.class);
        WeekendCriteria<ReceptionRecord, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andIn(ReceptionRecord::getRegNo, regNos);
        keywordCriteria.andEqualTo(ReceptionRecord::getHospitalCode, hospitalCode);
        records = selectByExample(weekend);
        return records;
    }

    /**
     * 根据挂号流水号获取接诊记录
     *
     * @param regNos
     * @param hospitalCode
     * @return
     */
    default List<ReceptionRecord> getReceptionByRegNoAndTable(List<Long> regNos, Integer hospitalCode, String tableName) {
        List<ReceptionRecord> records = new ArrayList<>();
        if (CollUtil.isEmpty(regNos)) {
            return records;
        }
        Weekend<ReceptionRecord> weekend = new Weekend<>(ReceptionRecord.class);
        if (StrUtil.isNotBlank(tableName)) {
            weekend.setTableName(tableName);
        }
        WeekendCriteria<ReceptionRecord, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andIn(ReceptionRecord::getRegNo, regNos);
        keywordCriteria.andEqualTo(ReceptionRecord::getHospitalCode, hospitalCode);
        records = selectByExample(weekend);
        return records;
    }


    /**
     * 根据挂号流水号获取接诊记录
     *
     * @param regNos
     * @param hospitalCode
     * @param startTime
     * @param endTime
     * @return
     */
    default List<ReceptionRecord> getReceptionByRegNoList(List<Long> regNos, Integer hospitalCode, Date startTime, Date endTime) {
        List<ReceptionRecord> records = new ArrayList<>();
        ReceptionRecord record = new ReceptionRecord();
        if (regNos == null || regNos.size() == 0) {
            return records;
        }
        Weekend<ReceptionRecord> weekend = new Weekend<>(ReceptionRecord.class);
        WeekendCriteria<ReceptionRecord, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andIn(ReceptionRecord::getRegNo, regNos);
        keywordCriteria.andEqualTo(ReceptionRecord::getHospitalCode, hospitalCode);
        if(startTime!=null&&endTime!=null){
            endTime = DateUtils.addDays(endTime, 1);
            keywordCriteria.andBetween(ReceptionRecord::getFirstDate,startTime,endTime);
        }
        records = selectByExample(weekend);
        return records;
    }


    List<ReceptionRecord> getReceptionByParam(ReceptionRecord reception);

    List<PatRecipeInfoDTO> getPatRecipe(@Param("receptionNoSet") Set<Long> receptionNoSet,
                                        @Param("hospitalCode") Integer hospitalCode);

    default List<ReceptionRecord> getReceptionListByPatIds(Set<Integer> patIdList, Date startDate, Date endDate) {
        Weekend<ReceptionRecord> weekend = new Weekend<>(ReceptionRecord.class);
        WeekendCriteria<ReceptionRecord, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ReceptionRecord::getPatId, patIdList);
        if (startDate != null && endDate != null) {
            weekendCriteria.andLessThanOrEqualTo(ReceptionRecord::getFirstDate, endDate)
                    .andGreaterThanOrEqualTo(ReceptionRecord::getFirstDate, startDate);
        }
        return selectByExample(weekend);
    }

    /**
     * 加载本次看诊中符合六大类的诊断
     *
     * @param receptionNo 就诊流水号
     * @param hospId      医院Id
     */
    List<ReceptionDiagnose> selectDiagnoseList(@Param("receptionNo") Long receptionNo, @Param("hospId") Integer hospId);

    /**
     * 加载本次看诊中符合六大类的诊断[根据诊断编码]
     *
     * @param receptionDiagnose
     */
    List<ReceptionDiagnose> selectDiagnoseListByDiagnoseNo(@Param("list") List<ReceptionDiagnose> receptionDiagnose);


    /**
     * 获取已经上报的疾病名称列表
     *
     * @param certificateNo
     */
    List<String> getSbName(@Param("certificateNo") String certificateNo, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 电子病历验签结果
     *
     * @param doctorId 医生id
     */
    CAResult getCAResult(@Param("doctorId") Integer doctorId);

    /**
     * 根据患者编号获取患者历史就诊记录
     *
     * @param patIds
     * @param hospitalCode
     */
    default List<ReceptionRecord> getReceptionRecord(Set<Integer> patIds, Integer hospitalCode) {
        Weekend<ReceptionRecord> weekend = new Weekend<>(ReceptionRecord.class);
        WeekendCriteria<ReceptionRecord, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(ReceptionRecord::getPatId, patIds);
        criteria.andEqualTo(ReceptionRecord::getHospitalCode, hospitalCode);
        return selectByExample(weekend);
    }

    /**
     * 根据就诊流水号获取看诊时间和科室
     *
     * @param hospitalCode
     * @param receptionNo
     */
    BLTimeAndDept getBlRecord(@Param("hospitalCode") Integer hospitalCode, @Param("receptionNo") Integer receptionNo);

    /**
     * 根据就诊流水号获取看诊记录【老系统】
     *
     * @param receptions
     */
    List<DiagnoseRecordInfo> getReceptionAndPatNo(@Param("list") Set<String> receptions);

    /**
     * 获取新门诊医生未书写病历jzlsh and regno
     *
     * @param param
     */
    List<PatBlDto> notFilledReceptionRecord(PatBlParam param);

    /**
     * 获取新门诊医生未提交病历jzlsh and regno
     *
     * @param param
     */
    List<PatBlDto> notCommittedReceptionRecord(PatBlParam param);

    /**
     * 获取新门诊医生病历jzlsh and regno
     *
     * @param param
     */
    List<PatBlDto> getCaseHistoryByDoctorId(@Param("param") PatBlParam param,@Param("status")Integer status);

    /**
     * 获取门诊医生老病历jzlsh and regno
     *
     * @param param
     */
    List<PatBlDto> getOldCaseHistoryByDoctorId(@Param("param") PatBlParam param,@Param("status")Integer status);

    /**
     * 获取新门诊医生未签名病历jzlsh and regno
     *
     * @param param
     */
    List<PatBlDto> notSignedReceptionRecord(PatBlParam param);

    /**
     * 根据挂号流水号，加载医保类型
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    Integer getInsuranceByRegNo(@Param("regNo") Long regNo, @Param("hospitalCode") Integer hospitalCode);

    Integer getInsuranceByChargeType(@Param("chargeType") Integer chargeType, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据挂号流水号获取有药品处方明细的看诊记录
     *
     * @param regNos
     * @param hospitalCode
     */
    List<ReceptionRecord> getDrugReceptionRecord(@Param("list") List<Long> regNos, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 查询互联网处方挂号详情信息
     *
     * @param internetRegNos
     * @param hospitalCode
     * @return
     */
    List<HistoryReceptionDto> getInternetDrugReceptionRecord(@Param("internetRegNos") List<String> internetRegNos, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 查询互联网处方数据
     *
     * @param regNos
     * @return
     */
    List<HistoryReceptionDto> getInternetDrugReceptionTimeByRegNos(@Param("regNos") List<String> regNos);


    /**
     * 查询医生最后一次看诊记录
     *
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    Long getLastReceptionByDoctorId(Integer doctorId, Integer deptId, Integer hospitalCode);
}
