package com.rjsoft.outPatient.infrastructure.repository.service;


import com.rjsoft.common.param.SearchParam;
import com.rjsoft.outPatient.domain.caseHistory.dto.*;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 电子病历
 */
public interface CaseHistoryRepository {

    /**
     * 获取患者病历状态
     *
     * @param regNo
     * @return
     */
    List<CaseHistory> getCaseHistoryStatusByRegNo(List<Long> regNo);

    /**
     * 根据就诊流水号获取病历状态（只返回状态相关字段，用于流程控制）
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    CaseHistory getCaseHistoryStatusByReceptionNo(Long receptionNo, Integer hospitalCode);

    /**
     * 根据就诊流水号，检查是否存在有效病历
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    boolean hasCaseHistoryByReceptionNo(Long receptionNo, Integer hospitalCode);

    /**
     * 根据就诊流水号删除病历
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    boolean delCaseHistoryByReceptionNo(Long receptionNo, Integer hospitalCode);

    /**
     * 根据参数查询
     *
     * @param medicalNum 就诊流水号
     * @param submitFlag 提交标识
     * @return
     */
    CaseHistory getCaseHistoryByMedicalNumAndSubmitFlag(Integer medicalNum, Integer submitFlag);

    /**
     * 根据就诊流水号、打印标识查询
     *
     * @param medicalNum   就诊流水号
     * @param printFlag    打印标识
     * @param hospitalCode 医院编码
     * @return CaseHistory
     */
    CaseHistory getCaseHistoryByMedicalNumAndPrintFlag(Integer medicalNum, Integer printFlag, Integer hospitalCode);

    /**
     * 根据就诊流水号查询病历
     *
     * @param medicalNum
     * @return
     */
    String getCaseHistoryBlByMedicalNum(Integer medicalNum);

    /**
     * 根据挂号流水号加载病历，排除指定列
     *
     * @param regNos
     * @param columns
     * @return
     */
    List<CaseHistory> getCaseHistoryByRegNos(List<Long> regNos, Date startTime, Date endTime, String... columns);


    /**
     * 根据挂号流水号加载互联网病历ID
     *
     * @param regNos
     * @param startDate
     * @param endDate
     * @return
     */
    List<InternetCaseHistory> getInternetCaseHistoryIdByRegNos(List<Long> regNos, Date startDate, Date endDate);

    /**
     * 根据挂号流水号加载互联网病历
     *
     * @param hospitalCode
     * @param internetRegNos
     * @return
     */
    List<InternetCaseHistory> getInternetCaseHistoryByRegNos(Integer hospitalCode, List<String> internetRegNos);

    /**
     * 根据挂号流水号获取挂号
     *
     * @param regNo
     * @return
     */
    PatientDTO getPatient(String regNo, Integer hospitalCode, Integer dataSources);


    /**
     * 根据医生编号获取医生信息
     *
     * @param doctorId
     * @param hospId
     * @return
     */
    Worker getDoctor(int doctorId, int hospId);


    /**
     * 根据科室编号获取科室信息
     *
     * @param ksbm
     * @param hospId
     * @return
     */
    Department getDept(int ksbm, int hospId);


    /**
     * 保存病历信息
     *
     * @param caseHistory
     * @return
     */
    int addCaseHistory(CaseHistory caseHistory, Integer hospitalCode, Integer dataSources, String blId);


    /**
     * 修改电子病历状态
     *
     * @param updateForm
     */
    void updateCaseHistoryStatus(CaseHistoryUpdateForm updateForm);

    /**
     * 病历提交撤销
     *
     * @param blId
     * @param submitStatus
     * @param opCode
     * @param dataSources
     * @param hospitalCode
     */
    void updateSubmitStatus(String blId, Integer submitStatus, Integer opCode, Integer dataSources, Integer hospitalCode);

    /**
     * 保存电子病历日志
     *
     * @param caseHistoryLog
     * @param hospitalCode
     * @param dataSources
     * @param blId
     */
    void saveCaseHistoryLog(CaseHistoryLog caseHistoryLog, Integer hospitalCode, Integer dataSources, String blId);

    /**
     * 保存电子病历操作日志
     *
     * @param caseHistoryOpLog
     */
    void saveCaseHistoryOpLog(CaseHistoryOpLog caseHistoryOpLog, Integer dataSources);

    /**
     * 初诊，修改舒辅电子病历
     *
     * @param sfbl
     */
    void updateSFCaseHistory(SFCaseHistoryForm sfbl, Integer hospitalCode, Integer dataSources);

    /**
     * 根据就诊流水号从mzys_new中获取电子病历
     *
     * @param jzlsh
     * @return
     */
    List<CaseHistory> queryDzblFromMzysNew(Integer jzlsh);

    /**
     * 根据就诊流水号从mzys中获取电子病历
     *
     * @param jzlsh
     * @return
     */
    List<CaseHistoryVO> queryDzblFromMzys(int jzlsh);

    /**
     * 根据患者id获取舒辅病历
     *
     * @param patId
     * @param hospitalCode
     * @param dataSources
     * @return
     */
    SFCaseHistory queryPatShuFu(String patId, Integer hospitalCode, Integer dataSources);

    /**
     * 查询患者当前主诉信息
     *
     * @param patId        患者编号
     * @param hospitalCode 医院编码
     * @return
     */
    List<PatChiefDTO> queryPatChief(int patId, int hospitalCode);

    /**
     * 加载本次病历
     *
     * @param receptionNo
     * @return
     */
    CaseHistory loadReviewCaseFromNow(Integer receptionNo);

    /**
     * 加载最新一条复诊病历
     *
     * @param regNo
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    CaseHistory loadReviewCaseFromNew(List<Long> regNo, Integer receptionNo, Integer hospitalCode);

    /**
     * 加载复诊病历 从Mzys获取
     *
     * @param outPatientNo 患者编号集合
     * @return
     */
    CaseHistory loadReviewCaseFromOld(String outPatientNo, Integer deptId, Integer receptionNo);

    /**
     * 根据就诊流水号+医院编码获取病历信息 从MZYS_NEW获取
     *
     * @param receptionNo
     * @param hospCode
     * @return
     */
    CaseHistory getCaseHistoryByReceptionNoFromNew(Integer receptionNo, Integer hospCode);

    /**
     * 根据就诊流水号查询病历记录，不包括病历内容
     *
     * @param receptionNo
     * @param hospCode
     * @return
     */
    CaseHistory getCaseHistoryByReceptionNoFromContent(String receptionNo, Integer hospCode, Integer dataSources);

    /**
     * 根据就诊流水号从mzys_new获取总分院电子病历
     *
     * @param receptionNo
     * @return
     */
    List<CaseHistory> getCaseHistoryByReceptionNoFromNewTwo(Integer receptionNo);

    /**
     * 根据就诊流水号+医院编码获取病历信息 从MZYS获取
     *
     * @param receptionNo
     * @param hospCode
     * @return
     */
    CaseHistory getCaseHistoryByReceptionNoFromOld(Integer receptionNo, Integer hospCode);


    /**
     * 根据病历主键id获取病历
     *
     * @param blId
     * @return
     */
    CaseHistory getCaseHistoryById(Integer blId);

    /**
     * 根据就诊流水号加载修改记录
     *
     * @param receptionNo
     * @param id
     * @return
     */
    List<CaseHistoryLog> getCaseHistoryLog(Integer receptionNo, Integer blNo, Integer id);

    /**
     * 根据主键修改电子病历数据
     *
     * @param updateCaseHistory
     * @return
     */
    int updateCaseHistory(CaseHistory updateCaseHistory);

    /**
     * 根据就诊流水号+医院编码修改电子病历
     *
     * @param updateCaseHistory
     * @return
     */
    int updateCaseHistoryByReception(CaseHistory updateCaseHistory, Integer hospitalCode, Integer dataSources);

    /**
     * 根据blId+医院编码从MZYSNEW获取病历数据
     *
     * @param blId
     * @param hospCode
     * @return
     */
    CaseHistory getCaseHistoryByBlIdFromMZYSNew(int blId, int hospCode);

    /**
     * 根据blId+医院编码从MZYS获取病历数据
     *
     * @param blId
     * @param hospCode
     * @return
     */
    CaseHistory getCaseHistoryByBlIdFromMZYS(int blId, int hospCode);

    /**
     * 根据身份证号码获取住院信息
     *
     * @param certificateNo
     * @param hospitalCode
     * @return
     */
    List<InHospitalInfo> queryHospitalInfos(String certificateNo, int hospitalCode);

    /**
     * 根据身份证号码获取患者所有出院信息
     *
     * @param patId
     * @return
     */
    List<IoTbInpatient> queryInpatientInfos(int patId, Integer status, String startTime, String endTime);

    /**
     * 根据住院流水号获取出院小结
     *
     * @param regNos
     * @param hospitalCode
     * @return
     */
    List<EmrBl> queryBlByRegNo(Set<String> regNos, int hospitalCode);

    /**
     * 获取总院具体病历信息
     *
     * @param regNo
     * @param bcDmno
     * @param recDate
     * @return
     */
    List<String> queryBlDetailGeneral(String regNo, String bcDmno, Date recDate);

    /**
     * 获取住院病历信息
     *
     * @param regNo
     * @param bcDmno
     * @param recDate
     * @return
     */
    List<String> getBlDetail(String regNo, String bcDmno, Date recDate, Integer hospitalCode);

    /**
     * 获取分院具体病历信息
     *
     * @param regNo
     * @param bcDmno
     * @param recDate
     * @return
     */
    List<String> queryBlDetail(String regNo, String bcDmno, Date recDate);

    /**
     * 根据患者编号查询患者历史病历
     *
     * @param hospitalCode
     * @param patIds
     * @param createTime
     * @param endTime
     */
    List<CaseHistory> queryBlHistory(Integer hospitalCode, Set<Integer> patIds, Date createTime, Date endTime);

    /**
     * 根据患者病历id获取患者病历信息
     *
     * @param id
     */
    CaseHistoryByIdResponse queryBlHistoryFromMzysNew(Integer id);

    /**
     * 根据患者病历id获取患者互联网病历信息
     *
     * @param blId
     * @return
     */
    CaseHistoryByIdResponse queryBlHistoryFromInternet(String blId);

    /**
     * 获取科室名称
     *
     * @param dept
     * @param hospitalCode
     * @return
     */
    String getDeptName(Integer dept, Integer hospitalCode);


    /**
     * 查询患者病历中现病历信息
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    String GetMedicalHistory(Long receptionNo, Integer hospitalCode);

    /**
     * 获取总院MZYS_TB_DZBL表病历数据
     *
     * @param guidRegNo
     * @param startDate
     * @param endDate
     * @return
     */
    List<QueryHistoryCaseInfoResult> getCaseHistoryFromZY(String guidRegNo, Date startDate, Date endDate);

    List<QueryHistoryCaseInfoResult> getCaseHistoryFromZYProc(Integer patId, Date startDate, Date endDate);

    /**
     * 获取总院MZYS_TB_DZBL_DATA偏移表病历数据
     *
     * @param guidRegNo
     * @param startDate
     * @param endDate
     * @return
     */
    List<QueryHistoryCaseInfoResult> getCaseHistoryFromZyOffset(String guidRegNo, Date startDate, Date endDate);

    /**
     * 根据guidRegNo到107..mzys获取分院电子病历数据
     *
     * @param guidRegNo
     * @return
     */
    List<QueryHistoryCaseInfoResult> getCaseHistoryFromFY(String guidRegNo, Date startDate, Date endDate);

    /**
     * 查询电子病历数据信息
     *
     * @param blId
     * @return
     */
    CaseHistoryByIdResponse queryBlHistoryFromZY(String blId);


    /**
     * 查询电子病历数据信息-迁移表
     *
     * @param blId
     * @return
     */
    CaseHistoryByIdResponse queryBlHistoryFromZyOffset(String blId);

    /**
     * 查询电子病历数据信息-分院
     *
     * @param blId
     * @return
     */
    CaseHistoryByIdResponse queryBlHistoryFromFY(String blId);

    /**
     * 获取医生病历信息
     *
     * @param doctorId
     * @param hospitalCode
     */
    List<CAAuthorizedData> caAuthorizedData(Integer doctorId, Integer hospitalCode);

    /**
     * 根据就诊流水号获取病历数据
     *
     * @param reception
     */
    List<DiagnoseRecordInfo> getCaseHistory(String reception, Integer hospitalCOde);

    /**
     * 根据就诊流水号获取患者病历内容返回数据
     *
     * @param receptionNo
     */
    BLContentDto queryNewPatBl(String receptionNo, Integer hospitalCode);

    /**
     * 根据就诊流水号获取患者病历内容返回数据
     *
     * @param receptionNo
     */
    BLContentDto queryOldPatBl(String receptionNo, Integer hospitalCode);

    /**
     * 【补写病历】查询审核病历列表
     *
     * @param param
     */
    List<CheckBlDto> queryCheckBls(CheckBlParam param);

    /**
     * 【补写病历】查询审核病历列表
     *
     * @param param
     */
    List<CheckBlDto> queryOldCheckBls(CheckBlParam param);

    /**
     * 根据病历卡号获取患者初诊日期
     *
     * @param hisCardNo
     */
    String getFirstVisitDate(String hisCardNo, Integer hospitalCode);

    /**
     * 根据病历Id,新老数据源，医院Id,修改病历处理标记为【已处理 clbj】
     *
     * @param id           病历Id
     * @param dataSources  数据源
     * @param hospitalCode 医院编码
     */
    void updateBlHandleState(String id, Integer dataSources, Integer hospitalCode);

    /**
     * 获取科室对照信息
     *
     * @return
     */
    List<DeptControl> GetDeptControl();

    /**
     * 查询病历操作日志
     *
     * @param param
     * @return
     */
    List<CaseHistoryLog> getCaseHistoryLogControl(SearchParam param);

    /**
     * 保存CA病历信息
     *
     * @param receptionNo
     * @param caId
     */
    void saveCaRecord(String receptionNo, Long caId);

    /**
     * 查询CA病历主键信息
     *
     * @param blId
     * @return
     */
    Integer getCaRecordId(Long blId);

    /**
     * 查询患者不挂号就诊，药理基地病历信息
     *
     * @param patientGuid
     * @param startDate
     * @param endDate
     * @return
     */
    List<QueryHistoryCaseInfoResult> getCaseHistoryByPatientGuid(String patientGuid, Date startDate, Date endDate);

    /**
     * 查询最老版本病历信息
     *
     * @param certificateNo
     * @param startDate
     * @param endDate
     * @param hospitalCode
     * @return
     */
    List<QueryHistoryCaseInfoResult> getOldestCaseHistory(String certificateNo, Date startDate, Date endDate, Integer hospitalCode);


    /**
     * 根据患者老版本病历详情信息
     *
     * @param id
     * @return
     */
    CaseHistoryByIdResponse getOldestCaseHistoryDetail(Integer id);

    /**
     * 查询最老版本病历信息
     *
     * @param patNo
     * @param startDate
     * @param endDate
     * @param hospitalCode
     * @return
     */
    List<QueryHistoryCaseInfoResult> getOldestCaseHistoryByPatNo(Integer patNo, Date startDate, Date endDate, Integer hospitalCode);

}
