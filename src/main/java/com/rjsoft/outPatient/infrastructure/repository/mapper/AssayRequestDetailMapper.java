package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.AssayRequestDetail;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * 化验申请明细
 * <AUTHOR>
public interface AssayRequestDetailMapper extends BaseMapper<AssayRequestDetail> {

    /**
     * 根据申请编号查询化验申请明细
     * @param requestNo
     * @param hospitalCode
     * @return
     */
    default List<AssayRequestDetail>getRequestDetailByRequestNo(Long requestNo,Integer hospitalCode) {
        AssayRequestDetail entity = new AssayRequestDetail();
        entity.setRequestNo(requestNo);
        entity.setHospitalCode(hospitalCode);
        List<AssayRequestDetail> requestDetailList = select(entity);
        for (AssayRequestDetail requestDetail : requestDetailList) {
            requestDetail.setOpFlag(1);
        }
        return requestDetailList;
    }

    /**
     * 根据化验申请编号查询申请明细
     * @param requestNo
     * @param hospitalCode
     * @return
     */
    default boolean delRequestDetailByRequestNo(Long requestNo,Integer hospitalCode) {
        AssayRequestDetail entity = new AssayRequestDetail();
        entity.setRequestNo(requestNo);
        entity.setHospitalCode(hospitalCode);
        return delete(entity) > 0;
    }

    /**
     * 批量新增
     * @param list
     * @return
     */
    int batchInsert(List<AssayRequestDetail> list);

    /**
     * 批量删除
     * @param list
     */
    void deleteByIds(List<AssayRequestDetail> list);

    /**
     * 批量修改
     * @param list
     */
    void batchUpdate(List<AssayRequestDetail> list);
}
