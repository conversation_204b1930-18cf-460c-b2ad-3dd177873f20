package com.rjsoft.outPatient.infrastructure.repository.service;

import com.github.pagehelper.PageInfo;
import com.rjsoft.outPatient.domain.execDept.dto.FeeCategoryExecDeptAddDto;
import com.rjsoft.outPatient.domain.execDept.dto.FeeCategoryExecDeptSelectDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubFeecategoryexedept;

import java.util.HashMap;
import java.util.List;

public interface MdmPubFeecategoryexedeptRepository {
    /**
     * 查询收费项目默认执行科室列表
     *
     * @param feeCategoryExecDeptDto
     * @return
     */
    PageInfo<MdmPubFeecategoryexedept> getFeeCategoryExecDeptItemList(FeeCategoryExecDeptSelectDto feeCategoryExecDeptDto);

    /**
     * 查询收费项目默认执行科室列表
     *
     * @param feeCategoryExecDeptAddDto
     * @return
     */
    List<HashMap<String,String>> getFeeCategoryExecDept(FeeCategoryExecDeptAddDto feeCategoryExecDeptAddDto, Integer id);

    /**
     * 新增收费类型默认执行科室列表
     *
     * @param feeCategoryExecDeptAddDto
     * @return
     */
    int addCategory(FeeCategoryExecDeptAddDto feeCategoryExecDeptAddDto);

    /**
     * 新增收费类型默认执行科室列表
     *
     * @param feeCategoryExecDeptAddDto
     * @return
     */
    int updateCategory(FeeCategoryExecDeptAddDto feeCategoryExecDeptAddDto);

    /**
     * 删除收费类型默认执行科室列表
     *
     * @param id
     * @return
     */
    int deleteFeeCategory(Integer id);

    /**
     * 当前时间段内，费用类型可执行科室
     * @param feeCategory
     * @param curTime
     * @param curDeptId
     * @param curHospitalId
     * @return
     */
    List<MdmPubFeecategoryexedept> getFeeCategoryExecDeptByTime(Integer feeCategory, String curTime, Integer curDeptId, Integer curHospitalId);

    /**
     * 当前时间段内，可执行科室列表
     * @param curTime
     * @param curDeptId
     * @param curHospitalId
     * @return
     */
    List<MdmPubFeecategoryexedept> getExecDeptList(String curTime, Integer curDeptId, Integer curHospitalId);
}
