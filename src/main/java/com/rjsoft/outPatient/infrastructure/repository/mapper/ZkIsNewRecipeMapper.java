package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ZkIsNewRecipe;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

public interface ZkIsNewRecipeMapper extends BaseMapper<ZkIsNewRecipe>, ExampleMapper<ZkIsNewRecipe> {

    /**
     * 查询招康合理用药处方记录
     *
     * @param recipeNo
     * @param hospitalCode
     * @return
     */
    default List<ZkIsNewRecipe> getZkIsNewRecipe(Integer recipeNo, Integer hospitalCode) {
        ZkIsNewRecipe zkIsNewRecipe = new ZkIsNewRecipe();
        zkIsNewRecipe.setRecipeNo(recipeNo);
        zkIsNewRecipe.setHospitalId(hospitalCode);
        return select(zkIsNewRecipe);
    }


}
