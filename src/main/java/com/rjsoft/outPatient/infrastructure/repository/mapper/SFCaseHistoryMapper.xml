<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.SFCaseHistoryMapper">


    <select id="getPatShuFu" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.SFCaseHistory">
        select top 1
        id,patientIdentifier as patId,
               generalInfo,
               chiefComplaint,
               hpi        as nowCaseHis,
               pastHistory,
               personalHistory,
               familyHistory,
               likely     as relatedInfo,
               likelyUrl  as relatedInfoUrl,
               hospitalCode,
               CreateTime as createTime,
               UpdateTime as updateTime
        from MZYS_TB_SFBL
        where patientIdentifier = #{patId} and hospitalCode = #{hospitalCode}
        order by CreateTime desc
    </select>

    <select id="getPatShuFuOld" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.SFCaseHistory">
        select top 1
        id, patientIdentifier patId,
               general<PERSON>n<PERSON>,
               chief<PERSON><PERSON>plaint,
               hpi             nowCase<PERSON>is,
               pastHistory,
               personalHistory,
               familyHistory,
               likely          relatedInfo,
               likelyUrl       relatedInfoUrl,
               hospitalCode,
               CreateTime      createTime,
               UpdateTime      updateTime
        from MZYS_TB_SFBL
        where patientIdentifier = #{patId} and hospitalCode = #{hospitalCode}
        and DATEDIFF(DAY, CreateTime, GETDATE()) = 0
        order by CreateTime desc
    </select>


</mapper>