package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import java.util.Date;

import java.util.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.param.RequestHead;
import com.rjsoft.common.param.SearchParam;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.caseHistory.Enum.CaseHistoryOpTypeEnum;
import com.rjsoft.outPatient.domain.caseHistory.dto.*;
import com.rjsoft.outPatient.domain.recipe.dto.HistoryReceptionDto;
import com.rjsoft.outPatient.infrastructure.hisInterface.msgCenter.service.MessageCenter;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.*;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.stream.Collectors;
import java.util.stream.Stream;


@AllArgsConstructor
@Service
public class MedicalRecordAuditRepositoryImpl implements MedicalRecordAuditRepository {

    CaseHistoryMapper caseHistoryMapper;
    MedicalRecordAuditMapper medicalRecordAuditMapper;
    MedicalRecordAuditRecordMapper medicalRecordAuditRecordMapper;
    RegisterListMapper registerListMapper;
    ReceptionRecordMapper receptionRecordMapper;
    CaseHistoryOpLogMapper caseHistoryOpLogMapper;
    CaseHistoryRepository caseHistoryRepository;
    MessageCenter messageCenter;


    @Override
    public MedicalRecordAudit getMedicalRecordAuditByBlId(String blId) {
        Weekend<MedicalRecordAudit> weekend = new Weekend<>(MedicalRecordAudit.class);
        Example.Criteria criteria = weekend.createCriteria();
        criteria.andEqualTo("blId", blId);
        MedicalRecordAudit medicalRecordAudit = medicalRecordAuditMapper.selectOneByExample(weekend);
        return medicalRecordAudit;
    }


    @Override
    @DatabaseAnnotation
    public List<MedicalRecordAudit> getDeptMedicalRecordAuditList(SearchParam param) {
        Integer hospitalCode = Converter.toInt32(param.getHospitalCode());

        Integer listFlag = Converter.toInt32(param.getKeys().get("listFlag"));
        String deptId = Converter.toString(param.getKeys().get("deptId"));
        String doctorId = Converter.toString(param.getKeys().get("doctorId"));
        String auditFlag = Converter.toString(param.getKeys().get("auditFlag"));
        String auditState = Converter.toString(param.getKeys().get("auditState"));
        String hisCardNo = Converter.toString(param.getKeys().get("hisCardNo"));
        String patName = Converter.toString(param.getKeys().get("patName"));
        String visitFlag = Converter.toString(param.getKeys().get("visitFlag"));
        String sortField = Converter.toString(param.getKeys().get("sortField"));

        if (!StringUtils.isEmpty(sortField)) {
            //如果排序字段不为空，根据排序字段信息关联特殊处理
            if (sortField.startsWith("regNo")) {
                sortField = sortField.replace("regNo", "b.ghlsh");
            }
            if (sortField.startsWith("hisCardNo")) {
                sortField = sortField.replace("hisCardNo", "a.blkh");
            }
            if (sortField.startsWith("receptionDate")) {
                sortField = sortField.replace("receptionDate", "b.sckzrq");
            }
            if (sortField.startsWith("caseHistoryDoctorName")) {
                sortField = sortField.replace("caseHistoryDoctorName", "b.ysbm");
            }
            if (sortField.startsWith("receptionDeptName")) {
                sortField = sortField.replace("receptionDeptName", "b.ksbm");
            }
        }

        //设置结束时间
        Calendar calendar = new GregorianCalendar(TimeZone.getTimeZone("GMT+8"));
        calendar.setTime(Optional.ofNullable(param.getEndTime()).orElse(new Date()));
        calendar.add(Calendar.DATE, 1);
        Date endTime = calendar.getTime();

        //设置开始时间
        Calendar calendar1 = new GregorianCalendar(TimeZone.getTimeZone("GMT+8"));
        calendar1.setTime(Converter.toDate("1970-01-01", "yyyy-MM-dd"));
        Date startTime = Optional.ofNullable(param.getStartTime()).orElse(calendar1.getTime());

        if (auditState == null) {
            PageHelper.startPage(param.getPageNum(), param.getPageSize(), sortField);
        }
        List<MedicalRecordAudit> list = medicalRecordAuditMapper.getDeptMedicalRecordAuditList(startTime, endTime, hospitalCode, listFlag, deptId, doctorId, auditFlag, auditState, hisCardNo, patName, visitFlag, sortField);
        PageInfo pageInfo = new PageInfo<>(list);
        param.setTotalCount(Converter.toInt32(pageInfo.getTotal()));
        return list;
    }

    @Override
    @DatabaseAnnotation
    public List<MedicalRecordAudit> getSpotCheckList(SearchParam param) {
        Integer hospitalCode = Converter.toInt32(param.getHospitalCode());
        String deptId = Converter.toString(param.getKeys().get("deptId"));
        String sortField = Converter.toString(param.getKeys().get("sortField"));

        if (!StringUtils.isEmpty(sortField)) {
            //如果排序字段不为空，根据排序字段信息关联特殊处理
            if (sortField.startsWith("regNo")) {
                sortField = sortField.replace("regNo", "b.ghlsh");
            }
            if (sortField.startsWith("hisCardNo")) {
                sortField = sortField.replace("hisCardNo", "a.blkh");
            }
            if (sortField.startsWith("receptionDate")) {
                sortField = sortField.replace("receptionDate", "b.sckzrq");
            }
            if (sortField.startsWith("caseHistoryDoctorName")) {
                sortField = sortField.replace("caseHistoryDoctorName", "b.ysbm");
            }
            if (sortField.startsWith("receptionDeptName")) {
                sortField = sortField.replace("receptionDeptName", "b.ksbm");
            }
        }

        //设置结束时间
        Calendar calendar = new GregorianCalendar(TimeZone.getTimeZone("GMT+8"));
        calendar.setTime(Optional.ofNullable(param.getEndTime()).orElse(new Date()));
        calendar.add(Calendar.DATE, 1);
        Date endTime = calendar.getTime();

        //设置开始时间
        Calendar calendar1 = new GregorianCalendar(TimeZone.getTimeZone("GMT+8"));
        calendar1.setTime(Converter.toDate("1970-01-01", "yyyy-MM-dd"));
        Date startTime = Optional.ofNullable(param.getStartTime()).orElse(calendar1.getTime());

        PageHelper.startPage(param.getPageNum(), param.getPageSize(), sortField);
        List<MedicalRecordAudit> list = medicalRecordAuditMapper.getSpotCheckList(startTime, endTime, hospitalCode, deptId, sortField);

        PageInfo pageInfo = new PageInfo<>(list);
        param.setTotalCount(Converter.toInt32(pageInfo.getTotal()));
        return list;
    }

    @Override
    @DatabaseAnnotation
    public List<MedicalRecordAudit> getCheckList(SearchParam param) {
        Integer hospitalCode = Converter.toInt32(param.getHospitalCode());
        String deptId = Converter.toString(param.getKeys().get("deptId"));
        String doctorId = Converter.toString(param.getKeys().get("doctorId"));
        String auditState = Converter.toString(param.getKeys().get("auditState"));
        String sortField = Converter.toString(param.getKeys().get("sortField"));

        if (!StringUtils.isEmpty(sortField)) {
            //如果排序字段不为空，根据排序字段信息关联特殊处理
            if (sortField.startsWith("regNo")) {
                sortField = sortField.replace("regNo", "b.ghlsh");
            }
            if (sortField.startsWith("hisCardNo")) {
                sortField = sortField.replace("hisCardNo", "a.blkh");
            }
            if (sortField.startsWith("receptionDate")) {
                sortField = sortField.replace("receptionDate", "b.sckzrq");
            }
            if (sortField.startsWith("caseHistoryDoctorName")) {
                sortField = sortField.replace("caseHistoryDoctorName", "b.ysbm");
            }
            if (sortField.startsWith("receptionDeptName")) {
                sortField = sortField.replace("receptionDeptName", "b.ksbm");
            }
        }

        //设置结束时间
        Calendar calendar = new GregorianCalendar(TimeZone.getTimeZone("GMT+8"));
        calendar.setTime(Optional.ofNullable(param.getEndTime()).orElse(new Date()));
        calendar.add(Calendar.DATE, 1);
        Date endTime = calendar.getTime();

        //设置开始时间
        Calendar calendar1 = new GregorianCalendar(TimeZone.getTimeZone("GMT+8"));
        calendar1.setTime(Converter.toDate("1970-01-01", "yyyy-MM-dd"));
        Date startTime = Optional.ofNullable(param.getStartTime()).orElse(calendar1.getTime());

        if (auditState == null) {
            PageHelper.startPage(param.getPageNum(), param.getPageSize(), sortField);
        }
        List<MedicalRecordAudit> list = medicalRecordAuditMapper.getCheckList(startTime, endTime, hospitalCode, deptId, doctorId, auditState, sortField);
        PageInfo pageInfo = new PageInfo<>(list);
        param.setTotalCount(Converter.toInt32(pageInfo.getTotal()));
        return list;
    }

    @Override
    @DatabaseAnnotation
    public List<MedicalRecordAuditRecord> getMedicalRecordAuditHistory(Integer medicalRecordAuditId, Integer hospitalCode) {
        MedicalRecordAuditRecord medicalRecordAuditRecord = new MedicalRecordAuditRecord();
        medicalRecordAuditRecord.setCheckId(medicalRecordAuditId);
        medicalRecordAuditRecord.setCheckSort(1);
        medicalRecordAuditRecord.setHospitalCode(hospitalCode);
        return medicalRecordAuditRecordMapper.select(medicalRecordAuditRecord);
    }

    @Override
    @DatabaseAnnotation
    public boolean saveMedicalRecordAudit(MedicalRecordAuditRecord medicalRecordAuditRecord, Integer blId) {
        if (blId == null) {
            return false;
        }

        //查询电子病历信息
        Weekend<CaseHistory> historyWeekend = new Weekend<>(CaseHistory.class);
        historyWeekend.weekendCriteria().andEqualTo(CaseHistory::getBlId, blId);
        CaseHistory caseHistory = caseHistoryMapper.selectOneByExample(historyWeekend);
        if (caseHistory == null) {
            return false;
        }

        //查询审核信息
        Weekend<MedicalRecordAudit> weekend = new Weekend<>(MedicalRecordAudit.class);
        weekend.weekendCriteria().andEqualTo(MedicalRecordAudit::getBlId, blId);
        MedicalRecordAudit medicalRecordAudit = medicalRecordAuditMapper.selectOneByExample(weekend);

        //保存审核信息
        if (medicalRecordAudit == null) {
            medicalRecordAudit = new MedicalRecordAudit();
            medicalRecordAudit.setRegNo(Converter.toInt32(caseHistory.getRegNo()));
            medicalRecordAudit.setPatName(caseHistory.getPatientName());
            medicalRecordAudit.setOutPatientNo(caseHistory.getBlCardNo());
            medicalRecordAudit.setBlId(Converter.toInt32(caseHistory.getBlId()));
            medicalRecordAudit.setCheckSort(medicalRecordAuditRecord.getCheckSort());
            medicalRecordAudit.setCreDoctorId(caseHistory.getDoctorId());
            medicalRecordAudit.setCreTime(new Date());
            medicalRecordAudit.setHospitalCode(caseHistory.getHospitalCode());
            medicalRecordAuditMapper.insert(medicalRecordAudit);
        }

        //保存审核记录信息
        medicalRecordAuditRecord.setCheckId(medicalRecordAudit.getId());
        medicalRecordAuditRecord.setCheckType(1);

        //审核不通过需保存病历内容
        if (medicalRecordAuditRecord.getCheckState().equals(0)) {
            medicalRecordAuditRecord.setBlXml(caseHistory.getBlXml());
            medicalRecordAuditRecord.setBlHtml(caseHistory.getBlHtml());
        }
        medicalRecordAuditRecordMapper.insert(medicalRecordAuditRecord);

        //病历表记录审核标记
        caseHistory.setAuditFlag(1);
        caseHistoryMapper.updateByPrimaryKeySelective(caseHistory);

        //添加操作日志
        CaseHistoryLog caseHistoryLog = new CaseHistoryLog();
        CaseHistoryLog entity = caseHistoryLog.CreateEntity(caseHistory);

        //科室审核
        if (medicalRecordAuditRecord.getCheckSort().equals(1)) {
            if (medicalRecordAuditRecord.getCheckState().equals(0)) {
                entity.setOpType(CaseHistoryOpTypeEnum.CHECK_FAIL.getCode());
            } else if (medicalRecordAuditRecord.getCheckState().equals(1)) {
                entity.setOpType(CaseHistoryOpTypeEnum.CHECK_PASS.getCode());
            }
        }
        //抽查审核
        if (medicalRecordAuditRecord.getCheckSort().equals(2)) {
            if (medicalRecordAuditRecord.getCheckState().equals(0)) {
                entity.setOpType(CaseHistoryOpTypeEnum.SPOT_CHECK_FAIL.getCode());
            } else if (medicalRecordAuditRecord.getCheckState().equals(1)) {
                entity.setOpType(CaseHistoryOpTypeEnum.SPOT_CHECK_PASS.getCode());
            }
        }
        entity.setOpDoctorId(Converter.toInt32(medicalRecordAuditRecord.getCheckDoctorId()));
        entity.setOpDoctorName(medicalRecordAuditRecord.getCheckDoctorName());
        caseHistoryRepository.saveCaseHistoryLog(entity, entity.getHospitalCode(), 1, entity.getBlNo());


        //发送消息
        String title = "";
        String content = "";
        if (medicalRecordAuditRecord.getCheckSort().equals(1) && medicalRecordAuditRecord.getCheckState().equals(0)) {
            title = "门诊病历审核结果";
            content = "您有门诊病历（患者姓名：[" + caseHistory.getPatientName().trim() + "]）未通过科室审核，审核意见为[" + medicalRecordAuditRecord.getCheckResult() + "]，请至门诊病历质控-审核结果页面查看详情";
        } else if (medicalRecordAuditRecord.getCheckSort().equals(2)) {
            title = "门诊办抽查结果";
            content = "您有门诊病历（患者姓名：[" + caseHistory.getPatientName().trim() + "]）抽查结果为不合格，存在的问题为[" + medicalRecordAuditRecord.getCheckResult() + "]，请至门诊病历质控-审核结果页面查看详情";
        }
        messageCenter.sendMessage(600, title, content, 20, caseHistory.getDoctorId().toString());
        
        return true;
    }


    @Override
    @DatabaseAnnotation
    public void addMedicalRecordAudit(Map<String, Object> params) {
        String jsonString = JSON.toJSONString(params.get("checkList"));
        List<MedicalRecordAuditDto> checkList = JSONObject.parseArray(jsonString, MedicalRecordAuditDto.class);
        if (checkList.size() <= 0) {
            return;
        }

        Integer checkDoctor = Converter.toInt32(params.get("checkDoctor"));
        String checkDoctorName = Converter.toString(params.get("checkDoctorName"));
        String deptId = Converter.toString(params.get("deptId"));

        //设置结束时间
        Calendar calendar = new GregorianCalendar(TimeZone.getTimeZone("GMT+8"));
        calendar.setTime(Optional.ofNullable(Converter.toDate(params.get("endTime"), "yyyy-MM-dd")).orElse(new Date()));
        calendar.add(Calendar.DATE, 1);
        Date endTime = calendar.getTime();

        //设置开始时间
        Calendar calendar1 = new GregorianCalendar(TimeZone.getTimeZone("GMT+8"));
        calendar1.setTime(Converter.toDate("1970-01-01", "yyyy-MM-dd"));
        Date startTime = Optional.ofNullable(Converter.toDate(params.get("startTime"), "yyyy-MM-dd")).orElse(calendar1.getTime());

        // 查询电子病历信息
        List<CaseHistory> caseHistories = caseHistoryMapper.getCaseHistoryByTime(startTime, endTime, checkList.get(0).getHospitalCode(), deptId);

        //处理抽查记录
        checkList.forEach(p -> {
            if (p.getMedicalRecordAuditId() != null) {
                MedicalRecordAudit medicalRecordAudit = medicalRecordAuditMapper.selectByPrimaryKey(p.getMedicalRecordAuditId());
                medicalRecordAudit.setCheckSort(2);
                medicalRecordAuditMapper.updateByPrimaryKey(medicalRecordAudit);
            } else {
                MedicalRecordAudit medicalRecordAudit = new MedicalRecordAudit();
                medicalRecordAudit.setRegNo(Converter.toInt32(p.getRegNo()));
                medicalRecordAudit.setPatName(p.getPatName());
                medicalRecordAudit.setOutPatientNo(p.getHisCardNo());
                medicalRecordAudit.setBlId(p.getBlId());
                medicalRecordAudit.setCheckSort(2);
                medicalRecordAudit.setCreDoctorId(checkDoctor);
                medicalRecordAudit.setCreTime(new Date());
                medicalRecordAudit.setHospitalCode(p.getHospitalCode());
                medicalRecordAuditMapper.insert(medicalRecordAudit);
            }

            //记录操作日志
            Weekend<CaseHistory> historyWeekend = new Weekend<>(CaseHistory.class);
            historyWeekend.weekendCriteria().andEqualTo(CaseHistory::getBlId, p.getBlId());
            CaseHistory caseHistory = caseHistoryMapper.selectOneByExample(historyWeekend);
            if (caseHistory != null) {
                CaseHistoryLog caseHistoryLog = new CaseHistoryLog();
                CaseHistoryLog entity = caseHistoryLog.CreateEntity(caseHistory);
                entity.setOpType(CaseHistoryOpTypeEnum.SPOT_CHECK.getCode());
                entity.setOpDoctorId(checkDoctor);
                entity.setOpDoctorName(checkDoctorName);
                caseHistoryRepository.saveCaseHistoryLog(entity, entity.getHospitalCode(), 1, entity.getBlNo());
            }
        });

        List<Integer> blIds = checkList.stream().map(MedicalRecordAuditDto::getBlId).distinct().collect(Collectors.toList());
        if (caseHistories.size() > 0) {
            caseHistories.removeIf(p -> blIds.contains(Converter.toInt32(p.getBlId())));

            caseHistories.forEach(p -> {
                Weekend<MedicalRecordAudit> weekend = new Weekend<>(MedicalRecordAudit.class);
                weekend.weekendCriteria().andEqualTo(MedicalRecordAudit::getBlId, p.getBlId());
                MedicalRecordAudit medicalAudit = medicalRecordAuditMapper.selectOneByExample(weekend);
                if (medicalAudit != null) {
                    medicalAudit.setCheckSort(1);
                    medicalRecordAuditMapper.updateByPrimaryKey(medicalAudit);
                } else {
                    MedicalRecordAudit medicalRecordAudit = new MedicalRecordAudit();
                    medicalRecordAudit.setRegNo(Integer.parseInt(p.getRegNo()));
                    medicalRecordAudit.setPatName(p.getPatientName());
                    medicalRecordAudit.setOutPatientNo(p.getBlCardNo());
                    medicalRecordAudit.setBlId(Integer.parseInt(p.getBlId()));
                    medicalRecordAudit.setCheckSort(1);
                    medicalRecordAudit.setCreDoctorId(checkDoctor);
                    medicalRecordAudit.setCreTime(new Date());
                    medicalRecordAudit.setHospitalCode(p.getHospitalCode());
                    medicalRecordAuditMapper.insert(medicalRecordAudit);

                    MedicalRecordAuditRecord medicalRecordAuditRecord = new MedicalRecordAuditRecord();
                    medicalRecordAuditRecord.setCheckId(medicalRecordAudit.getId());
                    medicalRecordAuditRecord.setCheckSort(2);
                    medicalRecordAuditRecord.setCheckType(2);
                    medicalRecordAuditRecord.setCheckState(1);
                    medicalRecordAuditRecord.setCheckResult("自动通过");
                    medicalRecordAuditRecord.setCheckDoctorId(Converter.toString(checkDoctor));
                    medicalRecordAuditRecord.setCheckDoctorName(checkDoctorName);
                    medicalRecordAuditRecord.setCheckDate(new Date());
                    medicalRecordAuditRecord.setHospitalCode(p.getHospitalCode());
                    medicalRecordAuditRecord.setReviseFlag(2);
                    medicalRecordAuditRecordMapper.insert(medicalRecordAuditRecord);
                }
            });
        }

        //记录病历操作日志
        if (caseHistories.size() > 0) {
            caseHistories.forEach(p -> {
                CaseHistoryLog caseHistoryLog = new CaseHistoryLog();
                CaseHistoryLog entity = caseHistoryLog.CreateEntity(p);
                entity.setOpType(CaseHistoryOpTypeEnum.SPOT_CHECK_AUTO.getCode());
                entity.setOpDoctorId(checkDoctor);
                entity.setOpDoctorName(checkDoctorName);
                caseHistoryRepository.saveCaseHistoryLog(entity, entity.getHospitalCode(), 1, entity.getBlNo());
            });
        }
    }

    @Override
    @DatabaseAnnotation
    public MedicalRecordAuditRecord getCheckCaseInfo(Integer medicalRecordAuditId, Integer hospitalCode) {
        Weekend<MedicalRecordAuditRecord> weekend = new Weekend<>(MedicalRecordAuditRecord.class);
        weekend.weekendCriteria().andEqualTo(MedicalRecordAuditRecord::getCheckId, medicalRecordAuditId)
                .andEqualTo(MedicalRecordAuditRecord::getHospitalCode, hospitalCode)
                .andEqualTo(MedicalRecordAuditRecord::getCheckState, 0)
                .andEqualTo(MedicalRecordAuditRecord::getReviseFlag, 0)
                .andEqualTo(MedicalRecordAuditRecord::getCheckSort, 1);
        return medicalRecordAuditRecordMapper.selectOneByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public boolean updateMedicalRecordAudit(MedicalRecordAuditRecord medicalRecordAuditRecord) {
        boolean success = true;
        boolean hasData = medicalRecordAuditRecordMapper.existsWithPrimaryKey(medicalRecordAuditRecord);
        if (hasData) {
            success = medicalRecordAuditRecordMapper.updateByPrimaryKeySelective(medicalRecordAuditRecord) > 0;
        }
        return success;
    }

    @Override
    @DatabaseAnnotation
    public boolean saveCaseHistoryLogByType(Integer blId, Integer doctorId, String doctorName, Integer hospitalCode) {
        Weekend<CaseHistory> historyWeekend = new Weekend<>(CaseHistory.class);
        historyWeekend.weekendCriteria().andEqualTo(CaseHistory::getBlId, blId);
        CaseHistory caseHistory = caseHistoryMapper.selectOneByExample(historyWeekend);
        if (caseHistory != null) {
            CaseHistoryLog caseHistoryLog = new CaseHistoryLog();
            CaseHistoryLog entity = caseHistoryLog.CreateEntity(caseHistory);
            entity.setOpType(CaseHistoryOpTypeEnum.READ.getCode());
            entity.setOpDoctorId(doctorId);
            entity.setOpDoctorName(doctorName);
            caseHistoryRepository.saveCaseHistoryLog(entity, entity.getHospitalCode(), 1, entity.getBlNo());
        }
        return true;
    }


}
