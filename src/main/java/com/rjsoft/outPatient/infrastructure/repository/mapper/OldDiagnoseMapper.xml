<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.OldDiagnoseMapper">
    <select id="getOldDiagnoseInfo" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.OldDiagnose">
        select top 1 a.*
        from ${tableName} a
        inner join ${tableNameRecord} b on b.jzlsh = a.jzlsh
        where b.hzbh in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by fhbz desc, a.cjrq desc;
    </select>
    <!--    <select id="getOldRegNoString" resultType="java.lang.String">-->
    <!--        select ghlsh regNo,item reception-->
    <!--        from MZYS_TB_KZJL-->
    <!--        where jzlsh in-->
    <!--        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">-->
    <!--            #{item}-->
    <!--        </foreach>-->
    <!--    </select>-->
    <!--    <select id="getOldRegNo" resultType="java.lang.Integer">-->
    <!--        select RegNo invalidRegNo,item reception-->
    <!--        from GUID_GhRegList-->
    <!--        where GuidReg in-->
    <!--        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">-->
    <!--            #{item}-->
    <!--        </foreach>-->

    <!--    </select>-->


    <resultMap id="HistoryDiagnoseResult"
               type="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.HistoryDiagnoseResult">
        <result property="guidReg" column="guidReg"/>
        <result property="visitTime" column="visitTime"/>
        <result property="doctorId" column="doctorId"/>
        <result property="receptionNo" column="receptionNo"/>
        <collection property="diagnoseList" column="receptionNo"
                    ofType="com.rjsoft.outPatient.domain.admissionApplicationForm.vo.HistoryDiagnoseVO">
            <result property="receptionNo" column="receptionNo"/>
            <result property="creDoctor" column="cjr"/>
            <result property="creTime" column="cjrq"/>
            <result property="uptDoctor" column="xgr"/>
            <result property="uptTime" column="xgrq"/>
            <result property="diagnoseCode" column="zdbm"/>
            <result property="diagnoseMark" column="zdbj"/>
            <result property="isReview" column="fhbz"/>
            <result property="reviewDoctor" column="fhys"/>
            <result property="diagnoseType" column="zdlx"/>
        </collection>
    </resultMap>

    <select id="getAllFromOldData" resultMap="HistoryDiagnoseResult">
        select ghlsh guidReg,  zdlsh, z.jzlsh receptionNo, bbh, cjr, cjrq, xgr, xgrq, zdbm, zdbj, fhbz, fhys, zdlx, k.sckzrq visitTime,
        k.zhkzys doctorId
        from MZYS_TV_MZYSZD z (nolock)
        left join MZYS_TV_KZJL k (nolock) on k.jzlsh = z.jzlsh
        where ghlsh in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>