<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.PubPatientInfoMapper">
    <update id="updateByPatNo" >
        update Tbt_PubPatientInfo set AddrH = #{addr} where PatNo = #{patNo}
    </update>

    <select id="getByPatNo" resultType="java.lang.String">
        select AddrH from Tbt_PubPatientInfo (nolock) where PatNo = #{patNo}
    </select>

    <select id="queryRelationPatientInfo" resultType="com.rjsoft.outPatient.domain.patient.vo.RelationPatRespVO">
        select * from (
        select b.EMPIId,b.<PERSON> as PatId,c.<PERSON>,c.CertificateType,c.<PERSON>,c.<PERSON>,999 as age,c<PERSON><PERSON> as his<PERSON><PERSON><PERSON><PERSON>,
        c.<PERSON>,d.<PERSON>,d.<PERSON>ity,d.Education,d.Occupation,d.<PERSON> as phone,d.birthPlace,d.Address,d.ContactsName ,d.ContactsRelationShipPhone,c.HospitalCode,
        null as CardNo
        from Patient_Tb_EMPIToPatient a
        inner join Patient_Tb_EMPIToPatient b on a.EMPIId = b .EMPIId and b.Status = 1
        inner join Reg_Tb_PatientList c on try_cast(b.IdentityNo as bigint) = c.PatID
        inner join Reg_Tb_PatientDetl d on c.PatID = d.PatID
        --left join Reg_Tb_PatientCard e on e.PatId = c.PatID and e.CardType = 0 and e.Status = 0
        where a.Status = 1 and a.IdentityNo = #{patId} and a.IdentityType = 'HIS.PATIENTID'
        ) a
        where PatId &lt;&gt; #{patId}
    </select>

    <select id="queryRelationPatientEmpiId" resultType="java.lang.Integer">
        select DISTINCT b.EMPIId EMPIId
        from Patient_Tb_EMPIToPatient a (nolock)
                 inner join Patient_Tb_EMPIToPatient b (nolock) on a.EMPIId = b .EMPIId and b.Status = 1
                 inner join Reg_Tb_PatientList c (nolock) on b.IdentityNo = CONVERT(varchar(20),c.PatID)
        where a.Status = 1 and a.IdentityType = 'HIS.PATIENTID'
          and c.CertificateNo = #{certificateNo}
          and c.Sex = #{patSex}
          and c.PatName = #{patName}
    </select>

    <select id="getPatientAllPatIds" resultType="java.lang.Integer">
        select a.IdentityNo as PatId
        from Patient_Tb_EMPIToPatient a(nolock)
        where a.Status = 1 and a.IdentityType = 'HIS.PATIENTID'
        and exists
            (
                select 1
                from Patient_Tb_EMPIToPatient b(nolock)
                where b.EMPIId = a.EMPIId
                  and b.Status = 1
                  and b.IdentityNo = #{patId}
            )
    </select>
</mapper>