package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.PatientDetail;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public interface PatientDetailMapper extends BaseMapper<PatientDetail>, ExampleMapper<PatientDetail> {

    /**
     * 查询患者补充信息
     *
     * @param patId
     * @param hospitalCode
     * @return
     */
    default PatientDetail getPatientDetail(Integer patId, Integer hospitalCode) {
        PatientDetail patientDetail = new PatientDetail();
        patientDetail.setPatID(patId);
//        patientDetail.setHospitalCode(hospitalCode);
        return selectOne(patientDetail);
    }

    /**
     * 查询患者补充信息
     *
     * @param patIds
     * @param hospitalCode
     * @return
     */
    default List<PatientDetail> queryPatientDetailByIds(List<Integer> patIds, Integer hospitalCode) {
        Weekend<PatientDetail> weekend = new Weekend<>(PatientDetail.class);
        WeekendCriteria<PatientDetail, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(PatientDetail::getPatID, patIds);
        //weekendCriteria.andEqualTo(PatientDetail::getHospitalCode, hospitalCode);
        return selectByExample(weekend);
    }


    default int judgeJudicialPat(Integer patId, Integer hospCode) {
        Weekend<PatientDetail> weekend = new Weekend<>(PatientDetail.class);
        WeekendCriteria<PatientDetail, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(PatientDetail::getPatID, patId);
//        weekendCriteria.andEqualTo(PatientDetail::getHospitalCode, hospCode);
        List<Integer> patFlags = Stream.of(1, 2).collect(Collectors.toList());
        weekendCriteria.andIn(PatientDetail::getPatFlag, patFlags);
        return selectCountByExample(weekend);
    }
}
