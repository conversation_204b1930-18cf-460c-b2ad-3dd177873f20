package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeDetailView;
import com.rjsoft.outPatient.infrastructure.repository.entity.OutpatientInvoice;
import com.rjsoft.outPatient.infrastructure.repository.entity.OutpatientInvoiceView;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 门诊发票
 * <AUTHOR>
public interface OutpatientInvoiceMapper extends Mapper<OutpatientInvoice> {
    default OutpatientInvoice getInvoice(Long regNo,Integer hospitalCode){
        OutpatientInvoice invoice = new OutpatientInvoice();
        invoice.setRegNo(regNo);
        invoice.setHospitalCode(Converter.toInt64(hospitalCode));
        invoice.setFlag(Converter.toInt64(1));
        invoice.setRemarkAI("云");
        return selectOne(invoice);
    }

    List<OutpatientInvoiceView> queryOutpatientInvoiceByRegNoList(@Param("regNoList") List<Long> regNoList, @Param("hospitalCode") Integer hospitalCode);

    List<ChargeDetailView> queryChargeDetailByChargeNoList(@Param("chargeNoList") List<Long> chargeNoList, @Param("hospitalCode") Integer hospitalCode);

}
