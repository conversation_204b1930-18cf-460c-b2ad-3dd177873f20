package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.OftenUseItem;
import com.rjsoft.outPatient.infrastructure.repository.entity.OftenUseItemUsage;
import com.rjsoft.outPatient.infrastructure.repository.mapper.OftenUseItemMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.OftenUseItemUsageMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.OftenUseItemRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

/**
 * 常用项目
 * <AUTHOR>
@Service
@AllArgsConstructor
public class OftenUseItemRepositoryImpl implements OftenUseItemRepository {
    OftenUseItemMapper oftenUseItemMapper;
    OftenUseItemUsageMapper oftenUseItemUsageMapper;


    @Override
    @DatabaseAnnotation
    public OftenUseItem getOftenUseItemByItemCode(Integer itemCode, Integer doctorId, Integer hospitalCode) {
        OftenUseItem entity = new OftenUseItem();
        entity.setItemCode(itemCode);
        entity.setDoctorId(doctorId);
        entity.setHospitalCode(hospitalCode);
        return oftenUseItemMapper.selectOne(entity);
    }

    @Override
    @DatabaseAnnotation
    public OftenUseItemUsage getOftenUseItemUsageByItemCode(Integer itemCode, Integer doctorId, Integer hospitalCode) {
        OftenUseItemUsage entity = new OftenUseItemUsage();
        entity.setItemCode(itemCode);
        entity.setDoctorId(doctorId);
        entity.setHospitalCode(hospitalCode);
        List<OftenUseItemUsage> list=oftenUseItemUsageMapper.select(entity);
        if(list.stream().count()==0){
            return null;
        }
        return list.get(0);
    }

    @Override
    @DatabaseAnnotation
    @Transactional(rollbackFor = {Exception.class})
    public boolean saveOftenUseItem(OftenUseItem oftenUseItem) {
        boolean success = oftenUseItemMapper.saveOftenUseItem(oftenUseItem);
        if (!success) {
            throw new RuntimeException("保存常用项目失败!");
        }
        if (oftenUseItem.getItemUsage() != null) {
            oftenUseItem.getItemUsage().setId(oftenUseItem.getId());
            success = oftenUseItemUsageMapper.saveOftenUseItemUsage(oftenUseItem.getItemUsage());
            if (!success) {
                throw new RuntimeException("保存常用项目用法失败!");
            }
        }
        return true;
    }

    @Override
    @DatabaseAnnotation
    public List<OftenUseItem> getOftenUseItemByDoctorId(Integer doctorId, List<String> itemCategory, Integer hospitalCode, Integer deptId) {
        final Weekend<OftenUseItem> weekend = new Weekend<>(OftenUseItem.class);
        final WeekendCriteria<OftenUseItem, Object> criteria = weekend.weekendCriteria();

        criteria.andEqualTo(OftenUseItem::getDoctorId, doctorId)
                .andEqualTo(OftenUseItem::getDeptId, deptId)
                .andEqualTo(OftenUseItem::getHospitalCode, hospitalCode);
        if (itemCategory != null) {
            criteria.andIn(OftenUseItem::getItemCategory, itemCategory);
        }

        return oftenUseItemMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<OftenUseItemUsage> getOftenUseItemUsageByDoctorId(Integer doctorId, Integer hospitalCode) {
        OftenUseItemUsage entity = new OftenUseItemUsage();
        entity.setDoctorId(doctorId);
        entity.setHospitalCode(hospitalCode);
        List<OftenUseItemUsage> list = oftenUseItemUsageMapper.select(entity);
        return list;
    }

    @Override
    @DatabaseAnnotation
    public void deleteOftenUseItemUsageById(Integer id, Integer hospitalCode) {
        OftenUseItemUsage entity = new OftenUseItemUsage();
        entity.setId(id);
        entity.setHospitalCode(hospitalCode);
        oftenUseItemUsageMapper.delete(entity);
    }

    @Override
    @DatabaseAnnotation
    public void deleteOftenUseItemById(Integer id, Integer hospitalCode) {
        OftenUseItem entity = new OftenUseItem();
        entity.setId(id);
        entity.setHospitalCode(hospitalCode);
        oftenUseItemMapper.delete(entity);
    }
}