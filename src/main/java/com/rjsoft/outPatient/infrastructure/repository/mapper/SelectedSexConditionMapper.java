package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.SelectedCondition;
import com.rjsoft.outPatient.infrastructure.repository.entity.SelectedSexCondition;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

public interface SelectedSexConditionMapper extends BaseMapper<SelectedSexCondition> {


    /**
     * 查询申请单对照性别条件
     *
     * @return
     */
    default List<SelectedSexCondition> getSelectedSexCondition() {
        SelectedSexCondition selectedSexCondition = new SelectedSexCondition();
        return select(selectedSexCondition);
    }

}
