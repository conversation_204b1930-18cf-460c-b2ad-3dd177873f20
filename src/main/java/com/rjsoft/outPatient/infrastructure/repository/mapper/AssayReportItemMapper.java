package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.AssayReportItem;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;


public interface AssayReportItemMapper extends BaseMapper<AssayReportItem>, ExampleMapper<AssayReportItem> {


    /**
     * 获取收费项目对应标本信息
     *
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    default List<AssayReportItem> getAssayReportItemByItemCode(Integer itemCode, Integer hospitalCode) {
        AssayReportItem entity = new AssayReportItem();
        entity.setItemCode(itemCode);
        entity.setHospitalCode(hospitalCode);
        return select(entity);
    }

    /**
     * 获取院区下所有收费项目对应标本信息
     *
     * @param hospitalCode
     * @return
     */
    default List<AssayReportItem> getAssayReportItemByHospitalCode(Integer hospitalCode) {
        AssayReportItem entity = new AssayReportItem();
        entity.setHospitalCode(hospitalCode);
        return select(entity);
    }
}
