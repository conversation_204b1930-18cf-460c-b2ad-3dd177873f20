package com.rjsoft.outPatient.infrastructure.repository.strategy.impl;

import cn.hutool.core.util.StrUtil;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.enums.RecipeStatusEnum;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.*;
import com.rjsoft.outPatient.infrastructure.repository.strategy.PreRecipeStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component("PreRecipeByIOInpatient")
@AllArgsConstructor
public class PreRecipeByIOInpatient implements PreRecipeStrategy {

    @Resource
    RecipeRepository recipeRepository;
    SystemTbPubItemsRepository systemTbPubItemsRepository	;
    SystemTvIteminfodeptRepository systemTvIteminfodeptRepository	;
    VirtualInventoryRepository virtualInventoryRepository	;
    ChargeItemRepository chargeItemRepository	;
    DrugFrequencyRepository drugFrequencyRepository;

    PreRecipeDetailMapper preRecipeDetailMapper	;
    RecipeDetailMapper recipeDetailMapper	;
    OldRecipeDetailMapper oldRecipeDetailMapper	;
    DrugToHospitalMapper drugToHospitalMapper	;
    SysFunctionMapper sysFunctionMapper	;
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<PreRecipeDetail> getPreRecipeDetails(CheckRecipeDto checkDto, Map errInfo) {
        List<PreRecipeDetail> details = getPreRecipeDetailsByIOInpatient(Converter.toInt32(checkDto.getOperatingId()),
                checkDto.getDoctorId(), checkDto.getHospitalCode());
        return details;
    }

    /**
     * 获取处方预存明细(根据常用项目)
     *
     * @param id
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    @DatabaseAnnotation(name = "HISDB")
    public List<PreRecipeDetail> getPreRecipeDetailsByIOInpatient(Integer id, Integer doctorId, Integer hospitalCode) {
        List<PreRecipeDetail> res = new ArrayList<>();
        WardTbAdv wardTbAdv = recipeRepository.getHistoryWardAdvById(id,hospitalCode);

        PreRecipeDetail preRecipeDetail = new PreRecipeDetail();
        preRecipeDetail.setPreSaveNo(sysFunctionMapper.getGetSequences(SequenceEnum.PRE_SAVE_NO));
        preRecipeDetail.setRecipeDetailNo(sysFunctionMapper.getGetSequences(SequenceEnum.RECIPE_DETAIL_NO));

        DataSourceSwitchAspect.changeDataSource("HISDB");
        SystemTbPubItems chargeItem1 = systemTbPubItemsRepository.getChargeItemById(wardTbAdv.getItemId(), hospitalCode, null);
//        ChargeItem chargeItem = chargeItemRepository.getChargeItemById(wardTbAdv.getItemId(), hospitalCode);
        //判断是否是特殊药品
        SystemTbPubItems chargeItem = systemTbPubItemsRepository.chargeItemTrim(chargeItem1);
        preRecipeDetail.ChangeChargeItem(chargeItem);
        preRecipeDetail.setUsage(wardTbAdv.getUsage()!=null?Integer.valueOf(wardTbAdv.getUsage()):null);
        FrequencyResult frequencyResult = drugFrequencyRepository.selFrequencyById(hospitalCode,wardTbAdv.getFreq()!=null?Integer.valueOf(wardTbAdv.getFreq()):-1);
        if(frequencyResult!=null&& StrUtil.isNotBlank(frequencyResult.getFreqCode())){
            DrugFrequency drugFrequency = drugFrequencyRepository.getDrugFrequencyByCode(frequencyResult.getFreqCode().trim(),hospitalCode);
            preRecipeDetail.setFrequency(drugFrequency.getId());
        }
        preRecipeDetail.setDose(StrUtil.isNotBlank(wardTbAdv.getDose())?wardTbAdv.getDose().trim():null);
        preRecipeDetail.setDoseUnit(StrUtil.isNotBlank(wardTbAdv.getDoseUnit())?wardTbAdv.getDoseUnit().trim():null);
        preRecipeDetail.setDays(wardTbAdv.getExecDay());
        BigDecimal acount = BigDecimal.ZERO;
        //标记是否特殊用法
        boolean specialUsageFlag = preRecipeDetail.whetherSpecialUsage();
        if (specialUsageFlag) {
            BigDecimal doseSpecial = preRecipeDetail.specialUsageSum();
            preRecipeDetail.setFrequency(-1);
            acount = acount.add(doseSpecial);
        } else {
            acount = acount.add(BigDecimal.valueOf(Double.valueOf(wardTbAdv.getDose())));
        }

        if(wardTbAdv.getDoseUnit().trim().equals(chargeItem.getWardUnit().trim())){

        }else if (wardTbAdv.getDoseUnit().equals(chargeItem.getClinicUnit().trim())){
            acount = acount.multiply(BigDecimal.valueOf(chargeItem.getClinicQty()));
        }else{
            acount = acount.divide(chargeItem.getDosage(), 4, BigDecimal.ROUND_HALF_UP);
        }
        acount = acount.setScale(0, BigDecimal.ROUND_UP);
        if (wardTbAdv.getFreq() != 1 && preRecipeDetail.getFrequency() != -1) {
            if ((wardTbAdv.getFreqType() == 1 || wardTbAdv.getFreqType() == 2)) {
                acount = acount.multiply(BigDecimal.valueOf(wardTbAdv.getFreqParam()));
            }
        }
        acount = acount.setScale(0, BigDecimal.ROUND_UP);
        acount = acount.multiply(BigDecimal.valueOf(wardTbAdv.getExecDay()==null?1:wardTbAdv.getExecDay()));
        acount = acount.divide(BigDecimal.valueOf(chargeItem.getClinicQty()), 0, BigDecimal.ROUND_UP);
        preRecipeDetail.setQuantity(acount);
        preRecipeDetail.setOpFlag(0);
        preRecipeDetail.setRecipeStatus(RecipeStatusEnum.UNREVIEWED.getCode());
        preRecipeDetail.setChangeRecipeFlag(0);
        preRecipeDetail.setPackageFlag(0);
        res.add(preRecipeDetail);
        return res;
    }

}
