package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.patient.dto.PatientInfoDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.PatientCard;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

@Mapper
public interface PatientCardMapper extends BaseMapper<PatientCard> {

    /**
     * 查询患者卡表信息
     *
     * @param patId
     * @return
     */
    default List<PatientCard> getPatientDetail(Integer patId) {
        PatientCard patientCard = new PatientCard();
        patientCard.setPatId(patId);
        return select(patientCard);
    }

    /**
     * 查询患者卡表信息
     *
     * @param patId
     * @param cardNo
     * @return
     */
    default PatientCard getPatientCard(Integer patId, String cardNo) {
        PatientCard patientCard = new PatientCard();
        patientCard.setPatId(patId);
        patientCard.setCardNo(cardNo);
        patientCard.setStatus(0);
        return select(patientCard).size() > 0 ? select(patientCard).get(0) : null;
    }


    /**
     * 根据卡号获取患者基本信息
     *
     * @param cardNo
     * @param hospitalCode
     * @return
     */
    List<PatientInfoDto> getPatientDetailByPatId(@Param("cardNo") String cardNo,
                                                 @Param("hospitalCode") Integer hospitalCode);


    List<PatientCard> getPatSciCardList(@Param("patId") Integer patId,
                                        @Param("hisCardNo") String hisCardNo,
                                        @Param("patFlag") Integer patFlag,
                                        @Param("status") Integer status);
}
