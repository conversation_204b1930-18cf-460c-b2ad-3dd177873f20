<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DiseaseReportCardMapper">


    <select id="getInfoByIdNoSbname" resultType="java.lang.Integer">
        select count(*)
        from Tbt_ZyEMR_Qpyzjszabgk a (nolock)
                 inner join Tbv_PsySixClassCode b (nolock) on a.icd10 = b.code
        where a.sfz = #{idNo}
          and b.sbname = #{sbname}
    </select>
</mapper>