<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.PatientCardMapper">

    <select id="getPatientDetailByPatId" resultType="com.rjsoft.outPatient.domain.patient.dto.PatientInfoDto">
        select distinct
            b.CardNo cardNo,
            a.PatName patName,
            a.CertificateNo certificateNo
        from Reg_Tb_PatientList a (nolock)
        inner  join  Reg_Tb_PatientCard b (nolock) ON a.PatId = b.PatID
        inner join (select max(patid) mpatid,cardNo from Reg_Tb_PatientCard c group by cardNo) d on d.mpatid=b.patid
        and d.cardno=b.cardno
        where  b.CardNo like  '' + #{cardNo} + '%'
          and a.IsDelete = 0
          and a.IsUse = 1
          and a.PatName !=''
and a.PatName is not null
and a.certificateNo !=''
and a.certificateNo is not null
    </select>

    <select id="getPatSciCardList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.PatientCard">
        select Id,CardType,CardNo,CardData,ChargeType,PatId,HisCardNo,AreaCode,CreateBy,CreateDate,Status,UpdateBy,UpdateDate,PatFlag
        from Reg_Tb_PatientCard (nolock)
        where PatId = #{patId}
          and HisCardNo = #{hisCardNo}
          and PatFlag = #{patFlag}
          and Status = #{status}
        order by Id desc
    </select>
</mapper>