package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.domain.applicationForm.dto.ApplicationProjectDto;
import com.rjsoft.outPatient.domain.applicationForm.dto.ApplicationProjectListDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;

import java.util.HashMap;
import java.util.List;

/**
 * 电子申请单
 */
public interface ApplicationFormRepository {

    //<editor-fold desc="申请单模板">

    /**
     * 查询申请单模板列表
     *
     * @param templateType
     * @param templateNo
     * @param hospitalCode
     * @return
     */
    List<ApplicationTemplate> getApplicationTemplate(Integer templateType, String templateNo, Integer hospitalCode);

    /**
     * 根据申请单编码集合，查询申请单
     *
     * @param templateNo
     * @param hospitalCode
     * @return
     */
    List<ApplicationTemplate> getApplicationTemplateByTemplateNos(List<String> templateNo, Integer hospitalCode);

    /**
     * 根据申请单编码集合，查询申请单
     *
     * @param templateNo
     * @param hospitalCode
     * @return
     */
    ApplicationTemplate getApplicationTemplateByTemplateNo(String templateNo, Integer hospitalCode);


    /**
     * 申请单模板（保存，修改）
     *
     * @param applicationTemplate
     * @return
     */
    boolean saveApplicationTemplate(ApplicationTemplate applicationTemplate);


    /**
     * 申请单模板删除
     *
     * @param id
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    boolean delApplicationTemplate(Integer id, Integer doctorId, Integer hospitalCode);


    /**
     * 根据项目ID获取申请单
     *
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    List<ApplicationTemplate> getApplicationTemplateByItemCode(Integer itemCode, Integer hospitalCode);
    //</editor-fold>

    //<editor-fold desc="申请单明细对照">

    /**
     * 查询申请单明细对照信息
     *
     * @param templateNo
     * @param hospitalCode
     * @return
     */
    List<ApplicationControl> getApplicationControl(String templateNo, Integer hospitalCode);

    /**
     * 根据流水号集合获取申请单主表
     *
     * @param ids
     * @param hospitalCode
     * @return
     */
    List<ApplicationControl> getApplicationControlByIds(List<Integer> ids, Integer hospitalCode);


    /**
     * 申请单明细对照（保存，修改）
     *
     * @param applicationControl
     * @return
     */
    boolean saveApplicationControl(ApplicationControl applicationControl);


    /**
     * 申请单明细对照删除
     *
     * @param id
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    boolean delApplicationControl(Integer id, Integer doctorId, Integer hospitalCode);
    //</editor-fold>

    //<editor-fold desc="申请单明细对照项目">

    /**
     * 查询申请单明细对照项目信息
     *
     * @param templateNo
     * @param hospitalCode
     * @return
     */
    List<ApplicationControlItems> getApplicationControlItem(String templateNo, Integer hospitalCode);


    /**
     * 保存申请单明细对照项目信息
     *
     * @param applicationControlItems
     * @return
     */
    boolean saveApplicationControlItem(ApplicationControlItems applicationControlItems);


    /**
     * 删除申请单明细对照项目信息
     *
     * @param applicationConNo
     * @return
     */
    boolean delApplicationControlItem(Integer applicationConNo, Integer hospitalCode);


    /**
     * 查询申请单明细对照项目信息（通过申请单流水号）
     *
     * @param applicationConNos
     * @param hospitalCode
     * @return
     */
    List<ApplicationControlItems> getApplicationControlItemByConNo(List<Integer> applicationConNos, Integer hospitalCode);
    //</editor-fold>


    /**
     * 查询申请单分组信息
     *
     * @param templateNo
     * @param fromGroupNo
     * @param hospitalCode
     * @return
     */
    List<ApplicationGroup> getApplicationGroup(String templateNo, Integer fromGroupNo, Integer hospitalCode);


    /**
     * 查询申请单部位信息（通过组号）
     *
     * @param GroupNos
     * @param hospitalCode
     * @return
     */
    List<ApplicationLocation> getApplicationLocation(List<Integer> GroupNos, Integer hospitalCode);


    /**
     * 查询申请单部位信息（通过id）
     *
     * @param ids
     * @param hospitalCode
     * @return
     */
    List<ApplicationLocation> getApplicationLocationById(List<Integer> ids, Integer hospitalCode);


    //<editor-fold desc="申请单内容">

    /**
     * 查询申请单内容
     *
     * @param id
     * @param hospitalCode
     * @return
     */
    List<ApplicationContent> getApplicationContent(Integer id, Integer hospitalCode);


    /**
     * 查询申请单内容，通过表单ID
     *
     * @param formId
     * @param hospitalCode
     * @return
     */
    ApplicationContent getApplicationContentByFormId(Integer formId, Integer hospitalCode);


    /**
     * 申请单内容（保存，修改）
     *
     * @param applicationContentDto
     * @return
     */
    boolean saveApplicationContent(ApplicationContent applicationContentDto);


    /**
     * 删除申请单部位内容
     *
     * @param contentId
     * @return
     */
    boolean deleteApplicationContentLocation(Integer contentId);
    //</editor-fold>


    /**
     * 查询申请单内容部位信息
     *
     * @param ids
     * @param hospitalCode
     * @return
     */
    List<ApplicationContentLoca> getApplicationContentLocation(List<Integer> ids, Integer hospitalCode);


    /**
     * 申请单内容部位（保存）
     *
     * @param applicationContentLoca
     * @return
     */
    boolean saveApplicationContentLocation(ApplicationContentLoca applicationContentLoca);


    /**
     * 申请单保存时查询申请单对照信息，匹配收费项目
     *
     * @param templateNo
     * @param fromGroupNo
     * @param selectedPlace
     * @param hospitalCode
     * @return
     */
    List<ApplicationControl> getApplicationControlByLoca(String templateNo, String fromGroupNo, String selectedPlace, Integer hospitalCode);

    /**
     * 根据组号获取申请单主表
     *
     * @param fromGroup
     * @param hospitalCode
     * @return
     */
    List<ApplicationControl> getApplicationControlByGroupNo(String fromGroup, Integer hospitalCode);


    /**
     * 申请单内容收费项目（保存，修改）
     *
     * @param contentId
     * @param itemCode
     * @return
     */
    boolean saveApplicationContentItems(Integer contentId, Integer itemCode);

    /**
     * 删除申请单内容收费项目
     *
     * @param contentId
     * @return
     */
    boolean deleteApplicationContentItems(Integer contentId);


    /**
     * 查询申请单内容收费项目信息
     *
     * @param contentId
     * @param hospitalCode
     * @return
     */
    List<ApplicationContentItems> GetApplicationContentItems(Integer contentId, Integer hospitalCode);

    /**
     * 根据收费项目ID查询申请单
     *
     * @param itemCode
     * @param hosptialCode
     * @return
     */
    List<ApplicationControlItems> getApplicationControlItemsBySfItem(Integer itemCode, Integer hosptialCode);


    /**
     * 通用表单保存明细信息
     *
     * @param formId
     * @param hospitalCode
     * @return
     */
    List<ReportDetail> getReportDetail(Integer formId, Integer hospitalCode);

    /**
     * 查询XTPZ根据gjz
     *
     * @param gjz
     * @return
     */
    MzysTbXTPZ getXtpz(String gjz);


    /**
     * 查询已开申请单列表
     *
     * @param receptionNo
     * @param doctorId
     * @param applyType
     * @param hospitalCode
     * @return
     */
    List<ApplicationProjectDto> getApplicationProjectList(Integer receptionNo, Integer doctorId, Integer applyType, Integer hospitalCode);

    /**
     * 查询已开申请单明细
     *
     * @param examineNo
     * @return
     */
    List<ApplicationProjectListDto> getApplicationProjectDetail(Integer examineNo);


    /**
     * 查询已开申请单明细（心理测量申请单）
     *
     * @param examineNo
     * @return
     */
    List<HashMap> getApplicationProjectDetailS(Integer examineNo);


    /**
     * 查询已开申请单内容，通过内容ID
     *
     * @param id
     * @param hospitalCode
     * @return
     */
    ApplicationContent getApplicationContentById(Integer id, Integer hospitalCode);


    /**
     * 获取已开申请单收费项目信息
     *
     * @param formId
     * @param hospitalCode
     * @return
     */
    List<ApplicationProjectListDto> getApplicationItems(Integer formId, Integer hospitalCode);


}
