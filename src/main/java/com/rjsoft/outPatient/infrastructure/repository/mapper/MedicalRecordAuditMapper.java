package com.rjsoft.outPatient.infrastructure.repository.mapper;


import com.rjsoft.outPatient.infrastructure.repository.entity.MedicalRecordAudit;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.Date;
import java.util.List;

public interface MedicalRecordAuditMapper extends BaseMapper<MedicalRecordAudit>, ExampleMapper<MedicalRecordAudit> {


    /**
     * 查询科室审核病历列表
     *
     * @return
     */
    List<MedicalRecordAudit> getDeptMedicalRecordAuditList(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("hospitalCode") Integer hospitalCode,
            @Param("listFlag") Integer listFlag,
            @Param("deptId") String deptId,
            @Param("doctorId") String doctorId,
            @Param("auditFlag") String auditFlag,
            @Param("auditState") String auditState,
            @Param("hisCardNo") String hisCardNo,
            @Param("patName") String patName,
            @Param("visitFlag") String visitFlag,
            @Param("sortField") String sortField);


    /**
     * 查询病历审核抽查列表
     *
     * @return
     */
    List<MedicalRecordAudit> getSpotCheckList(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("hospitalCode") Integer hospitalCode,
            @Param("deptId") String deptId,
            @Param("sortField") String sortField);


    /**
     * 查询病历审核结果列表
     *
     * @return
     */
    List<MedicalRecordAudit> getCheckList(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("hospitalCode") Integer hospitalCode,
            @Param("deptId") String deptId,
            @Param("doctorId") String doctorId,
            @Param("auditState") String auditState,
            @Param("sortField") String sortField);


}
