package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.Area;
import com.rjsoft.outPatient.infrastructure.repository.mapper.AreaMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.AreaRepository;
import lombok.AllArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class AreaRepositoryImpl implements AreaRepository {
    AreaMapper areaMapper;

    @Override
    @Cacheable(
            cacheNames = {"SysArea::Parent"},
            key = "#parentId",
            unless = "#result == null"
    )
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Area> getAreaByParentId(String parentId) {
        return areaMapper.getAreaByParentId(parentId,null);
    }

    @Override
    @Cacheable(
            cacheNames = {"SysArea::Code"},
            key = "#code",
            unless = "#result == null"
    )
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Area> getAreaByCode(String code) {
        return areaMapper.getAreaByParentId(null,code);
    }
}
