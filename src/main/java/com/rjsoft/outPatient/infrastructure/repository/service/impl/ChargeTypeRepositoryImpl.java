package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.utils.TyMapUtil;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeType;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ChargeTypeMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ChargeTypeRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/9/15-9:58 上午
 */
@Service

public class ChargeTypeRepositoryImpl implements ChargeTypeRepository {

    @Autowired
    private ChargeTypeMapper chargeTypeMapper;

    // XXX: yutao 2024/6/17 refresh
    Map<String, ChargeType> allChargeTypeMap = null;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public ChargeType getChargeTypeByCode(Integer chargeType, Integer hospitalCode) {
        final Weekend<ChargeType> weekend = new Weekend<>(ChargeType.class);
        weekend.weekendCriteria().andEqualTo(ChargeType::getChargeTypeCode, chargeType)
                .andEqualTo(ChargeType::getHospitalCode, hospitalCode);
        return chargeTypeMapper.selectOneByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public ChargeType getAllChargeTypeByCodeInCache(Integer chargeType, Integer hospitalCode) {
        if(allChargeTypeMap == null) {
            final Weekend<ChargeType> weekend = new Weekend<>(ChargeType.class);
            List<ChargeType> chargeTypeList = chargeTypeMapper.selectByExample(weekend);
            allChargeTypeMap = TyMapUtil.listToMapMul(chargeTypeList, "getHospitalCode", "getChargeTypeCode");
        }
        return allChargeTypeMap.get(""+hospitalCode+"_"+chargeType+"_");
    }

}
