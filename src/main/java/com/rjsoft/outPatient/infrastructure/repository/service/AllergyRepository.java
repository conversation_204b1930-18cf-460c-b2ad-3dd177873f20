package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.Allergy;
import com.rjsoft.outPatient.infrastructure.repository.entity.AllergyDic;

import java.util.List;
import java.util.Set;

/**
 * 过敏信息
 */
public interface AllergyRepository {

    /**
     * 获取过敏源
     *
     * @param inputCode
     * @param hospitalCode
     * @return
     */
    List<AllergyDic> getAllergyType(String inputCode, Integer hospitalCode);

    /**
     * 获取过敏源
     *
     * @param code
     * @param hospitalCode
     * @return
     */
    List<AllergyDic> getAllergyTypeByCode(Integer code, Integer hospitalCode);


    /**
     * 获取患者过敏信息
     *
     * @param id
     * @param patId
     * @param hisCardNo
     * @param hospitalCode
     * @return
     */
    List<Allergy> getPatAllergyInfo(Integer id, Integer patId, String hisCardNo, Integer hospitalCode);


    /**
     * 患者过敏信息（保存，修改）
     *
     * @param allergy
     * @return
     */
    boolean savePatAllergyInfo(Allergy allergy);


    /**
     * 根据过敏原，患者编号查询
     *
     * @param allergyCode 过敏原
     * @param patId       患者编号
     * @return
     */
    Allergy getPatAllergyInfo(Integer allergyCode, Integer patId);


    /**
     * 患者过敏信息停用
     *
     * @param id
     * @param patId
     * @param state
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    boolean delPatAllergyInfo(Integer id, Integer patId, Integer state, Integer doctorId, Integer hospitalCode);

    /**
     * 根据患者编号获取患者过敏信息
     *
     * @param patIds
     * @param hospitalId
     */
    List<Allergy> getAllergyList(Set<Integer> patIds, Integer hospitalId);

    /**
     * 获取患者过敏阳性信息
     *
     * @param patId
     * @param hospitalCode
     */
    List<Allergy> getConfirmAllergies(Integer patId, Integer hospitalCode);
    /**
     * 根据过敏源code集和患者编号集获取过敏信息
     * @param codeList
     * @param patIdList
     * @return
     */
    List<Allergy> queryByCodesAndPatIds(List<Integer> codeList, List<Long> patIdList);
}
