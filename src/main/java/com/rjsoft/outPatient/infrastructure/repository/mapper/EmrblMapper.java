package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.EmrBl;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2021/8/30 17:54
 * 病例信息
 */
public interface EmrblMapper extends Mapper<EmrBl> {

    /**
     * @param regNo
     * <AUTHOR>
     * @Retur List
     * @Date 17:57 2021/8/30
     * 根据住院流水号加载病历
     */
    default List<EmrBl> queryBlInfo(Set<String> regNo) {
        String[] BcDmnos = {"BC26", "BC2401", "BC0008", "BC0019", "BC0022", "BC4018", "BC4021"};
        List<String> list = Arrays.asList(BcDmnos);
        Weekend<EmrBl> weekend = new Weekend<>(EmrBl.class);
        WeekendCriteria<EmrBl, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(EmrBl::getRegNo, regNo);
        weekendCriteria.andIn(EmrBl::getBcDmno, list);
        weekendCriteria.andEqualTo(EmrBl::getIsVisible, 1);
        weekend.orderBy("recDate").desc();
        return selectByExample(weekend);
    }

    /**
     * 查询出院小结病历详情
     *
     * @param regNo
     * @param bcDmno
     * @param recDate
     * @return
     */
    List<String> queryDetail(@Param("regNo") String regNo,
                             @Param("bcDmno") String bcDmno,
                             @Param("recDate") Date recDate);


    /**
     * 查询出院小结病历详情
     *
     * @param regNo
     * @param bcDmno
     * @param recDate
     * @return
     */
    List<String> getBlDetail(@Param("regNo") String regNo,
                             @Param("bcDmno") String bcDmno,
                             @Param("recDate") Date recDate,
                             @Param("hospitalCode")Integer hospitalCode);


}
