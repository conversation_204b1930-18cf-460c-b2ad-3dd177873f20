package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.RisItemDj;
import org.springframework.cache.annotation.Cacheable;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

/**
 * 岱嘉项目表
 * <AUTHOR>
public interface RisItemDjMapper extends BaseMapper<RisItemDj>, ExampleMapper<RisItemDj> {

    /**
     * 根据项目编码获取ris项目
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    default RisItemDj getRisItemByCode(Integer itemCode,Integer hospitalCode) {
        RisItemDj entity = new RisItemDj();
        entity.setItemCode(itemCode);
        entity.setHospitalCode(hospitalCode);
        List<RisItemDj> list = select(entity);
        if (list.stream().count() == 0) {
            return null;
        }
        return list.get(0);
    }
}
