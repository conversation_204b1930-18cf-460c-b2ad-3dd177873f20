package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.lang.model.type.IntersectionType;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;

/**
 * 申请单明细对照收费项目
 */
@Data
@Table(name = "MZYS_TB_JCSQDZB_XM")
public class ApplicationControlItems  implements Serializable {

    @Column(name = "id", insertable = false, updatable = false)
    private Integer id;

    @Id
    @Column(name = "sqdzblsh")
    private Integer applicationConNo;

    @Column(name = "sfxm")
    private Integer itemCode;

    @Column(name = "xmmc")
    private String itemName;

    @Column(name = "yybm")
    private Integer hospitalCode;
}


