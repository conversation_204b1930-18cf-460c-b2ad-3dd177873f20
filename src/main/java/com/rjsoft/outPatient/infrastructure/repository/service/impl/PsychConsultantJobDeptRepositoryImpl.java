package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.PsychConsultantJobDept;
import com.rjsoft.outPatient.infrastructure.repository.mapper.PsychConsultantJobDeptMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.PsychConsultantJobDeptRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/25-2:56 下午
 */
@Service
@AllArgsConstructor
public class PsychConsultantJobDeptRepositoryImpl implements PsychConsultantJobDeptRepository {

    private final PsychConsultantJobDeptMapper psychConsultantJobDeptMapper;

    /**
     * 根据医生 id 查询
     *
     * @param doctorId     医生 id
     * @param hospitalCode 医院编码
     * @return {@link PsychConsultantJobDept}
     */
    @Override
    @DatabaseAnnotation
    public List<PsychConsultantJobDept> getByDoctorId(Integer doctorId, Integer hospitalCode) {
        final Weekend<PsychConsultantJobDept> weekend = new Weekend<>(PsychConsultantJobDept.class);
        weekend.weekendCriteria().andEqualTo(PsychConsultantJobDept::getDoctorId, doctorId)
                .andEqualTo(PsychConsultantJobDept::getHospitalCode, hospitalCode);
        return psychConsultantJobDeptMapper.selectByExample(weekend);
    }
}
