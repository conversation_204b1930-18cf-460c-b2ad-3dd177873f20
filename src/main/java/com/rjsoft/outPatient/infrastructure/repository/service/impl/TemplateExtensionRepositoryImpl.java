package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.TemplateExtension;
import com.rjsoft.outPatient.infrastructure.repository.mapper.TemplateExtensionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.TemplateExtensionRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/30-9:19 上午
 */
@Service
@AllArgsConstructor
public class TemplateExtensionRepositoryImpl implements TemplateExtensionRepository {

    private final TemplateExtensionMapper templateExtensionMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public TemplateExtension getTemplateExtensionByClassIdAndKeyCode(String classId, String keyCode, Integer hospitalId) {
        final Weekend<TemplateExtension> weekend = new Weekend<>(TemplateExtension.class);
        weekend.weekendCriteria().andEqualTo(TemplateExtension::getClassId, classId)
                .andEqualTo(TemplateExtension::getKeyCode, keyCode)
                .andEqualTo(TemplateExtension::getHospitalId, hospitalId);
        return templateExtensionMapper.selectOneByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<TemplateExtension> getDrugDangerLev(Integer drugId, Integer hospitalCode) {
        return templateExtensionMapper.getDrugDangerLev(drugId, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public String getDrugPropertyName(Integer dicCode, Integer hospId){
        return templateExtensionMapper.getDrugPropertyName(dicCode, hospId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<TemplateExtension> getDrugOriginal(Integer hospitalCode){
        return templateExtensionMapper.getDrugOriginal(hospitalCode);
    }

}
