package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeType;

/**
 * <AUTHOR>
 * @since 2021/9/15-9:55 上午
 */
public interface ChargeTypeRepository {

    /**
     * 根据患者收费类型查询
     *
     * @param chargeType    收费类型
     * @param hospitalCode  医院编码
     * @return  {@link ChargeType}
     */
    ChargeType getChargeTypeByCode(Integer chargeType, Integer hospitalCode);

    //    @DatabaseAnnotation(name = DatasourceName.HISDB)
    ChargeType getAllChargeTypeByCodeInCache(Integer chargeType, Integer hospitalCode);
}
