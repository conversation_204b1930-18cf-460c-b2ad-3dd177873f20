package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.TbUser;

/**
 * <AUTHOR>
 * @create 2021/8/30 17:09
 * @description
 **/
public interface PrescriptionAuditRepository {
    /**
     * 根据医生编码+医院编码获取医生信息 hisdb..tb_user
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    TbUser getUserByIdAndHosp(String doctorId, String hospitalCode);
}
