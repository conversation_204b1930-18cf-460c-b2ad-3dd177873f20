package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugSensitive;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugSensitiveMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DrugSensitiveRepository;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/26 - 14:02
 */

@Service
@AllArgsConstructor
public class DrugSensitiveRepositoryImpl implements DrugSensitiveRepository {

    private final DrugSensitiveMapper drugSensitiveMapper;

    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    public DrugSensitive getDrugSensitiveByCode(@NonNull Integer drugCode) {
        final DrugSensitive entity = new DrugSensitive();
        entity.setDrugCode(drugCode);
        return drugSensitiveMapper.selectOne(entity);
    }
}
