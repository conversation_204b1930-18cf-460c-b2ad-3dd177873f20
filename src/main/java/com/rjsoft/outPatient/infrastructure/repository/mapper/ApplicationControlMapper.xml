<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ApplicationControlMapper">


    <select id="getApplicationControl"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationControl">
            select
            a.lsh id,
            a.sqdbm templateNo,
            a.bwfz fromGroup,
            a.dzlx selectedType,
            b.lxmc selectedTypeName,
            a.xzbw selectedPlace,
            a.xzbwsl placeNum,
            a.jzsl mediumNum,
            a.zqjzsl mediumAllNum,
            a.tjlx selCondition,
            c.tjfh selConditionName,
            a.nlsx maxAge,
            a.nlxx minAge,
            a.xb sexCondition,
            d.xbmc sexConditionName,
            a.sfjg tollInterval,
            a.gzzh ruleGroupNo,
            a.gzzf IsruleGroup,
            a.yybm hospitalCode
            from MZYS_TB_JCSQDZB a (nolock)
            left join MZYS_TB_JCSQDZLX b (nolock) on a.dzlx=b.lxbm
            left join MZYS_TB_JCSQDZTJ c (nolock) on a.tjlx=c.tjlx
            left join MZYS_TB_JCSQDXB d (nolock) on a.xb=d.xbbm
            where a.sqdbm=#{templateNo}
            and a.yybm=#{hospitalCode}
            order by gzzh asc
    </select>

</mapper>