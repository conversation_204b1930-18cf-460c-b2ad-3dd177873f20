package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.domain.applicationForm.dto.ApplicationProjectDto;
import com.rjsoft.outPatient.domain.applicationForm.dto.ApplicationProjectListDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationContent;
import com.rjsoft.outPatient.infrastructure.repository.entity.ExamineRequest;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.HashMap;
import java.util.List;

/**
 * 检查申请主表
 *
 * <AUTHOR>
public interface ExamineRequestMapper extends BaseMapper<ExamineRequest>, ExampleMapper<ExamineRequest> {

    /**
     * 根据检查流水号加载检查主表记录
     *
     * @param examineDetailNo
     * @param examineNo
     * @return
     */
    default ExamineRequest getExamineRequestByExamineNo(Integer examineDetailNo, Integer examineNo) {
        ExamineRequest entity = new ExamineRequest();
        entity.setExamineNo(examineNo);
        entity.setExamineDetailNo(examineDetailNo);
        entity.setDeleteFlag(false);
        List<ExamineRequest> list = select(entity);
        if (list.stream().count() == 0) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 查询已开申请单列表
     *
     * @param receptionNo
     * @param doctorId
     * @param applyType
     * @param hospitalCode
     * @return
     */
    List<ApplicationProjectDto> getApplicationProjectList(Integer receptionNo, Integer doctorId, Integer applyType, Integer hospitalCode);

    /**
     * 查询已开申请单明细
     *
     * @param examineNo
     * @return
     */
    List<ApplicationProjectListDto> getApplicationProjectDetail(Integer examineNo);

    /**
     * 查询已开申请单明细（心理测量申请单）
     *
     * @param examineNo
     * @return
     */
    List<HashMap> getApplicationProjectDetailS(Integer examineNo);


    /**
     * 获取已开申请单收费项目信息
     *
     * @param formId
     * @param hospitalCode
     * @return
     */
    List<ApplicationProjectListDto> getApplicationItems(Integer formId, Integer hospitalCode);

    /**
     * 根据检查流水号和医院编码，修改检查申请单主表收费状态
     * @param examineNos
     * @param hospitalCode
     */
    default void updateExamineChargeStatus(List<String> examineNos ,Integer hospitalCode){
        Weekend<ExamineRequest> weekend = new Weekend<>(ExamineRequest.class);
        WeekendCriteria<ExamineRequest, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(ExamineRequest::getExamineNo,examineNos)
                .andEqualTo(ExamineRequest::getHospitalCode,hospitalCode);
        ExamineRequest request = new ExamineRequest();
        request.setStatus(0);
        updateByExampleSelective(request,weekend);
    }


}
