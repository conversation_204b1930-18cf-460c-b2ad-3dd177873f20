package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.param.SearchParam;
import com.rjsoft.outPatient.domain.reserve.bo.PhoneParam;
import com.rjsoft.outPatient.domain.reserve.dto.*;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 预约
 *
 * <AUTHOR>
 * @since 2021/8/11 - 13:05
 */
public interface ReserveRepository {

    /**
     * 查询医生排班信息
     *
     * @param doctorId     医生id
     * @param scheduleDate 开始时间
     * @param deptId
     * @return List<DoctorSchedulingInfoDto>
     */
    List<DoctorSchedulingInfoDto> queryDoctorSchedulingInfoDto(Integer doctorId, LocalDate scheduleDate, LocalDate endDate, Integer hospitalCode,Integer deptId, Integer scheType);

    /**
     * 查询时段
     *
     * @param timeMs       上午/下午
     * @param scheduleDate 排班日期
     * @param deptId       科室编码
     * @param doctorId     医生编码
     * @return {@link List<QueryTimeSpanDto>}
     */
    List<QueryTimeSpanDto> queryTimeSpan(String timeMs, Date scheduleDate, Integer deptId, Integer doctorId, String hospitalCode);

    /**
     * 查询
     * 调整排班页面医院号源列表
     *
     * @param adjustReserveTimeDTO {@link AdjustReserveTimeDTO}
     * @return {@link AdjustReserveTimeDTO} for list
     */
    List<AdjustReserveTimeDTO> getAdjustReserveView(AdjustReserveTimeDTO adjustReserveTimeDTO);

    /**
     * 查询排班科室列表
     *
     * @param inputCode    输入码 可以为null
     * @param dctCode      科室id
     * @param hospitalCode 医院编码
     * @return {@link List<ShiftDept>}
     */
    List<OnDutyDeptDTO> queryShiftDeptList(String inputCode, Integer dctCode, String startTime, String endTime);

    /**
     * 查询排班医生
     *
     * @param inputCode    输入码
     * @param hospitalCode 医院编码
     * @param deptCode
     * @return {@link List<ShiftDoctor>}
     */
    List<OnDutyDoctorDTO> queryShiftDoctorList(String inputCode, Integer hospitalCode, String deptCode);

    /**
     * 根据科室id获取对应医生Id
     *
     * @param deptCode 部门编号
     * @param hospId   医院Id
     */
    List<Integer> getReserveDctCode(Integer deptCode, Integer hospId);

    /**
     * 根据医生Id获取医生信息
     *
     * @param ids
     * @param hospId
     */
    List<DoctorInfoResponse> getDoctorInfo(List<Integer> ids, Integer hospId);

    /**
     * 查询预约信息
     *
     * @param source
     * @param hospitalId
     * @param startTime
     * @param endTime
     */
    List<AppointmentInfo> findAppointmentInfo(String source, Integer doctorId, Integer hospitalId, String startTime, String endTime, Integer deptId);


    /**
     * 查询加号信息
     *
     * @param param
     * @return
     */
    List<DctAddNumDetail> queryDctAddNumDetail(SearchParam param);

    /**
     * 获取医生排班信息
     *
     * @param doctorId
     * @param hospitalCode
     * @param deptCode
     * @return
     */
    List<DoctorAddNum> getDoctorSchedulingInfo(Integer doctorId, Integer hospitalCode, Integer deptCode);


    /**
     * 获取医生当日加号的数量
     *
     * @param subjectId
     * @param hospitalCode
     * @param deptCode
     * @param doctorId
     * @return
     */
    int queryDctAddNumDetailNum(Integer subjectId, Integer hospitalCode, Integer deptCode, Integer doctorId);


    /**
     * 查询当前时间上下午
     *
     * @param hospitalCode
     * @return
     */
    String GetTimeSpanExt(Integer hospitalCode);

    /**
     * 加号按钮功能-获取医生排班信息
     *
     * @param doctorId
     * @param deptCode
     * @param hospitalCode
     * @param timeMs
     * @return
     */
    List<DoctorAddNum> getDoctorScheduling(Integer doctorId, Integer deptCode, Integer hospitalCode, String timeMs);


    /**
     * 加号按钮功能-查询当天是否有该患者加号信息
     *
     * @param certificateNo
     * @param hospitalCode
     * @return
     */
    int getAddNumDetailCount(String certificateNo, Integer hospitalCode);

    /**
     * 获取时段id
     *
     * @param name
     * @param hospitalCode
     * @return
     */
    List<TimeSpanExt> getTimeSpanExtInfo(String name, Integer hospitalCode);

    /**
     * 获取总号源
     *
     * @param hospitalCode
     * @param mainId
     * @return
     */
    ReserveNum getVisitNum(Integer hospitalCode, Integer mainId);

    /**
     * 获取加号表中加号序号
     *
     * @param subjectId
     * @param hospitalCode
     * @return
     */
    Integer getSeqNum(Integer subjectId, Integer hospitalCode);

    /**
     * 获取科目详情表中中加号序号
     *
     * @param subjectId
     * @param hospitalCode
     * @return
     */
    Integer getSubjectSeqNum(Integer subjectId, Integer hospitalCode);

    /**
     * 加号
     *
     * @param detail
     */
    void addNum(DctAddNumDetail detail);

    /**
     * 根据Id取消加号
     *
     * @param id
     * @param doctorId
     */
    void deleteRegister(Integer id, Integer doctorId);

    /**
     * 根据ID查询加号申请信息
     *
     * @param id
     * @param hospitalCode
     * @return
     */
    DctAddNumDetail getDctAddNumDetailOne(Integer id, Integer hospitalCode);

    /**
     * 加号申请审核
     *
     * @param id
     * @param doctorId
     * @param checkStatus
     * @param hospitalCode
     * @return
     */
    boolean checkRegisterInfo(Integer id, Integer doctorId, Integer checkStatus, Integer hospitalCode);


    /**
     * 根据挂号流水号获取有效挂号数量
     *
     * @param regNo
     */
    RegisterList regCount(Long regNo, Integer hospitalCode);

    /**
     * 根据挂号流水号获取退号记录
     *
     * @param regNo
     * @param hospitalCode
     */
    int recordCount(Long regNo, Integer hospitalCode);

    /**
     * 向退号表添加申请记录
     *
     * @param param
     * @param operator
     * @return
     */
    int addReturnRecord(RegisterList param, Integer operator);

    /**
     * 医技预约
     *
     * @param time
     * @return
     */
    List<Map<String, Object>> medicalAppointment(String time);

    /**
     * 根据时段id获取对应的时段信息
     *
     * @param timeSpanId
     * @param hospitalCode
     * @return
     */
    TimeSpanExt getTimeSpanInfo(Integer timeSpanId, Integer hospitalCode);


    /**
     * 根据挂号序号获取预约文字
     *
     * @param regSerialNo        挂号序号
     * @param appointmentOrderId 预约号
     * @return
     */
    String getReserve(Integer regSerialNo, String appointmentOrderId, Integer hospitalCode);


    /**
     * 获取预约手机号
     *
     * @return
     */
    AppointmentPhone getAppointmentPhone(Integer deptId,Integer doctorId,Integer patId,Integer hospitalCode,Integer regNo);

    /**
     * 更新预约手机号
     *
     * @param phoneParam
     * @return
     */
    int updateAppointmentPhone(PhoneParam phoneParam);
}
