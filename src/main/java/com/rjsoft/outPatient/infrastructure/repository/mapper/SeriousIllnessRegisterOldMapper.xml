<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.SeriousIllnessRegisterOldMapper">
    <select id="getOldList" resultType="com.rjsoft.outPatient.domain.seriousIllness.vo.SeriousIllnessRegisterVo">
        select
        Id as id,
        regno as regNo,
        null as cardNo,
        null as hisCardNo,
        Name as patName,
        SF<PERSON> as patSfz,
        Phone as phone,
        Adress as address,
        Diagnosis as diagnosis,
        null as diagnosisId,
        TreatmentItem as treatmentItem,
        MedicalInstitution as medicalInstitution,
        Doctor as doctor,
        SignTime as signTime,
        Sealdoctorid as sealDoctorId,
        Seal as seal,
        SealTime as sealTime,
        Consignor as consignor,
        Consignor<PERSON><PERSON> as consignor<PERSON><PERSON>,
        RegisterTime as registerTime,
        Trustees as trustees,
        State as state,
        UseLift as useLift,
        ybSerialNo as ybSerialNo,
        TransfersOutName as transfersOutName,
        Credoctorid as creDoctorId,
        CreName as creName,
        CreTime as creTime,
        null as cancelTime,
        Upddoctorid as updDoctorId,
        UpdName as updName,
        UpdTime as updTime,
        DelFlag as delFlag,
        null as hospitalCode,
        null as source,
        null AS TrtDclaDetlSn,
        1 as oldFlag
        from MZYS_TB_DBDJD
        where DelFlag = 0
        <if test="stateList != null">
            and state in
            <foreach collection="stateList" index="index" item="state" separator="," close=")" open="(">
                #{state}
            </foreach>
        </if>
        <if test="cardNo != null and cardNo != ''">
            and exists (select 1 from [zxhis].[dbo].[Tbt_PubPatientInfo] a
            inner join [zxhis].[dbo].[Tbt_PubPatientInfo_CardNo] b on a.PatNo=b.PatNo
            where b.CardNo= #{cardNo}
            and isnull(b.EnableStatus,0)=0
            and ISNULL(b.ZtStatus,0)=0
            and a.Status=0
            and a.PatSfz = SFZ)
        </if>
        <if test="hisCardNo != null and hisCardNo != ''">
            and exists (select 1 from [zxhis].[dbo].[Tbt_PubPatientInfo] a
            inner join [zxhis].[dbo].[Tbt_PubPatientInfo_CardNo] b on a.PatNo=b.PatNo
            where b.HisCardNo= #{hisCardNo}
            and isnull(b.EnableStatus,0)=0
            and ISNULL(b.ZtStatus,0)=0
            and a.Status=0
            and a.PatSfz = SFZ)
        </if>
        <if test="patName != null and patName != ''">
            and Name like '%${patName}%'
        </if>
        <if test="startTime != null">
            and CreTime <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and CreTime <![CDATA[<=]]> #{endTime}
        </if>
        <if test="patSFZ != null and patSFZ != ''">
            and SFZ = #{patSFZ}
        </if>
        order by CreTime desc
    </select>

    <select id="getRegisterTimeByPatIdAndPatSFZ" resultType="java.util.Date">
        select top 1 RegisterTime from MZYS_TB_DBDJD where DelFlag = 0 and State = 8  and cast(hzbh as varchar(200)) = #{patId} and SFZ = #{idCard} order by RegisterTime desc
    </select>
</mapper>