package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDoctorRight;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeDoctorRightMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeDoctorRightRepository;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/23 - 17:59
 */
@Service
@AllArgsConstructor
public class RecipeDoctorRightRepositoryImpl implements RecipeDoctorRightRepository {

    private final RecipeDoctorRightMapper recipeDoctorRightMapper;

    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    public List<RecipeDoctorRight> getRecipeDoctorRightByDoctorIdAndRightCode(@NonNull Integer doctorId, @NonNull List<Integer> rightCodes) {
        final Weekend<RecipeDoctorRight> weekend = new Weekend<>(RecipeDoctorRight.class);
        weekend.weekendCriteria()
                .andEqualTo(RecipeDoctorRight::getDoctorId, doctorId);
        final WeekendCriteria<RecipeDoctorRight, Object> weekendCriteria = weekend.weekendCriteria();
        for (Integer rightCode : rightCodes) {
            weekendCriteria.orEqualTo(RecipeDoctorRight::getRightCode, rightCode);
        }
        weekend.and(weekendCriteria);
        return recipeDoctorRightMapper.selectByExample(weekend);
    }
}
