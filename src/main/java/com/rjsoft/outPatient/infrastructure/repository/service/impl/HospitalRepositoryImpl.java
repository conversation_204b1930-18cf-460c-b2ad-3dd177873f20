package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceConfig;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.Hospital;
import com.rjsoft.outPatient.infrastructure.repository.mapper.HospitalMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.HospitalRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

@Service
@AllArgsConstructor
public class HospitalRepositoryImpl implements HospitalRepository {
    HospitalMapper hospitalMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Hospital> getAllHospital() {
        Weekend<Hospital> weekend = new Weekend<>(Hospital.class);
        weekend.weekendCriteria().andEqualTo(Hospital::getIsDeleted,0);
        return hospitalMapper.selectByExample(weekend);
    }
}
