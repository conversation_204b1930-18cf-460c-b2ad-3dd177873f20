package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.patient.vo.RelationPatRespVO;

import java.util.List;

public interface PubPatientInfoMapper {

    int updateByPatNo(Integer patNo,String addr);
    String getByPatNo(Integer patNo);

    List<RelationPatRespVO> queryRelationPatientInfo(String patId);

    List<Integer> queryRelationPatientEmpiId(String certificateNo, String patName, Integer patSex, Integer hospitalCode);

    List<Integer> getPatientAllPatIds(String patId);
}
