package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 特殊药品
 *
 * <AUTHOR>
 * @since 2022/08/19-2:27 下午
 */
@Data
@Table(name = "SYSTEM_TB_USAGECONFIG")
public class SpecialDrugConfig implements Serializable {

    private static final long serialVersionUID = -8849258621944966959L;

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "Id", insertable = false, updatable = false)
    private Integer id;
    /**
     * 给药途径编码
     * 关联
     * 数据库: MZYS_NEW 架构: dbo 表: MZYS_TB_YPGYTJ 药品给药途径表
     */
    @Column(name = "Usage")
    private Integer usage;
    /**
     * 给药途径名称
     */
    @Column(name = "UsageName")
    private Integer usageName;
    /**
     * 应用范围
     */
    @Column(name = "UseRange")
    private Integer useRange;
    /**
     * 创建人 id
     */
    @Column(name = "Description")
    private String description;
    /**
     * 删除标志
     */
    @Column(name = "DelFlag")
    private Date delFlage;

    /**
     * 停启用标志
     */
    @Column(name = "EnableFlag")
    private Integer enableFlag;

    /**
     * 创建日期
     */
    @Column(name = "CreateOn")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createOn;

    /**
     * 创建人 id
     */
    @Column(name = "Creator")
    private String creator;

    /**
     * 修改日期
     */
    @Column(name = "UpdateOn")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateOn;

    /**
     * 修改人id
     */
    @Column(name = "Updator")
    private String updator;

    /**
     * 医院编码
     */
    @Column(name = "HospId")
    private Integer hospitalCode;

    /**
     * 联动次数
     */
    @Column(name = "InteracteNums")
    private Integer interacteNums;

    /**
     * 联动数量
     */
    @Column(name = "InteracteQuantity")
    private Integer interacteQuantity;

    /**
     * 1 给药途径
     * 2-煎药
     * 3-诊断
     * 4-西药剂型
     * 5-中药剂型
     */
    @Column(name = "Type")
    private Integer type;


}
