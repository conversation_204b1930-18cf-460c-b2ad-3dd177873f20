<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.AreaMapper">

    <select id="getAreaByParentId" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.Area">
        select rtrim(ParentId) ParentId, rtrim(Name)Name, rtrim(code)code, rtrim(level)level, rtrim(sort)sort, rtrim(status)status, rtrim(lang)lang, rtrim(InputCode)InputCode, rtrim(ZipCode)ZipCode from Tb_Dic_Area
        <where>
            <if test="parentId != null">
                and ParentId = #{parentId}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
        </where>
        order by code
    </select>

</mapper>