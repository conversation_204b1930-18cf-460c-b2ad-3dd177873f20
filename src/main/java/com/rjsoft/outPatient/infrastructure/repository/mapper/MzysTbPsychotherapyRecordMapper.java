package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbPsychotherapyRecord;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;

public interface MzysTbPsychotherapyRecordMapper extends Mapper<MzysTbPsychotherapyRecord> {

    default MzysTbPsychotherapyRecord getPsychotherapyRecordByApplyDetailId(Long applyId, Long applyDetailId) {
        Weekend<MzysTbPsychotherapyRecord> weekend = new Weekend<>(MzysTbPsychotherapyRecord.class);
        weekend.weekendCriteria()
                .andEqualTo(MzysTbPsychotherapyRecord::getApplyid, applyId)
                .andEqualTo(MzysTbPsychotherapyRecord::getApplydetailid, applyDetailId);
        return selectOneByExample(weekend);
    }

    default void deletePsychotherapyRecordByApplyDetailId(Long applyId, Long applyDetailId) {
        Weekend<MzysTbPsychotherapyRecord> weekend = new Weekend<>(MzysTbPsychotherapyRecord.class);
        weekend.weekendCriteria()
                .andEqualTo(MzysTbPsychotherapyRecord::getApplyid, applyId)
                .andEqualTo(MzysTbPsychotherapyRecord::getApplydetailid, applyDetailId);
        deleteByExample(weekend);
    }

}