package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbXTPZ;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SysFunctionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.SysFunctionRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021/11/6-11:11 上午
 */
@Service
@AllArgsConstructor
public class SysFunctionRepositoryImpl implements SysFunctionRepository {

    private final SysFunctionMapper sysFunctionMapper;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public Date getDate() {
        return sysFunctionMapper.getDate();
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public Long getGetSequences(SequenceEnum seq) {
        //手动创建事务
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        //获取事务状态
        TransactionStatus status = platformTransactionManager.getTransaction(def);
        //避免调用时生成主键重复问题
        Long getSequences = sysFunctionMapper.getGetSequences(seq);
        //提交事务
        platformTransactionManager.commit(status);
        return getSequences;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public Integer getWeekday() {
        return sysFunctionMapper.getWeekday();
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public MzysTbXTPZ getXtpz(String gjz) {
        return sysFunctionMapper.getXtpz(gjz);
    }
}
