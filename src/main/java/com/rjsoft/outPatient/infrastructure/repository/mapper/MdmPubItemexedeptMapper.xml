<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.MdmPubItemexedeptMapper">
    <select id="getItemExecDeptItem" resultType="java.util.HashMap">
        select
            (CONVERT ( CHAR ( 10 ), getdate( ), 120) + ' ' + startTime) startTime,
            (CONVERT ( CHAR ( 10 ), getdate( ), 120 ) + ' ' + endTime ) endTime
        from MDM_Pub_ItemExeDept
        where itemCode = #{itemCode}
          and useDept = #{useDeptId}
          <if test="id != null">
              and id != #{id}
          </if>
    </select>

    <select id="getItemExecDeptByTime" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubItemexedept">
        SELECT id, itemcode, itemname, execdept, execdeptname, exechospitalid, feecategorycode, hospitalId, starttime,
        endtime, updatetime, updateuserid, createtime, createuserid, usedept, usedeptname, type
        FROM MDM_Pub_ItemExeDept
        where itemCode = #{itemCode}
        and usedept = #{curDeptId}
        and hospitalId = #{curHospitalId}
        and CONVERT(time,startTime) &lt;= #{curTime}
        and CONVERT(time,endtime) &gt;= #{curTime}
    </select>

    <select id="getItemExecDeptByTimeItemCodeList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubItemexedept">
        SELECT id, itemcode, itemname, execdept, execdeptname, exechospitalid, feecategorycode, hospitalId, starttime,
        endtime, updatetime, updateuserid, createtime, createuserid, usedept, usedeptname, type
        FROM MDM_Pub_ItemExeDept
        where itemCode IN
        <foreach item="item" index="index" collection="itemCodeList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and usedept = #{curDeptId}
        and hospitalId = #{curHospitalId}
        and CONVERT(time,startTime) &lt;= #{curTime}
        and CONVERT(time,endtime) &gt;= #{curTime}
    </select>

    <select id="getExecDeptList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubItemexedept">
        SELECT id, itemcode, itemname, execdept, execdeptname, exechospitalid, feecategorycode, hospitalId, starttime,
               endtime, updatetime, updateuserid, createtime, createuserid, usedept, usedeptname, type
        FROM MDM_Pub_ItemExeDept
        where usedept = #{curDeptId}
          and hospitalId = #{curHospitalId}
          and CONVERT(time,startTime) &lt;= #{curTime}
          and CONVERT(time,endtime) &gt;= #{curTime}
    </select>
</mapper>