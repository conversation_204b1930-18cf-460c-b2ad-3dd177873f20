package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2021/8/20 15:02
 * @description 科研患者
 **/
@Data
@Table(name = "Reg_Tb_PatientList_KY")
public class PatientListResearch implements Serializable {

    /**
     * 患者编码
     */
    @Column(name = "PatId")
    private Integer patId;
    /**
     * 组号
     */
    @Column(name = "GroupNo")
    private String groupNo;

    /**
     * 科研课题id
     */
    @Column(name = "ProjectId")
    private String projectId;
    /**
     * 创建人
     */
    @Column(name = "Creator")
    private Integer creator;

    /**
     * 创建时间
     */
    @Column(name = "CreateTime")
    private Date createTime;
    /**
     * 修改人
     */
    @Column(name = "Updater")
    private Integer updater;

    /**
     * 修改时间
     */
    @Column(name = "UpdateTime")
    private Date updateTime;
    /**
     * 医院编码
     */
    @Column(name = "HospitalCode")
    private Integer hospCode;
}
