package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.RegTbCallRegInfo;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

public interface RegTbCallRegInfoMapper extends Mapper<RegTbCallRegInfo> {
    default Integer deleteByRegNoList(List<Long> regNoList, Integer hospitalCode) {
        Weekend<RegTbCallRegInfo> weekend = new Weekend<>(RegTbCallRegInfo.class);
        weekend.weekendCriteria().andIn(RegTbCallRegInfo::getRegNo, regNoList).andEqualTo(RegTbCallRegInfo::getHospitalId, hospitalCode);
        return deleteByExample(weekend);
    }

    Integer insertBatchNew(List<RegTbCallRegInfo> list);

}