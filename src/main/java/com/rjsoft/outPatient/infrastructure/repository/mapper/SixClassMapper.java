package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Agent;
import com.rjsoft.outPatient.infrastructure.repository.entity.SixClassCode;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface SixClassMapper extends Mapper<SixClassCode> {

    default SixClassCode getSixClass(String diagnoseCode) {
        SixClassCode sixClassCode = new SixClassCode();
        sixClassCode.setCode(diagnoseCode);
        return selectOne(sixClassCode);
    }

    /**
     * 根据诊断编码获取诊断名称
     * @param diagnoseCodes
     *
     */
    default List<SixClassCode> getSixClassByCodes(List<String> diagnoseCodes){
        Weekend<SixClassCode> weekend = new Weekend<>(SixClassCode.class);
        WeekendCriteria<SixClassCode, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(SixClassCode::getCode,diagnoseCodes);
        return selectByExample(weekend);
    }
}
