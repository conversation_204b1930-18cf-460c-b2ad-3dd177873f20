package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.DrugUseWay;

/**
 * <AUTHOR>
 * @since 2021/8/9 - 9:41
 */
public interface DrugUseWayRepository {

    /**
     * 查询药品给药途径
     *
     * @param code         给药途径编码
     * @param hospitalCode 医院编码
     * @return DrugUseWay
     */
    DrugUseWay getDrugUseWayByCode(Integer code, Integer hospitalCode);

}
