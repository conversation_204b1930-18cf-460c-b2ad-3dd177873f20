package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationFormType;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

public interface ApplicationFormTypeMapper extends BaseMapper<ApplicationFormType> {

    /**
     * 查询申请单类型
     *
     * @return
     */
    default List<ApplicationFormType> getApplicationFormType() {
        ApplicationFormType applicationFormType = new ApplicationFormType();
        return select(applicationFormType);
    }
}
