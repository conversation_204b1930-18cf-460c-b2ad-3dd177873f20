package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.ExecDeptConfig;
import com.rjsoft.outPatient.infrastructure.repository.entity.ExecDeptTime;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ExecDeptTimeMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ExecDeptTimeRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;


import java.util.List;

@Service
@AllArgsConstructor
public class ExecDeptTimeRepositoryImpl implements ExecDeptTimeRepository {

    ExecDeptTimeMapper execDeptTimeMapper;


    @Override
    @DatabaseAnnotation
    public ExecDeptTime getExecDeptTime(String deptType, Integer hospitalCode) {
        return execDeptTimeMapper.getExecDeptTime(deptType, hospitalCode);
    }


}