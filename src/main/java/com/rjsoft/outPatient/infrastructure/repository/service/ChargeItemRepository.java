package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDto;
import com.rjsoft.outPatient.domain.recipe.dto.ChargeItemDto;
import com.rjsoft.outPatient.domain.recipe.dto.CodeResponse;
import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeItem;
import com.rjsoft.outPatient.infrastructure.repository.entity.DoctorAntimicrobial;

import java.util.List;

/**
 * 收费项目
 *
 * <AUTHOR>
public interface ChargeItemRepository {


    /**
     * 获取收费项目
     *
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    ChargeItem getChargeItemByIdIsStopped(Integer itemCode, Integer deptId, Integer hospitalCode, Integer stopped);
    /**
     * 获取收费项目
     *
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    ChargeItem getChargeItemById(Integer itemCode, Integer hospitalCode);

    @DatabaseAnnotation(name = "HISDB")
    List<ChargeItem> getChargeItemByIdList(List<Integer> itemCodeList, Integer hospitalCode);

    /**
     * 获取收费项目名称
     *
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    String getChargeItemNameById(Integer itemCode, Integer hospitalCode);

    /**
     * 获取收费项目根据项明细集合,
     * 内部增加了TyMap中CHargeItem和specItem的map初始化。
     *         TyMdc.put("chargeItemByIdMap", chargeItemByIdMap);
     *         TyMdc.put("specialItemByIdMap", SpecialItemsMap);
     * 同一主线程只要运行过本方法，后面可以直接取map
     *
     * @param ids
     * @param hospitalCode
     * @return
     */
    List<ChargeItem> getChargeItemByIdsAndInitSpec(List<Integer> ids, Integer hospitalCode);

    void specialItemByIdList2TyMdc(List<Integer> ids, List<Integer> hospitalCodeList);

    /**
     * 根据项目类别，查询系统套餐
     *
     * @param category
     * @param hospitalCode
     * @return
     */
    List<ChargeItem> getPackageItemByCategory(Integer category, Integer hospitalCode, String itemNameOrInputCode);

    /**
     * 根据输入码查询、执行科室、查询药品
     *
     * @param inputCode    输入码
     * @param itemCategory 收费类型编码
     * @param hospitalCode 医院编码
     * @return ChargeItem
     */
    List<ChargeItemDto> getDrugChargeItemByInputCode(String inputCode, Integer itemCategory, Integer hospitalCode, Integer useDept);


    /**
     * 根据输入码查询、执行科室、查询非药品
     *
     * @param inputCode    输入码
     * @param itemCategory 收费类型编码
     * @param hospitalCode 医院编码
     * @param useDept 医生开处方科室
     * @return ChargeItem
     */
    List<ChargeItemDto> getNonDrugChargeItemByInputCode(String inputCode, List<Integer> itemCategory, Integer hospitalCode, Integer useDept);

    /**
     * 根据处方项目编码获取收费表对应的医院处方代码和医保处方代码
     *
     * @param itemCode
     * @param hospitalCode
     */
    List<CodeResponse> getCode(List<Integer> itemCode, Integer hospitalCode);

    /**
     * 根据处方项目编码获取收费表对应的医院处方代码和医保处方代码
     *
     * @param itemCode
     * @param hospitalCode
     */
    List<CodeResponse> getCodeByItemCode(List<Integer> itemCode, Integer hospitalCode, Integer execDept);

    /**
     * 输入码搜索收费项目
     *
     * @param inputCode
     * @param hospitalCode 医院编码
     * @return {@link ChargeItem}
     */
    List<ChargeItem> getSimpleChargeItemList(String inputCode, Integer hospitalCode);


    /**
     * 查询医生抗菌药权限信息
     *
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    List<DoctorAntimicrobial> getDoctorAntimicrobial(Integer doctorId, Integer hospitalCode);

    /**
     * 查询收费项目列表
     *
     * @param inputCode
     * @param itemCategory
     * @param hospitalCode
     * @return
     */
    List<ChargeItem> getChargeItemByInputCode(String inputCode, List<Integer> itemCategory, Integer hospitalCode);

    /**
     * 查询门诊默认收费项目列表
     *
     * @param inputCode
     * @return
     */
     List<ChargeItemExecDto> getChargeItemDtos(String inputCode, Integer hospitalId);
}
