package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegTbDBConnection;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RegTbDBConnectionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RegTbDBConnectionRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

@AllArgsConstructor
@Service
public class RegTbDBConnectionRepositoryImpl implements RegTbDBConnectionRepository {
    private RegTbDBConnectionMapper regTbDBConnectionMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public String getConnectionString(String dbName, Integer hospitalCode) {
        Weekend<RegTbDBConnection> weekend = new Weekend<>(RegTbDBConnection.class);
        weekend.weekendCriteria().andEqualTo(RegTbDBConnection::getDbName,dbName).andEqualTo(RegTbDBConnection::getHospitalCode,hospitalCode.toString());
        List<RegTbDBConnection> regTbDBConnectionList = regTbDBConnectionMapper.selectByExample(weekend);
        if(regTbDBConnectionList!=null&regTbDBConnectionList.size()>0){
            return regTbDBConnectionList.get(0).getConnectionString();
        }else{
            return null;
        }
    }
}
