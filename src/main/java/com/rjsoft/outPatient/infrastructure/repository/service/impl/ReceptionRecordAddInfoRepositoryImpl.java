package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.ReceptionRecordAddInfo;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ReceptionRecordAddInfoMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ReceptionRecordAddInfoRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021/8/9 - 15:17
 */
@Service
@AllArgsConstructor
public class ReceptionRecordAddInfoRepositoryImpl implements ReceptionRecordAddInfoRepository {

    private final ReceptionRecordAddInfoMapper receptionRecordAddInfoMapper;

    @Override
    @DatabaseAnnotation
    public ReceptionRecordAddInfo getReceptionRecordAddInfoById(Long receptionNo, Integer hospitalCode) {
        final ReceptionRecordAddInfo entity = new ReceptionRecordAddInfo();
        entity.setReceptionNo(receptionNo);
        entity.setHospitalCode(hospitalCode);
        return receptionRecordAddInfoMapper.selectByPrimaryKey(entity);
    }
}
