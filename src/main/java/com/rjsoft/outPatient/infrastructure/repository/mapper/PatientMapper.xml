<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.PatientMapper">

    <select id="getPatInfo" resultType="com.rjsoft.outPatient.domain.diseaseReport.dto.ReportContentResult">
        select b1.PatID                     patId,
               b1.PatName                   patName,
               b1.Sex                       gender,
               b1.Birthday                  birthday,
               b2.Nation                    nation,
               b2.Education                 education,
               Occupation                   occupation,
               b2.Marriage                  maritalStatus,
               b2.CompanyAddress            workPlace,
               b1.CertificateNo             idNo,
               b2.ContactsName              contractName,
               b2.ContactsRelationShip      contractRelation,
               b2.ContactsRelationShipPhone contractPhone,
               b2.PatPhone                  phone,
               b1.HospitalCode
        from Reg_Tb_PatientList b1 (nolock)
                 inner join Reg_Tb_PatientDetl b2 (nolock)
                            on b1.PatID = b2.PatID
        where b1.PatID = #{patId}
    </select>

    <select id="getCommonDoctorNo" resultType="java.lang.Integer">
        select a.ItemCode
        from Dbt_GhValidSfItem a (nolock)
                 inner join GUID_GhRegList b (nolock) on a.RegNo = b.RegNo
        where b.GuidReg = #{regNo}
    </select>

    <select id="getPatientByRegNo" resultType="com.rjsoft.outPatient.domain.patient.dto.GetPatientByRegNoDto">
        select b2.PatName       as patName,
               b2.Birthday      as birthday,
               b2.Sex           as gender,
               b2.CertificateNo as idCard,
               b1.CardNo        as infoCardNo,
               b1.OutPatientNo  as caseHistoryNo
        from Reg_Tb_RegisterList_Time b1 (nolock)
                 inner join Reg_Tb_PatientList b2 (nolock) on b1.PatID = b2.PatID
        where b1.RegNo = #{regNo}
    </select>

    <select id="getAllPatientByRegNo" resultType="com.rjsoft.outPatient.domain.patient.dto.GetPatientByRegNoDto">
        select b2.PatName       as patName,
               b2.Birthday      as birthday,
               b2.Sex           as gender,
               b2.CertificateNo as idCard,
               b1.CardNo        as infoCardNo,
               b1.OutPatientNo  as caseHistoryNo
        from Reg_Tv_RegisterList b1 (nolock)
                 inner join Reg_Tb_PatientList b2 (nolock) on b1.PatID = b2.PatID
        where b1.RegNo = #{regNo}
    </select>

    <select id="getWeiNingDto" resultType="com.rjsoft.outPatient.domain.recipe.dto.WeiNingDto">
        SELECT b.CertificateNo      khid,
               b.PatName            khxm,
               b.PatId              patid,
               '123100004250973702' yydm,
               '上海市精神卫生中心'          yymc,
               c.RegNo              jzlsh,
               1                    jzlb,
               15                   sjlx,
               1                    mxsjlx,
               15                   ywlsh,
               GETDATE() timestamp,
        NEWID( ) requestId
        FROM
            Reg_Tb_PatientList b (nolock),
            Reg_Tb_RegisterList_Time c (nolock)
        WHERE
            b.PatID= #{patId}
          AND c.RegNo= #{regNo}
    </select>

    <select id="getPatInfoByRegNo" resultType="com.rjsoft.outPatient.domain.recipe.dto.RecipeInfoResponse">
        SELECT b.HisDictionaryName sex,
               a.Birthday          birthday,
               c.RegistType        registerType,
               d.Name              doctorName
        FROM Reg_Tb_PatientList a (nolock)
                 LEFT JOIN TB_Dic_HisDictionary b ON a.Sex = b.DictionaryCode
            AND DictionaryTypeID = 34
                 INNER JOIN Reg_Tv_RegisterList c (nolock) ON a.PatID = c.PatID
                 LEFT JOIN System_Tb_Worker d ON c.doctorid = d.WorkerId and c.HospitalCode = d.HospitalId
        WHERE c.RegNo = #{regNo}
          AND b.HospitalId = #{hospitalCode}
          AND c.HospitalCode = #{hospitalCode}
    </select>

    <select id="getRegisterType" resultType="java.lang.Integer">
        exec Usp_MZYS_YLJK_GetPatJZLX
        @regno=
        #{regNo},
        @
        hospitalCode
        =
        #{hospitalCode}
    </select>

    <select id="getPubPatientInfoOther"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.PubPatientInfoOther">
        select PatNo    patNo,
               hb       regType,
               zycs     hospCount,
               sfkjsbzl ifCure,
               hj1      domicile1,
               hj2      domicile2,
               hj3      domicile3,
               hj4      domicile4,
               hj5      domicile5,
               hj6      domicile6,
               xj1      presentAddr1,
               xj2      presentAddr2,
               xj3      presentAddr3,
               xj4      presentAddr4,
               xj5      presentAddr5,
               xj6      presentAddr6
        from Tbt_PubPatientInfo_Other (nolock)
        where PatNo = #{patNo}
    </select>

    <select id="getPatientAgeAndSex" resultType="com.rjsoft.outPatient.domain.reserve.dto.ReserveRegisterResponse">
        SELECT DATEDIFF(YEAR,a.Birthday, getdate()) patAge,
        b.HisDictionaryName patSex,
        a.CertificateNo certificateNo
        FROM Reg_Tb_PatientList a (nolock)
        LEFT JOIN Tb_Dic_HisDictionary b
        on b.DictionaryTypeId = 34 AND a.Sex = b.DictionaryCode AND
        a.IsDelete = b.IsDelete AND a.IsUse = b.IsUse
        WHERE a.CertificateNo in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND a.IsDelete = 0
        AND a.IsUse = 1
        AND b.HospitalId = #{hospitalId}
    </select>

    <select id="getNewDiseasePatNos" resultType="com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo">
        select PatID patientNo,
        HospNo hospNo,
        Sex patSex
        from Reg_Tb_PatientList (nolock)
        where IsDelete=0
        and IsUse=1
        and cast(PatID as varchar(50)) in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item.patientNo}
        </foreach>
    </select>

    <select id="getOldDiseasePatNos" resultType="com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo">
        select hzbh patientNo,blkh hospNo,xb patSex from GHSF_TB_HZJBXX where zt = 0 and cast(hzbh as varchar(50)) in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item.patientNo}
        </foreach>
    </select>

    <select id="getGuidPatientByCertificateNo" resultType="java.lang.String">
        select b.hzbh
        from Tbt_PubPatientInfo a (nolock)
                 inner join GHSF_TB_HZJBXX_GUID b (nolock) on a.PatNo = b.PatNo
        where a.PatSfz = #{certificateNo}
    </select>

    <select id="getGuidPatientByPatNo" resultType="java.lang.String">
        select b.hzbh
        from Tbt_PubPatientInfo a (nolock)
                 inner join GHSF_TB_HZJBXX_GUID b on a.PatNo = b.PatNo
        where a.PatID = #{patNo}
    </select>

    <select id="getPatId" resultType="java.lang.Long">
        declare @patid bigint
        exec Usp_Sequences_Reg_PatID @patid output
        select @patid
    </select>

    <select id="getPatCreeditTags" resultType="java.lang.String">
        select ISNULL(stuff((select ','+ CreeditTag  from Pat_TV_CreditTagByIDCard where IdentityNo = #{idCardNo}  for xml path('')),1,1,''),'') as CreeditTags
    </select>

    <select id="queryTreatmentPatientList" resultType="com.rjsoft.outPatient.domain.thridParty.vo.TreatPatientVO">
        SELECT
	        t.PatID patientId,
            t.PatName patientName,
            t.HospNo outPatientNo,
            t.CertificateNo,
            t.Sex sexCode,
            a.marriage marriageCode,
            a.education educationCode,
            t.HospitalCode
        FROM Reg_Tb_PatientList t (nolock)
        INNER JOIN Reg_Tb_PatientDetl a (nolock) ON t.PatID = a.PatID
        WHERE t.CertificateNo IN
        <foreach collection="certificateNos" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and EXISTS (select 1 from MZYS_TB_DZBL sub where sub.hzbh=t.PatID)
    </select>
    <select id="queryPatInfoByPatIds" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.PatientList">
        SELECT
            PatID AS patID,
            NewPatID AS newPatID,
            HospNo AS hospNo,
            PatName AS patName,
            FirstName AS firstName,
            LastName AS lastName,
            ShowName AS showName,
            PatNameSpell AS patNameSpell,
            PatNameCode AS patNameCode,
            CertificateType AS certificateType,
            CertificateNo AS certificateNo,
            Birthday AS birthday,
            Sex AS sex,
            OpCode AS opCode,
            CreateTime AS createTime,
            IsDelete AS isDelete,
            [Order],
            IsUse AS isUse,
            CreatedBy AS createdBy,
            CreatedDate AS createdDate,
            UpdateBy AS updateBy,
            UpdateDate AS updateDate,
            HospitalCode AS hospitalCode,
            RegionCode AS regionCode
        FROM Reg_Tb_PatientList (nolock)
        where PatID IN
        <foreach collection="patIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryPatientDetailByIds" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.PatientDetail">
        SELECT
            PatID AS patID,
            NewPatID AS newPatID,
            RecordNo AS recordNo,
            DiagnoseCode AS diagnoseCode,
            VisitFlag AS visitFlag,
            HealthCardNo AS healthCardNo,
            Marriage AS marriage,
            Nation AS nation,
            Occupation AS occupation,
            Nationality AS nationality,
            birthplace AS birthplace,
            Education AS education,
            Religion AS religion,
            PatPhone AS patPhone,
            AccountFlag AS accountFlag,
            EvidenceNo AS evidenceNo,
            IndustrialInjury AS industrialInjury,
            Member AS member,
            Source AS source,
            AccountUnit AS accountUnit,
            AccountUnitCode AS accountUnitCode,
            AccountUnitType AS accountUnitType,
            PersonalProperty AS personalProperty,
            HealthCare AS healthCare,
            RetirementSituation AS retirementSituation,
            Attributes AS attributes,
            PatType AS patType,
            CompanyName AS companyName,
            CompanyCode AS companyCode,
            CompanyType AS companyType,
            CompanyPhone AS companyPhone,
            CompanyContacts AS companyContacts,
            CompanyContactsTel AS companyContactsTel,
            CompanyContactsPhone AS companyContactsPhone,
            CompanyAddress AS companyAddress,
            ContactsName AS contactsName,
            ContactsRelationShip AS contactsRelationShip,
            ContactsRelationShipPhone AS contactsRelationShipPhone,
            ContactsRelationShipTel AS contactsRelationShipTel,
            ContactsRelationZip AS contactsRelationZip,
            ContactsRelationShipAddress AS contactsRelationShipAddress,
            Remark AS remark,
            OpCode AS opCode,
            CreateTime AS createTime,
            IsDelete AS isDelete,
            [Order],
            IsUse AS isUse,
            CreatedBy AS createdBy,
            CreatedDate AS createdDate,
            UpdateBy AS updateBy,
            UpdateDate AS updateDate,
            HospitalCode AS hospitalCode,
            ExtendInfo AS extendInfo,
            SpecialFlag AS specialFlag,
            SeriousDiseaseCode AS seriousDiseaseCode,
            patflag AS patFlag,
            LiveProv AS liveProv,
            LiveCity AS liveCity,
            LiveCounty AS liveCounty,
            LiveTown AS liveTown,
            LiveVillage AS liveVillage,
            LiveAddr AS liveAddr
        FROM Reg_Tb_PatientDetl (nolock)
        where PatID IN
        <foreach collection="patIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
