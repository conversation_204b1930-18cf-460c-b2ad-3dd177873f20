package com.rjsoft.outPatient.infrastructure.repository.service;


import com.rjsoft.outPatient.domain.enterHospitalRequisition.dto.ProcessScheduleResult;
import com.rjsoft.outPatient.infrastructure.repository.entity.EnterHospitalRequisition;
import com.rjsoft.outPatient.infrastructure.repository.entity.NotificationDetails;

import java.util.List;

/**
 * 告知单明细
 *
 * <AUTHOR>
 * @since 2021/7/19 - 14:16
 */
public interface NotificationDetailsRepository {

    /**
     * 根据就诊流水号查询告知单明细
     *
     * @param rgNo 就诊流水号
     * @return
     */
    Integer findCountNotificationDetailsByRgNoAndGzdName(String rgNo,Integer hospitalCode);

    /**
     * 根据 就诊流水号、告知单名称，完成状态 查询告知单明细
     *
     * @param rgNo    就诊流水号
     * @param gzdName 告知单名称
     * @return
     */
    NotificationDetails findNotificationDetailsByRgNoAndGzdName(String rgNo, String gzdName, Integer hospitalCode);

    /**
     * 查询报告结果
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    ProcessScheduleResult getInspect(String receptionNo, Integer hospitalCode);

    List<String> getNotificationDetailsByRgNosAndGzdName(List<String> rgNos, String gzdName);

    List<EnterHospitalRequisition> getSq(List<Long> regNos1);
}
