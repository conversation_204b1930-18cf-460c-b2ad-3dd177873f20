package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.EmrTbRecinfo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;
import java.util.Set;

public interface EmrTbRecinfoMapper extends BaseMapper<EmrTbRecinfo>, ExampleMapper<EmrTbRecinfo> {
    List<EmrTbRecinfo> getEmrByRegNo(@Param("regNos") List<Integer> regNos, @Param("recTempCodeList") List<String> recTempCodeList,@Param("hospitalCode") int hospitalCode);
}
