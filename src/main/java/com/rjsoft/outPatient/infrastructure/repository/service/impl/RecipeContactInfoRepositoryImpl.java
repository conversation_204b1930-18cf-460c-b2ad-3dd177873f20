package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.infrastructure.repository.entity.OldRecipeContactInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeContactInfo;
import com.rjsoft.outPatient.infrastructure.repository.mapper.OldRecipeContactInfoMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeContactInfoMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeContactInfoRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

/**
 * 门诊处方草药代煎
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class RecipeContactInfoRepositoryImpl implements RecipeContactInfoRepository {
    RecipeContactInfoMapper recipeContactInfoMapper;
    OldRecipeContactInfoMapper oldRecipeContactInfoMapper;

    @Override
    @DatabaseAnnotation
    public List<RecipeContactInfo> getRecipeContactInfoByRecipeNo(List<Integer> recipeNos, Integer hospitalCode) {
        Weekend<RecipeContactInfo> weekend = new Weekend<>(RecipeContactInfo.class);
        weekend.weekendCriteria().andIn(RecipeContactInfo::getRecipeNo,recipeNos)
                .andEqualTo(RecipeContactInfo::getHospitalCode,hospitalCode);
        return recipeContactInfoMapper.selectByExample(weekend);
    }

    @Override
    public List<OldRecipeContactInfo> getOldRecipeContactInfoByRecipeNo(List<String> recipeNos, Integer hospitalCode) {
        String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.MZYS : DatasourceName.MZYS3;
        DataSourceSwitchAspect.changeDataSource(dataSourceName);
        Weekend<OldRecipeContactInfo> weekend = new Weekend<>(OldRecipeContactInfo.class);
        weekend.weekendCriteria().andIn(OldRecipeContactInfo::getRecipeNo,recipeNos);
        return oldRecipeContactInfoMapper.selectByExample(weekend);
    }
}
