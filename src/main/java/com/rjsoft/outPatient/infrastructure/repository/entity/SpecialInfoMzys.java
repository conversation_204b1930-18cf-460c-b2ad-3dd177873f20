package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * 患者特殊说明
 */

@Data
@Table(name = "MZYS_TB_HZTSQK")
public class SpecialInfoMzys implements Serializable {

    @Id
    @Column(name = "tsqkbm")
    private String tsqkbm;

    /**
     * 病历卡号
     */
    @Column(name = "blkh")
    private String hisCardNo;

    /**
     * 患者编号
     */
    @Column(name = "hzbh")
    private String patId;

    /**
     * 说明类型
     */
    @Column(name = "smlx")
    private Integer specialType;

    /**
     * 特殊说明
     */
    @Column(name = "tssm")
    private String content;

    /**
     * 医生编码
     */
    @Column(name = "ysbm")
    private Integer creDoctor;

    /**
     * 医生姓名
     */
    @Column(name = "ysxm")
    private String creDoctorName;

    /**
     * 创建日期
     */
    @Column(name = "cjrq")
    private Date creTime;

    /**
     * 填写日期
     */
    @Column(name = "txrq")
    private Date insertTime;

    /**
     * 删除标记
     */
    @Column(name = "scbj")
    private Integer isDelete;

    /**
     * 删除医生
     */
    @Column(name = "scys")
    private Integer delDoctor;

    /**
     * 删除日期
     */
    @Column(name = "scrq")
    private Date deleteTime;

    /**
     * 医生编码
     */
    @Transient
    private Integer hospitalCode;

    @Transient
    private String source;

    @Transient
    @Column(insertable = false, updatable = false)
    private Integer doctorId;

    @Transient
    private Integer id;
}
