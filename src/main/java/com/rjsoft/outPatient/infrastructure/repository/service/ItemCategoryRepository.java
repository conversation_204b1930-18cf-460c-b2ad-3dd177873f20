package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.ItemCategory;
import java.util.List;

/**
 * 收费项目收费类型字典
 */
public interface ItemCategoryRepository {


    /**
     * 查询收费项目收费类型字典
     *
     * @param hospitalCode
     * @return
     */
    List<ItemCategory> getItemCategoryList(Integer hospitalCode);

    /**
     * 查询门诊收费项目收费类型字典
     *
     * @param hospitalCode
     * @return
     */
    List<ItemCategory> getItemCategoryByhospitalCode(Integer hospitalCode);
}
