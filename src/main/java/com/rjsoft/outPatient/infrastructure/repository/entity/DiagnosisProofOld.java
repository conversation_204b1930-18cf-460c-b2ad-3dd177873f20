package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 疾病证明
 *
 * <AUTHOR>
@Data
@Table(name = "MZYS_TB_JBZM")
public class DiagnosisProofOld implements Serializable {
    /**
     *
     */
    @Id
    @Column(name = "id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 挂号流水号
     */
    @Column(name = "regno")
    private Integer regNo;

    /**
     * 患者编号
     */
//    @Column(name = "hzbh")
//    private String patId;

    /**
     * 姓名
     */
    @Column(name = "Name")
    private String name;

    /**
     * 性别
     */
    @Column(name = "Sex")
    private String sex;

    /**
     * 年龄
     */
    @Column(name = "Age")
    private Integer age;
    /**
     * 病历卡号
     */
    @Column(name = "BlCardNum")
    private String blCardNum;

    /**
     * 诊断时间
     */
    @Column(name = "DiagnosisTime")
    private Date diagnosisTime;

    /**
     * 确诊时间
     */
    @Column(name = "QDiagnosisTime")
    private Date confirmDiagnosisTime;

    /**
     * 入院时间
     */
    @Column(name = "InHospitalTime")
    private Date inHospitalTime;

    /**
     * 诊断
     */
    @Column(name = "Diagnosis")
    private String diagnosis;

    /**
     * 备注
     */
    @Column(name = "Remarks")
    private String remarks;

    /**
     * 其他
     */
    @Column(name = "Other")
    private String other;

    /**
     * 盖章
     */
    @Column(name = "Seal")
    private String seal;


    /**
     * 盖章时间
     */
    @Column(name = "SealTime")
    private Date sealTime;

    /**
     * 状态
     */
    @Column(name = "State")
    private Integer state;


    /**
     * 创建人
     */
    @Column(name = "CreName")
    private String creName;

    /**
     * 创建人id
     */
    @Column(name = "CreId")
    private String creId;

    /**
     * 创建时间
     */
    @Column(name = "CreTime")
    private Date creTime;

    /**
     * 更新人
     */
    @Column(name = "UpdName")
    private String updName;

    /**
     * 更新时间
     */
    @Column(name = "UpdTime")
    private Date updTime;

    /**
     * 删除标记
     */
    @Column(name = "DelFlag")
    private Boolean delFlag;

    /**
     * 是否住院标记
     */
    @Transient
    private Integer inFlag;

    /**
     * 疾病证明诊断
     */
    @Transient
    private List<DiagnosisProofDisease> diagnosisProofDiseaseList;

}
