package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 老处方煎药信息
 *
 * @since 2022-09-20 10:49:37
 */
@Data
@Table(name = "MZYS_TB_CYDJXX")
public class OldRecipeContactInfo implements Serializable {

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "jzlsh")
    private String receptionNo;

    @Column(name = "cflsh")
    private String recipeNo;

    @Column(name = "djid")
    private Integer boilId;

    @Column(name = "djname")
    private String boilName;

    @Column(name = "lxr")
    private String contactName;

    @Column(name = "lxdh")
    private String contactPhone;

    @Column(name = "lxdz")
    private String contactAddress;

    @Column(name = "ysbm")
    private Integer doctorId;

    @Column(name = "createtime")
    private Date createTime;

}

