package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetailSpecial;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeDetailSpecialMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeSpecialMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeDetailSpecialRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@AllArgsConstructor
public class RecipeDetailSpecialRepositoryImpl implements RecipeDetailSpecialRepository {
    private RecipeDetailSpecialMapper recipeDetailSpecialMapper;

    @Override
    @DatabaseAnnotation
    public boolean addRecipeDetailSpecial(List<RecipeDetail> recipeDetailList, Integer doctorId) {
        if(recipeDetailList==null||recipeDetailList.size()==0||doctorId==null){
            return false;
        }
        List<RecipeDetailSpecial> recipeDetailSpecialList = new ArrayList<>();
        Date date = new Date();
        for(RecipeDetail recipeDetail:recipeDetailList) {
            RecipeDetailSpecial recipeDetailSpecial = new RecipeDetailSpecial();
            BeanUtils.copyProperties(recipeDetail,recipeDetailSpecial);
            recipeDetailSpecial.setCreateTime(date);
            recipeDetailSpecial.setDoctorId(doctorId);
            recipeDetailSpecialList.add(recipeDetailSpecial);
       }
       for(RecipeDetailSpecial recipeDetailSpecial:recipeDetailSpecialList){
           recipeDetailSpecialMapper.insertSelective(recipeDetailSpecial);
       }
       return true;
    }
}
