<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.SysFunctionMapper">

    <select id="getXtpz" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbXTPZ">
   select id,gnmk,gjz,nr,sm,sjc from dbo.MZYS_TB_XTPZ where gjz=#{gjz}
  </select>
    <select id="getGetSequences" resultType="java.lang.Long" statementType="CALLABLE">
        {call usp_GetSequencesMZ('${seq.getTable()}','${seq.getColumn()}')}
    </select>
    <select id="getGetSequencesList" resultType="java.lang.Long" statementType="CALLABLE">
        {call usp_GetSequencesMZMul(${num},'${seq.getTable()}','${seq.getColumn()}')}
    </select>

</mapper>