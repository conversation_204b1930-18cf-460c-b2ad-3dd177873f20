package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.utils.ObjectUtils;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ApplyLabItemDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ApplyLabItemDetailRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 化验指标
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class ApplyLabItemDetailRepositoryImpl implements ApplyLabItemDetailRepository {

    ApplyLabItemDetailMapper applyLabItemDetailMapper;


    @Override
    @DatabaseAnnotation(name = DatasourceName.APPLY)
    public List<ApplyLabItemDetail> getApplyLabItemDetail(Integer hospitalCode) {
        ApplyLabItemDetail entity = new ApplyLabItemDetail();
        entity.setHospId(hospitalCode);
        return applyLabItemDetailMapper.select(entity);
    }


}
