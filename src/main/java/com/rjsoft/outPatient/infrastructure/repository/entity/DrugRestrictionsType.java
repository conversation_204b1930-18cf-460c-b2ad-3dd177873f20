package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 药品限制
 * MZYS_NEW..MZYS_TB_YPXZ
 *
 * <AUTHOR>
 * @since 2021/9/27-1:13 下午
 */
@Data
@Table(name = "MZYS_TB_YPXZ")
public class DrugRestrictionsType  implements Serializable {

    /**
     * 唯一标识符
     */
    @Id
    @Column(name = "id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;
    /**
     * 限制明细编号
     */
    @Column(name = "xzlxbh")
    private Integer restrictionCode;
    /**
     * 限制明细名称
     */
    @Column(name = "xzlxmc")
    private String restrictionName;
    /**
     * 备注
     */
    @Column(name = "bz")
    private String remark;
    /**
     * 创建时间
     */
    @Column(name = "cjsj")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    @Column(name = "cjr")
    private Integer createdBy;
    /**
     * 医院编码
     */
    @Column(name = "yybm")
    private Integer hospitalCode;

}
