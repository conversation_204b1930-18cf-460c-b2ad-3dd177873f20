<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ReceptionRecordMapper">


    <select id="getReceptionByParam"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ReceptionRecord">
        select jzlsh as receptionNo
        from MZYS_TB_KZJL  (nolock)
        where DateDiff(dd, sckzrq, #{firstDate}) = 0
          and hzbh = #{patId}
          and ysbm = #{doctorId}
          and ksbm = #{deptId}
          and hospitalCode = #{hospitalCode}
    </select>
    <select id="getPatRecipe" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.PatRecipeInfoDTO">
        SELECT
        jzlsh as receptionNo,
        xmmc as drugName,
        jl as dose,
        jldw as doseUnit,
        yf as usage,
        gytj as administrationRoute
        FROM MZYS_TB_MZCFMX  (nolock)
        WHERE jzlsh in
        <foreach item="receptionNo" collection="receptionNoSet" separator="," open="(" close=")" index="">
            #{receptionNo}
        </foreach>
        and hospitalCode = #{hospitalCode}
    </select>
    <select id="selectDiagnoseList" resultType="com.rjsoft.outPatient.domain.reception.dto.ReceptionDiagnose">
        select distinct a.jzlsh receptionNo, rtrim(a.zdbm) diagnoseNo
        from mzys_tb_mzyszd a  (nolock)
        where a.jzlsh = #{receptionNo}
          and a.zdbj = 0
          and a.yybm = #{hospId}
    </select>
    <select id="selectDiagnoseListByDiagnoseNo"
            resultType="com.rjsoft.outPatient.domain.reception.dto.ReceptionDiagnose">

        SELECT
        b.SBCode sbCode,
        b.sbname sbName
        FROM
        Tbv_PsySixClassCode b  (nolock)
        WHERE
        <foreach collection="list" separator="or" index="index" item="item">
            b.code=#{item.diagnoseNo}
        </foreach>


    </select>
    <select id="getSbName" resultType="java.lang.String">
        select b.sbname
        from tbv_PsyNewPatient a (nolock),
             Tbv_PsySixClassCode b (nolock)
        where a.diagnoseCode = b.sbcode
          and a.idNo = #{certificateNo}
          and a.HospitalCode = #{hospitalCode}
        union all
        select b.sbname
        from Tbt_ZyEMR_Qpmzxbr a (nolock),
             Tbv_PsySixClassCode b (nolock)
        where a.diagnoseCode = b.code
          and a.IdNo = #{certificateNo}
          and a.HospitalCode = #{hospitalCode}
          and Status >= 1
        union all
        select b.sbname
        from Tbv_ZyEMR_Qpyzjszabgk a (nolock),
             Tbv_PsySixClassCode b (nolock)
        where a.icd10 = b.code
          and a.sfz = #{certificateNo}
          and a.HospitalCode = #{hospitalCode};
    </select>
    <select id="getCAResult" resultType="com.rjsoft.outPatient.domain.reception.dto.CAResult">
        SELECT cjrq         createTime,
               CASE
                   WHEN Authorized = 1 THEN
                       'success'
                   ELSE 'fail'
                   END      confirmResult,
               COUNT(jzlsh) blCount
        FROM (
                 SELECT a.jzlsh,
                        a.ysbm,
                        CONVERT(VARCHAR (10), a.cjrq, 120) cjrq,
                        B.upload                           Authorized
                 FROM MZYS_TB_DZBL a (nolock)
                          INNER JOIN MZYS_TB_CALastUploadTime c (nolock) ON a.ysbm = c.DoctorId
                          INNER JOIN MZYS_TB_CABL b (nolock) ON b.jzlsh = a.jzlsh
                 WHERE a.cjrq > c.LastTime
                   AND a.cjrq &lt; DATEADD(MINUTE,- 10, GETDATE())
                   AND a.ysbm = 12002
                   AND 1 = 2
             ) t
        GROUP BY t.cjrq,
                 Authorized
        ORDER BY Authorized
    </select>
    <select id="getBlRecord" resultType="com.rjsoft.outPatient.domain.caseHistoryTemp.dto.BLTimeAndDept">
        SELECT top 1 zhkzrq receptionDate, ksbm dept, hzbh patId, ghlsh regNo
        FROM MZYS_TB_KZJL (nolock)
        where jzlsh = #{receptionNo}
          and hospitalCode = #{hospitalCode}
    </select>

    <select id="getReceptionAndPatNo" resultType="com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo">
        select jzlsh reception,hzbh patientNo,cast(ghlsh as varchar(50)) regNo from MZYS_TB_KZJL (nolock)
        WHERE cast(jzlsh as varchar(50)) in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        union all
        select jzlsh reception,hzbh patientNo ,cast(ghlsh as varchar(50)) regNo from MZYS_TB_KZJL_DATA (nolock) WHERE cast(jzlsh
        as varchar(50)) in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="notFilledReceptionRecord" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto">
        select * from (
            select ROW_NUMBER() over (order by tt.receptionDate desc) RowNumber, tt.*
            from (
                select
                    cast(a.ghlsh as varchar(50)) regNo,
                    cast(a.jzlsh as varchar(50)) receptionNo,
                    a.sckzrq receptionDate,
                    b.OutPatientNo hisCardNo,
                    b.PatID patId,
                    b.PatName patName,
                    b.DeptID deptId,
                    b.VisitFlag visitFlag,
                    c.Sex sexCode,
                    c.CertificateNo certificateNo,
                    a.hospitalCode hospitalCode,
                    1 dataSources,
                    0 isCommitted
                from MZYS_TB_KZJL a (nolock)
                inner join Reg_Tv_RegisterList b (nolock) on a.ghlsh=b.RegNo and a.hospitalCode=b.HospitalCode and b.Status = 0 and b.IsDelete = 0
                inner join Reg_Tb_PatientList c  (nolock) on b.PatID = c.PatID
                where a.hospitalCode = #{hospitalCode} and a.ysbm = #{doctorId} and isnull(a.sfxyxbl, 1) = 1
                and convert(varchar (10), a.sckzrq, 120) between #{startTime} and #{endTime}
                <if test="hisCardNo != '' and hisCardNo != null">
                    and b.OutPatientNo=#{hisCardNo}
                </if>
                <if test="patName != '' and patName != null ">
                    and b.PatName = #{patName}
                </if>
                and not exists (
                    select 1
                    from MZYS_TB_DZBL sub (nolock)
                    where sub.jzlsh = a.jzlsh  and sub.hospitalCode = a.hospitalCode and sub.ysbm = #{doctorId}
                    and convert(varchar (10), sub.cjrq, 120) between #{startTime} and #{endTime}
                )
            ) tt
        ) ttt
        where RowNumber between #{pageSize} * (#{pageNum}-1)+1 and #{pageNum} * #{pageSize}
    </select>

    <select id="notCommittedReceptionRecord" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto">
        select * from (
            select ROW_NUMBER() over (order by tt.receptionDate desc) RowNumber, tt.*
            from (
                select
                    Convert(varchar (50), a.jzlsh) receptionNo,
                    cast(a.regno as varchar(50)) regNo,
                    b.sckzrq receptionDate,
                    c.OutPatientNo hisCardNo,
                    c.PatID patId,
                    c.PatName patName,
                    c.DeptID deptId,
                    c.VisitFlag visitFlag,
                    d.Sex sexCode,
                    d.CertificateNo certificateNo,
                    b.hospitalCode hospitalCode,
                    1 dataSources,
                    0 isCommitted
                from MZYS_TB_DZBL a (nolock)
                inner join MZYS_TB_KZJL b (nolock) on a.jzlsh = b.jzlsh and a.hospitalCode = b.hospitalCode
                inner join Reg_Tv_RegisterList c (nolock) on b.ghlsh=c.RegNo and b.hospitalCode=c.HospitalCode and c.Status = 0 and c.IsDelete = 0
                inner join Reg_Tb_PatientList d  (nolock) on c.PatID = d.PatID
                where a.ysbm = #{doctorId} and a.hospitalCode = #{hospitalCode} and a.tjbj = 0
                and convert(varchar (10), b.sckzrq, 120) between #{startTime} and #{endTime}
                <if test="hisCardNo != '' and hisCardNo != null">
                    and c.OutPatientNo=#{hisCardNo}
                </if>
                <if test="patName != '' and patName != null ">
                    and c.PatName = #{patName}
                </if>
            ) tt
        ) ttt
        where RowNumber between #{pageSize} * (#{pageNum}-1)+1 and #{pageNum} * #{pageSize}
    </select>

    <select id="getCaseHistoryByDoctorId" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto">
        select * from (
            select ROW_NUMBER() over (order by tt.receptionDate desc) RowNumber, tt.*
            from (
                SELECT
                    CONVERT(VARCHAR(50), a.jzlsh) receptionNo,
                    CAST(a.regno AS VARCHAR(50)) regNo,
                    b.sckzrq receptionDate,
                    c.OutPatientNo hisCardNo,
                    c.PatID patId,
                    c.PatName patName,
                    c.DeptID deptId,
                    c.VisitFlag visitFlag,
                    d.Sex sexCode,
                    d.CertificateNo certificateNo,
                    b.hospitalCode hospitalCode,
                    1 dataSources
                FROM MZYS_TB_DZBL a (nolock)
                INNER JOIN MZYS_TB_KZJL b (nolock) ON a.jzlsh = b.jzlsh AND a.hospitalCode = b.hospitalCode
                INNER JOIN HISDB..Reg_Tv_RegisterList c ON b.ghlsh = c.RegNo AND b.hospitalCode = c.hospitalCode
                INNER JOIN HISDB..Reg_Tb_PatientList d on c.PatID = d.PatID
                where a.tjbj = #{status} and a.hospitalCode = #{param.hospitalCode} and a.ysbm = #{param.doctorId}
                and CONVERT ( VARCHAR ( 10 ), b.sckzrq, 120 ) BETWEEN #{param.startTime} and #{param.endTime}
                <if test="param.hisCardNo != null and param.hisCardNo != ''">
                    and a.blkh =#{param.hisCardNo}
                </if>
                <if test="param.patName != null and param.patName != ''">
                    and a.hzxm =#{param.patName}
                </if>
            ) tt
        ) ttt
        where RowNumber between #{param.pageSize} * (#{param.pageNum}-1)+1 and #{param.pageNum} * #{param.pageSize}
    </select>

    <select id="getOldCaseHistoryByDoctorId" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto">
        select Convert(varchar (50), a.jzlsh) receptionNo,
        rtrim(f.blkh) hisCardNo,
        cast(b.ghlsh as varchar(50)) regNo,
        rtrim(f.xm) patName,
        rtrim(f.hzbh) patId,
        b.sckzrq receptionDate,
        e.DeptId deptId,
        d.VisiteFlag visitFlag,
        f.sfzh certificateNo,
        f.xb sexCode
        from MZYS_TB_DZBL a
        inner join MZYS_TB_KZJL b on a.jzlsh = b.jzlsh
        inner join zxhis.dbo.GUID_GhRegList c on b.ghlsh = c.GuidReg
        inner join zxhis.dbo.Tbt_GhRegList_time d on c.RegNo = d.RegNo
        inner join zxhis.dbo.Tbt_GhRegDetl_Time e on c.RegNo = e.RegNo and d.Status = e.Status
        left join GHSF_TB_HZJBXX f on a.hzbh = f.hzbh
        where a.ysbm = #{param.doctorId}
        and d.Status = 0
        and convert(varchar (10), b.sckzrq, 120) between #{param.startTime} and #{param.endTime}
        and tjbj = #{status}
        <if test="param.hisCardNo != null and param.hisCardNo != ''">
            and f.blkh =#{param.hisCardNo}
        </if>
        <if test="param.patName != null and param.patName != ''">
            and f.xm =#{param.patName}
        </if>
    </select>

    <select id="notSignedReceptionRecord" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto">
        select * from (
            select ROW_NUMBER() over (order by tt.receptionDate desc) RowNumber, tt.*
            from (
                select
                    b.jzlsh receptionNo,
                    a.RegNo,
                    b.sckzrq receptionDate,
                    c.OutPatientNo hisCardNo,
                    c.PatID patId,
                    c.PatName patName,
                    c.DeptID deptId,
                    c.VisitFlag visitFlag,
                    d.Sex sexCode,
                    d.CertificateNo certificateNo,
                    b.hospitalCode hospitalCode,
                    1 dataSources,
                    0 isCommitted
                from MZYS_TB_CAResult a (nolock)
                inner join MZYS_TB_KZJL b (nolock) on a.RegNo = cast(b.ghlsh as varchar(50))
                inner join Reg_Tv_RegisterList c (nolock) on b.ghlsh=c.RegNo and b.hospitalCode=c.HospitalCode and c.Status = 0 and c.IsDelete = 0
                inner join Reg_Tb_PatientList d  (nolock) on c.PatID = d.PatID
                where (a.Type = 1 or a.Type = 2) and b.ysbm = #{doctorId} and b.hospitalCode = #{hospitalCode}
                and convert(varchar (10), b.sckzrq, 120) between #{startTime} and #{endTime}
                <if test="hisCardNo != '' and hisCardNo != null">
                    and c.OutPatientNo=#{hisCardNo}
                </if>
                <if test="patName != '' and patName != null ">
                    and c.PatName = #{patName}
                </if>
            ) tt
        ) ttt
        where RowNumber between #{pageSize} * (#{pageNum}-1)+1 and #{pageNum} * #{pageSize}
    </select>
    <select id="getInsuranceByRegNo" resultType="java.lang.Integer">
        select top 1 b.IsInsurance
        from Reg_Tv_RegisterList a (nolock)
                 inner join System_TB_ChargeType b
                            on a.ChargeType = b.ChargeTypeCode and a.HospitalCode = b.HospitalCode
        where a.RegNo = #{regNo}
          and b.HospitalCode = #{hospitalCode}
    </select>

    <select id="getInsuranceByChargeType" resultType="java.lang.Integer">
        select top 1 IsInsurance
        from System_TB_ChargeType
        where ChargeTypeCode = #{chargeType}
        and HospitalCode = #{hospitalCode}
    </select>

    <select id="getDrugReceptionRecord"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ReceptionRecord">
        select a.jzlsh receptionNo,a.ghlsh regNo,a.ksbm deptId,a.ysbm doctorId,a.sckzrq firstDate,a.hospitalCode
        hospitalCode
        from MZYS_TB_KZJL a (nolock)
        inner join MZYS_TB_MZCFMX b (nolock) on a.ghlsh = b.ghlsh and a.jzlsh=b.jzlsh
        where b.ghlsh in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and b.sflx in (12, 13)
        and ISNULL(b.sqd, '') != 'MECT'
        <if test="hospitalCode != null">
            and b.hospitalCode =#{hospitalCode}
        </if>
    </select>

    <select id="getInternetDrugReceptionRecord"
            resultType="com.rjsoft.outPatient.domain.recipe.dto.HistoryReceptionDto">
        select h.RegNo regNo, f.deptName, z.Name as doctorName, f.DeptId deptId, z.WorkerId doctorId, h.hospitalCode,
        h.RegistTime receptionTime
        from Reg_Tv_RegisterList h (nolock)
        inner join reg_tb_PatientList p (nolock) on p.PatID=h.PatID
        left join System_Tb_Department f on f.DeptId=h.DeptId and h.HospitalCode=f.HospitalId
        left join System_Tb_Worker z on h.DoctorId=z.WorkerId and h.HospitalCode=z.HospitalId
        where h.Status=0
        <if test="hospitalCode != null">
            AND z.HospitalId = #{hospitalCode}
        </if>
        AND h.regNo in
        <foreach collection="internetRegNos" index="index" item="regNo" separator="," close=")" open="(">
            #{regNo}
        </foreach>
    </select>

    <select id="getInternetDrugReceptionTimeByRegNos"
            resultType="com.rjsoft.outPatient.domain.recipe.dto.HistoryReceptionDto">
        select  convert(varchar(10), VisitId) receptionNo,regNo
        from Tbt_Internet_Prescription (nolock)
        where
        regNo in
        <foreach collection="regNos" index="index" item="regNo" separator="," close=")" open="(">
            #{regNo}
        </foreach>
    </select>


    <select id="getLastReceptionByDoctorId"
            resultType="java.lang.Long">
        SELECT TOP 1 ghlsh FROM MZYS_TB_KZJL (nolock)
        WHERE ksbm = #{deptId}
          AND ysbm = #{doctorId}
          AND hospitalCode = #{hospitalCode}
          AND DATEDIFF(DAY, sckzrq, GETDATE())=0
        ORDER BY sckzrq DESC
    </select>
</mapper>