package com.rjsoft.outPatient.infrastructure.repository.strategy.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.utils.ObjectUtils;
import com.rjsoft.common.utils.TyListUtil;
import com.rjsoft.common.utils.TyMapUtil;
import com.rjsoft.common.utils.TyMdc;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.common.enums.RecipeStatusEnum;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDeptDto;
import com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDeptDtoWithItemCode;
import com.rjsoft.outPatient.domain.recipe.RecipeDomain;
import com.rjsoft.outPatient.domain.recipe.constant.TyKey;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.*;
import com.rjsoft.outPatient.infrastructure.repository.strategy.PreRecipeStrategy;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Component("PreRecipeByHistory")
@AllArgsConstructor
public class PreRecipeByHistory implements PreRecipeStrategy {

    @Resource
    RecipeRepository recipeRepository;

    SystemTbPubItemsRepository	systemTbPubItemsRepository	;
    SystemTvIteminfodeptRepository systemTvIteminfodeptRepository	;
    VirtualInventoryRepository virtualInventoryRepository	;
    ChargeItemRepository	chargeItemRepository	;

    PreRecipeDetailMapper preRecipeDetailMapper	;
    RecipeDetailMapper recipeDetailMapper	;
    OldRecipeDetailMapper oldRecipeDetailMapper	;
    DrugToHospitalMapper drugToHospitalMapper	;
    SysFunctionMapper sysFunctionMapper	;



    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<PreRecipeDetail> getPreRecipeDetails(CheckRecipeDto checkDto, Map errInfo) {
        List<PreRecipeDetail> details = getPreRecipeDetailsByHistory(checkDto.getOperatingId(), checkDto.getPreSaveNo(),
                checkDto.getDoctorId(), checkDto.getHospitalCode(), checkDto.getRecipeDataSources(),
                checkDto.getRecipeHospitalCode(), errInfo);
        return details;
    }

    /**
     * 获取处方预存明细(历史处方)
     *
     * @param receptionNo
     * @param doctorId
     * @param hospitalCode
     * @param recipeDataSources
     * @param recipeHospitalCode
     * @return
     */
    @DatabaseAnnotation(name = "HISDB")
    public List<PreRecipeDetail> getPreRecipeDetailsByHistory(String receptionNo, Long preSaveNo, Integer doctorId,
                                                              Integer hospitalCode, String recipeDataSources,
                                                              Integer recipeHospitalCode, Map errInfo) {
        List<PreRecipeDetail> res = new ArrayList<>();

        if (preSaveNo != null) {
            PreRecipeDetail entity = new PreRecipeDetail();
            entity.setPreSaveNo(preSaveNo);
            entity.setHospitalCode(hospitalCode);
            List<PreRecipeDetail> preRecipeDetails = preRecipeDetailMapper.select(entity);
            for (PreRecipeDetail preRecipeDetail : preRecipeDetails) {
                preRecipeDetail.setOpFlag(1);
                preRecipeDetail.setOpCode(doctorId);
                res.add(preRecipeDetail);
            }
            return res;
        }


        if (ObjectUtils.isNumber(receptionNo)) {
            List<RecipeDetail> recipeDetails = recipeDetailMapper.getRecipeByReceptionNo(Converter.toInt64(receptionNo), hospitalCode, "");
            if (recipeDetails == null || recipeDetails.isEmpty()) {
                recipeDetails = recipeDetailMapper.getRecipeByReceptionNo(Converter.toInt64(receptionNo), hospitalCode, "MZYS_TB_MZCFMX_DATA");
            }
            for (RecipeDetail recipeDetail : recipeDetails) {
                res.add(new PreRecipeDetail(recipeDetail));
            }
        } else {
            String dataSource = recipeHospitalCode.equals(HospitalClassify.GENERAL.getHospitalCode()) ? DatasourceName.MZYS : DatasourceName.MZYS3;
            DataSourceSwitchAspect.changeDataSource(dataSource);
            List<OldRecipeDetail> oldRecipeDetails = oldRecipeDetailMapper.getOldRecipeDetailByReceptionNo(receptionNo, ItemCategoryEnum.getDrugTypeCodeList(), null);
            if (oldRecipeDetails == null || oldRecipeDetails.size() <= 0) {
                oldRecipeDetails = oldRecipeDetailMapper.getOldRecipeDetailByReceptionNo(receptionNo, ItemCategoryEnum.getDrugTypeCodeList(), "MZYS_TB_MZCFMX_DATA");
            }
            if (oldRecipeDetails == null || oldRecipeDetails.size() <= 0) {
                return res;
            }

            for (OldRecipeDetail oldRecipeDetail : oldRecipeDetails) {
                //医院不同，需要查询药品主索引表，替换药品ID
                if (!hospitalCode.equals(recipeHospitalCode)) {
                    DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
                    List<DrugToHospital> drugToHospitalList = drugToHospitalMapper.getDrugToHospital(oldRecipeDetail.getItemCode());
                    if (drugToHospitalList.size() <= 0) {
                        return res;
                    }
                    List<DrugToHospital> drugToHospitalListHosp = drugToHospitalList.stream().filter(p -> p.getHospitalId().equals(hospitalCode)).collect(Collectors.toList());
                    if (drugToHospitalListHosp.size() <= 0) {
                        return res;
                    }
                    oldRecipeDetail.setItemCode(drugToHospitalListHosp.get(0).getDrugId());
                }
                res.add(new PreRecipeDetail(oldRecipeDetail));
            }
        }
        res.removeIf(i -> ItemCategoryEnum.notWesternAndChinesePat(i.getFeeCategory()));

        //这里是一个关键分支，在这里写入主线程级缓存
        List<Integer> itemCodeListPre = TyListUtil.toFieldList(res, "getItemCode");
        recipeRepository.fillItemExecDeptByTimeItemCodeMap2TyMdc(itemCodeListPre, TyKey.ITEM_EXEC_DEPT_BY_TIME_ITEM_CODE_MAP);

        //判断是否存在相同药品，存在相同药品提示不能导入
        Map<Integer,List<PreRecipeDetail>> gp_map =  res.stream().collect(Collectors.groupingBy(i->i.getItemCode()));
        for (Integer itemCode : gp_map.keySet()) {
            List<PreRecipeDetail> groupResult = gp_map.get(itemCode);
            if (groupResult.stream().count() > 1) {
                errInfo.put("errInfo", groupResult.get(0).getItemName() + "存在重复处方，重复处方不允许全部导入，请单独导入。");
                return res;
            }
        }


        initTyMdcStockAndSpecItemsMap(res);

        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        Long preSaveNoNew = sysFunctionMapper.getGetSequences(SequenceEnum.PRE_SAVE_NO);
        Date date = sysFunctionMapper.getDate();
        List<Long> getSequencesList = new ArrayList<>();
        if(res!=null&& res.size()>0) {
            getSequencesList = sysFunctionMapper.getGetSequencesList(res.size(), SequenceEnum.RECIPE_DETAIL_NO);
        }
        int id = 0;

        List<Integer> itemCodeList = TyListUtil.toDistinctFieldList(res, "getItemCode");
        List<Integer> exeDeptIdList = TyListUtil.toDistinctFieldList(res, "getExecDept");

        List<SystemTbPubItems> systemTbPubItemList = systemTbPubItemsRepository.ListChargeItemByIdIsStopped(
                itemCodeList, exeDeptIdList, hospitalCode, null);
        Map<Integer,SystemTbPubItems> systemTbPubItemMapByItemCode = TyMapUtil.listToMap(systemTbPubItemList,
                "getItemCode");
        TyMdc.put(TyKey.SYSTEM_TB_PUB_ITEM_MAP_BY_ITEM_CODE,systemTbPubItemMapByItemCode);
        for (PreRecipeDetail preRecipeDetail : res) {
            preRecipeDetail.setPreSaveNo(preSaveNoNew);
            DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
            //改成从list中取 yutao
//            Long recipeDetailNo = sysFunctionMapper.getGetSequences(SequenceEnum.RECIPE_DETAIL_NO);
            Long recipeDetailNo = getSequencesList.get(id);
            id+=1;

            preRecipeDetail.setRecipeDetailNo(recipeDetailNo);
            preRecipeDetail.setRecipeNo(0L);
            preRecipeDetail.setRecipeStatus(RecipeStatusEnum.UNREVIEWED.getCode());
            preRecipeDetail.setOpFlag(0);
            preRecipeDetail.setGroupNo(0);
            preRecipeDetail.setOpTime(date);

            //药品直接获取当前配置的执行科室
            Integer feeCategory = preRecipeDetail.getFeeCategory();
            if (feeCategory!=null && ItemCategoryEnum.isDrug(feeCategory)) {
                recipeRepository.setDefaultExeDept(preRecipeDetail, hospitalCode, preRecipeDetail.getItemCode(), feeCategory);
            }

            //SystemTbPubItems chargeItem = systemTbPubItemsRepository.getChargeItemById(preRecipeDetail.getItemCode(),hospitalCode,null);
//            SystemTbPubItems chargeItem = systemTbPubItemsRepository.getChargeItemByIdIsStopped(preRecipeDetail.getItemCode(),
//                    preRecipeDetail.getExecDept(), hospitalCode,null);
            SystemTbPubItems chargeItem = systemTbPubItemsRepository.tyMdcGetChargeItemByIdIsStopped(preRecipeDetail.getItemCode(),
                    preRecipeDetail.getExecDept(), hospitalCode,null);

//            ChargeItem chargeItem = chargeItemRepository.getChargeItemById(preRecipeDetail.getItemCode(), hospitalCode);
            if(chargeItem == null){
                Log.info(preRecipeDetail.getItemName() + "无匹配药品或未启用，无法进行处方导入");
                errInfo.put("errInfo", preRecipeDetail.getItemName() + "无匹配药品或未启用，无法进行处方导入");
                return res;
            }

            chargeItem = systemTbPubItemsRepository.chargeItemTrim(chargeItem); //这实现..坑
            preRecipeDetail.ChangeChargeItemPart(chargeItem);
            ChargeItem calcItem = new ChargeItem();
            BeanUtils.copyProperties(chargeItem, calcItem);
            recipeRepository.calcRecipeQty(preRecipeDetail, calcItem);
        }
        return res;
    }

    private void initTyMdcStockAndSpecItemsMap(List<PreRecipeDetail> res) {
        List<Integer>itemCodeList = new ArrayList<>();
        List<Integer>deptIdList = new ArrayList<>();

        for (PreRecipeDetail preRecipeDetail : res) {
            Integer itemCode = preRecipeDetail.getItemCode();
            //默认个不存在的整数，用来排除不正确的入参
            Integer itemCodeInt = -999998;
            try {
                itemCodeInt = Integer.valueOf(itemCode);
            } catch (Exception e) {

            }
            itemCodeList.add(itemCodeInt);
        }
        List<ChargeItemExecDeptDtoWithItemCode> defaultExecDepts = systemTvIteminfodeptRepository.getItemsExecDeptByItemCodeList(itemCodeList);
        //先只考虑itemCode,查询中   这些都是写死的WHERE a.userange = 1 AND a.stopped =0 AND a.exeDept != 0
        Map<Integer,List<ChargeItemExecDeptDtoWithItemCode>> defaultExecDeptMap = TyMapUtil.listToListMap(defaultExecDepts, "getItemCode");
        TyMdc.put(TyKey.DEFAULT_EXEC_DEPT_MAP, defaultExecDeptMap);

        Map<Integer, Integer> deptIdMap = new HashMap<>();
        Map<Integer,Integer>hospitalIdMap = new HashMap<>();
        for (ChargeItemExecDeptDto c : defaultExecDepts) {
            deptIdMap.put(c.getExecDept(), c.getExecDept());
            hospitalIdMap.put(c.getExecHospitalId(),c.getExecHospitalId());
        }
        List<Integer> hospitalIdList = new ArrayList<>(hospitalIdMap.keySet());
        deptIdList = deptIdMap.keySet().stream().collect(Collectors.toList());

        //查个Map出来
        List<DeptItempStock> stockList =
                virtualInventoryRepository.getClinicPackFactorInventoryByDrugIdAndDeptId(itemCodeList,
                        deptIdList);
        Map<String, BigDecimal> stockMap = new HashMap<>();
        for (DeptItempStock dis : stockList) {
            String key=""+dis.getDeptId()+"_"+dis.getItemCode();
            stockMap.put(key, dis.getStock());
        }
        TyMdc.put(TyKey.STOCK_MAP,stockMap);

        chargeItemRepository.specialItemByIdList2TyMdc(itemCodeList,hospitalIdList);
    }

}
