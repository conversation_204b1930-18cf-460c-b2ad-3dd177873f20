package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugUseWay;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugUseWayMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DrugUseWayRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021/8/9 - 9:41
 */
@Service
@AllArgsConstructor
public class DrugUseWayRepositoryImpl implements DrugUseWayRepository {

    private final DrugUseWayMapper drugUseWayMapper;


    @Override
    @DatabaseAnnotation
    public DrugUseWay getDrugUseWayByCode(Integer code, Integer hospitalCode) {
        final DrugUseWay entity = new DrugUseWay();
        entity.setCode(code);
        entity.setHospitalCode(hospitalCode);
        return drugUseWayMapper.selectOne(entity);
    }
}
