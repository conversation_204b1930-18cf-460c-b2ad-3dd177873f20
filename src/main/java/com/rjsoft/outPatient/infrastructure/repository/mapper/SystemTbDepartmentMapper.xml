<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.SystemTbDepartmentMapper">

    <select id="judgeSpecialDisease" resultType="java.lang.Integer">
        select count(*)
        from hisdb..System_Tb_Department
        where DeptId = #{deptId}
          and HospitalId = #{hospitalCode}
          and DeptClassCode = 1
          and Remark = '专病'
    </select>

    <select id="speciaDiseaseCount" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ItemCount">
        select count(*),DeptId id,HospitalId pId
        from hisdb..System_Tb_Department
        where  DeptClassCode = 1
          and Remark = '专病'
        group by DeptId,HospitalId;
    </select>

    <select id="getDeptList" resultType="com.rjsoft.outPatient.domain.caseHistoryTemp.dto.DeptDto">
        SELECT DeptId     AS deptId,
               DeptName   AS deptName,
               HospitalId AS hospCode
        FROM System_Tb_Department
        WHERE DeptClassCode = 1
          AND HospitalId = #{hospCode}
          AND Status = 1
        ORDER BY SID
    </select>
    <select id="getZyDeptList" resultType="com.rjsoft.outPatient.domain.caseHistoryTemp.dto.DeptDto">
        SELECT DeptId     AS deptId,
               DeptName   AS deptName,
               HospitalId AS hospCode
        FROM System_Tb_Department
        WHERE DeptClassCode = 2
          AND HospitalId = #{hospCode}
          AND Status = 1
        ORDER BY SID
    </select>
</mapper>