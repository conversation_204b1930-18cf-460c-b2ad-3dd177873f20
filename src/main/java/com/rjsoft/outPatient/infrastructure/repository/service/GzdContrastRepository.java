package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.GzdContrast;

import java.util.List;

/**
 * 项目告知单对照
 *
 * <AUTHOR>
public interface GzdContrastRepository {

    /**
     * 查询告知单项目类型对照设置
     *
     * @param gzdType
     * @param hospitalCode
     * @return
     */
    List<GzdContrast> getGzdContrastByType(Integer gzdType, Integer hospitalCode);

    /**
     * 保存告知单对照
     *
     * @param gzdContrast
     * @return
     */
    boolean saveGzdContrast(GzdContrast gzdContrast);

    /**
     * 删除告知单对照
     *
     * @param id
     * @return
     */
    boolean delGzdContrast(Integer id);

    /**
     * 根据itemId查询告知单类型
     *
     * @param itemId
     * @param hospitalCode
     * @return
     */
    GzdContrast getGzdContrast(Integer itemId, Integer hospitalCode);
}
