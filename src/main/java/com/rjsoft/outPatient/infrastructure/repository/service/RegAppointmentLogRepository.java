package com.rjsoft.outPatient.infrastructure.repository.service;

import com.github.pagehelper.PageInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegAppointmentLog;

import java.util.List;

public interface RegAppointmentLogRepository {
    PageInfo<RegAppointmentLog> getList(String cardNo, String beginTime, String endTime, Integer type, Integer hsopitalId, Integer pageNum, Integer pageSize);

    int insertLog(RegAppointmentLog regAppointmentLog);
}
