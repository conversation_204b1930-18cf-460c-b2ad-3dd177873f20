package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationContentItems;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

public interface ApplicationContentItemsMapper extends BaseMapper<ApplicationContentItems> {

    default List<ApplicationContentItems> GetApplicationContentItems(Integer contentId, Integer hospitalCode) {
        ApplicationContentItems applicationContentItems = new ApplicationContentItems();
        applicationContentItems.setContentId(contentId);
        return select(applicationContentItems);
    }

    /**
     * 根据 内容id 删除
     *
     * @param contentId 内容id
     * @return 受影响行数
     */
    @DatabaseAnnotation
    default int deleteByContentId(Integer contentId) {
        final ApplicationContentItems entity = new ApplicationContentItems(contentId);
        return delete(entity);
    }

}
