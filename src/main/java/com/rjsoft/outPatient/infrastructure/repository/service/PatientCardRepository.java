package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.PatientCard;

import java.util.List;

public interface PatientCardRepository {
    List<PatientCard> getPatSciCardList(Integer patId, String hisCardNo);

    void savePatSciCard(PatientCard patientCard);

    PatientCard getExitSciCard(PatientCard patientCard);


    /**
     * 查询患者卡表信息
     *
     * @param patId
     * @param cardNo
     * @return
     */
    PatientCard getPatientCard(Integer patId, String cardNo);

    List<PatientCard> queryPatSciCardListByCardNo(String cardNo);

}
