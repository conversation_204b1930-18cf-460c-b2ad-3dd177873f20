<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ApplicationControlItemMapper">


    <select id="getApplicationControlItem"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationControlItems">
            select distinct
            b.sqdzblsh applicationConNo,
            b.sfxm itemCode,
            b.xmmc itemName,
            b.yybm hospitalCode
            from MZYS_TB_JCSQDZB a (nolock)
            inner join MZYS_TB_JCSQDZB_XM b (nolock) on a.lsh=b.sqdzblsh
            where a.sqdbm=#{applicationConNo}
            and b.yybm=#{hospitalCode}
    </select>

</mapper>