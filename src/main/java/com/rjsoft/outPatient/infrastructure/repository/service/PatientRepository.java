package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.RetPatientResult;
import com.rjsoft.outPatient.domain.disease.vo.DiseaseProofPatInVO;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo;
import com.rjsoft.outPatient.domain.diseaseReport.dto.ReportContentResult;
import com.rjsoft.outPatient.domain.patient.dto.GetPatientByRegNoDto;
import com.rjsoft.outPatient.domain.patient.dto.PatientInfoDto;
import com.rjsoft.outPatient.domain.patient.vo.RelationPatRespVO;
import com.rjsoft.outPatient.domain.recipe.dto.RecipeInfoResponse;
import com.rjsoft.outPatient.domain.recipe.dto.WeiNingDto;
import com.rjsoft.outPatient.domain.reserve.dto.ReserveRegisterResponse;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 患者信息
 */
public interface PatientRepository {

    /**
     * 查询患者基本信息
     *
     * @param patId
     * @param hospitalCode
     * @return
     */
    PatientList getPatientList(Integer patId, Integer hospitalCode);

    /**
     * 根据身份证号获取患者信息
     *
     * @param patSfz
     * @param hospitalCode
     * @return
     */
    List<PatientList> getPatientListBySfz(String patSfz, Integer hospitalCode);

    /**
     * 查询患者补充信息
     *
     * @param patId
     * @param hospitalCode
     * @return
     */
    PatientDetail getPatientDetail(Integer patId, Integer hospitalCode);

    /**
     * 查询患者补充信息
     *
     * @param patIds
     * @param hospitalCode
     * @return
     */
    List<PatientDetail> queryPatientDetailByIds(List<Integer> patIds, Integer hospitalCode);

    /**
     * 查询患者卡表信息
     *
     * @param patId
     * @return
     */
    List<PatientCard> getPatientCard(Integer patId);

    /**
     * 更新患者基本信息
     *
     * @param patientList
     * @return
     */
    boolean UpdatePatientList(PatientList patientList);

    /**
     * 更新患者补充信息
     *
     * @param patientDetail
     * @return
     */
    boolean UpdatePatientDetail(PatientDetail patientDetail);


    /**
     * 保存代办人信息
     *
     * @param agent
     * @return
     */
    boolean saveAgentInfo(Agent agent);

    /**
     * 查询代办人信息
     *
     * @param patId       患者id
     * @param receptionNo 患者就诊流水号
     * @param hospCode    医院编码
     */
    List<Agent> queryAgentInfo(Integer patId, Integer receptionNo, Integer hospCode);

    /**
     * 是否存在代办信息；根据就诊流水号、医院编码
     *
     * @param receptionNo 就诊流水号
     * @param hospCode    医院编码
     * @return 存在返回 {@code TRUE} 否则 {@code FALSE}
     */
    boolean isExistsAgentInfo(Integer receptionNo, Integer hospCode);

    /**
     * 根据患者编号判断是否是司法患者
     *
     * @param patId    患者编号
     * @param hospCode 医院编码
     * @return
     */
    boolean judgeJudicialPat(Long patId, Integer hospCode);

    /**
     * 根据患者编号和医院编码获取疾病报告中患者基本信息
     *
     * @param patId    患者编号
     * @param hospCode 医院编码
     * @return
     */
    ReportContentResult getPatInfo(String patId, String hospCode);

    /**
     * 根据身份证号+医院编码获取患者信息
     *
     * @param certificateNo 身份证号
     * @param hospCode      医院编码
     * @return
     */
    List<PatientList> getPatInfoByIdCard(String certificateNo, Integer hospCode);

    /**
     * 根据挂号流水号获取普通医生编码
     *
     * @param regNo 挂号流水号
     * @return
     */
    int getCommonDoctorNo(Long regNo);

    /**
     * 根据patId查询患者信息
     *
     * @param patId
     * @param hospitalCode
     * @return
     */
    RetPatientResult getPatient(Integer patId, Integer hospitalCode);

    /**
     * 查询患者挂号信息
     *
     * @param outPatientNo
     * @param hospitalCode
     * @return
     */
    List<RegisterListResult> listOutPatientNo(String outPatientNo, Integer hospitalCode);

    /**
     * 根据患者编码+医院编码从hisdb..Reg_Tb_PatientList_KY获取科研患者信息
     *
     * @param patId
     * @param hospitalCode
     * @return
     */
    PatientListResearch getResearchPatient(Integer patId, Integer hospitalCode);

    /**
     * 查询MZYS库患者科研信息
     *
     * @param patId
     * @param hospitalCode
     * @return
     */
    PatientListResearchOld getResearchOldPatient(Integer patId, Integer hospitalCode);

    /**
     * 根据挂号流水号获取患者信息
     *
     * @param regNo
     * @return
     */
    GetPatientByRegNoDto getPatientByRegNo(String regNo);

    /**
     * 获取患者信息
     *
     * @param regNo
     * @param hospitalCode
     * @param workerId
     */
    RecipeInfoResponse getPatInfoByRegNo(Long regNo, Integer hospitalCode, Integer workerId);

    /**
     * 根据挂号流水号和医院编号获取就诊类型
     *
     * @param regNo
     * @param hospitalCode
     */
    Integer getRegisterType(Integer regNo, Integer hospitalCode);

    /**
     * 根据患者身份证号获取患者全部信息
     *
     * @param certificateNo
     * @param hospitalId
     */
    List<PatientList> getPatientInfos(String certificateNo, Integer hospitalId);


    /**
     * 根据patId+hospId获取patNo  hisdb..Tbt_PatID_PatNo
     *
     * @param patIdNo
     * @return
     */
    Integer getPatNo(String patIdNo, Integer hospitalCode);

    /**
     * 根据patNo 获取Tbt_PubPatientInfo_Other
     *
     * @param patNo
     * @return
     */
    PubPatientInfoOther getPubPatientInfoOther(Integer patNo, Integer hospCode);

    /**
     * 根据患者身份证号获取患者年龄和性别
     *
     * @param certificateNo
     * @param hospitalId
     */
    List<ReserveRegisterResponse> getPatientAgeAndSex(List<String> certificateNo, Integer hospitalId);

    /**
     * 根据卡号获取患者基本信息
     *
     * @param cardNo
     * @param hospitalCode
     * @param page
     * @param siz
     * @return
     */
    List<PatientInfoDto> getPatientDetailByPatId(String cardNo, Integer hospitalCode, Integer page, Integer siz);

    /**
     * 根据患者编号获取患者信息
     *
     * @param list
     * @param hospitalCode
     */
    List<DiagnoseRecordInfo> getDiseasePatNos(@Param("list") List<DiagnoseRecordInfo> list, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据患者编号列表、医院编码 获取患者基本信息
     *
     * @param patIds
     * @param hospitalCode
     */
    List<PatientList> getPatInfoByPatIds(List<String> patIds, Integer hospitalCode);


    /**
     * 获取老系统GUID患者编号
     *
     * @param certificateNo
     * @return
     */
    List<String> getGuidPatientByCertificateNo(String certificateNo);

    /**
     * 获取老系统GUID患者编号
     *
     * @param patNo
     * @return
     */
    List<String> getGuidPatientByPatNo(Integer patNo);

    /**
     * 查询预检台和门诊患者对照信息
     *
     * @param patId
     * @return
     */
    PatIdPatNo getPatIDPatNo(Integer patId, Integer hospitalCode);

    /**
     * 查询预检台和门诊患者对照信息
     *
     * @param patIdList
     * @return
     */
    List<PatIdPatNo> queryPatIDPatNoList(List<Integer> patIdList, Integer hospitalCode);

    /**
     * 查询预检台和门诊患者对照信息
     *
     * @param patId
     * @return
     */
    List<PatIdPatNo> getPatIDPatNoList(Integer patId, Integer hospitalCode);

    /**
     * 更新预检台在沪地址
     *
     * @param patNo
     * @return
     */
    int updateByPatNo(Integer patNo, String addr);

    /**
     * 查询预检台在沪地址
     *
     * @param patNo
     * @return
     */
    String getByPatNo(Integer patNo);

    List<RelationPatRespVO> queryRelationPatientInfo(String patId);

    /**
     * certificateNo、patName、patSex查询患者主索引
     * @param certificateNo,patName,patSex
     * @return
     */
    List<Integer> queryRelationPatientEmpiId(String certificateNo, String patName, Integer patSex, Integer hospitalCode);

    /**
     * 根据patId List获取patNo映射数据
     * @param patIds
     * @return
     */
    List<PatIdPatNo> getPatIDPatNoList(List<Integer> patIds);

    /**
     * 新增患者时获取patId
     *
     * @return
     */
    Long getPatId();

    /**
     * 新增患者
     *
     * @return
     */
    boolean savePatient(PatientList patientList);

    /**
     * 根据patId获取住院记录
     * @param patId
     * @param hospitalCode
     * @return
     */
    List<DiseaseProofPatInVO> getInPatinetInfo(Integer patId, Integer hospitalCode) throws Exception;

    /**
     * 获取患者失信标签
     *
     * @return
     */
    String getPatCreeditTags(String idCardNo);

    List<Integer> getPatientAllPatIds(String patId);

    /**
     * 查询预检台和门诊患者对照信息
     *
     * @param patId
     * @return
     */
    List<PatIdPatNo> getPatIDPatNo(Integer patId);


    /**
     * 根据就诊信息的患者编号和就诊流水号，匹配卫宁推送数据
     *
     * @param patId
     * @param regNo
     */
    WeiNingDto getWeiNingDto(Long patId, Long regNo);

}
