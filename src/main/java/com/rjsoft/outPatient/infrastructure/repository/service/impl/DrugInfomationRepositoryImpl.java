package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.utils.TyMapUtil;
import com.rjsoft.common.utils.TyMdc;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.domain.prescriptionAudit.dto.DrugUnitInfoDto;
import com.rjsoft.outPatient.domain.recipe.constant.TyKey;
import com.rjsoft.outPatient.domain.recipe.dto.DrugYYYDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugInfomation;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugInfomationMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DrugInfomationRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.*;

/**
 * 药品信息
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class DrugInfomationRepositoryImpl implements DrugInfomationRepository {

    DrugInfomationMapper drugInfomationMapper;

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<DrugInfomation> getDrugInfoByIds(Iterable<Integer> ids, Integer hospitalCode) {
        return drugInfomationMapper.getDrugInfoByIds(ids, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public DrugInfomation getDrugInfoById(Integer id, Integer hospitalCode) {
        return drugInfomationMapper.getDrugInfoById(id, hospitalCode);
    }

     @Override
     @DatabaseAnnotation(name = "HISDB")
    public DrugInfomation tyMdcGetDrugInfoById(Integer id, Integer hospitalCode) {
         Map<Integer,DrugInfomation> filtedDrugInfomationMap = TyMdc.get(TyKey.FILTED_DRUG_INFOMATION_MAP);
         if(filtedDrugInfomationMap != null){
             return filtedDrugInfomationMap.get(id);
         }
        return drugInfomationMapper.getDrugInfoById(id, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public Map<Integer,DrugInfomation> mapDrugInfoById(List<Integer> itemCodList, Integer hospitalCode) {
        List<DrugInfomation> drugInfomations = getDrugInfoByIdList(itemCodList,hospitalCode);
        Map<Integer,DrugInfomation> map = TyMapUtil.listToMap(drugInfomations, "getDrugId");
        return map;
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<DrugInfomation> getDrugInfoByIdList(List<Integer> idList, Integer hospitalCode) {
        if(idList==null||idList.size()==0){
            return new ArrayList<>();
        }
        Weekend<DrugInfomation> weekend = new Weekend<>(DrugInfomation.class);

        weekend.weekendCriteria()
                .andIn(DrugInfomation::getDrugId, idList)
                .andEqualTo(DrugInfomation::getHospitalId, hospitalCode);

        return drugInfomationMapper.selectByExample(weekend);
    }


    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<DrugInfomation> getDrugInfomationByDrugIdsAndProperty(Integer operateType, List<Integer> ids, Integer hospitalCode) {
        final Weekend<DrugInfomation> weekend = new Weekend<>(DrugInfomation.class);
        if (operateType.equals(1)) {
            weekend.weekendCriteria().andEqualTo(DrugInfomation::getHospitalId, hospitalCode)
                    .andIn(DrugInfomation::getDrugId, ids)
                    .andNotIn(DrugInfomation::getProperty, Arrays.asList("3", "5"));
        } else {
            weekend.weekendCriteria().andEqualTo(DrugInfomation::getHospitalId, hospitalCode)
                    .andIn(DrugInfomation::getDrugId, ids)
                    .andIn(DrugInfomation::getProperty, Collections.singletonList("5"));
        }
        return drugInfomationMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public DrugUnitInfoDto getDrugUnitInfo(String drugId, String hospitalCode) {
        return drugInfomationMapper.getDrugUnitInfo(drugId, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = "PRIMARY")
    public Boolean checkDrugIsYYY(Integer itemId, Integer hospId) {
        Map<Integer, DrugYYYDto> drugYYYDtoMap = TyMdc.get(TyKey.DRUG_YYY_DTO_MAP);
        if(drugYYYDtoMap==null) {
            Boolean b = false;
            DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
            b = drugInfomationMapper.checkDrugIsYYY(itemId, hospId);
            return b;
        }else{
            DrugYYYDto drugYYYDto = drugYYYDtoMap.get(itemId);
            return drugYYYDto==null?null:drugYYYDto.getKeyValue();
        }
    }

    @Override
    @DatabaseAnnotation(name = "PRIMARY")
    public List<DrugYYYDto> listDrugAllYYY(Integer hospId) {
        return drugInfomationMapper.ListDrugAllYYY(hospId);
    }

    @Override
    @DatabaseAnnotation(name = "PRIMARY")
    public Integer checkYYYAuthority(Integer itemId, Integer hospId) {
        return drugInfomationMapper.checkYYYAuthority(itemId, hospId);
    }

    @Override
    public Boolean doctorOpenYYYCheck(Integer itemId, Integer doctorId, Integer hospId) {
        Log.info("医生开设原研药权限判断-------------------------是否原研药物判断");
        Boolean isOriginal = checkDrugIsYYY(itemId, hospId);
        if (isOriginal != null && isOriginal) {
            Log.info("医生开设原研药权限判断-------------------------医生开设权限判断");
            Integer authority = checkYYYAuthority(doctorId, hospId);
            if (authority > 0) {
                return true;
            } else {
                return false;
            }
        }
        return true;
    }

}
