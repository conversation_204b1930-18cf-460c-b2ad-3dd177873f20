package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.ItemSet;
import com.rjsoft.outPatient.infrastructure.repository.entity.ItemSetDetail;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ItemSetDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ItemSetMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ChargeItemRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.ItemSetDetailRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.SystemTbPubItemsRepository;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 项目套餐明细
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class ItemSetDetailRepositoryImpl implements ItemSetDetailRepository {
    ItemSetDetailMapper itemSetDetailMapper;
    ChargeItemRepository chargeItemRepository;
    ItemSetMapper itemSetMapper;
    SystemTbPubItemsRepository systemTbPubItemsRepository;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<ItemSet> getItemSetByCategory(Integer itemCategory, Integer hospitalCode) {
        final Weekend<ItemSet> weekend = new Weekend<>(ItemSet.class);
        weekend.weekendCriteria().andEqualTo(ItemSet::getSetType, itemCategory)
                .andEqualTo(ItemSet::getHospitalCode, hospitalCode)
                .andIn(ItemSet::getUseRange, Arrays.asList(0, 1));
        return itemSetMapper.selectByExample(weekend);
    }

    /**
     * 根据套餐ID查询套餐明细
     *
     * @param setId
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<ItemSetDetail> getItemSetDetailBySetId(Integer setId, Integer hospitalCode) {
        final Weekend<ItemSetDetail> weekend = new Weekend<>(ItemSetDetail.class);
        weekend.weekendCriteria().andEqualTo(ItemSetDetail::getSetId, setId)
                .andEqualTo(ItemSetDetail::getHospitalId, hospitalCode)
                .andIn(ItemSetDetail::getUseRange, Arrays.asList(0, 1));
        return itemSetDetailMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public boolean hasItemSetDetail(Integer setId, Integer hospitalCode) {
        if (ObjectUtils.isEmpty(setId) || ObjectUtils.isEmpty(hospitalCode)) {
            return false;
        }
        // 查询出套餐明细
        final Weekend<ItemSetDetail> weekend = new Weekend<>(ItemSetDetail.class);
        weekend.setCountProperty("itemId");
        weekend.weekendCriteria()
                .andEqualTo(ItemSetDetail::getSetId, setId)
                .andEqualTo(ItemSetDetail::getHospitalId, hospitalCode)
                .andIn(ItemSetDetail::getUseRange, Arrays.asList(0, 1));
        final List<ItemSetDetail> setDetailList = itemSetDetailMapper.selectByExample(weekend);

        if (ObjectUtils.isNotEmpty(setDetailList)) {
            // 通过 itemCode 查询收费项目表统计数量；
            final long count = setDetailList.parallelStream().map(ItemSetDetail::getItemId)
                    .map(i -> systemTbPubItemsRepository.getChargeItemById(i, hospitalCode,0))
                    .filter(ObjectUtils::isNotEmpty).count();
            return count > 0;
        }
        return false;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Integer> getItemIdsBySetId(Integer setId, Integer hospitalCode) {
        final List<ItemSetDetail> itemSetDetails = itemSetDetailMapper.selectByExample(new Example.Builder(ItemSetDetail.class)
                .select("itemId")
                .where(WeekendSqls.<ItemSetDetail>custom()
                        .andEqualTo(ItemSetDetail::getSetId, setId)
                        .andEqualTo(ItemSetDetail::getHospitalId, hospitalCode)
                        .andIn(ItemSetDetail::getUseRange, Arrays.asList(0, 1))).build());
        return itemSetDetails.isEmpty() ? null : itemSetDetails.stream().map(ItemSetDetail::getItemId).collect(Collectors.toList());
    }

    /**
     * 根据套餐ID集合获取套餐明细
     *
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<ItemSetDetail> getItemSetDetailBySetIds(List<Integer> setIdList,Integer deptId,Integer hospitalCode) {
        return itemSetDetailMapper.getItemSetDetailByDeptId(setIdList,deptId,hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public String getItemSetNameByIdOrDefault(Long packageId, Integer hospitalCode, String defaultValue) {
        final Weekend<ItemSet> weekend = new Weekend<>(ItemSet.class);
        weekend.selectProperties("setName");
        weekend.weekendCriteria()
                .andEqualTo(ItemSet::getSetId, packageId)
                .andEqualTo(ItemSet::getHospitalCode, hospitalCode)
                .andIn(ItemSet::getUseRange, Arrays.asList(0, 1));
        final ItemSet itemSet = itemSetMapper.selectOneByExample(weekend);
        if (itemSet == null) {
            return defaultValue;
        }
        return Optional.ofNullable(itemSet.getSetName()).orElse(defaultValue);
    }

}
