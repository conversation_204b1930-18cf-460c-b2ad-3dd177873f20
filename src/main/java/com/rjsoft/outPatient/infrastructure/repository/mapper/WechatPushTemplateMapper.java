package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Agent;
import com.rjsoft.outPatient.infrastructure.repository.entity.WechatPushTemplate;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;

public interface WechatPushTemplateMapper extends BaseMapper<WechatPushTemplate> {

    /**
     * 微信推送数据
     * @param content
     * @param telephone
     */
    int insertTemplate(@Param("content") String content, @Param("telephone")String telephone,@Param("doctorId")Integer doctorId);

    /**
     * 插入短信
     * @param content
     * @param telephone
     */
    int insertMessage(@Param("content") String content, @Param("telephone")String telephone,@Param("doctorId")Integer doctorId,@Param("categoryID")Integer categoryID);
}
