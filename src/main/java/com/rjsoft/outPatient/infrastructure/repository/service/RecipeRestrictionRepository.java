package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;

import java.util.List;
import java.util.Map;

/**
 * 处方限制
 *
 * <AUTHOR>
 * @since 2021/9/27-2:11 下午
 */
public interface RecipeRestrictionRepository {


    /**
     * 根据参数统计数量
     * select * from MZYS_TB_CFXZMX where ysbm = ? and xmbm = ? and yybm = ?;
     *
     * @param doctorId     医生 id
     * @param deptId
     * @param itemCode     项目 id
     * @param hospitalCode 医院编码
     * @return record count
     */
    Integer getRecipeRestrictionCount(Integer doctorId, Integer deptId, Integer itemCode, Integer hospitalCode);

    /**
     * 查询处方限制编码
     *
     * @param itemCode     项目编码
     * @param hospitalCode 医院编码
     * @return 处方限制编码
     */
    Integer getRecipeRestrictionCode(Integer itemCode, Integer hospitalCode);

    @DatabaseAnnotation
    Map<Integer,Integer> listRecipeRestrictionCode2Map(List<Integer> itemCodeList, Integer hospitalCode);
}
