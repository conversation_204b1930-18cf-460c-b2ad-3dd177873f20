package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.common.enums.RecipeTypeEnum;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DrugInfoDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.beans.BeanUtils;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.entity.IDynamicTableName;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 门诊医生处方明细
 *
 * <AUTHOR>
@Data
@Table(name = "MZYS_TB_MZCFMX_Special")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RecipeDetailSpecial extends BaseRecipeDetail implements IDynamicTableName, Serializable {

    @Column(name = "cfmxlsh")
    private Long recipeDetailNo;


    @Column(name = "cfmxid")
    private Long recipeDetailId;

    /**
     * 处方id
     */
    @Column(name = "cfId")
    private Long recipeId;


    /**
     * 项目序号
     */
    @Column(name = "xmxh")
    private Integer itemNo;


    /**
     * 首次开方医生
     */
    @Column(name = "sckfys")
    private Integer firstDoctorId;
    /**
     * 首次开方时间
     */
    @Column(name = "sckfsj")
    private Date firstDate;
    /**
     * 最后开方医生
     */
    @Column(name = "zhkfys")
    private Integer lastDoctorId;
    /**
     * 最后开方时间
     */
    @Column(name = "zhkfsj")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastDate;

    /**
     * 处方副数
     */
    @Column(name = "cffs")
    private Integer recipeCount;


    /**
     * 使用时间
     */
    @Column(name = "sysj")
    private Date useTime;


    /**
     * 门诊手术编码
     */
    @Column(name = "mzssbm")
    private Integer surgeryCode;
    /**
     * 门诊收费部位
     */
    @Column(name = "mzssbw")
    private String surgeryPart;
    /**
     * 化验标本
     */
    @Column(name = "jcbwbh")
    private Integer examinePartNo;


    /**
     * 检查单位
     */
    @Column(name = "jcdw")
    private String examineUnit;

    /**
     * 加急
     */
    @Column(name = "hybb")
    private String urgent;
    /**
     * 折扣率
     */
    @Column(name = "zkl")
    private BigDecimal discount;


    /**
     * 化验分类标识
     */
    @Column(name = "flmc")
    private String assayCategoryName;
    /**
     * 抗生素
     */
    @Column(name = "exa")
    private Integer exa;

    /**
     * 打印状态
     */
    @Column(name = "syzt")
    private Integer printStatus;

    /**
     * 保存流水号
     */
    @Column(name = "preSaveNo")
    private Long preSaveNo;

    /**
     * 检查明细收费项目流水号
     */
    @Column(name = "jcmxxmlsh")
    private Integer examineItemDetailNo;

    /**
     * 标识此处方是否是申请单开立出的
     * 0 非申请单，1 申请单项目 2 岱嘉申请单
     */
    @Column(name = "sqdbj")
    private Integer examineFlag;

    /**
     * 申请单分组ID
     */
    @Column(name = "sqdzh")
    private Integer ApplyGroupId;

    /**
     * 处方状态
     */
    @Column(name = "cfzt")
    private Integer recipeStatus;

    /**
     * 配伍标识
     */
    @Column(name = "compatibleType")
    private String compatibleType;

    /**
     * 检查类型编码
     */
    @Transient
    private String examineCode;

    /**
     * 检查类型名称
     */
    @Transient
    private String examineName;

    /**
     * 数据标识
     * 0 库中不存在
     * 1 默认表中
     * 2 转移表中
     */
    @Transient
    private Integer dataFlag;


    /**
     * 动态表名
     */
    @Transient
    private String tableName;

    /**
     * 是否跨院区 1-是 0-否
     */
    @Column(name = "isOtherHospital")
    private Integer isOtherHospital;

    /**
     * 收费项目的院区hospitalCode
     */
    @Column(name = "itemHospitalCode")
    private Integer itemHospitalCode;

    /**
     * 全局ID
     */
    @Column(name = "GlobalId")
    private Integer globalId;

    /**
     * 执行科室院区标识
     */
    @Column(name = "exeHospitalId")
    private Integer exeHospitalId;

    @Column(name = "createTime")
    private Date createTime;

    @Column(name = "doctorId")
    private Integer doctorId;

    @Override
    public String getDynamicTableName() {
        return tableName;
    }

    public RecipeDetailSpecial() {
    }

    public RecipeDetailSpecial(BaseRecipeDetail baseRecipeDetail) {
        super(baseRecipeDetail);
        dataFlag = 0;

    }

    public RecipeDetailSpecial(ExamineRequest request) {
        regNo = null;
        receptionNo = request.getReceptionNo();
        recipeNo = request.getRecipeNo();
        recipeDetailNo = request.getRecipeDetailNo();
        recipeDetailId = request.getRecipeDetailId();
        examineItemCode = request.getExamineItemCode();
        itemName = request.getExamineItemName();
        feeCategory = request.getFeeCategory();
        recipeCategory = request.getFeeCategory();
        if (ItemCategoryEnum.Examination.getCategoryCode().equals(recipeCategory)) {
            recipeCategory = ItemCategoryEnum.Examination.getCategoryCode();
        } else if (ItemCategoryEnum.Laboratory.getCategoryCode().equals(recipeCategory)) {
            recipeCategory = ItemCategoryEnum.Laboratory.getCategoryCode();
        } else {
            recipeCategory = RecipeTypeEnum.TREATMENT_ITEMS.getRecipeType();
        }
        dose = "-1";
        doseUnit = "";
        frequency = -1;
        specification = "";
        usage = -1;
        specialUsage = "";
        days = 1;
        quantity = BigDecimal.ONE;
        price = request.getAmount();
        unit = "";
        clincUnitNum = 1;
        amount = request.getAmount();
        groupNo = 0;
        dispensing = -1;
        doctorEntrust = "";
        status = request.getStatus();
        execDept = -1;
        changeRecipeFlag = 0;
        packageFlag = 0;
        packageDetailNo = null;
        applicationForm = request.getApplyName();
        examineNo = Converter.toString(request.getExamineNo());
        packageId = null;
        recipeStatus = 0;
        hospitalCode = request.getHospitalCode();
        reviewStatus = 0;
        itemNo = 0;
        examineFlag = 1;
    }

    @Override
    public void changeValues(BaseRecipeDetail baseRecipeDetail) {
        super.changeValues(baseRecipeDetail);
    }

    @Override
    public void changeValuesSelective(BaseRecipeDetail baseRecipeDetail) {
        super.changeValuesSelective(baseRecipeDetail);
    }

}
