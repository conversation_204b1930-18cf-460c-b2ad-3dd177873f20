package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 处方煎药信息
 * <AUTHOR>
@Data
@Table(name = "MZYS_TB_CYDJXX")
public class RecipeContactInfo implements Serializable {

    /**
     * Id
     */
    @Id
    @Column(name = "ID", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 处方流水号
     */
    @Column(name = "cflsh")
    private Long recipeNo;

    /**
     * 就诊流水号
     */
    @Column(name = "jzlsh")
    public Long receptionNo;

    /**
     * 代煎ID
     */
    @Column(name = "djid")
    public Integer boilId;

    /**
     * 代煎名称
     */
    @Column(name = "djname")
    private String boilName;

    /**
     * 联系人
     */
    @Column(name = "lxr")
    private String contactName;

    /**
     * 联系人电话
     */
    @Column(name = "lxdh")
    private String contactPhone;

    /**
     * 联系人地址
     */
    @Column(name = "lxdz")
    private String contactAddress;

    /**
     * 医生编码
     */
    @Column(name = "ysbm")
    private Integer doctorId;

    /**
     * 创建时间
     */
    @Column(name = "createTime")
    private Date createTime;

    /**
     * 医院编码
     */
    @Column(name = "yybm")
    private Integer hospitalCode;

    /**
     * 数据标识
     * 0 库中不存在
     * 1 默认表中
     * 2 转移表中
     */
    @Transient
    private Integer dataFlag;
}
