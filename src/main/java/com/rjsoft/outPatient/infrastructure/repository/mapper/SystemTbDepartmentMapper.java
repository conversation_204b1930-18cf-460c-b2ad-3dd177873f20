package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.DeptDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.Department;
import com.rjsoft.outPatient.infrastructure.repository.entity.ItemCount;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;


public interface SystemTbDepartmentMapper extends BaseMapper<Department>, ExampleMapper<Department> {

    /**
     * 根据科室编码+医院编码判断是否专病
     *
     * @param deptId
     * @param hospitalCode
     * @return
     */
    int judgeSpecialDisease(@Param("deptId") Integer deptId, @Param("hospitalCode") String hospitalCode);


    /**
     * 根据科室编码+医院编码判断是否专病
     *
     * @return
     */
    List<ItemCount> speciaDiseaseCount();


    /**
     * 获取科室列表
     *
     * @param hospCode
     * @return
     */
    List<DeptDto> getDeptList(@Param("hospCode") Integer hospCode);

    /**
     * 获取住院科室列表
     *
     * @param hospCode
     * @return
     */
    List<DeptDto> getZyDeptList(@Param("hospCode") Integer hospCode);


    default Department getDept(int deptId, int hospId) {
        Weekend<Department> weekend = new Weekend<>(Department.class);
        weekend.weekendCriteria().andEqualTo(Department::getDeptId, deptId)
                .andEqualTo(Department::getHospitalId, hospId);
        return selectOneByExample(weekend);
    }

}