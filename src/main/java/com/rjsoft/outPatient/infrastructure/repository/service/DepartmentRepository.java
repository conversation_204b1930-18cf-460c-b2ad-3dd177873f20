package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.Department;

import java.util.List;
import java.util.Map;

/**
 *  部门信息
 * <AUTHOR>
 * @create 2021/11/24 11:26
 */

public interface DepartmentRepository {
    /**
     * 根据部门编码获取部门信息
     * @param deptId
     * @param hospitalCode
     */
     Department getDepartmentByDeptId(Integer deptId, Integer hospitalCode);

    Map<Integer,Department> getAllDepartmentByHospitalCode(Integer hospitalCode);

    Department getDepartmentByDeptIdPreLoad(Integer deptId, Integer hospitalCode);

    /**
     * 根据部门编码获取部门信息
     * 从ignite中获取
     * @param deptId
     * @param hospitalCode
     */
    Department getDepartmentByDeptIdFIgnite(Integer deptId, Integer hospitalCode);

    /**
     * 根据部门编码列表获取部门信息
     * @param deptIds
     * @param hospitalCode
     */
    List<Department> getDepartmentByDeptIdList(List<Integer> deptIds, Integer hospitalCode);


    /**
     * 根据部门列表
     * @param hospitalCodeList
     */
    List<Department> getDepartmentList(List<Integer> hospitalCodeList);

    /**
     * 根据hospitalId获取部门列表
     * @param hospitalId
     */
    List<Department> getDepartmentList(Integer hospitalId);

    /**
     * 根据hospitalId获取门诊可使用部门列表
     * @param hospitalId
     */
    List<Department> getMzDepartmentList(Integer hospitalId,String type);

}
