package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
    * 门诊当月费用信息统计
    */
@Data
@Table(name = "MZYS_TB_MonthFeeRation")
public class MzysTbMonthFeeRation implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 医生工号
     */
    @Column(name = "worker_no")
    private String workerNo;

    /**
     * 统计月份
     */
    @Column(name = "stat_month")
    private String statMonth;

    /**
     * 总费用
     */
    @Column(name = "total_fee")
    private BigDecimal totalFee;

    /**
     * 药品总费用
     */
    @Column(name = "med_fee")
    private BigDecimal medFee;

    /**
     * 西药费用
     */
    @Column(name = "western_med_fee")
    private BigDecimal westernMedFee;

    /**
     * 中成药费用
     */
    @Column(name = "chinese_pat_med_fee")
    private BigDecimal chinesePatMedFee;

    /**
     * 草药费用
     */
    @Column(name = "herb_med_fee")
    private BigDecimal herbMedFee;

    /**
     * 药占比
     */
    @Column(name = "med_ratio")
    private BigDecimal medRatio;

    /**
     * 当月人次数
     */
    @Column(name = "month_person_num")
    private Integer monthPersonNum;

    /**
     * 药品次均费用
     */
    @Column(name = "med_avg_fee")
    private BigDecimal medAvgFee;

    /**
     * 非药品次均费用
     */
    @Column(name = "non_med_avg_fee")
    private BigDecimal nonMedAvgFee;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}