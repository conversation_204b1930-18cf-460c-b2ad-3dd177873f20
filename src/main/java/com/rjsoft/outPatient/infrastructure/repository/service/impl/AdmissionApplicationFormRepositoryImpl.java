package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.AssayResult;
import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.AssayStateResult;
import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.OutPatientInvoiceTimeResult;
import com.rjsoft.outPatient.infrastructure.repository.mapper.AdmissionApplicationFormDaoMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.AdmissionApplicationFormRepository;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AdmissionApplicationFormRepositoryImpl implements AdmissionApplicationFormRepository {
    AdmissionApplicationFormDaoMapper admissionApplicationFormDaoMapper;

    /**
     * 查询化验结果
     *
     * @param recipeDetailId
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "MZYS")
    public List<AssayResult> getAssay(Long recipeDetailId) {
        return admissionApplicationFormDaoMapper.getAssay(recipeDetailId);
    }

    /**
     * 查询化验状态
     *
     * @param recipeDetailId
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "MZYS")
    public List<AssayStateResult> getAssayState(Long recipeDetailId) {
        return admissionApplicationFormDaoMapper.getAssayState(recipeDetailId);
    }

    /**
     * 根据ghlsh=regNo查询患者的账户标志Reg_Tb_OutpatientInvoice_Time表的AccountFlag
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public OutPatientInvoiceTimeResult getOutPatient(Long regNo, Integer hospitalCode) {
        return admissionApplicationFormDaoMapper.getOutPatient(regNo, hospitalCode);
    }
}
