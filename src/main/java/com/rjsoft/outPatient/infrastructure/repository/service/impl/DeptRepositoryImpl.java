package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.SystemNo;
import com.rjsoft.common.configuration.SysConfig;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.param.RequestHead;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugDutyTimeTypeMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugExecDeptMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SysFunctionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DeptRepository;

import java.util.Date;
import java.util.List;

/**
 * 科室
 * <AUTHOR>
public class DeptRepositoryImpl implements DeptRepository {

    DrugExecDeptMapper drugExecDeptMapper;
    DrugDutyTimeTypeMapper drugDutyTimeTypeMapper;
    SysFunctionMapper sysFunctionMapper;
    SysConfig sysConfig;

    @Override
    @DatabaseAnnotation
    public List<Integer> getDrugExecDeptByTime() {
        Integer weekday = sysFunctionMapper.getWeekday();
        Date date = sysFunctionMapper.getDate();
        Integer timeType = drugDutyTimeTypeMapper.getTimeTypeByDate(date);
        return drugExecDeptMapper.getExecDeptByTime(timeType, weekday);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<Integer> getLocalConfigDrugRoom() {
        final RequestHead requestHead = RequestHead.get();
        final String loginIp = requestHead.getLoginIp();
        final String hospitalCode = requestHead.getHospitalCode();
        //获取门诊西药
        String westernMed = sysConfig.getLocalConfig(SystemNo.OUTPATIENT, "WesternMed", hospitalCode, loginIp);
        String chineseHerbMed = sysConfig.getLocalConfig(SystemNo.OUTPATIENT, "ChineseHerbMed", hospitalCode, loginIp);
        String chinesePatMed = sysConfig.getLocalConfig(SystemNo.OUTPATIENT, "ChinesePatMed", hospitalCode, loginIp);


        return null;
    }
}
