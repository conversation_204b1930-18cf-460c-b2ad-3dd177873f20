package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.config.dto.HisDictionaryDto;
import com.rjsoft.outPatient.domain.dictionary.dto.GeneralDicDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @create 2021/10/18 15:47
 * @description 通用字典mapper
 **/
public interface GeneralDicMapper {


    /**
     * Zyemr.tbt_Psy_dic_Nation 根据码值获取民族字典
     *
     * @param code
     * @return
     */
    @Select("select code,name,status from tbt_Psy_dic_Nation where code = #{code}")
    GeneralDicDto getNationDicByCode(@Param("code") String code);

    /**
     * Zyemr.tbt_Psy_dic_Education 根据码值获取文化程度字典
     *
     * @param code
     * @return
     */
    @Select("select code,name,status from tbt_Psy_dic_Education where code = #{code}")
    GeneralDicDto getEducationDicByCode(@Param("code") String code);

    /**
     * tbt_Psy_dic_MaritalStatus 根据码值获取婚姻状况字典信息
     *
     * @param maritalStatus
     * @return
     */
    @Select("select code,name,status from tbt_Psy_dic_MaritalStatus where code = #{code}")
    GeneralDicDto getMaritalDicByCode(@Param("code") String code);


    HisDictionaryDto getGenaralDic(@Param("dictionaryTypeID") Integer dictionaryTypeID,
                                   @Param("hisDictionaryCode") String hisDictionaryCode,
                                   @Param("hospId") Integer hospId);
}
