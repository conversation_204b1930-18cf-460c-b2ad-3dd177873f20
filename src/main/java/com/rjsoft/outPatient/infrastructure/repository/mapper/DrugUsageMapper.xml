<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DrugUsageMapper">
    <select id="selectPrvName" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.PrvNameResult">
        select DrugId,HisDictionaryCode,HisDictionaryName from
        Drug_Tb_Usage a
        inner join Dic_Tv_Usage b on a.Usage = b.HisDictionaryCode and a.HospitalId = b.HospitalId
        where a.HospitalId = #{hospId} and DrugId = #{itemCode}
        order by sid
    </select>
</mapper>