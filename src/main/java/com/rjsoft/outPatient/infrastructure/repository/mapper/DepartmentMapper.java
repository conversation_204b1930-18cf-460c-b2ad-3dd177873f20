package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Department;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

public interface DepartmentMapper extends BaseMapper<Department>, ExampleMapper<Department> {

    /**
     * 根据部门编码获取部门信息
     * @param deptId
     * @param hospitalCode
     */
    default Department getDepartmentByDeptId(Integer deptId,Integer hospitalCode){
        Department department = new Department();
        department.setDeptId(deptId);
        department.setHospitalId(hospitalCode);
        department.setStatus(1);
        return selectOne(department);
    }

    /**
     * 根据部门编码获取院区所有部门信息
     * @param hospitalCode
     */
    default List<Department> getAllDepartmentByHospitalCode(Integer hospitalCode){
        Department department = new Department();
        department.setHospitalId(hospitalCode);
        department.setStatus(1);
        return select(department);
    }

}
