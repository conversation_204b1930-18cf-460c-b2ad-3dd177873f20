package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 患者预约手机号表
 */
@Data
@Table( name ="Reg_Tb_Appointment_Phone" )
public class AppointmentPhone implements Serializable {
    /**
     * id 主键
     */
    @Id
    @Column(name = "id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;
    /**
     * 手机号
     */
    @Column(name = "phone")
    private String phone;

    /**
     * 科室id
     */
    @Column(name = "deptId")
    private Integer deptId;

    /**
     * 医生id
     */
    @Column(name = "doctorId")
    private Integer doctorId;

    /**
     * 挂号编码
     */
    @Column(name = "regNo")
    private Integer regNo;
    /**
     * 患者id
     */
    @Column(name = "patId")
    private Integer patId;
    /**
     * 创建时间
     */
    @Column(name = "creTime")
    private Date creTime;
    /**
     * 是否删除
     */
    @Column(name = "delFlag")
    private Boolean delFlag;
    /**
     * 医院编码
     */
    @Column(name = "hospitalCode")
    private Integer hospitalCode;
    /**
     * 修改时间
     */
    @Column(name = "opTime")
    private Date opTime;
}
