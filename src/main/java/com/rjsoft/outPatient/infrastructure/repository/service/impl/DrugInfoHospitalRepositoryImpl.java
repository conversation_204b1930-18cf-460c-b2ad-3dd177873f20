package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugInfoHospital;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugInfomation;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugInfoHospitalMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugInfomationMapper;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class DrugInfoHospitalRepositoryImpl implements DrugInfoHospitalRepository{

    @Autowired
    private DrugInfoHospitalMapper drugInfoHospitalMapper;

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<DrugInfoHospital> getDrugInfoHospital(Integer drugId, Integer hospitalId, Integer useDeptRange) {
        return drugInfoHospitalMapper.getDrugInfoHospital(drugId,hospitalId,useDeptRange);
    }
}
