package com.rjsoft.outPatient.infrastructure.repository.mapper;

import cn.hutool.core.collection.CollUtil;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbCfyyly;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface MzysTbCfyylyMapper extends Mapper<MzysTbCfyyly> {

    default List<MzysTbCfyyly> selectListByPreSaveNo(Long receptionNo, Long preSaveNo, Integer hospitalCode) {
        Weekend<MzysTbCfyyly> weekend = new Weekend<>(MzysTbCfyyly.class);
        weekend.weekendCriteria()
                .andEqualTo(MzysTbCfyyly::getReceptionNo, receptionNo)
                .andEqualTo(MzysTbCfyyly::getPreSaveNo, preSaveNo)
                .andEqualTo(MzysTbCfyyly::getHospitalId, hospitalCode);
        return this.selectByExample(weekend);
    }

    default int deleteByRecipeDetailIds(List<Long> recipeDetailIds, Integer hospitalCode) {
        if(CollUtil.isEmpty(recipeDetailIds)){
            return 0;
        }
        //Weekend<MzysTbCfyyly> weekend = new Weekend<>(MzysTbCfyyly.class);
        //weekend.weekendCriteria()
        //        .andIn(MzysTbCfyyly::getRecipeDetailId, recipeDetailIds)
        //        .andEqualTo(MzysTbCfyyly::getHospitalId, hospitalCode);
        //return this.deleteByExample(weekend);
        Weekend<MzysTbCfyyly> weekend = new Weekend<>(MzysTbCfyyly.class);
        WeekendCriteria<MzysTbCfyyly, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(MzysTbCfyyly::getRecipeDetailId, recipeDetailIds).andEqualTo(MzysTbCfyyly::getHospitalId, hospitalCode);
        MzysTbCfyyly mzysTbCfyyly = new MzysTbCfyyly();
        mzysTbCfyyly.setStatus(0);
        return this.updateByExampleSelective(mzysTbCfyyly, weekend);
    }

    default List<MzysTbCfyyly> selectListByItemCodeList(Long receptionNo, List<Integer> itemCodeList, Integer hospitalCode) {
        Weekend<MzysTbCfyyly> weekend = new Weekend<>(MzysTbCfyyly.class);
        weekend.weekendCriteria()
                .andEqualTo(MzysTbCfyyly::getReceptionNo, receptionNo)
                .andIn(MzysTbCfyyly::getItemCode, itemCodeList)
                .andEqualTo(MzysTbCfyyly::getHospitalId, hospitalCode);
        return this.selectByExample(weekend);
    }

    default String getReasonByItemCode(Long receptionNo, Integer itemCode, Integer hospitalCode) {
        Weekend<MzysTbCfyyly> weekend = new Weekend<>(MzysTbCfyyly.class);
        weekend.weekendCriteria()
                .andEqualTo(MzysTbCfyyly::getReceptionNo, receptionNo)
                .andEqualTo(MzysTbCfyyly::getItemCode, itemCode)
                .andEqualTo(MzysTbCfyyly::getHospitalId, hospitalCode)
                .andEqualTo(MzysTbCfyyly::getStatus, true);
        List<MzysTbCfyyly> mzysTbCfyylyList = this.selectByExample(weekend);
        if (CollUtil.isEmpty(mzysTbCfyylyList)) {
            return null;
        }
        return mzysTbCfyylyList.get(0).getReason();
    }


}