<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeMapper">

    <select id="getRecipeInfoByReceptionNo"
            resultType="com.rjsoft.outPatient.domain.prescriptionAudit.dto.AuditPrescriptionDto">
        select distinct a.cflsh as id, a.zhkfrq as presTime
        from MZYS_TB_MZCF a (nolock)
        inner join MZYS_TB_MZCFMX b (nolock) on a.cfid = b.cfid and a.hospitalCode = b.hospitalCode
        where a.jzlsh = #{receptionNo}
          and a.sckfys = #{doctorId}
          and a.hospitalCode = #{hospitalCode}
          and a.sflx != 14 and (a.mzcflx &lt; 6 or a.mzcflx &gt; 8)
        order by a.zhkfrq desc
    </select>

    <select id="getRecipeMaxSerialNumberNext" resultType="java.lang.Integer">
        select max(xmxh)
        from MZYS_TB_MZCFMX (nolock)
        where jzlsh = #{receptionNo}
        and ((#{feeCategory} in (12, 13) and sflx in (12, 13))
            or (#{feeCategory} = 14 and sflx = 14)
            or (#{feeCategory} in (6, 8) and sflx in (6, 8))
            or (#{feeCategory} = 7 and sflx = 7)
            or (#{feeCategory} not in (6, 7, 8, 12, 13, 14) and
                (sflx &lt; 6 or (sflx &gt; 8 and sflx &lt; 12) or sflx &gt; 14)))
          and hospitalCode = #{hospitalCode}
    </select>
    <select id="getRecipeCount" resultType="java.lang.Integer">
        select count(cfmxlsh)
        from MZYS_TB_MZCFMX
        where jzlsh = #{receptionNo}
          and zt = 0
          and cfzt in (-1, 1, 3, 5)
          and sflx in (12, 13, 14)
          and isnull(sqd, '') != 'MECT'
    </select>
    <select id="getBlCount" resultType="java.lang.Integer">
        select count(jzlsh)
        from MZYS_TB_DZBL (nolock)
        where jzlsh = #{receptionNo}
          and hospitalCode = #{hospId}
          and tjbj = 1
    </select>
    <select id="getRecipeType" resultType="com.rjsoft.outPatient.domain.recipe.dto.RecipeTypeResponse">
        SELECT a.mzcflx recipeType,
               b.cflxmc recipeName
        FROM MZYS_TB_MZCF a
                 LEFT JOIN MZYS_TB_MZCFLX b ON a.mzcflx = b.cflxbm
        WHERE a.cflsh = #{recipeNo}
          AND a.hospitalCode = #{hospitalCode}
    </select>
    <select id="getItemCodeTail" resultType="java.lang.Integer">
        SELECT b.ItemCode itemCodeDetail
        FROM Dbt_MzSfTplList a
                 INNER JOIN Dbt_MzSfTplDetl b ON a.TplCode = b.TplCode
        WHERE a.ItemCode = #{itemCode}
    </select>
    <select id="getItemCodeTailByItemCodeList" resultType="com.rjsoft.outPatient.domain.recipe.dto.ChargeItemDto">
        SELECT a.ItemCode code,b.ItemCode subCode
        FROM Dbt_MzSfTplList a
        INNER JOIN Dbt_MzSfTplDetl b ON a.TplCode = b.TplCode
        WHERE a.ItemCode IN
        <foreach collection="itemCodeList" item="itemCode" open="(" close=")" separator=",">
            #{itemCode}
        </foreach>
    </select>
    <select id="getAllItemCodeTail" resultType="com.rjsoft.outPatient.domain.recipe.dto.ChargeItemDto">
        SELECT a.ItemCode code,b.ItemCode subCode
        FROM Dbt_MzSfTplList a
        INNER JOIN Dbt_MzSfTplDetl b ON a.TplCode = b.TplCode
    </select>

    <select id="getRecipeByTypeBy" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.Recipe">
        select a.cflsh        recipeNo,
               a.cfId         recipeId,
               a.ghlsh        regNo,
               a.jzlsh        receptionNo,
               a.dbrbh        commissionNo,
               a.sckfys       firstDoctorId,
               a.sckfrq       firstDate,
               a.zhkfys       lastDoctorId,
               a.zhkfrq       lastDate,
               a.zxks         execDept,
               a.sflx         feeCategory,
               a.cfzt         status,
               a.mzcflx       recipeCategory,
               a.cybz         herbFlag,
               a.hospitalCode hospitalCode
        from MZYS_TB_MZCF a (nolock)
        inner join MZYS_TB_MZCFMX b (nolock) on b.cflsh = a.cflsh and b.zt = 0
        where a.jzlsh = #{receptionNo}
          and (b.sflx = 12 or b.sflx = 13 or b.sflx = 14)
          and a.mzcflx = #{recipeCategory}
          and a.hospitalCode = #{hospitalCode}
    </select>

    <insert id="savePrescription">
        exec Usp_CA_SavePrescription
        #{receptionNo},
        #{caId}
    </insert>

    <select id="getCaPrescriptionId" resultType="java.lang.Integer">
        select serialno
        from MZYS_TB_CACFZB (nolock)
        where caId = #{caId}
    </select>

    <update id="updateRecipeDetailExedept">
        update MZYS_TB_MZCFMX set zxks = #{exeDept},exeHospitalId = #{exeHospitalId}
        where cfmxlsh = #{recipeDetailNo} and hospitalCode = #{hospitalCode}
    </update>

    <select id="getPrescriptionCommentByDoctorId" resultType="com.rjsoft.outPatient.domain.recipe.dto.PrescriptionCommentDto">
        SELECT ZYHM
        ,CFXH
        ,HZXM
        ,DPSJ
        ,DPDM
        ,REPLACE(DPNR,char(13)+char(10),'')as DPNR
        ,KSMC
        ,KFYSMC
        FROM [***********\SQLSERVER_BI].[CIS_1507].dbo.VW_MZDPFK
        WHERE KFYSDM= #{doctorId}
        AND (DPSJ between #{startDate} and #{endDate})
    </select>

    <select id="getInsuranceOutRecipeCount" resultType="java.lang.Integer">
        select count(1)
        from MZYS_Tb_InsuranceOutRecipe
        where ReceptionNo = #{ReceptionNo}
          and RecipeNo = #{RecipeNo}
          and HospitalCode = #{HospitalCode}
    </select>

    <insert id="insertInsuranceOutRecipeDto" parameterType="com.rjsoft.outPatient.domain.reception.dto.InsuranceOutRecipeDto" >
        insert into MZYS_Tb_InsuranceOutRecipe
        (
                 ReceptionNo
                ,CardNo
                ,PatId
                ,DoctorId
                ,DoctorName
                ,DeptId
                ,DeptName
                ,RecipeNo
                ,RecipeStatus
                ,Status
                ,CreDoctorId
                ,CreTime
                ,HospitalCode
        )
        values
            (
                 #{insuranceOutRecipeDto.ReceptionNo}
                ,#{insuranceOutRecipeDto.CardNo}
                ,#{insuranceOutRecipeDto.PatId}
                ,#{insuranceOutRecipeDto.DoctorId}
                ,#{insuranceOutRecipeDto.DoctorName}
                ,#{insuranceOutRecipeDto.DeptId}
                ,#{insuranceOutRecipeDto.DeptName}
                ,#{insuranceOutRecipeDto.RecipeNo}
                ,#{insuranceOutRecipeDto.RecipeStatus}
                ,#{insuranceOutRecipeDto.Status}
                ,#{insuranceOutRecipeDto.CreDoctorId}
                ,#{insuranceOutRecipeDto.CreTime}
                ,#{insuranceOutRecipeDto.HospitalCode}
            )
    </insert>

    <delete id="deleteInsuranceOutRecipeByRecipeNo">
        delete from MZYS_Tb_InsuranceOutRecipe
        where RecipeNo = #{RecipeNo}
          and ReceptionNo = #{ReceptionNo}
          and HospitalCode = #{HospitalCode}
          and RecipeStatus != 1
          and Status != 1
    </delete>

    <select id="getInsuranceOutRecipeByStatus" resultType="java.lang.Integer">
        select count(1)
        from MZYS_Tb_InsuranceOutRecipe
        where ReceptionNo = #{ReceptionNo}
          and RecipeNo = #{RecipeNo}
          and HospitalCode = #{HospitalCode}
          and RecipeStatus = 1
          and Status = 1
    </select>
</mapper>