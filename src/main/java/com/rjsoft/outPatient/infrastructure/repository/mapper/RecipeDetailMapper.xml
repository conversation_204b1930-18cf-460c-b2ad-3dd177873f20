<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeDetailMapper">

    <select id="getDrugInfo"
            resultType="com.rjsoft.outPatient.domain.diseaseReport.dto.DrugInfo">
        {call UspPsyCaclDrugInfo(#{itemCode}, #{dose}, #{frequency})}
    </select>

    <select id="getRecipeDetailByRecipeNo"
            resultType="com.rjsoft.outPatient.domain.prescriptionAudit.vo.AuditMedicine">
        select xmxh as ordinal,
               xmmc as name,
               xmbm as HisCode,
               ''   as medicareCode,
               ''   as approvalNo,
               gg   as spec,
               ''   as specUnit,
               ''   as specFactor,
               zh   as groupNo,
               ''   as reason,
               ''   as unit,
               ''   as oneTimeDose,
               yf   as freq,
               gytj as path,
               ''   as beginTime,
               ''   as endTime,
               ts   as days,
               ''   as pydNo,
               ''   as linkGroup,
               ''   as needAlert,
               xmsl as dispenseAmount,
               dw   as dispenseUnit,
               jl   as dose,
               jldw as doseUnit
        from MZYS_TB_MZCFMX (nolock)
        where cflsh = #{recipeNo}
          and hospitalCode = #{hospitalCode}
    </select>
    <select id="getRecipeByReceptionFromMzysnew"
            resultType="com.rjsoft.outPatient.domain.diseaseReport.dto.ReceptionDrugDTO">
        select xmxh itemNo,
               xmbm itemCode,
               xmmc itemName,
               yf   frequency,
               jl   dose,
               jldw doseUnit
        from MZYS_TB_MZCFMX (nolock)
        where jzlsh = #{receptionNo}
          and sflx in (12, 13, 14)
    </select>
    <select id="getRecipeFromOld" resultType="com.rjsoft.outPatient.domain.diseaseReport.dto.ReceptionDrugDTO">
        select xmxh itemNo,
               xmbm itemCode,
               xmmc itemName,
               yf   frequency,
               jl   dose,
               jldw doseUnit
        from MZYS_TB_MZCFMX (nolock)
        where jzlsh = #{receptionNo}
          and sflx in (12, 13, 14)
    </select>

    <select id="getTreatDto" resultType="com.rjsoft.outPatient.domain.recipe.dto.TreatDto">
        select c.hzbh patId,
        convert(varchar(60),a.ghlsh)regNo,
        c.jzlsh receptionNo,
        c.zhkzys receiveDoctorId,
        c.zhkzrq receptDate,
        a.xmbm itemCode,
        a.xmmc itemName,
        a.xmsl itemCount,
        a.gg ypgg,
        a.jl dose,
        a.jldw doseUnit,
        a.yf frequent,
        a.gytj route,
        d.gytjmc routeName,
        a.dw unit,
        isnull(a.zh,0) groupNo,
        a.cflsh recipeNo,
        a.cfmxlsh recipeDetailNo,
        isnull(a.syzt,0) printStatus,
        case isnull(a.syzt,0) when 0 then '否' when 1 then '是' end printStatusName
        from MZYS_TB_MZCFMX a (nolock)
        inner join MZYS_TB_KZJL c (nolock) on a.jzlsh=c.jzlsh and a.hospitalCode = c.hospitalCode
        inner join MZHS_TB_SYGYTJ d (nolock) on a.gytj=d.gytjbm
        and d.gytjmc in ('肌肉注射','皮下注射','静脉注射','静脉点滴')
        where a.ghlsh = #{regNo}
        and a.hospitalCode = #{hospitalCode}
        <if test="type != null">
            and isnull(a.syzt,0) = #{type}
        </if>
    </select>

    <select id="getInternetDrugRecipeByRegNo"
            resultType="com.rjsoft.outPatient.domain.recipe.dto.HistoryRecipeDetailDto">
        select a.PresId         recipeDetailNo,
               a.PrescriptionNo recipeNo,
               a.regNo,
               a.PhysicCode     itemCode,
               b.Name           itemName,
               b.DrugCategory   itemCategory,
               b.DrugCategory   recipeCategory,
               b.DrugGauge      specifications,
               a.Dosage         dose,
               a.DoseUnit       doseUnit,
               d.yflsh          frequency,
               a.Frequency      frequencyCode,
               a.Dosage         specialUsage,
               c.gytjbm as usage,
			   c.gytjmc usageName,
			   a.DayCount days,
			   a.TotalDosage quantity,
               a.zt status,
               a.ApplyDoctor doctor,
               rtrim(a.TotalUnit) unit,
               a.CreateTime receptionTime
        from Tbt_Internet_Prescription a (nolock)
            inner join Tbt_DrugBasicInfo b (nolock)
        on a.PhysicCode = b.Code
            left join mzys..MZYS_TB_YPGYTJ c
            on a.Usage = convert (varchar (20), c.srm)
            left join mzys..MZYS_TB_YPYF d on a.Frequency = d.yfbm
        where a.regNo = #{regNo}
    </select>

    <select id="getInternetRecipeByReceptionNo"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail">
        select 0 recipeDetailNo,
        a.SerialNo recipeDetailId,
        a.PresId recipeId,
        0 as itemNo,
        a.ApplyDoctor firstDoctorId,
        a.OpTime firstDate,
        a.ApplyDoctor lastDoctorId,
        a.OpTime lastDate,
        1 as recipeCount,
        getDate() as useTime,
        0 as surgeryCode,
        -1 as examinePartNo,
        0 as discount,
        0 as exa,
        -1 as recipeStatus,
        0 as amount,
        a.PrescriptionNo recipeDetailNo,
        a.regNo,
        a.PhysicCode itemCode,
        b.Name itemName,
        b.DrugCategory itemCategory,
        b.DrugCategory recipeCategory,
        b.DrugCategory feeCategory,
        b.DrugGauge specification,
        b.RetailPrice price,
        a.Dosage dose,
        a.DoseUnit doseUnit,
        a.TotalUnit unit,
        d.yflsh frequency,
        a.Frequency frequencyCode,
        a.Dosage specialUsage ,
        c.gytjbm usage,
        c.gytjmc usageName,
        a.DayCount days,
        a.TotalDosage quantity,
        a.zt status,
        a.ApplyDoctor doctor
        from Tbt_Internet_Prescription a (nolock)
        inner join Tbt_DrugBasicInfo b (nolock) on a.PhysicCode = b.Code
        left join mzys..MZYS_TB_YPGYTJ c (nolock) on a.Usage = convert(varchar(20),c.srm)
        left join mzys..MZYS_TB_YPYF d (nolock) on a.Frequency = d.yfbm
        <where>
            <if test="importFlag != null and importFlag != 0">
                a.VisitId = #{presId}
            </if>
            <if test="importFlag != null and importFlag != 1">
                a.PresId = #{presId}
            </if>
        </where>
    </select>


    <select id="hasRecipeDetailByReceptionNo"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail">
        select b.cfmxlsh      recipeDetailNo,
               b.xmmc         itemName,
               b.hospitalCode hospitalCode
        from MZYS_TB_MZCF a (nolock)
                 inner join MZYS_TB_MZCFMX b (nolock) on b.cflsh = a.cflsh and a.hospitalCode = b.hospitalCode
        where a.jzlsh = #{receptionNo}
          and a.hospitalCode = #{hospitalCode}
    </select>

</mapper>