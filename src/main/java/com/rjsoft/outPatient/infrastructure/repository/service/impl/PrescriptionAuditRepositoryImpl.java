package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.TbUser;
import com.rjsoft.outPatient.infrastructure.repository.mapper.TbUserMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.PrescriptionAuditRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2021/8/30 17:09
 * @description
 **/
@Service
@AllArgsConstructor
public class PrescriptionAuditRepositoryImpl implements PrescriptionAuditRepository {
    TbUserMapper tbUserMapper;

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public TbUser getUserByIdAndHosp(String doctorId, String hospitalCode) {
        return tbUserMapper.getUserByIdAndHosp(doctorId,hospitalCode);
    }
}
