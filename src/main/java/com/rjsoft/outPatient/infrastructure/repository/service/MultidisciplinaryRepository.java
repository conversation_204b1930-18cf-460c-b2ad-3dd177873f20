package com.rjsoft.outPatient.infrastructure.repository.service;

import com.github.pagehelper.PageInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubMultidisciplinary;

import java.util.List;


public interface MultidisciplinaryRepository {
    public PageInfo getMultidisciplinaryList(String name, Integer hospitalId, String state, Integer pageNum, Integer pageSize);
    public MdmPubMultidisciplinary getMultidisciplinaryByUniqueID(Long uniqueID);
    public List<MdmPubMultidisciplinary> getMultidisciplinaryByTypeName(String typeName, Integer hospitalId);
    public boolean updateMultidisciplinaryState(Long uniqueID, String state);
    public boolean addMultidisciplinary(MdmPubMultidisciplinary multidisciplinary);
    public boolean updateMultidisciplinary(MdmPubMultidisciplinary multidisciplinary);
}
