package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.GzdContrast;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * 项目告知单对照配置
 * <AUTHOR>
public interface GzdContrastMapper extends BaseMapper<GzdContrast> {

    /**
     * 获取告知单对照
     * @param gzdType
     * @param hospitalCode
     * @return
     */
    default List<GzdContrast>getGzdContrastByType(Integer gzdType,Integer hospitalCode) {
        GzdContrast entity = new GzdContrast();
        entity.setGzdType(gzdType);
        entity.setHospitalCode(hospitalCode);
        return select(entity);
    }
}
