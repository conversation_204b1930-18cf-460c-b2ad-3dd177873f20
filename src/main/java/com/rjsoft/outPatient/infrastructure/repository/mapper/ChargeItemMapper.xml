<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ChargeItemMapper">

    <resultMap id="ChargeItemDto" type="com.rjsoft.outPatient.domain.recipe.dto.ChargeItemDto">
        <id column="ItemCode" property="itemCode"/>
        <id column="hospitalId" property="hospitalId"/>
        <result column="ItemName" property="itemName"/>
        <result column="ItemCategory" property="itemCategory"/>
        <result column="price" property="price"/>
        <result column="ClinicQty" property="clinicQty"/>
        <result column="ClinicUnit" property="clinicUnit"/>
        <result column="WardUnit" property="wardUnit"/>
        <result column="Dosage" property="dosage"/>
        <result column="DosageUnit" property="dosageUnit"/>
        <result column="DrugGuage" property="drugGuage"/>
        <result column="exeDept" property="exeDept"/>
        <result column="productPlace" property="productPlace"/>
        <result column="useRange" property="useRange"/>
        <result column="itemTypeToAdv" property="itemTypeToAdv"/>
        <result column="checkCode" property="checkCode"/>
        <result column="mzItemId" property="mzItemId"/>
        <result column="wardItemId" property="wardItemId"/>
        <result column="isSet" property="isSet"/>
        <result column="DeptId" property="exeDept"/>
        <result column="drugPropertyId" property="drugPropertyId"/>
        <result column="AbxLev" property="abxLev"/>
        <result column="stock" property="stock"/>
        <result column="globalId" property="globalId"/>
    </resultMap>

    <select id="getChargeItemByInputCodeAndDeptId" resultMap="ChargeItemDto">
        SELECT *
        from (
                 SELECT distinct i.ItemName                                                 ItemName,
                        i.ItemCode                                                 ItemCode,
                        i.ItemCategory                                             ItemCategory,
                        ISNULL((i.ClinicExpensePrice + i.ClinicNonExpensePrice), 0) *
                        ISNULL(s.ClinicQuantity, i.ClinicQty)                      price,
                        i.ClinicQty                                                ClinicQty,
                        i.ClinicUnit                                               ClinicUnit,
                        i.WardUnit                                                 WardUnit,
                        convert(varchar(20), i.Dosage)                             Dosage,
                        i.DosageUnit                                               DosageUnit,
                        i.DrugGuage                                                DrugGuage,
                        l.DeptId                                                   exeDept,
                        i.productPlace                                             productPlace,
                        i.hospitalId                                               hospitalId,
                        i.useRange                                                 useRange,
                        i.itemTypeToAdv                                            itemTypeToAdv,
                        i.checkCode                                                checkCode,
                        i.mzItemId                                                 mzItemId,
                        i.wardItemId                                               wardItemId,
                        i.isSet                                                    isSet,
                        l.DeptId                                                   DeptId,
                        d.Property                                                 drugPropertyId,
                        d.AbxLev                                                   AbxLev,
                        (case when ext.keycode = 'YPWPFLAG'and
                        isnull((select sum(isnull(v.Quantity * v.PackFactor, 0))
                        from ${storeTbname} v
                        where l.DrugId = v.DrugId
                        AND l.HospitalId = v.HospitalId
                        AND l.DeptId = v.DeptId),0) = 0
                        then 99999
                        else
                        ((select sum(isnull(v.Quantity * v.PackFactor, 0))
                        from ${storeTbname} v
                        where l.DrugId = v.DrugId
                        AND l.HospitalId = v.HospitalId
                        AND l.DeptId = v.DeptId) / isnull(d.ClinicPackFactor, 0))
                        end) stock,
                        i.globalId,
                        l.hospitalId exeHospitalId,
                        i.exeDeptFlag
                 FROM System_Tv_PubItems i
                 INNER JOIN Drug_Tb_DrugInfomation d ON i.ItemCode = d.DrugId AND i.HospitalId = d.HospitalId
                 INNER JOIN Drug_Tb_LocalDeptDrug l ON d.DrugId = l.DrugId AND d.HospitalId = l.HospitalId
                 LEFT JOIN System_Tb_SpecialItems s ON s.ItemCode = i.ItemCode AND s.HospitalCode = i.HospitalId
                 LEFT JOIN system_tv_iteminfodept iid on iid.DrugId = i.ItemCode and iid.usedeptrange=i.UseRange
                    and iid.Stopped=0  AND iid.HospitalId = i.HospitalId
                LEFT JOIN MDM_Pub_FeeCategoryExeDept fce on fce.feeCategoryCode=i.ItemCategory and fce.hospitalId = i.HospitalId
                    and l.DeptId=fce.execDept
                left join System_Tb_TemplateExtension ext on i.itemcode = ext.classId and i.HospitalId = ext.HospitalId and ext.keycode = 'YPWPFLAG'
        WHERE i.ItemCategory = #{itemCategory}
                 AND i.UseRange IN (0, 1)
                 AND i.Stopped = 0
                 AND l.IsUsed=1
                   <if test="inputType==0 or inputType=='' ">
                   AND (i.ItemName LIKE CONCAT('%', #{inputCode}, '%') OR
                        i.InputCode1 LIKE CONCAT('%', #{inputCode}, '%') OR
                        i.InputCode2 LIKE CONCAT('%', #{inputCode}, '%') OR
                        i.InputCode3 LIKE CONCAT('%', #{inputCode}, '%'))
                   </if>
                   <if test="inputType==1 ">
                       AND (i.ItemName LIKE CONCAT('', #{inputCode}, '%') OR
                       i.InputCode1 LIKE CONCAT('', #{inputCode}, '%') OR
                       i.InputCode2 LIKE CONCAT('', #{inputCode}, '%') OR
                       i.InputCode3 LIKE CONCAT('', #{inputCode}, '%'))
                   </if>
                   <if test="hospitalCode != null">
                   AND i.HospitalId = #{hospitalCode}
                   </if>
                   And (iid.DeptId=-1 or iid.DeptId=#{useDept})
                    and fce.useDept=#{useDept}
                    and CONVERT(time,fce.startTime) &lt;= #{curTime}
                    and CONVERT(time,fce.endtime) &gt;= #{curTime}
             ) T
        where T.stock is not null
    </select>
    <select id="getCode" resultType="com.rjsoft.outPatient.domain.recipe.dto.CodeResponse">
        SELECT ItemCode itemCode,
        CheckCode checkCode
        FROM System_Tv_PubItems
        WHERE ItemCode IN
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND Stopped = 0
        AND UseRange IN (0, 1)
        <if test="hospitalCode != null">
        AND HospitalId = #{hospitalCode}
        </if>
    </select>
    <select id="getCodeByItemCode" resultType="com.rjsoft.outPatient.domain.recipe.dto.CodeResponse">
        SELECT ItemCode itemCode,
        CheckCode checkCode
        FROM System_Tv_PubItems
        WHERE ItemCode IN
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND Stopped = 0
        AND UseRange IN (0, 1)
        AND ExeDept = #{execDept}
        <if test="hospitalCode != null">
            AND HospitalId = #{hospitalCode}
        </if>
    </select>
    <select id="getNonChargeItemByInputCodeAndDeptId" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ChargeViewItem">
        SELECT i.ItemName                                          itemName,
        i.ItemCode                                                 itemCode,
        i.ItemCategory                                             itemCategory,
        i.ClinicQty                                                clinicQty,
        i.ClinicUnit                                               clinicUnit,
        i.WardUnit                                                 wardUnit,
        convert(varchar(20), i.Dosage)                             dosage,
        i.DosageUnit                                               dosageUnit,
        i.DrugGuage                                                drugGuage,
        i.exeDept                                                  exeDept,
        i.productPlace                                             productPlace,
        i.hospitalId                                               hospitalId,
        i.useRange                                                 useRange,
        i.itemTypeToAdv                                            itemTypeToAdv,
        i.checkCode                                                checkCode,
        i.mzItemId                                                 mzItemId,
        i.wardItemId                                               wardItemId,
        i.isSet                                                    isSet,
        i.inputCode1                                               inputCode1,
        i.inputCode2                                               inputCode2,
        i.inputCode3                                               inputCode3,
        i.clinicExpensePrice                                       clinicExpensePrice,
        clinicNonExpensePrice                                      clinicNonExpensePrice,
        itemTypeToAdv                                              itemTypeToAdv,
        i.globalId,
        i.exeHospitalId,
        i.exeDeptFlag
        FROM System_Tv_PubItems i
        INNER JOIN system_tv_iteminfodept iid on iid.DrugId = i.ItemCode and iid.usedeptrange=i.UseRange and i.HospitalId = iid.HospitalId
        and iid.Stopped=0
        WHERE i.ItemCategory in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND i.UseRange IN (0, 1)
        AND i.Stopped = 0
        <if test="inputType==0 or inputType=='' ">
            AND (i.ItemName LIKE CONCAT('%', #{inputCode}, '%') OR
            i.InputCode1 LIKE CONCAT('%', #{inputCode}, '%') OR
            i.InputCode2 LIKE CONCAT('%', #{inputCode}, '%') OR
            i.InputCode3 LIKE CONCAT('%', #{inputCode}, '%'))
        </if>
        <if test="inputType==1 ">
            AND (i.ItemName LIKE CONCAT('', #{inputCode}, '%') OR
            i.InputCode1 LIKE CONCAT('', #{inputCode}, '%') OR
            i.InputCode2 LIKE CONCAT('', #{inputCode}, '%') OR
            i.InputCode3 LIKE CONCAT('', #{inputCode}, '%'))
        </if>
        <if test="hospitalCode!=null">
            AND iid.HospitalId = #{hospitalCode}
        </if>
        And (iid.DeptId=-1 or iid.DeptId=#{useDept})

    </select>

    <select id="getChargeItemDtos" resultType="com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDto">
        select ItemCode,ItemName,itemCategory feeCategoryCode from System_Tv_PubItems
        <where>
            UseRange in (0, 1) and hospitalid = #{hospitalId}
            <if test="inputCode!=null and inputCode!=''">
              and (ItemName LIKE CONCAT('', #{inputCode}, '%') OR
                InputCode1 LIKE CONCAT('', #{inputCode}, '%') OR
                InputCode2 LIKE CONCAT('', #{inputCode}, '%') OR
                InputCode3 LIKE CONCAT('', #{inputCode}, '%'))
            </if>
        </where>
        GROUP BY ItemCode,ItemName,itemCategory
    </select>

    <select id="getChargeItemByIdIsStopped" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ChargeItem">
        select distinct a.itemCode,a.itemName,a.inputCode1,a.inputCode2,a.inputCode3,a.itemCategory,a.childItemCategory,a.clinicExpensePrice,a.clinicNonExpensePrice,
        a.clinicQty,a.clinicUnit,a.wardUnit,a.dosage,a.dosageUnit,a.drugGuage,a.exeDept,a.stopped,a.abClass,a.productPlace,tradePrice,a.retailPrice,
        a.itemTypeToAdv,a.checkCode,a.mzItemId,a.wardItemId,a.useRange,a.hospitalId,a.globalId,a.isSet
        from System_Tb_PubItems a
        INNER JOIN Drug_Tb_DrugInfomation d ON a.ItemCode = d.DrugId AND a.HospitalId = d.HospitalId
        INNER JOIN Drug_Tb_LocalDeptDrug l ON d.DrugId = l.DrugId AND d.HospitalId = l.HospitalId and l.DeptId=#{deptId}
        where a.itemCode = #{itemCode} and a.hospitalId = #{hospitalCode}  and a.stopped = #{stopped} and a.useRange in (0,1)
        and d.IsStop = #{ydStopped}
        and l.IsUsed = #{ypUsed}
    </select>

</mapper>