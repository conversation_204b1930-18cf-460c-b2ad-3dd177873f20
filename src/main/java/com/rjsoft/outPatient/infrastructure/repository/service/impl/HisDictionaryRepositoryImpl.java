package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.DictionaryType;
import com.rjsoft.outPatient.infrastructure.repository.entity.HisDictionary;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DictionaryTypeMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.HisDictionaryMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.HisDictionaryRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 医院字典
 * <AUTHOR>
@Service
@AllArgsConstructor
public class HisDictionaryRepositoryImpl implements HisDictionaryRepository {
    HisDictionaryMapper hisDictionaryMapper;
    DictionaryTypeMapper dictionaryTypeMapper;

    /**
     * 根据字典类型集合获取系统字典
     * @param types
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<HisDictionary> getDictionaryByTypes(List<Integer> types, Integer hospitalCode) {
        return hisDictionaryMapper.getDictionaryByTypes(types, hospitalCode);
    }

    /**
     * 根据类型集合查询字典类型
     * @param types
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<DictionaryType> getDictionaryTypeByIds(List<Integer>types) {
        return dictionaryTypeMapper.getDictionaryTypeByIds(types);
    }
}
