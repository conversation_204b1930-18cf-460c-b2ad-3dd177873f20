package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.utils.TyMdc;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugInfomation;
import com.rjsoft.outPatient.infrastructure.repository.entity.ItemInfoDept;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ItemInfoDeptMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ItemInfoDeptRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;
import java.util.Map;

@Service
public class ItemInfoDeptRepositoryImpl implements ItemInfoDeptRepository {
    @Autowired
    private ItemInfoDeptMapper itemInfoDeptMapper;

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public ItemInfoDept getDrugInfoDept(Integer drugId, Integer hospitalId, Integer deptId, Integer useDeptRange) {
        return itemInfoDeptMapper.getDrugInfoDept(drugId, hospitalId, deptId, useDeptRange);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<ItemInfoDept> getItemInfoHospital(Integer itemCode, Integer hospitalId, Integer useDeptRange) {
        return itemInfoDeptMapper.getItemInfoHospital(itemCode, hospitalId, useDeptRange);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    // FIXME: yutao 2024/6/17 加上cacheable
    public List<ItemInfoDept> getItemInfoListByItemCodes(List<Integer> itemCodes, Integer hospitalId, Integer deptId, Integer useDeptRange, Integer receiveDeptId) {
        if(receiveDeptId == null)
        {
            receiveDeptId = -99;
        }

        Weekend<ItemInfoDept> weekend = new Weekend<>(ItemInfoDept.class);
        Boolean itemIsAllNotDrug = TyMdc.get("itemIsAllNotDrug",Boolean.FALSE);
        Boolean itemIsAllDrug = TyMdc.get("itemIsAllDrug",Boolean.FALSE);
        weekend.setTableName("System_Tv_ItemInfoDept");
        if(itemIsAllDrug){
            weekend.setTableName("System_Tv_DrugsDept");
        }
        if(itemIsAllNotDrug){
            weekend.setTableName("System_Tv_Not_DrugsDept");
        }
        WeekendCriteria<ItemInfoDept, Object>  weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ItemInfoDept::getDrugId,itemCodes)
                .andEqualTo(ItemInfoDept::getHospitalId,hospitalId)
                .andEqualTo(ItemInfoDept::getUseDeptRange,1)
                .andEqualTo(ItemInfoDept::getStopped,0);
        WeekendCriteria<ItemInfoDept, Object>  weekendCriteria2 = weekend.weekendCriteria();
        weekendCriteria2.andEqualTo(ItemInfoDept::getDeptId,deptId).orEqualTo(ItemInfoDept::getDeptId,-1).orEqualTo(ItemInfoDept::getDeptId,receiveDeptId);
        weekend.and(weekendCriteria2);


        return itemInfoDeptMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<ItemInfoDept> getItemInfoListByDrugIds(List<Integer> drugIds, Integer hospitalId, Integer deptId, Integer useDeptRange, Integer receiveDeptId) {
        if(receiveDeptId == null)
        {
            receiveDeptId = -99;
        }
        Weekend<ItemInfoDept> weekend = new Weekend<>(ItemInfoDept.class);
        weekend.setTableName("System_Tv_DrugsDept");
        WeekendCriteria<ItemInfoDept, Object>  weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ItemInfoDept::getDrugId,drugIds)
                .andEqualTo(ItemInfoDept::getHospitalId,hospitalId)
                .andEqualTo(ItemInfoDept::getUseDeptRange,1)
                .andEqualTo(ItemInfoDept::getStopped,0);
        WeekendCriteria<ItemInfoDept, Object>  weekendCriteria2 = weekend.weekendCriteria();
        weekendCriteria2.andEqualTo(ItemInfoDept::getDeptId,deptId).orEqualTo(ItemInfoDept::getDeptId,-1).orEqualTo(ItemInfoDept::getDeptId,receiveDeptId);
        weekend.and(weekendCriteria2);
        return itemInfoDeptMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public ItemInfoDept getItemInfoListByItemCode(Integer itemCode, Integer hospitalId, Integer deptId, Integer useDeptRange) {
        Weekend<ItemInfoDept> weekend = new Weekend<>(ItemInfoDept.class);
        WeekendCriteria<ItemInfoDept, Object>  weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ItemInfoDept::getDrugId,itemCode)
                .andEqualTo(ItemInfoDept::getHospitalId,hospitalId)
                .andEqualTo(ItemInfoDept::getUseDeptRange,1)
                .andEqualTo(ItemInfoDept::getStopped,0);
        WeekendCriteria<ItemInfoDept, Object>  weekendCriteria2 = weekend.weekendCriteria();
        weekendCriteria2.andEqualTo(ItemInfoDept::getDeptId,deptId).orEqualTo(ItemInfoDept::getDeptId,-1);
        weekend.and(weekendCriteria2);
        return itemInfoDeptMapper.selectOneByExample(weekend);
    }
}
