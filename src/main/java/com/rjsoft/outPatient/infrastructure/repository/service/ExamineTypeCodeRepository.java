package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.ExamineTypeCode;

/**
 * <AUTHOR>
 * @since 2021/10/13-2:03 下午
 */
public interface ExamineTypeCodeRepository {

    /**
     * 根据检查类型编码查询
     *
     * @param examineType 检查类型编码
     * @return {@link ExamineTypeCode}
     */
    ExamineTypeCode getExamineTypeCodeByExamineType(Integer examineType);

}
