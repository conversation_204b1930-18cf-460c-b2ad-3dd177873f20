package com.rjsoft.outPatient.infrastructure.repository.strategy.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ApplicationContentItemsMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ApplicationContentMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ApplicationTemplateMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SysFunctionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ChargeItemRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.PreRecipeDetailRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeDetailRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeRepository;
import com.rjsoft.outPatient.infrastructure.repository.strategy.PreRecipeStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component("PreRecipeByApplyId")
@AllArgsConstructor
public class PreRecipeByApplyId implements PreRecipeStrategy {

    @Resource
    RecipeRepository recipeRepository;

    ChargeItemRepository chargeItemRepository;

    ApplicationContentItemsMapper applicationContentItemsMapper;

    ApplicationContentMapper applicationContentMapper;

    ApplicationTemplateMapper applicationTemplateMapper;

    SysFunctionMapper sysFunctionMapper;

    RecipeDetailRepository recipeDetailRepository;

    PreRecipeDetailRepository preRecipeDetailRepository;

    @Override
    @DatabaseAnnotation
    public List<PreRecipeDetail> getPreRecipeDetails(CheckRecipeDto checkDto, Map errInfo) {
        List<PreRecipeDetail> details = getPreRecipeDetailsByApplyId(Converter.toInt64(checkDto.getOperatingId()),
                checkDto.getDoctorId(), checkDto.getHospitalCode(), checkDto.getPreSaveNo());
        return details;
    }


    /**
     * 获取处方预存明细(根据申请单ID)
     *
     * @param applyId
     * @param doctorId
     * @param preSaveNo
     * @param hospitalCode
     * @return
     */
    //FIXME:yutao 2024-06-12 有两个参数没用，是不是要修一下
    @DatabaseAnnotation
    private List<PreRecipeDetail> getPreRecipeDetailsByApplyId(Long applyId, Integer doctorId, Integer hospitalCode, Long preSaveNo) {
        List<PreRecipeDetail> res = new ArrayList<>();
        List<ApplicationContentItems> list = applicationContentItemsMapper.GetApplicationContentItems(Converter.toInt32(applyId), hospitalCode);
        if (list == null) {
            return null;
        }
        ApplicationContent applicationContent = applicationContentMapper.getApplicationContentById(Converter.toInt32(applyId), hospitalCode);
        ApplicationTemplate template = applicationTemplateMapper.getApplicationTemplateByNo(applicationContent.getTemplateNo(), applicationContent.getHospitalCode());
        String examineCode = "";
        String examineName = "";
        if (template != null) {
            examineCode = Converter.toString(template.getExamineType());
            examineName = template.getExamineName();
        }

        // 申请单收费项目
        List<Integer> ids = list.stream().map(ApplicationContentItems::getItemCode).collect(Collectors.toList());

        List<ChargeItem> chargeItemList = chargeItemRepository.getChargeItemByIdsAndInitSpec(ids, hospitalCode);

        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        List<PreRecipeDetail> preRecipeDetails = preRecipeDetailRepository.getPreRecipeDetailsByApplyId(applyId, hospitalCode);
        if (preRecipeDetails.isEmpty()) {
            final Long newPreSaveNo = sysFunctionMapper.getGetSequences(SequenceEnum.PRE_SAVE_NO);
            for (ChargeItem items : chargeItemList) {
                PreRecipeDetail preRecipeDetail = new PreRecipeDetail();
                preRecipeDetail.ChangeChargeItem(items);
                preRecipeDetail.initByApply(applyId);
                preRecipeDetail.setPreSaveNo(newPreSaveNo);
                preRecipeDetail.setApplicationForm(applicationContent.getTemplateName());
                preRecipeDetail.setRecipeDetailNo(sysFunctionMapper.getGetSequences(SequenceEnum.RECIPE_DETAIL_NO));
                preRecipeDetail.setExamineCode(examineCode);
                preRecipeDetail.setExamineName(examineName);
                res.add(preRecipeDetail);
            }
            return res;
        }

        List<Integer> itemIdList = preRecipeDetails.parallelStream().map(PreRecipeDetail::getItemCode).collect(Collectors.toList());
        // 处方预存明细收费项目
        List<ChargeItem> itemList = chargeItemRepository.getChargeItemByIdsAndInitSpec(itemIdList, hospitalCode);
        // 比对判断是 新增、修改、删除收费项目
        final List<ChargeItem> updateList = new ArrayList<>();
        // 新的有 旧的没有
        List<ChargeItem> addList = new ArrayList<>();
        // 旧的有 新的没有
        List<ChargeItem> delList = new ArrayList<>();

        itemList.parallelStream().forEach(x -> chargeItemList.parallelStream().filter(y -> x.getItemCode().equals(y.getItemCode())).forEach(updateList::add));
        chargeItemList.parallelStream().filter(x -> !itemList.contains(x)).forEach(addList::add);
        itemList.parallelStream().filter(j -> !chargeItemList.contains(j)).forEach(delList::add);

        for (ChargeItem items : updateList) {
            final PreRecipeDetail recipeDetail = preRecipeDetails.parallelStream().filter(i -> i.getItemCode().equals(items.getItemCode())).findFirst().get();
            recipeDetail.setApplicationForm(applicationContent.getTemplateName());
            recipeDetail.ChangeChargeItem(items);
            recipeDetail.setOpFlag(1);
            recipeDetail.setExamineCode(examineCode);
            recipeDetail.setExamineName(examineName);
            res.add(recipeDetail);
        }
        for (ChargeItem items : addList) {
            final Long newPreSaveNo = preRecipeDetails.get(0).getPreSaveNo();
            PreRecipeDetail preRecipeDetail = new PreRecipeDetail();
            preRecipeDetail.ChangeChargeItem(items);
            preRecipeDetail.setApplicationForm(applicationContent.getTemplateName());
            preRecipeDetail.initByApply(applyId);
            preRecipeDetail.setPreSaveNo(newPreSaveNo);
            preRecipeDetail.setRecipeDetailNo(sysFunctionMapper.getGetSequences(SequenceEnum.RECIPE_DETAIL_NO));
            preRecipeDetail.setExamineCode(examineCode);
            preRecipeDetail.setExamineName(examineName);
            res.add(preRecipeDetail);
        }
        for (ChargeItem items : delList) {
            final PreRecipeDetail recipeDetail = preRecipeDetails.parallelStream().filter(i -> i.getItemCode().equals(items.getItemCode())).findFirst().get();
            recipeDetail.setOpFlag(-1);
            res.add(recipeDetail);
        }
        return res;
    }

}
