package com.rjsoft.outPatient.infrastructure.repository.strategy.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.PreRecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeRepository;
import com.rjsoft.outPatient.infrastructure.repository.strategy.PreRecipeStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component("PreRecipeByItemCode")
public class PreRecipeByItemCode extends PreRecipeByItemCodeBase implements PreRecipeStrategy {

    @Resource
    RecipeRepository recipeRepository;

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<PreRecipeDetail> getPreRecipeDetails(CheckRecipeDto checkDto, Map errInfo) {
        List<PreRecipeDetail> details = getPreRecipeDetailsByItemCode(checkDto);
        return details;
    }

}
