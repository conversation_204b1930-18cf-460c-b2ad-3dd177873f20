package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.param.SearchParam;
import com.rjsoft.outPatient.domain.config.dto.ExecDeptConfigDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.ExecDeptConfig;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 执行科室配置
 *
 * <AUTHOR>
public interface ExecDeptConfigRepository {

    /**
     * 保存执行科室配置
     *
     * @param config
     * @return
     */
    boolean saveExecDeptConfig(ExecDeptConfig config);


    /**
     * 查询执行科室内容信息，判断 是否允许保存
     *
     * @param configId
     * @param configType
     * @param feeCategory
     * @param hospitalCode
     * @return
     */
    boolean getExecDept(Integer configId, String configType, Integer feeCategory, Integer hospitalCode);


    /**
     * 删除执行科室
     *
     * @param id
     * @param hospitalCode
     * @return
     */
    boolean delExecDeptConfig(Integer id, Integer hospitalCode);


    /**
     * 查询执行科室列表
     *
     * @param searchParam
     * @return
     */
    List<ExecDeptConfig> getExecDeptConfigByInputCode(SearchParam searchParam);


    /**
     * 根据医院编码查询执行科室列表
     *
     * @param hospitalCode
     * @return
     */
    List<ExecDeptConfig> getExecDeptConfigFeeCategory(Integer hospitalCode);


    /**
     * 根据项目编码查询执行科室
     *
     * @param itemCode
     * @param itemCategory
     * @param regDept
     * @param hospitalCode
     * @return
     */
    List<ExecDeptConfig> getExecDeptByItemCode(Integer itemCode, Integer itemCategory, Integer regDept, Integer hospitalCode);

    /**
     * 根据挂号科室，项目ID，获取默认执行科室
     *
     * @param regDept
     * @param itemCode
     * @param itemCategory
     * @param hospitalCode
     * @return
     */
    Integer getDefaultExecDept(Integer regDept, Integer itemCode, Integer itemCategory, Integer hospitalCode, String loginIp);

    Integer getDefaultExecDept(Integer regDept, Integer itemCode, Integer itemCategory, Integer hospitalCode);

    /**
     * 根据[药品项目编码]和[医院编码] 获取默认执行科室
     *
     * @param itemCode
     * @param hospitalCode
     * @param feeCategory
     * @param configType
     * @return
     */
    List<ExecDeptConfig> getDept(Integer itemCode, Integer hospitalCode, Integer feeCategory, String configType);


}
