package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 医生开药权限
 *
 * <AUTHOR>
 * @since 2021/7/23 - 17:55
 */
@Data
@Table(name = "Tbt_MzRecipeDoctorRight")
public class RecipeDoctorRight implements Serializable {

    /**
     * 权限编码
     */
    @Column(name = "RightCode")
    private Integer rightCode;

    /**
     * 医生编码
     */
    @Column(name = "DoctorId")
    private Integer doctorId;

}
