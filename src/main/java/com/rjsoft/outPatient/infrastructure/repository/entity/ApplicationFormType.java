package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.rjsoft.outPatient.domain.dictionary.dto.CommonDicEntity;
import com.rjsoft.outPatient.domain.dictionary.vo.CommonDicVo;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 申请单类型
 */
@Data
@Table(name = "MZYS_TB_JCSQD_Type")
public class ApplicationFormType extends CommonDicEntity  implements Serializable {

    @Column(name = "type")
    private Integer code;

    @Column(name = "name")
    private String name;

    @Override
    public CommonDicVo conversionFill() {
        return new CommonDicVo(this);
    }
}
