package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeRightToItem;

import java.util.List;

/**
 * 药品开立权限
 *
 * <AUTHOR>
 * @since 2021/7/23 - 17:09
 */
public interface RecipeRightToItemRepository {

    /**
     * 查询药品开立权限
     *
     * @param drugCode 药品编码
     * @param rightCode 权限编码
     * @param isRightCodeEq 权限编码查询条件  true  rightCode == ？
     *                                     false rightCode != ？
     * @return
     */
    Integer getRecipeRightToItemCount(Integer drugCode, Integer rightCode, Boolean isRightCodeEq);

    /**
     * 根据药品编码和权限代码查询 {@link List<RecipeRightToItem>}
     *
     * @param drugCode 药品编码
     * @param rightCodes 权限代码
     * @return {@link List<RecipeRightToItem>}
     */
    List<RecipeRightToItem> getRecipeRightToItemByDrugCodeAndRightCode(Integer drugCode, List<Integer> rightCodes);

}
