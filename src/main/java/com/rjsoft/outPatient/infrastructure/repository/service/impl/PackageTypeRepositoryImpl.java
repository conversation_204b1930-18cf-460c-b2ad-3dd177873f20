package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.PackageType;
import com.rjsoft.outPatient.infrastructure.repository.mapper.PackageTypeMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.PackageTypeRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/30 - 16:05
 */

@Service
@AllArgsConstructor
public class PackageTypeRepositoryImpl implements PackageTypeRepository {

    private final PackageTypeMapper packageTypeMapper;

    @Override
    @DatabaseAnnotation
    public List<PackageType> getPackageTypesByPackageTypeNo(Integer packageTypeNo, Integer hospitalCode) {
        final PackageType entity = new PackageType();
        entity.setPackageType(packageTypeNo);
        entity.setHospitalCode(hospitalCode);
        return packageTypeMapper.select(entity);
    }
}
