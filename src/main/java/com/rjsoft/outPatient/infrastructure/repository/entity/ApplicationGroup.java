package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * 申请单分组信息
 */
@Data
@Table(name = "MZYS_TB_JCSQDZH")
public class ApplicationGroup  implements Serializable {

    @Id
    @Column(name = "zhlsh")
    private Integer fromGroupNo;

    @Column(name = "sqdbm")
    private String templateNo;

    @Column(name = "zhmc")
    private String fromGroupName;

    @Column(name = "yybm")
    private Integer hospitalCode;


}


