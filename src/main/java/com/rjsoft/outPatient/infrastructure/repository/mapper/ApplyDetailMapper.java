package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplyDetail;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.List;

/**
 * 申请单明细
 * <AUTHOR>
public interface ApplyDetailMapper extends BaseMapper<ApplyDetail>, ExampleMapper<ApplyDetail> {

    /**
     * 根据ID获取申请单明细
     * @param param 项目入参顺序：ID
     * @return
     */
    default ApplyDetail getApplyDetailById(Object... param){
        ApplyDetail entity=new ApplyDetail();
        entity.setId(Converter.toInt32(param[0]));
        return selectByPrimaryKey(entity);
    }

    /**
     * 根据申请主表查询明细
     * @param mainId
     * @return
     */
    default List<ApplyDetail> getApplyDetailsByListId(Integer mainId){
        ApplyDetail entity=new ApplyDetail();
        entity.setListId(mainId);
        return select(entity);
    }

    /**
     * 根据申请单加载明细
     * @param mainIds
     * @return
     */
    default List<ApplyDetail> getApplyDetailsByListId(List<Integer> mainIds){
        if(mainIds==null||mainIds.size()==0){
            return new ArrayList<>();
        }
        Weekend weekend=new Weekend(ApplyDetail.class);
        WeekendCriteria<ApplyDetail,Object>weekendCriteria=weekend.weekendCriteria();
        weekendCriteria.andIn(ApplyDetail::getListId,mainIds);

        return selectByExample(weekend);
    }

    /**
     * 根据检查流水号修改申请单项目收费类型
     * @param examineNos
     * @param hospitalCode
     */
    default void updateApplyDetailStatus(List<Integer> examineNos,Integer hospitalCode){
        Weekend<ApplyDetail> weekend = new Weekend<>(ApplyDetail.class);
        WeekendCriteria<ApplyDetail, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(ApplyDetail::getListId,examineNos)
                .andEqualTo(ApplyDetail::getHospitalId,hospitalCode);
        ApplyDetail detail = new ApplyDetail();
        detail.setChargeFlag(0);
        updateByExampleSelective(detail,weekend);
    }
}
