package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.enums.YesOrNoEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.PackageDetail;
import com.rjsoft.outPatient.infrastructure.repository.mapper.PackageDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SysFunctionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.PackageDetailRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/7/6 - 17:08
 */
@Service
@AllArgsConstructor
public class PackageDetailRepositoryImpl implements PackageDetailRepository {

    PackageDetailMapper packageDetailMapper;
    SysFunctionMapper sysFunctionMapper;

    @Override
    @DatabaseAnnotation
    public List<PackageDetail> getPackageDetailByPackageId(int packageId) {
        final PackageDetail packageDetail = new PackageDetail();
        packageDetail.setPackageId(packageId);
        // 0 正常 1 删除
        packageDetail.setStatus(YesOrNoEnum.NO.getCode());
        return packageDetailMapper.select(packageDetail);
    }

    @Override
    @DatabaseAnnotation
    public void savePackageDetail(PackageDetail packageDetail) {
        packageDetail.setCreateTime(sysFunctionMapper.getDate());
        packageDetailMapper.insertSelective(packageDetail);
    }

    @Override
    @DatabaseAnnotation
    public void updatePackageDetail(PackageDetail packageDetail, Example example) {
        packageDetail.setUpdateTime(sysFunctionMapper.getDate());
        if (example == null) {
            packageDetailMapper.updateByPrimaryKeySelective(packageDetail);
        } else {
            packageDetailMapper.updateByExampleSelective(packageDetail, example);
        }
    }

    @Override
    @DatabaseAnnotation
    public List<Integer> getPackageDetailIdsByPackageId(Integer packageId) {
        Example example = new Example(PackageDetail.class);
        example.selectProperties("packageDetailId")
                .createCriteria()
                .andEqualTo("status", YesOrNoEnum.NO.getCode())
                .andEqualTo("packageId", packageId);
        return packageDetailMapper.selectByExample(example)
                .stream().map(PackageDetail::getPackageDetailId).collect(Collectors.toList());
    }
}
