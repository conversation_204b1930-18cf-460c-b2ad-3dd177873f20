package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * 检查申请明细（ZXHIS库）
 * <AUTHOR>
@Data
@Table(name = "Tbt_RisRequestDtl")
public class ExamineRequestDetailZxHis  implements Serializable {

    /**
     *检查明细流水号
     */
    @Column(name = "Id")
    private Integer examineDetailNo;

    /**
     *risId
     */
    @Column(name = "risid")
    private Integer risId;

    /**
     *检查编号
     */
    @Column(name = "RisNo")
    private String examineCode;


    /**
     *处方明细ID
     */
    @Column(name = "CfMxId")
    private Long recipeDetailId;


    /**
     *处方id
     */
    @Column(name = "Cfid")
    private Long recipeId;

    /**
     *项目编码
     */
    @Column(name = "ItemCode")
    private Integer itemCode;

    /**
     *项目名称
     */
    @Column(name = "ItemName")
    private String itemName;

    /**
     *收费标记
     */
    @Column(name = "SfFlag")
    private  Integer sfFlag;

    /**
     *检查内容
     */
    @Column(name = "Content")
    private String content;

    /**
     * 病历ID
     */
    @Column(name = "BlId")
    public Integer blId;

    /**
     *检查部位
     */
    @Column(name = "JcBw")
    private String examinePart;

    /**
     *检查部位名称
     */
    @Column(name = "JcBwName")
    private String examinePartName;

    /**
     * 数量
     */
    @Column(name = "Num")
    private Integer quantity;

    /**
     *操作员
     */
    @Column(name = "OPERATOR")
    private Integer operator;

    /**
     *创建日期
     */
    @Column(name = "CreateTime")
    private Date createTime;

    /**
     *修改日期
     */
    @Column(name = "UpdateTime")
    private  Date updateTime;

    /**
     *旧RIS流水号
     */
    @Column(name = "OldRisId")
    private  Integer oldRisId;

    /**
     *收费日期
     */
    @Column(name = "SfTime")
    private Date sfTime;

    /**
     *住院收费标记
     */
    @Column(name = "WardSfFlag")
    private Integer wardSfFlag;

    /**
     *退费时间
     */
    @Column(name = "TfTime")
    private  Date refundTime;

    /**
     *
     */
    @Column(name = "BwTcFlag")
    private  Integer bwTcFlag;

    /**
     *
     */
    @Column(name = "BwNum")
    private  Integer bwNum;
}
