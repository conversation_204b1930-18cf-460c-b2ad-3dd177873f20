package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.DiagnoseUploadType;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

public interface DiagnoseUploadTypeMapper extends BaseMapper<DiagnoseUploadType>, ExampleMapper<DiagnoseUploadType> {


    /**
     * 查询诊断上报方式
     *
     * @param diagnoseCode
     * @param hospitalCode
     * @return
     */
    List<DiagnoseUploadType> getDiagnoseUploadType(@Param("diagnoseCode") String diagnoseCode, @Param("hospitalCode") Integer hospitalCode);


    /**
     * 查询疾病诊断类型
     *
     * @param hospitalCode
     * @return
     */
    List<DiagnoseUploadType> getReportUploadType(@Param("hospitalCode") Integer hospitalCode);


    /**
     * 查询疾病诊断类型
     *
     * @param hospitalCode
     * @return
     */
    List<DiagnoseUploadType> getReportUploadTypeDiagnose(@Param("hospitalCode") Integer hospitalCode);


}
