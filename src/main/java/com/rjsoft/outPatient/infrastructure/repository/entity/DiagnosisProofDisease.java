package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 疾病证明诊断
 *
 * <AUTHOR>
 * @since 2021/7/8 - 15:42
 */
@Data
@Table(name = "MZYS_TB_JBZM_ZD")
public class DiagnosisProofDisease  implements Serializable {

    /**
     * id
     */
    @Id
    @Column(name = "id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 疾病证明表主键id
     */
    @Column(name = "MainId")
    private Integer mainId;

    /**
     * 诊断编码
     */
    @Column(name = "zdbm")
    private String diagnoseCode;

    /**
     * 诊断名称
     */
    @Column(name = "zdmc")
    private String diagnoseName;

    /**
     * 创建r
     */
    @Column(name = "CreateBy")
    private Integer createBy;

    /**
     * 创建人姓名
     */
    @Column(name = "CreateName")
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "CreateTime")
    private Date createTime;

    /**
     * 医院编码
     */
    @Column(name = "hospitalCode")
    private Integer hospitalCode;

}
