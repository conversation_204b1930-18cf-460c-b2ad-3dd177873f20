package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.domain.prescriptionAudit.dto.DrugUnitInfoDto;
import com.rjsoft.outPatient.domain.recipe.dto.DrugYYYDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugInfomation;

import java.util.List;
import java.util.Map;

/**
 * 药典信息
 *
 * <AUTHOR>
public interface DrugInfomationRepository {

    /**
     * 根据ID集合查询药典信息
     *
     * @param ids
     * @param hospitalCode
     * @return
     */
    List<DrugInfomation> getDrugInfoByIds(Iterable<Integer> ids, Integer hospitalCode);

    /**
     * 根据ID集合查询药典信息
     *
     * @param id
     * @param hospitalCode
     * @return
     */
    DrugInfomation getDrugInfoById(Integer id, Integer hospitalCode);

    @DatabaseAnnotation(name = "HISDB")
    DrugInfomation tyMdcGetDrugInfoById(Integer id, Integer hospitalCode);

    @DatabaseAnnotation(name = "HISDB")
    Map<Integer,DrugInfomation> mapDrugInfoById(List<Integer> itemCodList, Integer hospitalCode);

    @DatabaseAnnotation(name = "HISDB")
    List<DrugInfomation> getDrugInfoByIdList(List<Integer> idList, Integer hospitalCode);

    /**
     * 根据id集合、药品属性查询药典信息
     *
     * @param operateType  操作类型 1：普通处方，2：精二类处方
     * @param ids
     * @param hospitalCode
     * @return
     */
    List<DrugInfomation> getDrugInfomationByDrugIdsAndProperty(Integer operateType, List<Integer> ids, Integer hospitalCode);

    /**
     * 根据药品编码+医院编码查询药品单位信息
     *
     * @param drugId
     * @param hospitalCode
     * @return
     */
    DrugUnitInfoDto getDrugUnitInfo(String drugId, String hospitalCode);

    /**
     * 校验当前药物是否为原研药
     * @param itemId
     * @param hospId
     * @return
     */
    Boolean checkDrugIsYYY(Integer itemId, Integer hospId);

    List<DrugYYYDto> listDrugAllYYY(Integer hospId);

    Integer checkYYYAuthority(Integer doctorId, Integer hospId);

    Boolean doctorOpenYYYCheck(Integer itemId, Integer doctorId, Integer hospId);
}
