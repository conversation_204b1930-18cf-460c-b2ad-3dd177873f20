<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.VirtualInventoryMapper">
    <select id="getStorePackFactorInventoryByDrugIdAndDeptId"
            resultType="BigDecimal">
        SELECT (SELECT sum(isnull(v.Quantity * v.PackFactor, 0))
                FROM Drug_Tb_StoreChangeOutPatientVirtual v
                WHERE l.DrugId = v.DrugId
                  AND l.HospitalId = v.HospitalId
                  AND l.DeptId = v.DeptId) / isnull(d.StorePackFactor, 0) inventory
        FROM System_Tb_PubItems i
                 INNER JOIN Drug_Tb_DrugInfomation d
                            ON i.ItemCode = d.DrugId AND i.HospitalId = d.HospitalId
                 INNER JOIN Drug_Tb_LocalDeptDrug l ON i.ItemCode = l.DrugId AND i.HospitalId = l.HospitalId
        WHERE i.HospitalId = #{hospitalId}
          AND i.Stopped = 0
          AND l.DeptId = #{deptId}
          AND i.ItemCategory in (12, 13, 14)
          AND i.ItemCode = #{drugId}
    </select>

    <select id="getClinicPackFactorInventoryByDrugIdAndDeptIdList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.DeptItempStock">
        SELECT (SELECT sum(isnull(v.Quantity * v.PackFactor, 0))
        FROM Drug_Tb_StoreChangeOutPatientVirtual v
        WHERE l.DrugId = v.DrugId
        AND l.HospitalId = v.HospitalId
        and v.deptId = l.deptId
        AND l.DeptId IN
        <foreach item="deptId" collection="deptIdList" open="(" separator="," close=")">
            #{deptId}
        </foreach>) / isnull(s.ClinicQuantity, i.ClinicQty) stock,i.ItemCode,l.DeptId
        FROM System_Tb_PubItems i
        LEFT JOIN System_Tb_SpecialItems s ON i.ItemCode = s.ItemCode AND i.HospitalId = s.HospitalCode
        INNER JOIN Drug_Tb_LocalDeptDrug l ON i.ItemCode = l.DrugId AND i.HospitalId = l.HospitalId
        WHERE
            i.Stopped = 0
        AND l.DeptId IN
        <foreach item="deptId" collection="deptIdList" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        AND i.ItemCategory in (12, 13, 14)
        AND i.ItemCode IN
        <foreach item="drugId" collection="drugIdList" open="(" separator="," close=")">
            #{drugId}
        </foreach>
    </select>
    <select id="getClinicPackFactorInventoryByDrugIdAndDeptId" resultType="java.math.BigDecimal">
        SELECT (SELECT sum(isnull(v.Quantity * v.PackFactor, 0))
                FROM Drug_Tb_StoreChangeOutPatientVirtual v
                WHERE l.DrugId = v.DrugId
                  AND l.HospitalId = v.HospitalId
                  AND l.DeptId = v.DeptId) / isnull(s.ClinicQuantity, i.ClinicQty) inventory
        FROM System_Tb_PubItems i
                 LEFT JOIN System_Tb_SpecialItems s ON i.ItemCode = s.ItemCode AND i.HospitalId = s.HospitalCode
                 INNER JOIN Drug_Tb_LocalDeptDrug l ON i.ItemCode = l.DrugId AND i.HospitalId = l.HospitalId
        WHERE i.HospitalId = #{hospitalId}
          AND i.Stopped = 0
          AND l.DeptId = #{deptId}
          AND i.ItemCategory in (12, 13, 14)
          AND i.ItemCode = #{drugId}
    </select>

    <select id="getInHospPackFactorInventoryByDrugIdAndDeptId" resultType="java.math.BigDecimal">
        SELECT (SELECT sum(isnull(v.Quantity * v.PackFactor, 0))
                FROM Drug_Tb_StoreChangeInPatientVirtual v
                WHERE l.DrugId = v.DrugId
                  AND l.HospitalId = v.HospitalId
                  AND l.DeptId = v.DeptId) inventory
        FROM System_Tb_PubItems i
                 LEFT JOIN System_Tb_SpecialItems s ON i.ItemCode = s.ItemCode AND i.HospitalId = s.HospitalCode
                 INNER JOIN Drug_Tb_LocalDeptDrug l ON i.ItemCode = l.DrugId AND i.HospitalId = l.HospitalId
        WHERE i.HospitalId = #{hospitalId}
          AND i.Stopped = 0
          AND l.DeptId = #{deptId}
          AND i.ItemCategory in (12, 13, 14)
          AND i.ItemCode = #{drugId}
    </select>

    <delete id="deleteRecord">
        DELETE
        FROM Drug_Tb_StoreChangeOutPatientVirtual
        WHERE DrugId = #{drugId}
          AND DeptId = #{deptId}
          AND HospitalId = #{hospitalId}
          AND SourceDetailId = #{sourceDetailId};
    </delete>

</mapper>