<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.RegisterListMapper">

    <select id="getPageRegisterListByDoctor"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        select * from (
        select
        a.RegNo regNo,
        a.ReturnRegNo returnRegNo,
        a.PatID patID,
        a.NewPatID newPatID,
        a.CardNo cardNo,
        a.OutPatientNo outPatientNo,
        a.HospNo hospNo,
        a.PatName patName,
        a.CourseID courseID,
        a.CourseName courseName,
        a.DeptID deptID,
        a.DoctorID doctorID,
        a.<PERSON><PERSON><PERSON><PERSON> doctorLevel,
        a.ChargeType chargeType,
        a.<PERSON><PERSON> blanceWay,
        a.RegistType registType,
        a.AppointmentWay appointmentWay,
        rtrim(a.AppointmentNo) appointmentNo,
        case when rsg.RegistOrder is not null then rsg.RegistOrder ELSE a.RegistOrder end  RegistOrder,
        a.registMode RegistMode,
        a.VisitTime visitTime,
        a.Status status,
        a.FzFlag fzFlag,
        a.RegistTime registTime,
        a.ComputerNo computerNo,
        a.OpCode opCode,
        a.CreateTime createTime,
        a.CureCode cureCode,
        a.VisitFlag visitFlag,
        a.ReferralFlag referralFlag,
        a.DeptKind deptKind,
        a.HospitalCode hospitalCode,
        a.UnitNo unitNo,
        a.VipFlag vipFlag,
        a.GhDoctor ghDoctor,
        isnull(datediff(year, P.Birthday, getdate()), '') patAge,
        P.Birthday birthday,
        p.Sex patSex,
        p.CertificateNo certificateNo,
        pd.PatPhone patPhone,
        rtrim(zg.Name) doctorName,
        case when a.RegistType = 1 then '急诊|' else '' end + rtrim(dept.DeptName) deptName,
        case
        when a.VisitFlag &lt; 2 then
        case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上初诊' else '初诊' end
        else
        case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上复诊' else '复诊' end
        end visitFlagName
        from Reg_Tb_RegisterList_Time a (nolock)
        inner join Reg_Tb_OutpatientInvoice_Time b (nolock) on a.RegNo = b.RegNo and a.HospitalCode = b.HospitalCode
        inner join Reg_Tb_PatientList p (nolock) on a.PatID = p.PatID
        inner join Reg_Tb_PatientDetl pd (nolock) on a.PatID = pd.PatID
        left join Reg_Tv_ConsultationList sq (nolock) on a.RegNo = sq.regno and a.HospitalCode = sq.yybm
        left join System_Tb_Worker zg on a.DoctorID = zg.WorkerId and a.HospitalCode = zg.HospitalId
        left join System_Tb_Department dept on a.DeptID = dept.DeptId and a.HospitalCode = dept.HospitalId
        left join Reg_Tb_SpecialSourceRelation rs (nolock) on a.RegNo = rs.DispensingRegNo and a.HospitalCode = rs.HospitalCode
        left join Reg_Tb_RegisterList_Time rsg (nolock) on rs.SpecialRegNo = rsg.RegNo and rsg.HospitalCode = rs.HospitalCode
        where a.RegistTime between #{startTime} and #{endTime}
        and a.HospitalCode = #{hospitalCode}
        and ((a.CardNo = #{cardNo} or '' = #{cardNo}) or (a.OutPatientNo = #{cardNo} or '' = #{cardNo}))
        and ((0 = #{selectRange} and a.DeptID = #{deptId})
        or (0 = #{selectRange} and sq.hzks = #{deptId})
        or (1 = #{selectRange} and a.DoctorID = #{workerId})
        or (1 = #{selectRange} and sq.hzys = #{workerId}))
        and ((0 = #{fzFlag} and isnull(a.FzFlag, 0) in (0, 1)) or
        (1 = #{fzFlag} and isnull(a.FzFlag, 0) not in (0, 1)))
        <if test="list.size > 0">
            and (a.deptId not in
            <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        and a.Status = 0
        and a.IsDelete = 0
        and b.Flag = 1
        ) a
        order by DeptID ASC, ${sortField}
    </select>

    <select id="getPageRegisterAllListByDoctor"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        select * from (
        select a.RegNo regNo,
        a.ReturnRegNo returnRegNo,
        a.PatID patID,
        a.NewPatID newPatID,
        a.CardNo cardNo,
        a.OutPatientNo outPatientNo,
        a.HospNo hospNo,
        a.PatName patName,
        a.CourseID courseID,
        a.CourseName courseName,
        a.DeptID deptID,
        a.DoctorID doctorID,
        a.DoctorLevel doctorLevel,
        a.ChargeType chargeType,
        a.BlanceWay blanceWay,
        a.RegistType registType,
        a.AppointmentWay appointmentWay,
        rtrim(a.AppointmentNo) appointmentNo,
        case when rsg.RegistOrder is not null then rsg.RegistOrder ELSE a.RegistOrder end  RegistOrder,
        a.registMode RegistMode,
        a.VisitTime visitTime,
        a.Status status,
        a.FzFlag fzFlag,
        a.RegistTime registTime,
        a.ComputerNo computerNo,
        a.OpCode opCode,
        a.CreateTime createTime,
        a.CureCode cureCode,
        a.VisitFlag visitFlag,
        a.ReferralFlag referralFlag,
        a.DeptKind deptKind,
        a.HospitalCode hospitalCode,
        a.UnitNo unitNo,
        isnull(datediff(year, P.Birthday, getdate()), '') patAge,
        P.Birthday birthday,
        p.Sex patSex,
        p.CertificateNo certificateNo,
        pd.PatPhone patPhone,
        rtrim(zg.Name) doctorName,
        case when a.RegistType = 1 then '急诊|' else '' end + rtrim(dept.DeptName) deptName,
        case
        when a.VisitFlag &lt; 2 then
        case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上初诊' else '初诊' end
        else
        case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上复诊' else '复诊' end
        end visitFlagName
        from Reg_Tv_RegisterList a (nolock)
        inner join Reg_Tv_OutpatientInvoice b (nolock) on a.RegNo = b.RegNo and a.HospitalCode = b.HospitalCode
        inner join Reg_Tb_PatientList p (nolock) on a.PatID = p.PatID
        inner join Reg_Tb_PatientDetl pd (nolock) on a.PatID = pd.PatID
        left join Reg_Tv_ConsultationList sq (nolock) on a.RegNo = sq.regno and a.HospitalCode = sq.yybm
        left join System_Tb_Worker zg on a.DoctorID = zg.WorkerId and a.HospitalCode = zg.HospitalId
        left join System_Tb_Department dept on a.DeptID = dept.DeptId and a.HospitalCode = dept.HospitalId
        LEFT JOIN Reg_Tb_SpecialSourceRelation rs (nolock) ON a.RegNo = rs.DispensingRegNo
        AND a.HospitalCode = rs.HospitalCode
        LEFT JOIN Reg_Tv_RegisterList rsg ON rs.SpecialRegNo = rsg.RegNo
        AND rsg.HospitalCode = rs.HospitalCode
        where a.RegistTime between #{startTime} and #{endTime}
        and a.HospitalCode = #{hospitalCode}
        and ((a.CardNo = #{cardNo} or '' = #{cardNo}) or (a.OutPatientNo = #{cardNo} or '' = #{cardNo}))
        and ((0 = #{selectRange} and a.DeptID = #{deptId})
        or (0 = #{selectRange} and sq.hzks = #{deptId})
        or (1 = #{selectRange} and a.DoctorID = #{workerId})
        or (1 = #{selectRange} and sq.hzys = #{workerId}))
        and ((0 = #{fzFlag} and isnull(a.FzFlag, 0) in (0, 1)) or
        (1 = #{fzFlag} and isnull(a.FzFlag, 0) not in (0, 1)))
        <if test="list.size > 0">
            and (a.deptId not in
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        and a.Status = 0
        and a.IsDelete = 0
        and b.Flag = 1
        ) a
        order by DeptID ASC, ${sortField}
    </select>

    <select id="getRegisterCountByDoctor" resultType="java.util.HashMap">
        select count(case when isnull(a.FzFlag, 0) in (0, 1) then 1 else null end) as  noSeeDoctorNum,
               count(case when isnull(a.FzFlag, 0) not in (0, 1) then 1 else null end) seeDoctorNum
        from Reg_Tb_RegisterList_Time a (nolock)
                 inner join Reg_Tb_OutpatientInvoice_Time b (nolock) on a.RegNo = b.RegNo and a.HospitalCode = b.HospitalCode
                 left join Reg_Tv_ConsultationList sq (nolock) on a.RegNo = sq.regno and a.HospitalCode = sq.yybm
        where a.RegistTime between #{startTime} and #{endTime}
          and a.HospitalCode = #{hospitalCode}
        <if test="cardNo != null and cardNo != ''">
            and (
            a.CardNo = #{cardNo}
            or a.OutPatientNo = #{cardNo}
            )
        </if>
        <if test="selectRange == 0">
            and (a.DeptID = #{deptId} or sq.hzks = #{deptId})
        </if>
        <if test="selectRange == 1">
            and (a.DoctorID = #{workerId} or sq.hzys = #{workerId})
        </if>
        <if test="list.size > 0">
            and (a.deptId not in
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
          and a.Status = 0
          and a.IsDelete = 0
          and b.Flag = 1
    </select>

    <select id="getRegisterAllCountByDoctor" resultType="java.util.HashMap">
        select count(case when isnull(a.FzFlag, 0) in (0, 1) then 1 else null end) as  noSeeDoctorNum,
               count(case when isnull(a.FzFlag, 0) not in (0, 1) then 1 else null end) seeDoctorNum
        from Reg_Tv_RegisterList a (nolock)
                 inner join Reg_Tv_OutpatientInvoice b (nolock) on a.RegNo = b.RegNo and a.HospitalCode = b.HospitalCode
                 left join Reg_Tv_ConsultationList sq (nolock) on a.RegNo = sq.regno and a.HospitalCode = sq.yybm
        where a.RegistTime between #{startTime} and #{endTime}
          and a.HospitalCode = #{hospitalCode}
        <if test="cardNo != null and cardNo != ''">
            and (
            a.CardNo = #{cardNo}
            or a.OutPatientNo = #{cardNo}
            )
        </if>
        <if test="selectRange == 0">
            and (a.DeptID = #{deptId} or sq.hzks = #{deptId})
        </if>
        <if test="selectRange == 1">
            and (a.DoctorID = #{workerId} or sq.hzys = #{workerId})
        </if>
        <if test="list.size > 0">
            and (a.deptId not in
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
          and a.Status = 0
          and a.IsDelete = 0
          and b.Flag = 1
    </select>

    <select id="queryPatientInfo" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.PatientDTO">
        select b1.RegNo                    as regNo,
               b1.CardNo                   as cardNo,
               b1.PatID                    as patId,
               b2.PatName                  as patName,
               b2.Birthday                 as birthday,
               dbo.funCalcAge(b2.Birthday) as patAge,
               b2.Sex                      as patSex,
               b2.CertificateNo            as certificateNo,
               b1.OutPatientNo             as hisCardNo
        from Reg_Tv_RegisterList b1  (nolock)
                 inner join Reg_Tb_PatientList b2 (nolock) on b1.PatID = b2.PatID
        where b1.RegNo = #{regNo}
    </select>

    <select id="queryOldPatientInfo" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.PatientDTO">
        SELECT top 1 d.GuidReg regNo,a.hzbh patId, a.xm patName, a.sr birthday, a.xb sex, a.blkh hisCardNo
        from GHSF_TB_HZJBXX a (nolock)
                 inner join zxhis.dbo.GHSF_TB_HZJBXX_GUID b (nolock) on a.hzbh = b.hzbh
                 inner join zxhis.dbo.Tbt_GhRegList c (nolock) on b.PatNo = c.PatNo
                 inner join zxhis.dbo.GUID_GhRegList d (nolock) on c.RegNo = d.RegNo
        where d.GuidReg = #{regNo}
    </select>

    <select id="getRegisterByHisCardNo"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        select top(1) *
        from Reg_Tb_RegisterList_Time (nolock)
        where OutPatientNo = #{hisCardNo}
          and HospitalCode = #{hospitalCode}
        order by RegistTime desc

    </select>

    <select id="getAllRegNoByCertificateNo" resultType="java.lang.Long">
        select r.RegNo
        from Reg_Tv_RegisterList r (nolock)
                 left join Reg_Tb_PatientList p (nolock) on p.PatID = r.PatID
        where p.CertificateNo = #{certificateNo}
          and r.Status = 0
    </select>

    <select id="getAllRegNoByPatId" resultType="java.lang.Long">
        select RegNo
        from Reg_Tv_RegisterList (nolock)
        where PatID = #{patId}
          and Status = 0
    </select>

    <select id="queryAllRegNoByPatIds" resultType="java.lang.Long">
        select RegNo
        from Reg_Tv_RegisterList (nolock)
        where Status = 0
        and PatID in
        <foreach item="item" collection="patIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>

    <select id="getAllRegistTimeByCertificateNo" resultType="java.util.Date">
        select top(1) a.RegistDate
        from Tbt_GhRegList a (nolock)
                 inner join GUID_GhRegList b (nolock) on a.RegNo = b.RegNo
                 inner join Tbt_PubPatientInfo c (nolock) on a.PatNo = c.PatNo
        where c.PatSfz = #{certificateNo}
          and a.Status = 0
          and a.VisiteFlag = 1
        order by a.RegistDate
    </select>

    <select id="getMajorAllRegNoByPatId" resultType="com.rjsoft.outPatient.domain.diagnose.dto.MajorRegNoDto">
        select r.RegNo regNo, r.VisitFlag visitFlag, DoctorID doctorID, registTime registDate
        from Reg_Tv_RegisterList r (nolock)
                 left join Reg_Tb_PatientList p (nolock) on p.PatID = r.PatID
        where p.patId = #{patId}
          and r.Status = 0
    </select>

    <select id="getMajorAllRegNoFromOldData" resultType="com.rjsoft.outPatient.domain.diagnose.dto.MajorRegNoDto">
        select DISTINCT b.GuidReg    guidReg,
                        a.VisiteFlag visitFlag,
                        OperId       doctorId,
                        a.RegNo      regNo,
                        a.RegistDate registDate
        from Tbt_GhRegList a (nolock)
                 inner join GUID_GhRegList b (nolock) on a.RegNo = b.RegNo
                 inner join Tbt_PubPatientInfo c (nolock) on a.PatNo = c.PatNo
        where c.PatNo = #{patNo}
          and a.Status = 0
    </select>

    <select id="queryMajorAllRegNoFromOldData" resultType="com.rjsoft.outPatient.domain.diagnose.dto.MajorRegNoDto">
        select DISTINCT b.GuidReg    guidReg,
        a.VisiteFlag visitFlag,
        OperId       doctorId,
        a.RegNo      regNo,
        a.RegistDate registDate
        from Tbt_GhRegList a (nolock)
        inner join GUID_GhRegList b (nolock) on a.RegNo = b.RegNo
        inner join Tbt_PubPatientInfo c (nolock) on a.PatNo = c.PatNo
        where a.Status = 0
        and c.PatNo in
        <foreach collection="patNos" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getIOAllRegNosByPatId" resultType="java.lang.Long">
        select RegNo
        from IO_Tb_InPatient (nolock)
        where PatId = #{patId}
          and status = 9
        order by InWardTime desc
    </select>

    <select id="getIOCheckInfoByRegNo" resultType="java.util.HashMap">
        select case
                   when (select count(*)
                         from EMR_Tb_RecInfo (nolock)
                         where RegNo = #{regNo}
                           and RecTempCode = 'BC4003'
                           and HospitalId = 1
                           and RecStatus = 1) > 0
                       then 1
                   else 0 end AS zr,
               case
                   when (select count(*)
                         from EMR_Tb_RecInfo (nolock)
                         where RegNo = #{regNo}
                           and RecTempCode = 'BC4005'
                           and HospitalId = 1
                           and RecStatus = 1) > 0
                       then 1
                   else 0 end AS zz,
               case
                   when (select count(*)
                         from EMR_Tb_RecInfo (nolock)
                         where RegNo = #{regNo}
                           and RecTempCode = 'BC4024'
                           and HospitalId = 1
                           and RecStatus = 1) > 0
                       then 1
                   else 0 end AS rc
    </select>

    <select id="getIODiagnoseInfoByRegNo" resultType="com.rjsoft.outPatient.domain.diagnose.dto.MajorIoDiagnoseDto">
        select
        distinct
        rtrim(diagnosisCode) diagnosisCode,
        diagnosisName,
        createon createon
        from IO_Tb_PatientDiagnosis a (nolock)
        <where>
            <if test="regNo!=null">
                and regNo = #{regNo}
            </if>
            and DiagnosisType = 1
            and (a.isDelete is null or a.isDelete = 0)
        </where>
    </select>

    <select id="getAllRegNoFromOldData" resultType="java.lang.String">
        select DISTINCT b.GuidReg
        from Tbt_GhRegList a (nolock)
                 inner join GUID_GhRegList b (nolock) on a.RegNo = b.RegNo
                 inner join Tbt_PubPatientInfo c (nolock) on a.PatNo = c.PatNo
        where c.PatSfz = #{certificateNo}
          and a.Status = 0
    </select>
    <select id="getAllRegNoFromOldDataByPatId" resultType="java.lang.String">
        select DISTINCT b.GuidReg
        from Tbt_GhRegList a (nolock)
                 inner join GUID_GhRegList b (nolock) on a.RegNo = b.RegNo
                 inner join Tbt_PubPatientInfo c (nolock) on a.PatNo = c.PatNo
        where c.PatNo = #{patId}
          and a.Status = 0
    </select>

    <select id="getAllRegNoFromOldDataByPatIds" resultType="java.lang.String">
        select DISTINCT b.GuidReg
        from Tbt_GhRegList a (nolock)
                 inner join GUID_GhRegList b (nolock) on a.RegNo = b.RegNo
                 inner join Tbt_PubPatientInfo c (nolock) on a.PatNo = c.PatNo
        where c.PatNo in
        <foreach collection="patIds" index="index" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
        and a.Status = 0
        <if test="startTime != null  ">
            and a.RegistDate &gt;= #{startTime}
        </if>
        <if test="endTime != null  ">
            and a.RegistDate &lt;= #{endTime}
        </if>
    </select>

    <select id="getRegTvRegisterList"
            resultType="com.rjsoft.outPatient.domain.reception.dto.ReceptionRegisterDto">
        select top 1 RegistTime as creDate
        from Reg_Tv_RegisterList (nolock)
        where RegNo = #{regNo}
    </select>
    <select id="selectInvalidRegNos" resultType="java.lang.Long">
        select RegNo from Reg_Tv_RegisterList (nolock) where Status=80 and RegNo in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryDiagnoses" resultType="com.rjsoft.outPatient.domain.diagnose.dto.GeneralDiagnoseDto">
        select jzlsh reception, cjrq createDate, case isnull(fhbz, 0) when -1 then 0 else isnull(fhbz, 0) end isReview
        from Tbv_HistoryDiagnose
        where sfzh = #{certificateNo}
        order by fhbz desc, cjrq desc
    </select>
    <select id="getPatBlInfoByRegNos" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto">
        select a.OutPatientNo hisCardNo,a.PatID patId, a.PatName patName, a.DeptID deptId,
        a.VisitFlag visitFlag,b.Sex sexCode,
        cast(a.RegNo as varchar(50)) regNo,b.CertificateNo certificateNo
        from Reg_Tv_RegisterList a (nolock)
        inner join Reg_Tb_PatientList b on a.PatID = b.PatID
        where RegNo in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="hisCardNo != '' and hisCardNo != null">
            and a.OutPatientNo=#{hisCardNo}
        </if>
        <if test="patName != '' and patName != null ">
            and a.PatName = #{patName}
        </if>
        and a.Status = 0
        and a.IsDelete = 0;
    </select>
    <select id="getNoFilledPatBlInfo" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto">
        select CONVERT(varchar (50), a.jzlsh) receptionNo,
        cast(a.ghlsh as varchar(50)) regNo,
        rtrim(b.blkh) hisCardNo,
        rtrim(b.hzbh) patId,
        rtrim(b.xm) patName,
        a.sckzrq receptionDate,
        e.DeptId deptId,
        d.VisiteFlag visitFlag,
        b.sfzh certificateNo,
        b.xb sexCode
        from MZYS.dbo.MZYS_TB_KZJL a (nolock)
        left join MZYS.dbo.GHSF_TB_HZJBXX b (nolock) on a.hzbh = b.hzbh
        inner join zxhis.dbo.GUID_GhRegList c (nolock) on a.ghlsh = c.GuidReg
        inner join zxhis.dbo.Tbt_GhRegList_time d (nolock) on c.RegNo = d.RegNo
        inner join zxhis.dbo.Tbt_GhRegDetl_Time e (nolock) on c.RegNo = e.RegNo and d.Status = e.Status
        where a.ysbm = #{doctorId}
        and convert(varchar (10), a.sckzrq, 120) between #{startTime} and #{endTime}
        and isnull(a.sfxyxbl,1) = 1
        <if test="hisCardNo != '' and hisCardNo != null">
            and b.blkh = #{hisCardNo}
        </if>
        <if test="patName != '' and patName != null">
            and b.xm = #{patName}
        </if>
        and d.Status = 0
        and a.jzlsh not in (select a.jzlsh
        from MZYS.dbo.MZYS_TB_DZBL a
        inner join MZYS.dbo.MZYS_TB_KZJL b on a.jzlsh = b.jzlsh
        where convert(varchar (10), b.sckzrq, 120) between #{startTime} and #{endTime}
        and a.ysbm = #{doctorId})
    </select>
    <select id="getNoCommittedPatBlInfo" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto">
        select Convert(varchar (50), a.jzlsh) receptionNo,
        rtrim(f.blkh) hisCardNo,
        cast(b.ghlsh as varchar(50)) regNo,
        rtrim(f.xm) patName,
        rtrim(f.hzbh) patId,
        b.sckzrq receptionDate,
        e.DeptId deptId,
        d.VisiteFlag visitFlag,
        f.sfzh certificateNo,
        f.xb sexCode
        from MZYS_TB_DZBL a (nolock)
        inner join MZYS_TB_KZJL b (nolock) on a.jzlsh = b.jzlsh
        inner join zxhis.dbo.GUID_GhRegList c (nolock) on b.ghlsh = c.GuidReg
        inner join zxhis.dbo.Tbt_GhRegList_time d (nolock) on c.RegNo = d.RegNo
        inner join zxhis.dbo.Tbt_GhRegDetl_Time e (nolock) on c.RegNo = e.RegNo and d.Status = e.Status
        left join GHSF_TB_HZJBXX f on a.hzbh = f.hzbh
        where a.ysbm = #{doctorId}
        <if test="hisCardNo != '' and hisCardNo != null">
            and f.blkh = #{hisCardNo}
        </if>
        <if test="patName != '' and patName != null">
            and f.xm = #{patName}
        </if>
        and d.Status = 0
        and convert(varchar (10), b.sckzrq, 120) between #{startTime} and #{endTime}
        and tjbj = 0
    </select>
    <select id="getNoSignedPatBlInfo" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto">
        select cast(c.jzlsh as varchar(50)) receptionNo,
        rtrim(f.blkh) hisCardNo,
        cast(c.ghlsh as varchar(50)) regNo,
        rtrim(f.xm) patName,
        rtrim(f.hzbh) patId,
        c.sckzrq receptionDate,
        e.DeptId deptId,
        d.VisiteFlag visitFlag,
        f.sfzh certificateNo,
        f.xb sexCode
        from MZYS_TB_CAResult a (nolock)
        inner join zxhis.dbo.GUID_GhRegList b (nolock) on a.RegNo = CAST(b.GuidReg AS VARCHAR ( 100 ))
        inner join MZYS_TB_KZJL c (nolock) on c.ghlsh = b.GuidReg
        inner join zxhis.dbo.Tbt_GhRegList_time d (nolock) on a.RegNo = d.RegNo
        inner join zxhis.dbo.Tbt_GhRegDetl_Time e (nolock) on a.RegNo = e.RegNo and d.Status = e.Status
        left join GHSF_TB_HZJBXX f (nolock) on c.hzbh = f.hzbh
        where a.WorkerId = #{doctorId}
        and d.Status = 0
        and (a.Type = 1 or a.Type = 2)
        and convert(varchar (10), c.sckzrq, 120) between #{startTime} and #{endTime}
        <if test="hisCardNo != '' and hisCardNo != null">
            and f.blkh = #{hisCardNo}
        </if>
        <if test="patName != '' and patName != null">
            and f.xm = #{patName}
        </if>
    </select>

    <update id="updateOldRegisterList">
        update Tbt_GhRegDetl_Time
        set DoctorId = #{doctorId},
            FzFlag   = #{fzFlag}
        where RegNo = #{regNo}
    </update>

    <select id="getRegisterInfoByRegNo"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        select top 1 a.*, isnull(datediff(year, P.Birthday, getdate()), '') patAge,
               dbo.funCalcAge(P.Birthday) carryUnitAge,
               p.Sex                      patSex,
               p.CertificateNo            certificateNo,
               pd.PatPhone                patPhone,
               rtrim(zg.Name)             doctorName
        from Reg_Tb_RegisterList_Time a (nolock)
                 inner join HISDB..Reg_Tb_PatientList p (nolock) on a.PatID = p.PatID
                 inner join hisdb..Reg_Tb_PatientDetl pd (nolock) on a.PatID = pd.PatID
                 left join HISDB..Reg_Tv_ConsultationList sq (nolock) on a.RegNo = sq.regno and a.HospitalCode = sq.yybm
                 left join HISDB..System_Tb_Worker zg on a.DoctorID = zg.WorkerId and a.HospitalCode = zg.HospitalId
        where a.RegNo = #{regNo}
          and a.HospitalCode = #{hospitalCode}
          and a.Status = 0
          and a.IsDelete = 0
    </select>

    <select id="getAllRegisterInfoByRegNo"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        SELECT
            a.*,
            isnull( datediff( YEAR, P.Birthday, getdate( ) ), '' ) patAge,
            P.Birthday birthday,
            p.Sex patSex,
            p.CertificateNo certificateNo,
            pd.PatPhone patPhone,
            rtrim( zg.Name ) doctorName,
            CASE

                WHEN a.RegistType = 1 THEN
                    '急诊|' ELSE ''
                END + rtrim( dept.DeptName ) deptName,
            CASE

                WHEN a.VisitFlag >
                     2 THEN
                    CASE

                        WHEN b.InvoicePrefix = 'xszf'
                            OR b.InvoicePrefix = 'yczy' THEN
                            '线上初诊' ELSE '初诊'
                        END ELSE
                    CASE

                        WHEN b.InvoicePrefix = 'xszf'
                            OR b.InvoicePrefix = 'yczy' THEN
                            '线上复诊' ELSE '复诊'
                        END
                END visitFlagName
        FROM
            Reg_Tv_RegisterList a (nolock)
                INNER JOIN Reg_Tv_OutpatientInvoice b (nolock) ON a.RegNo = b.RegNo
                AND a.HospitalCode = b.HospitalCode
                INNER JOIN Reg_Tb_PatientList p ON a.PatID = p.PatID
                INNER JOIN Reg_Tb_PatientDetl pd ON a.PatID = pd.PatID
                LEFT JOIN Reg_Tv_ConsultationList sq ON a.RegNo = sq.regno
                AND a.HospitalCode = sq.yybm
                LEFT JOIN System_Tb_Worker zg ON a.DoctorID = zg.WorkerId
                AND a.HospitalCode = zg.HospitalId
                LEFT JOIN System_Tb_Department dept ON a.DeptID = dept.DeptId
                AND a.HospitalCode = dept.HospitalId
        WHERE a.HospitalCode = #{hospitalCode}
          AND a.regNo = #{regNo}
          AND a.Status = 0
          AND a.IsDelete = 0
          AND b.Flag = 1
    </select>

    <select id="getPageRegisterListByPatid"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        select a.*,
               isnull(datediff(year, P.Birthday, getdate()), '')                        patAge,
               dbo.funCalcAge(P.Birthday)                                               carryUnitAge,
               p.Sex                                                                    patSex,
               p.CertificateNo                                                          certificateNo,
               pd.PatPhone                                                              patPhone,
               rtrim(zg.Name)                                                           doctorName,
               case when a.RegistType = 1 then '急诊|' else '' end + rtrim(dept.DeptName) deptName,
               case
                   when a.VisitFlag &lt; 2 then
                       case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上初诊' else '初诊' end
                   else
                       case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上复诊' else '复诊' end
                   end                                                                  visitFlagName
        from Reg_Tb_RegisterList_Time a (nolock)
                 inner join Reg_Tb_OutpatientInvoice_Time b (nolock) on a.RegNo = b.RegNo and a.HospitalCode = b.HospitalCode
                 inner join Reg_Tb_PatientList p (nolock) on a.PatID = p.PatID
                 inner join Reg_Tb_PatientDetl pd (nolock) on a.PatID = pd.PatID
                 left join Reg_Tv_ConsultationList sq (nolock) on a.RegNo = sq.regno and a.HospitalCode = sq.yybm
                 left join System_Tb_Worker zg on a.DoctorID = zg.WorkerId and a.HospitalCode = zg.HospitalId
                 left join System_Tb_Department dept on a.DeptID = dept.DeptId and a.HospitalCode = dept.HospitalId
        where a.RegistTime between #{startTime} and #{endTime}
          and a.HospitalCode = #{hospitalCode}
          and a.PatID = #{patId}
          and a.Status = 0
          and a.IsDelete = 0
          and b.Flag = 1
        order by DeptID ASC
    </select>


    <select id="getRegisterListFromOldData"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        select DISTINCT a.RegNo regNo,
        a.CardNo cardNo,
        a.HisCardNo outPatientNo,
        a.Status status,
        a.RegistDate registTime,
        b.GuidReg guidRegNo
        from Tbt_GhRegList a (nolock)
        inner join GUID_GhRegList b (nolock) on a.RegNo = b.RegNo
        inner join Tbt_PubPatientInfo c (nolock) on a.PatNo = c.PatNo
        where c.PatSfz = #{certificateNo}
        and a.Status = 0
        <if test="startTime != null  ">
            and a.RegistDate &gt;= #{startTime}
        </if>
        <if test="endTime != null  ">
            and a.RegistDate &lt;= #{endTime}
        </if>
    </select>

    <select id="getRegisterListOldDataByPatNo"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        select DISTINCT a.RegNo regNo,
        a.CardNo cardNo,
        a.HisCardNo outPatientNo,
        a.Status status,
        a.RegistDate registTime,
        b.GuidReg guidRegNo
        from Tbt_GhRegList a  (nolock)
        inner join GUID_GhRegList b  (nolock) on a.RegNo = b.RegNo
        inner join Tbt_PubPatientInfo c (nolock) on a.PatNo = c.PatNo
        where c.PatId = #{patNo}
        and a.Status = 0
        <if test="startTime != null  ">
            and a.RegistDate &gt;= #{startTime}
        </if>
        <if test="endTime != null  ">
            and a.RegistDate &lt;= #{endTime}
        </if>
    </select>

    <update id="updateRegistOrderByRegNo" >
        UPDATE Reg_Tb_RegisterList_Time SET RegistOrder = #{registOrder}, VipFlag = 1 WHERE RegNo = #{regNo}  AND HospitalCode = #{hospitalCode}
    </update>

    <update id="updateRegistVipFlag" >
        UPDATE Reg_Tb_RegisterList_Time SET VipFlag = 1 WHERE RegNo = #{regNo}  AND HospitalCode = #{hospitalCode}
    </update>

    <select id="verifyIsJMYB" resultType="java.lang.Integer">
        select distinct case when b.Insutype != #{insuType} then
                                 case when b.PsnInsuStas = 1 then 0 else 2 end
                             else 1
                             end
        from Reg_Tv_RegisterList a (nolock)
                 inner join Reg_GjYiBao_PatientAccount b (nolock) on a.PatId = b.PatID and a.CardNo = b.CardNo
        where a.RegNo = #{regNo}
          and a.HospitalCode = #{hospitalCode};
    </select>

    <select id="getRegisterListPageByDoctor"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        select *
        from
        (
            select ROW_NUMBER() over (order by ${sortField}) RowNumber,* from (
            select
            a.RegNo regNo,
            a.ReturnRegNo returnRegNo,
            a.PatID patID,
            a.NewPatID newPatID,
            a.CardNo cardNo,
            a.OutPatientNo outPatientNo,
            a.HospNo hospNo,
            a.PatName patName,
            a.CourseID courseID,
            a.CourseName courseName,
            a.DeptID deptID,
            a.DoctorID doctorID,
            a.DoctorLevel doctorLevel,
            a.ChargeType chargeType,
            --isnull(dbo.funCalcChargeType(a.ChargeType,b.AccountFlag),e.ChargeTypeName)  chargeTypeName,
            CASE
                WHEN a.ChargeType = 30 AND b.AccountFlag IS NOT NULL AND b.AccountFlag != '' THEN
                    CASE
                        WHEN SUBSTRING(b.AccountFlag, 5, 1) = '1' THEN '账户封存'
                        WHEN SUBSTRING(b.AccountFlag, 12, 1) IN ('B', 'C', 'D', 'E', 'F') THEN '居民医保'
                        WHEN SUBSTRING(b.AccountFlag, 12, 1) = 'G' THEN '医疗互助帮困'
                        WHEN SUBSTRING(b.AccountFlag, 12, 1) = 'H' THEN '新农合'
                        ELSE
                            CASE
                                WHEN SUBSTRING(b.AccountFlag, 1, 1) = '1' THEN '医保在职'
                                ELSE '医保退休'
                            END
                    END
                ELSE e.ChargeTypeName
            END AS chargeTypeName,
            a.BlanceWay blanceWay,
            a.RegistType registType,
            a.AppointmentWay appointmentWay,
            rtrim(a.AppointmentNo) appointmentNo,
            case when rsg.RegistOrder is not null then rsg.RegistOrder ELSE a.RegistOrder end RegistOrder,
            a.registMode RegistMode,
            a.VisitTime visitTime,
            a.Status status,
            a.FzFlag fzFlag,
            a.RegistTime registTime,
            a.ComputerNo computerNo,
            a.OpCode opCode,
            a.CreateTime createTime,
            a.CureCode cureCode,
            a.VisitFlag visitFlag,
            a.ReferralFlag referralFlag,
            a.DeptKind deptKind,
            a.HospitalCode hospitalCode,
            a.UnitNo unitNo,
            a.VipFlag vipFlag,
            a.GhDoctor ghDoctor,
            isnull(datediff(year, P.Birthday, getdate()), '') patAge,
            P.Birthday birthday,
            p.Sex patSex,
            p.CertificateNo certificateNo,
            pd.PatPhone patPhone,
            rtrim(zg.Name) doctorName,
            case when a.RegistType = 1 then '急诊|' else '' end + rtrim(dept.DeptName) deptName,
            case
            when a.VisitFlag &lt; 2 then
            case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上初诊' else '初诊' end
            else
            case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上复诊' else '复诊' end
            end visitFlagName,
            rcg.call_reg_no callRegNo
            from Reg_Tb_RegisterList_Time a(nolock)
            inner join Reg_Tb_OutpatientInvoice_Time b(nolock) on a.RegNo = b.RegNo and a.HospitalCode = b.HospitalCode
            inner join Reg_Tb_PatientList p(nolock) on a.PatID = p.PatID
            inner join Reg_Tb_PatientDetl pd(nolock) on a.PatID = pd.PatID
            left join Reg_Tv_ConsultationList sq on a.RegNo = sq.regno and a.HospitalCode = sq.yybm
            left join System_Tb_Worker zg(nolock) on a.DoctorID = zg.WorkerId and a.HospitalCode = zg.HospitalId
            left join System_Tb_Department dept(nolock) on a.DeptID = dept.DeptId and a.HospitalCode = dept.HospitalId
            left join Reg_Tb_SpecialSourceRelation rs(nolock) on a.RegNo = rs.DispensingRegNo and a.HospitalCode = rs.HospitalCode
            left join Reg_Tb_RegisterList_Time rsg(nolock) on rs.SpecialRegNo = rsg.RegNo and rsg.HospitalCode = rs.HospitalCode
        left join System_TB_ChargeType e (nolock) on e.ChargeTypeCode=a.ChargeType and a.HospitalCode=e.HospitalCode
        left join Reg_Tb_CallRegInfo rcg(nolock) on a.RegNo = rcg.reg_no and a.HospitalCode = rcg.hospital_id
        where a.RegistTime between #{startTime} and #{endTime}
            and a.HospitalCode = #{hospitalCode}
            <if test="cardNo != null and cardNo != ''">
                and (
                a.CardNo = #{cardNo}
                or a.OutPatientNo = #{cardNo}
                )
            </if>
            <if test="selectRange == 0">
                and (a.DeptID = #{deptId} or sq.hzks = #{deptId})
            </if>
            <if test="selectRange == 1">
                and (a.DoctorID = #{workerId} or sq.hzys = #{workerId})
            </if>
            <if test="fzFlag == 0">
                and isnull(a.FzFlag, 0) in (0, 1)
            </if>
            <if test="fzFlag == 1">
                and isnull(a.FzFlag, 0) not in (0, 1)
            </if>
            <if test="list.size > 0">
                and (a.deptId not in
                <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            and a.Status = 0
            and a.IsDelete = 0
            and b.Flag = 1
            ) a
        ) t
        where RowNumber between #{paperSize} * (#{paperNum}-1)+1 and #{paperNum} * #{paperSize}
    </select>

    <select id="getRegisterListByDoctor"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        select distinct
        a.RegNo regNo,
        a.ReturnRegNo returnRegNo,
        a.PatID patID,
        a.NewPatID newPatID,
        a.CardNo cardNo,
        a.OutPatientNo outPatientNo,
        a.HospNo hospNo,
        a.PatName patName,
        a.CourseID courseID,
        a.CourseName courseName,
        a.DeptID deptID,
        a.DoctorID doctorID,
        a.DoctorLevel doctorLevel,
        a.ChargeType chargeType,
        a.BlanceWay blanceWay,
        a.RegistType registType,
        a.AppointmentWay appointmentWay,
        rtrim(a.AppointmentNo) appointmentNo,
        case when rsg.RegistOrder is not null then rsg.RegistOrder ELSE a.RegistOrder end RegistOrder,
        a.registMode RegistMode,
        a.VisitTime visitTime,
        a.Status status,
        a.FzFlag fzFlag,
        a.RegistTime registTime,
        a.ComputerNo computerNo,
        a.OpCode opCode,
        a.CreateTime createTime,
        a.CureCode cureCode,
        a.VisitFlag visitFlag,
        a.ReferralFlag referralFlag,
        a.DeptKind deptKind,
        a.HospitalCode hospitalCode,
        a.UnitNo unitNo,
        a.VipFlag vipFlag,
        a.GhDoctor ghDoctor,
        isnull(datediff(year, P.Birthday, getdate()), '') patAge,
        P.Birthday birthday,
        p.Sex patSex,
        p.CertificateNo certificateNo,
        pd.PatPhone patPhone,
        rtrim(zg.Name) doctorName,
        case when a.RegistType = 1 then '急诊|' else '' end + rtrim(dept.DeptName) deptName,
        case
        when a.VisitFlag &lt; 2 then
        case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上初诊' else '初诊' end
        else
        case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上复诊' else '复诊' end
        end visitFlagName
        from Reg_Tb_RegisterList_Time a(nolock)
        inner join Reg_Tb_OutpatientInvoice_Time b(nolock) on a.RegNo = b.RegNo and a.HospitalCode = b.HospitalCode
        inner join Reg_Tb_PatientList p(nolock) on a.PatID = p.PatID
        inner join Reg_Tb_PatientDetl pd(nolock) on a.PatID = pd.PatID
        left join Reg_Tv_ConsultationList sq on a.RegNo = sq.regno and a.HospitalCode = sq.yybm
        left join System_Tb_Worker zg(nolock) on a.DoctorID = zg.WorkerId and a.HospitalCode = zg.HospitalId
        left join System_Tb_Department dept(nolock) on a.DeptID = dept.DeptId and a.HospitalCode = dept.HospitalId
        left join Reg_Tb_SpecialSourceRelation rs(nolock) on a.RegNo = rs.DispensingRegNo and a.HospitalCode = rs.HospitalCode
        left join Reg_Tb_RegisterList_Time rsg(nolock) on rs.SpecialRegNo = rsg.RegNo and rsg.HospitalCode = rs.HospitalCode
        where a.HospitalCode = #{hospitalCode}
        and a.regno = #{regNo}
        <if test="list.size > 0">
            and (a.deptId not in
            <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        and a.Status = 0
        and a.IsDelete = 0
        and b.Flag = 1
    </select>

    <select id="getRegisterAllListPageByDoctor"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        select *
        from
        (
            select ROW_NUMBER() over (order by ${sortField}) RowNumber,* from (
            select a.RegNo regNo,
            a.ReturnRegNo returnRegNo,
            a.PatID patID,
            a.NewPatID newPatID,
            a.CardNo cardNo,
            a.OutPatientNo outPatientNo,
            a.HospNo hospNo,
            a.PatName patName,
            a.CourseID courseID,
            a.CourseName courseName,
            a.DeptID deptID,
            a.DoctorID doctorID,
            a.DoctorLevel doctorLevel,
            a.ChargeType chargeType,
            --isnull(dbo.funCalcChargeType(a.ChargeType,b.AccountFlag),e.ChargeTypeName)  chargeTypeName,
            CASE
                WHEN a.ChargeType = 30 AND b.AccountFlag IS NOT NULL AND b.AccountFlag != '' THEN
                    CASE
                        WHEN SUBSTRING(b.AccountFlag, 5, 1) = '1' THEN '账户封存'
                        WHEN SUBSTRING(b.AccountFlag, 12, 1) IN ('B', 'C', 'D', 'E', 'F') THEN '居民医保'
                        WHEN SUBSTRING(b.AccountFlag, 12, 1) = 'G' THEN '医疗互助帮困'
                        WHEN SUBSTRING(b.AccountFlag, 12, 1) = 'H' THEN '新农合'
                        ELSE
                            CASE
                                WHEN SUBSTRING(b.AccountFlag, 1, 1) = '1' THEN '医保在职'
                                ELSE '医保退休'
                            END
                    END
                ELSE e.ChargeTypeName
            END AS chargeTypeName,
            a.BlanceWay blanceWay,
            a.RegistType registType,
            a.AppointmentWay appointmentWay,
            rtrim(a.AppointmentNo) appointmentNo,
            case when rsg.RegistOrder is not null then rsg.RegistOrder ELSE a.RegistOrder end RegistOrder,
            a.registMode RegistMode,
            a.VisitTime visitTime,
            a.Status status,
            a.FzFlag fzFlag,
            a.RegistTime registTime,
            a.ComputerNo computerNo,
            a.OpCode opCode,
            a.CreateTime createTime,
            a.CureCode cureCode,
            a.VisitFlag visitFlag,
            a.ReferralFlag referralFlag,
            a.DeptKind deptKind,
            a.HospitalCode hospitalCode,
            a.UnitNo unitNo,
            a.GhDoctor ghDoctor,
            isnull(datediff(year, P.Birthday, getdate()), '') patAge,
            P.Birthday birthday,
            p.Sex patSex,
            p.CertificateNo certificateNo,
            pd.PatPhone patPhone,
            rtrim(zg.Name) doctorName,
            case when a.RegistType = 1 then '急诊|' else '' end + rtrim(dept.DeptName) deptName,
            case
            when a.VisitFlag &lt; 2 then
            case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上初诊' else '初诊' end
            else
            case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上复诊' else '复诊' end
            end visitFlagName,
            rcg.call_reg_no callRegNo
            from Reg_Tv_RegisterList a(nolock)
            inner join Reg_Tv_OutpatientInvoice b(nolock) on a.RegNo = b.RegNo and a.HospitalCode = b.HospitalCode
            inner join Reg_Tb_PatientList p(nolock) on a.PatID = p.PatID
            inner join Reg_Tb_PatientDetl pd(nolock) on a.PatID = pd.PatID
            left join Reg_Tv_ConsultationList sq on a.RegNo = sq.regno and a.HospitalCode = sq.yybm
            left join System_Tb_Worker zg(nolock) on a.DoctorID = zg.WorkerId and a.HospitalCode = zg.HospitalId
            left join System_Tb_Department dept(nolock) on a.DeptID = dept.DeptId and a.HospitalCode = dept.HospitalId
            LEFT JOIN Reg_Tb_SpecialSourceRelation rs(nolock) ON a.RegNo = rs.DispensingRegNo
            AND a.HospitalCode = rs.HospitalCode
            LEFT JOIN Reg_Tv_RegisterList rsg ON rs.SpecialRegNo = rsg.RegNo
            AND rsg.HospitalCode = rs.HospitalCode
        left join System_TB_ChargeType e (nolock) on e.ChargeTypeCode=a.ChargeType and a.HospitalCode=e.HospitalCode
        left join Reg_Tb_CallRegInfo rcg(nolock) on a.RegNo = rcg.reg_no and a.HospitalCode = rcg.hospital_id
        where a.RegistTime between #{startTime} and #{endTime}
            and a.HospitalCode = #{hospitalCode}
            <if test="cardNo != null and cardNo != ''">
                and (
                a.CardNo = #{cardNo}
                or a.OutPatientNo = #{cardNo}
                )
            </if>
            <if test="selectRange == 0">
                and (a.DeptID = #{deptId} or sq.hzks = #{deptId})
            </if>
            <if test="selectRange == 1">
                and (a.DoctorID = #{workerId} or sq.hzys = #{workerId})
            </if>
            <if test="fzFlag == 0">
                and isnull(a.FzFlag, 0) in (0, 1)
            </if>
            <if test="fzFlag == 1">
                and isnull(a.FzFlag, 0) not in (0, 1)
            </if>
            <if test="list.size > 0">and (a.deptId not in
                <foreach
                        collection="list" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                and a.Status = 0
                and a.IsDelete = 0
                and b.Flag = 1
                ) a
        ) t
        where RowNumber between #{paperSize} * (#{paperNum}-1)+1 and #{paperNum} * #{paperSize}
    </select>

    <select id="getRegisterAllListByDoctor"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        select distinct a.RegNo regNo,
        a.ReturnRegNo returnRegNo,
        a.PatID patID,
        a.NewPatID newPatID,
        a.CardNo cardNo,
        a.OutPatientNo outPatientNo,
        a.HospNo hospNo,
        a.PatName patName,
        a.CourseID courseID,
        a.CourseName courseName,
        a.DeptID deptID,
        a.DoctorID doctorID,
        a.DoctorLevel doctorLevel,
        a.ChargeType chargeType,
        a.BlanceWay blanceWay,
        a.RegistType registType,
        a.AppointmentWay appointmentWay,
        rtrim(a.AppointmentNo) appointmentNo,
        case when rsg.RegistOrder is not null then rsg.RegistOrder ELSE a.RegistOrder end RegistOrder,
        a.registMode RegistMode,
        a.VisitTime visitTime,
        a.Status status,
        a.FzFlag fzFlag,
        a.RegistTime registTime,
        a.ComputerNo computerNo,
        a.OpCode opCode,
        a.CreateTime createTime,
        a.CureCode cureCode,
        a.VisitFlag visitFlag,
        a.ReferralFlag referralFlag,
        a.DeptKind deptKind,
        a.HospitalCode hospitalCode,
        a.UnitNo unitNo,
        isnull(datediff(year, P.Birthday, getdate()), '') patAge,
        P.Birthday birthday,
        p.Sex patSex,
        p.CertificateNo certificateNo,
        pd.PatPhone patPhone,
        rtrim(zg.Name) doctorName,
        case when a.RegistType = 1 then '急诊|' else '' end + rtrim(dept.DeptName) deptName,
        case
        when a.VisitFlag &lt; 2 then
        case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上初诊' else '初诊' end
        else
        case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上复诊' else '复诊' end
        end visitFlagName
        from Reg_Tv_RegisterList a (nolock)
        inner join Reg_Tv_OutpatientInvoice b(nolock) on a.RegNo = b.RegNo and a.HospitalCode = b.HospitalCode
        inner join Reg_Tb_PatientList p(nolock) on a.PatID = p.PatID
        inner join Reg_Tb_PatientDetl pd(nolock) on a.PatID = pd.PatID
        left join Reg_Tv_ConsultationList sq (nolock) on a.RegNo = sq.regno and a.HospitalCode = sq.yybm
        left join System_Tb_Worker zg(nolock) on a.DoctorID = zg.WorkerId and a.HospitalCode = zg.HospitalId
        left join System_Tb_Department dept(nolock) on a.DeptID = dept.DeptId and a.HospitalCode = dept.HospitalId
        LEFT JOIN Reg_Tb_SpecialSourceRelation rs(nolock) ON a.RegNo = rs.DispensingRegNo
        AND a.HospitalCode = rs.HospitalCode
        LEFT JOIN Reg_Tv_RegisterList rsg ON rs.SpecialRegNo = rsg.RegNo
        AND rsg.HospitalCode = rs.HospitalCode
        where a.HospitalCode = #{hospitalCode}
        and a.regno = #{regNo}
        <if test="list.size > 0">and (a.deptId not in
            <foreach
                    collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        and a.Status = 0
        and a.IsDelete = 0
        and b.Flag = 1
    </select>

    <select id="getByOutPatientNo" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        select top 100 * from Reg_Tv_RegisterList (nolock)
                         where deptid = #{deptId} and HospitalCode = #{hospitalCode} and DoctorID = #{doctorId} and OutPatientNo = #{outPatientNo} and IsDelete = 0
    </select>

    <select id="listRegisterByRegNo" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        select
        a.RegNo regNo,
        a.ReturnRegNo returnRegNo,
        a.PatID patID,
        a.NewPatID newPatID,
        a.CardNo cardNo,
        a.OutPatientNo outPatientNo,
        a.HospNo hospNo,
        a.PatName patName,
        a.CourseID courseID,
        a.CourseName courseName,
        a.DeptID deptID,
        a.DoctorID doctorID,
        a.DoctorLevel doctorLevel,
        a.ChargeType chargeType,
        isnull(dbo.funCalcChargeType(a.ChargeType,b.AccountFlag),e.ChargeTypeName)  chargeTypeName,
        a.BlanceWay blanceWay,
        a.RegistType registType,
        a.AppointmentWay appointmentWay,
        rtrim(a.AppointmentNo) appointmentNo,
        case when rsg.RegistOrder is not null then rsg.RegistOrder ELSE a.RegistOrder end RegistOrder,
        a.registMode RegistMode,
        a.VisitTime visitTime,
        a.Status status,
        a.FzFlag fzFlag,
        a.RegistTime registTime,
        a.ComputerNo computerNo,
        a.OpCode opCode,
        a.CreateTime createTime,
        a.CureCode cureCode,
        a.VisitFlag visitFlag,
        a.ReferralFlag referralFlag,
        a.DeptKind deptKind,
        a.HospitalCode hospitalCode,
        a.UnitNo unitNo,
        a.VipFlag vipFlag,
        a.GhDoctor ghDoctor,
        isnull(datediff(year, P.Birthday, getdate()), '') patAge,
        P.Birthday birthday,
        p.Sex patSex,
        p.CertificateNo certificateNo,
        pd.PatPhone patPhone,
        rtrim(zg.Name) doctorName,
        case when a.RegistType = 1 then '急诊|' else '' end + rtrim(dept.DeptName) deptName,
        case
        when a.VisitFlag &lt; 2 then
        case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上初诊' else '初诊' end
        else
        case when b.InvoicePrefix = 'xszf' or b.InvoicePrefix = 'yczy' then '线上复诊' else '复诊' end
        end visitFlagName
        from Reg_Tb_RegisterList_Time a(nolock)
        inner join Reg_Tb_OutpatientInvoice_Time b(nolock) on a.RegNo = b.RegNo and a.HospitalCode = b.HospitalCode
        inner join Reg_Tb_PatientList p(nolock) on a.PatID = p.PatID
        inner join Reg_Tb_PatientDetl pd(nolock) on a.PatID = pd.PatID
        left join System_Tb_Worker zg(nolock) on a.DoctorID = zg.WorkerId and a.HospitalCode = zg.HospitalId
        left join System_Tb_Department dept(nolock) on a.DeptID = dept.DeptId and a.HospitalCode = dept.HospitalId
        left join Reg_Tb_SpecialSourceRelation rs(nolock) on a.RegNo = rs.DispensingRegNo and a.HospitalCode = rs.HospitalCode
        left join Reg_Tb_RegisterList_Time rsg(nolock) on rs.SpecialRegNo = rsg.RegNo and rsg.HospitalCode = rs.HospitalCode
        left join System_TB_ChargeType e (nolock) on e.ChargeTypeCode=a.ChargeType and a.HospitalCode=e.HospitalCode
        where  a.HospitalCode = #{hospitalCode}
        and a.regno in
        <foreach collection="regNoList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and a.Status = 0
        and a.IsDelete = 0
        and b.Flag = 1
    </select>
    <select id="queryRegisterDtoByRegNoList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
        SELECT
            RegNo AS regNo,
            ReturnRegNo AS returnRegNo,
            PatID AS patID,
            NewPatID AS newPatID,
            CardNo AS cardNo,
            OutPatientNo AS outPatientNo,
            HospNo AS hospNo,
            PatName AS patName,
            CourseID AS courseID,
            CourseName AS courseName,
            DeptID AS deptID,
            DoctorID AS doctorID,
            DoctorLevel AS doctorLevel,
            ChargeType AS chargeType,
            BlanceWay AS blanceWay,
            RegistType AS registType,
            AppointmentWay AS appointmentWay,
            AppointmentNo AS appointmentNo,
            RegistOrder AS registOrder,
            registMode AS RegistMode,
            VisitTime AS visitTime,
            Status AS status,
            FzFlag AS fzFlag,
            RegistTime AS registTime,
            ComputerNo AS computerNo,
            OpCode AS opCode,
            CreateTime AS createTime,
            CureCode AS cureCode,
            VisitFlag AS visitFlag,
            ReferralFlag AS referralFlag,
            DeptKind AS deptKind,
            HospitalCode AS hospitalCode,
            UnitNo AS unitNo,
            GhDoctor AS ghDoctor
        FROM Reg_Tv_RegisterList (nolock)
        where HospitalCode = #{hospitalCode} and IsDelete=0
        and RegNo in
        <foreach collection="regNoList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>