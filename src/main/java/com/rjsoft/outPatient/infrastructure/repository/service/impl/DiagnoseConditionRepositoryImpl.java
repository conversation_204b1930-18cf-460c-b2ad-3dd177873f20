package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.DiagnoseCondition;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DiagnoseConditionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DiagnoseConditionRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/2 - 15:12
 */
@Service
@AllArgsConstructor
public class DiagnoseConditionRepositoryImpl implements DiagnoseConditionRepository {

    private final DiagnoseConditionMapper diagnoseConditionMapper;

    @Override
    @DatabaseAnnotation
    public boolean isSevereByDiagnoses(List<String> diagnoseCode, Integer hospitalCode) {
        final Weekend<DiagnoseCondition> weekend = new Weekend<>(DiagnoseCondition.class);
        weekend.weekendCriteria()
                .andIn(DiagnoseCondition::getDiagnoseCode, diagnoseCode)
                .andEqualTo(DiagnoseCondition::getCondition, 1)
                .andEqualTo(DiagnoseCondition::getHospitalCode, hospitalCode);
        return diagnoseConditionMapper.selectCountByExample(weekend) > 1;
    }

    @Override
    public boolean notSevereByDiagnoses(List<String> diagnoseCode, Integer hospitalCode) {
        return !isSevereByDiagnoses(diagnoseCode, hospitalCode);
    }
}
