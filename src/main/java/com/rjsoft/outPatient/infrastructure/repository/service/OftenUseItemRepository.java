package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.OftenUseItem;
import com.rjsoft.outPatient.infrastructure.repository.entity.OftenUseItemUsage;

import java.util.List;

/**
 * 常用项目
 *
 * <AUTHOR>
public interface OftenUseItemRepository {


    /**
     * 根据项目ID获取常用项目
     *
     * @param itemCode
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    OftenUseItem getOftenUseItemByItemCode(Integer itemCode, Integer doctorId, Integer hospitalCode);

    /**
     * 根据项目编码获取常用项目用法
     *
     * @param itemCode
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    OftenUseItemUsage getOftenUseItemUsageByItemCode(Integer itemCode, Integer doctorId, Integer hospitalCode);

    /**
     * 保存常用项目
     *
     * @param oftenUseItem
     * @return
     */
    boolean saveOftenUseItem(OftenUseItem oftenUseItem);

    /**
     * 根据医生ID查询常用项目
     *
     * @param doctorId
     * @param itemCategory
     * @param hospitalCode
     * @param deptId
     * @return
     */
    List<OftenUseItem> getOftenUseItemByDoctorId(Integer doctorId, List<String> itemCategory, Integer hospitalCode, Integer deptId);

    /**
     * 根据医生ID查询常用用法
     *
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    List<OftenUseItemUsage> getOftenUseItemUsageByDoctorId(Integer doctorId, Integer hospitalCode);

    /**
     * 根据 id 删除
     *
     * @param id           主键
     * @param hospitalCode 医院编码
     */
    void deleteOftenUseItemById(Integer id, Integer hospitalCode);

    /**
     * 根据 id 删除常用用法
     *
     * @param id           主键
     * @param hospitalCode 医院编码
     */
    void deleteOftenUseItemUsageById(Integer id, Integer hospitalCode);
}
