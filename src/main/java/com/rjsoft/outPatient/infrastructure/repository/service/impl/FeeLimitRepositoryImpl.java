package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.FeeLimit;
import com.rjsoft.outPatient.infrastructure.repository.mapper.FeeLimitMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.FeeLimitRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 费用限制
 * <AUTHOR>
@Service
@AllArgsConstructor
public class FeeLimitRepositoryImpl implements FeeLimitRepository {

    FeeLimitMapper feeLimitMapper;

    /**
     * 查询费用限制
     * @param patientType
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<FeeLimit> getFeeLimitByPatType(Integer patientType, Integer hospitalCode) {
        return feeLimitMapper.getFeeLimitByPatType(patientType, hospitalCode);
    }

    /**
     * 保存费用限制
     * @param feeLimit
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean saveFeeLimit(FeeLimit feeLimit) {
        boolean success;
        if (feeLimit.getId() == null || feeLimit.getId() == 0) {
            success = feeLimitMapper.insert(feeLimit) > 0;
        } else {
            success = feeLimitMapper.updateByPrimaryKey(feeLimit) > 0;
        }
        return success;
    }

    /**
     * 删除费用限制
     * @param limitCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean delFeeLimit(Integer limitCode) {
        FeeLimit entity = new FeeLimit();
        entity.setLimitCode(limitCode);
        return feeLimitMapper.delete(entity) > 0;
    }
}
