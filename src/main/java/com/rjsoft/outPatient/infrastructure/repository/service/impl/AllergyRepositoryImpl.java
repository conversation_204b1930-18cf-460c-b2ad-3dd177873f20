package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.Allergy;
import com.rjsoft.outPatient.infrastructure.repository.entity.AllergyDic;
import com.rjsoft.outPatient.infrastructure.repository.mapper.AllergyDicMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.AllergyMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SysFunctionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.AllergyRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 过敏信息
 */
@AllArgsConstructor
@Service
public class AllergyRepositoryImpl implements AllergyRepository {

    AllergyDicMapper allergyDicMapper;
    AllergyMapper allergyMapper;
    SysFunctionMapper sysFunctionMapper;

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<AllergyDic> getAllergyType(String inputCode, Integer hospitalCode) {
        return allergyDicMapper.getAllergyType(inputCode, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<AllergyDic> getAllergyTypeByCode(Integer code, Integer hospitalCode) {
        AllergyDic allergyDic = new AllergyDic();
        allergyDic.setCode(code);
        allergyDic.setHospitalId(hospitalCode);
        return allergyDicMapper.select(allergyDic);
    }


    @Override
    @DatabaseAnnotation
    public List<Allergy> getPatAllergyInfo(Integer id, Integer patId, String hisCardNo, Integer hospitalCode) {
        return allergyMapper.getPatAllergyInfo(id, patId, hisCardNo, hospitalCode);
    }


    @Override
    @DatabaseAnnotation
    public boolean savePatAllergyInfo(Allergy allergy) {
        boolean success;
        boolean hasData = allergyMapper.existsWithPrimaryKey(allergy);
        if (hasData) {
            allergy.setUptDoctor(allergy.getDoctorId());
            allergy.setUptTime(sysFunctionMapper.getDate());
            success = allergyMapper.updateByPrimaryKeySelective(allergy) > 0;
        } else {
            allergy.setIsDelete(0);
            allergy.setCreDoctor(allergy.getDoctorId());
            allergy.setCreTime(sysFunctionMapper.getDate());
            success = allergyMapper.insert(allergy) > 0;
        }
        return success;
    }

    @Override
    @DatabaseAnnotation
    public Allergy getPatAllergyInfo(Integer allergyCode, Integer patId) {
        return allergyMapper.getPatAllergyInfo(allergyCode, patId);
    }

    @Override
    @DatabaseAnnotation
    public boolean delPatAllergyInfo(Integer id, Integer patId, Integer state, Integer doctorId, Integer hospitalCode) {
        return allergyMapper.delPatAllergyInfo(id, patId, state, doctorId, hospitalCode);
    }

    /**
     * 根据患者编号获取患者过敏信息
     *
     * @param patIds
     * @param hospitalId
     */
    @Override
    @DatabaseAnnotation
    public List<Allergy> getAllergyList(Set<Integer> patIds, Integer hospitalId) {
        return allergyMapper.getAllergyList(patIds, hospitalId);
    }

    /**
     * 获取患者过敏阳性信息
     *
     * @param patId
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public List<Allergy> getConfirmAllergies(Integer patId, Integer hospitalCode) {
        return allergyMapper.getConfirmAllergies(patId, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public List<Allergy> queryByCodesAndPatIds(List<Integer> codeList, List<Long> patIdList) {
        return allergyMapper.queryByCodesAndPatIds(codeList, patIdList);
    }

}
