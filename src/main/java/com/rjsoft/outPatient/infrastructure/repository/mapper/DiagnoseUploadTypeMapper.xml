<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DiagnoseUploadTypeMapper">

    <select id="getDiagnoseUploadType"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.DiagnoseUploadType">
        select a.*,
               b.ReportDiseaseId   reportDiseaseId,
               b.ReportDiseaseCode reportDiseaseCode,
               b.ReportDiseaseName reportDiseaseName
        from System_Tb_UploadType a
                 inner join System_Tb_ReportDisease b on a.uploadTypeId = b.uploadTypeId
                 inner join System_Tb_DiseasetodDagnosisCode c on b.ReportDiseaseId = c.ReportDiseaseId
        where c.diagnosisCode = #{diagnoseCode}
          and a.hospId = #{hospitalCode}
          and a.enanleFlag = 1
          and b.enanleFlag = 1
    </select>

    <select id="getReportUploadType"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.DiagnoseUploadType">
        select *
        from System_Tb_UploadType
        where hospId = 1
          and enanleFlag = 1
    </select>

    <select id="getReportUploadTypeDiagnose"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.DiagnoseUploadType">
        select distinct diagnosisCode, diagnosisCodeName
        from System_Tb_DiseasetodDagnosisCode
    </select>

</mapper>