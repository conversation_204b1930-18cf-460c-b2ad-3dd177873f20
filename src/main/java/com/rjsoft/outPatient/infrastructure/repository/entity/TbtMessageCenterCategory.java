package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;

@Data
@Table( name ="tbt_LK_PushMessage" )
public class TbtMessageCenterCategory {
    @Column(name = "PushType")
    private String pushType;

    @Column(name = "Code")
    private Integer code;

    @Column(name = "Name")
    private String name;

    @Column(name = "Template")
    private String template;

    @Column(name = "JsonTemplate")
    private String jsonTemplate;

    @Column(name = "WeChatTemplate")
    private String weChatTemplate;

    @Column(name = "FirstTemplate")
    private String firstTemplate;
}
