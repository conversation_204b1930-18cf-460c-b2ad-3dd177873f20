package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.dictionary.AutoTranslate;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.dto.HttpResult;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.common.utils.InsuranceRequestUtils;
import com.rjsoft.outPatient.config.HisConfig;
import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.RetPatientResult;
import com.rjsoft.outPatient.domain.disease.vo.DiseaseProofPatInVO;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo;
import com.rjsoft.outPatient.domain.diseaseReport.dto.ReportContentResult;
import com.rjsoft.outPatient.domain.patient.dto.GetPatientByRegNoDto;
import com.rjsoft.outPatient.domain.patient.dto.PatientInfoDto;
import com.rjsoft.outPatient.domain.patient.vo.RelationPatRespVO;
import com.rjsoft.outPatient.domain.recipe.dto.RecipeInfoResponse;
import com.rjsoft.outPatient.domain.recipe.dto.WeiNingDto;
import com.rjsoft.outPatient.domain.reserve.dto.ReserveRegisterResponse;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.PatientRepository;
import com.ruijing.code.util.DateUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.ArrayList;
import java.util.List;

/**
 * 患者信息
 */
@AllArgsConstructor
@Service
public class PatientResositoryImpl implements PatientRepository {

    PatientMapper patientMapper;
    PatientDetailMapper patientDetailMapper;
    PatientCardMapper patientCardMapper;
    SysFunctionMapper sysFunctionMapper;
    AgentMapper agentMapper;
    PatientListResearchMapper patientListResearchMapper;
    PatientListResearchOldMapper patientListResearchOldMapper;
    AdmissionApplicationFormDaoMapper admissionApplicationFormDaoMapper;
    OldPatientInfoMapper oldPatientInfoMapper;
    HisConfig hisConfig;
    PatIdPatNoMapper patIdPatNoMapper;
    PubPatientInfoMapper pubPatientInfoMapper;
    InsuranceRequestUtils insuranceRequestUtils;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public PatientList getPatientList(Integer patId, Integer hospitalCode) {
        return patientMapper.getPatientList(patId, hospitalCode);
    }

    /**
     * 根据身份证号获取患者信息
     *
     * @param patSfz
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<PatientList> getPatientListBySfz(String patSfz, Integer hospitalCode) {
        if (StringUtils.isEmpty(patSfz) || patSfz.trim().length() == 0) {
            return new ArrayList<>();
        }
        PatientList entity = new PatientList();
        entity.setCertificateNo(patSfz);
//        entity.setHospitalCode(hospitalCode);
        return patientMapper.select(entity);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public PatientDetail getPatientDetail(Integer patId, Integer hospitalCode) {
        return patientDetailMapper.getPatientDetail(patId, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<PatientDetail> queryPatientDetailByIds(List<Integer> patIds, Integer hospitalCode) {
        return patientDetailMapper.queryPatientDetailByIds(patIds, hospitalCode);
    }


    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<PatientCard> getPatientCard(Integer patId) {
        return patientCardMapper.getPatientDetail(patId);
    }


    @Override
    @DatabaseAnnotation(name = "HISDB")
    public boolean UpdatePatientList(PatientList patientList) {
        Integer newID = Converter.toInt32(sysFunctionMapper.getGetSequences(SequenceEnum.PATIENT_ID));
        patientList.setNewPatID(newID);
        boolean success = patientMapper.updateByPrimaryKeySelective(patientList) >= 0;
        return success;
    }


    @Override
    @DatabaseAnnotation(name = "HISDB")
    public boolean UpdatePatientDetail(PatientDetail patientDetail) {
        boolean success = patientDetailMapper.updateByPrimaryKeySelective(patientDetail) > 0;
        return success;
    }


    @Override
    @DatabaseAnnotation
    public boolean saveAgentInfo(Agent agent) {
        boolean success;
        boolean hasData = agentMapper.existsWithPrimaryKey(agent);
        if (hasData) {
            agent.setUptTime(sysFunctionMapper.getDate());
            success = agentMapper.updateByPrimaryKeySelective(agent) > 0;
        } else {
            agent.setCreTime(sysFunctionMapper.getDate());
            success = agentMapper.insert(agent) > 0;
        }
        return success;
    }

    @Override
    @DatabaseAnnotation
    public List<Agent> queryAgentInfo(Integer patId, Integer receptionNo, Integer hospCode) {
        final Agent entity = new Agent();
        entity.setPatId(patId);
        if (!receptionNo.equals(0)) {
            entity.setReceptionNo(receptionNo);
        }
        entity.setHospitalCode(String.valueOf(hospCode));
        return agentMapper.select(entity);
    }

    @Override
    @DatabaseAnnotation
    public boolean isExistsAgentInfo(Integer receptionNo, Integer hospCode) {
        Agent entity = new Agent();
        entity.setReceptionNo(receptionNo);
        entity.setHospitalCode(String.valueOf(hospCode));
        return agentMapper.selectCount(entity) > 0;
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public boolean judgeJudicialPat(Long patId, Integer hospCode) {
        return patientDetailMapper.judgeJudicialPat(Math.toIntExact(patId), hospCode) > 0;
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public ReportContentResult getPatInfo(String patId, String hospCode) {
        return patientMapper.getPatInfo(patId, hospCode);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<PatientList> getPatInfoByIdCard(String certificateNo, Integer hospCode) {
        return patientMapper.getPatInfoByIdCard(certificateNo, hospCode);
    }

    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    public int getCommonDoctorNo(Long regNo) {
        return patientMapper.getCommonDoctorNo(regNo);
    }

    @Override
    @AutoTranslate
    @DatabaseAnnotation(name = "HISDB")
    public RetPatientResult getPatient(Integer patId, Integer hospitalCode) {
        return admissionApplicationFormDaoMapper.getPatient(patId, hospitalCode);
    }

    /**
     * 查询患者挂号信息
     *
     * @param outPatientNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<RegisterListResult> listOutPatientNo(String outPatientNo, Integer hospitalCode) {
        return admissionApplicationFormDaoMapper.listOutPatientNo(outPatientNo, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public PatientListResearch getResearchPatient(Integer patId, Integer hospitalCode) {
        PatientListResearch research = new PatientListResearch();
        research.setPatId(patId);
//        research.setHospCode(hospitalCode);
        return patientListResearchMapper.selectOne(research);
    }

    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    public PatientListResearchOld getResearchOldPatient(Integer patId, Integer hospitalCode) {
        PatientListResearchOld researchOld = new PatientListResearchOld();
        researchOld.setPatId(patId);
        return patientListResearchOldMapper.selectOne(researchOld);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public GetPatientByRegNoDto getPatientByRegNo(String regNo) {
        GetPatientByRegNoDto rtnDto = patientMapper.getPatientByRegNo(regNo);
        if (rtnDto == null) {
            rtnDto = patientMapper.getAllPatientByRegNo(regNo);
        }
        return rtnDto;
    }

    /**
     * 获取患者信息
     *
     * @param regNo
     * @param hospitalCode
     * @param workerId
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public RecipeInfoResponse getPatInfoByRegNo(Long regNo, Integer hospitalCode, Integer workerId) {
        return patientMapper.getPatInfoByRegNo(regNo, hospitalCode, workerId);
    }

    /**
     * 根据挂号流水号和医院编号获取就诊类型
     *
     * @param regNo
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public Integer getRegisterType(Integer regNo, Integer hospitalCode) {
        return patientMapper.getRegisterType(regNo, hospitalCode);
    }

    /**
     * 根据患者身份证号获取患者全部信息
     *
     * @param certificateNo
     * @param hospitalId
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<PatientList> getPatientInfos(String certificateNo, Integer hospitalId) {
        return patientMapper.getPatientInfos(certificateNo, hospitalId);
    }


    /**
     * 根据patId+hospId获取patNo  hisdb..Tbt_PatID_PatNo
     *
     * @param patIdNo
     * @return
     */
    @Override
    public Integer getPatNo(String patIdNo, Integer hospitalCode) {
        if (hospitalCode == 1) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
        }
        return patientMapper.getPatNo(patIdNo);
    }

    @Override
    public PubPatientInfoOther getPubPatientInfoOther(Integer patNo, Integer hospCode) {
        if (hospCode == 1) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
        }
        return patientMapper.getPubPatientInfoOther(patNo);
    }

    /**
     * 根据患者身份证号获取患者年龄和性别
     *
     * @param certificateNo
     * @param hospitalId
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<ReserveRegisterResponse> getPatientAgeAndSex(List<String> certificateNo, Integer hospitalId) {
        return patientMapper.getPatientAgeAndSex(certificateNo, hospitalId);
    }

    /**
     * 根据卡号获取患者基本信息
     *
     * @param cardNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<PatientInfoDto> getPatientDetailByPatId(String cardNo, Integer hospitalCode, Integer page, Integer size) {

        //PageHelper.startPage(page, size, null);
        return patientCardMapper.getPatientDetailByPatId(cardNo, hospitalCode);
    }

    /**
     * 根据患者编号获取患者信息
     *
     * @param list
     * @param hospitalCode
     */
    @Override
    public List<DiagnoseRecordInfo> getDiseasePatNos(List<DiagnoseRecordInfo> list, Integer hospitalCode) {
        DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
        List<DiagnoseRecordInfo> newDiseasePatNos = patientMapper.getNewDiseasePatNos(list, hospitalCode);
        if (!hisConfig.getIsHistoryVersion().equals("true")) {
            if (hospitalCode.equals(1)) {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
            } else {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
            }
            newDiseasePatNos.addAll(patientMapper.getOldDiseasePatNos(list, hospitalCode));
        }
        return newDiseasePatNos;
    }

    /**
     * 根据患者编号列表、医院编码 获取患者基本信息
     *
     * @param patIds
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<PatientList> getPatInfoByPatIds(List<String> patIds, Integer hospitalCode) {
        return patientMapper.getPatInfoByPatIds(patIds, hospitalCode);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public List<String> getGuidPatientByCertificateNo(String certificateNo) {
        return patientMapper.getGuidPatientByCertificateNo(certificateNo);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public List<String> getGuidPatientByPatNo(Integer patNo) {
        return patientMapper.getGuidPatientByPatNo(patNo);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public PatIdPatNo getPatIDPatNo(Integer patId, Integer hospitalCode) {
        Weekend<PatIdPatNo> weekend = new Weekend<PatIdPatNo>(PatIdPatNo.class);
        weekend.weekendCriteria().andEqualTo(PatIdPatNo::getPatID, patId).andEqualTo(PatIdPatNo::getHospitalCode, hospitalCode);
        return patIdPatNoMapper.selectOneByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<PatIdPatNo> queryPatIDPatNoList(List<Integer> patIdList, Integer hospitalCode) {
        Weekend<PatIdPatNo> weekend = new Weekend<PatIdPatNo>(PatIdPatNo.class);
        weekend.weekendCriteria().andIn(PatIdPatNo::getPatID, patIdList).andEqualTo(PatIdPatNo::getHospitalCode, hospitalCode);
        return patIdPatNoMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<PatIdPatNo> getPatIDPatNoList(Integer patId, Integer hospitalCode) {
        Weekend<PatIdPatNo> weekend = new Weekend<PatIdPatNo>(PatIdPatNo.class);
        weekend.weekendCriteria().andEqualTo(PatIdPatNo::getPatID, patId).andEqualTo(PatIdPatNo::getHospitalCode, hospitalCode);
        return patIdPatNoMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public int updateByPatNo(Integer patNo, String addr) {
        return pubPatientInfoMapper.updateByPatNo(patNo, addr);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public String getByPatNo(Integer patNo) {
        return pubPatientInfoMapper.getByPatNo(patNo);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    //@Cacheable(cacheNames = "queryRelationPatientInfo", unless = "#result == null")
    public List<RelationPatRespVO> queryRelationPatientInfo(String patId) {
        return pubPatientInfoMapper.queryRelationPatientInfo(patId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Integer> queryRelationPatientEmpiId(String certificateNo, String patName, Integer patSex, Integer hospitalCode) {
        return pubPatientInfoMapper.queryRelationPatientEmpiId(certificateNo,patName,patSex,hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<PatIdPatNo> getPatIDPatNoList(List<Integer> patIds) {
        Weekend<PatIdPatNo> weekend = new Weekend<PatIdPatNo>(PatIdPatNo.class);
        weekend.weekendCriteria().andIn(PatIdPatNo::getPatID, patIds);
        return patIdPatNoMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Long getPatId() {
        return patientMapper.getPatId();
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public boolean savePatient(PatientList patientList) {
        patientDetailMapper.insertSelective(patientList.getDetail());
        patientMapper.insertSelective(patientList);
        return true;
    }

    @Override
    public List<DiseaseProofPatInVO> getInPatinetInfo(Integer patId, Integer hospitalCode) throws Exception {
        String reqJson;
        JSONObject req = new JSONObject();
        req.put("patId",patId);
        req.put("hospitalId", hospitalCode);
        JSONObject keys = new JSONObject();
        keys.put("Keys", req);
        reqJson = keys.toJSONString();
        HttpResult httpResult = insuranceRequestUtils.doPost(reqJson,hisConfig.getInPatientApi(), String.valueOf(hospitalCode));
        List<DiseaseProofPatInVO> result = new ArrayList<DiseaseProofPatInVO>();
        if(httpResult!=null&&httpResult.getStatus()==200){
            String body = httpResult.getBody();
            JSONObject object = JSONObject.parseObject(body);
            JSONObject output = object.getJSONObject("Content");
            if(output!=null&&output.size() > 0){
                DiseaseProofPatInVO patInfo = new DiseaseProofPatInVO();
                patInfo.setPatId(patId);
                patInfo.setHospitalId(hospitalCode);
                String regNo = output.getString("regNo");
                String inTime = output.getString("inTime");
                String hospNo = output.getString("hospNo");
                String patName = output.getString("patName");
                patInfo.setRegNo(regNo);
                patInfo.setHospNo(hospNo);
                patInfo.setPatName(patName);
                patInfo.setInTime(DateUtil.parseDate(inTime,"yyyy-MM-ddHH:mm"));
                result.add(patInfo);
            }
        }

        return result;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public String getPatCreeditTags(String idCardNo) {
        return patientMapper.getPatCreeditTags(idCardNo);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Integer> getPatientAllPatIds(String patId) {
        return pubPatientInfoMapper.getPatientAllPatIds(patId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<PatIdPatNo> getPatIDPatNo(Integer patId) {
//        Weekend<PatIdPatNo> weekend = new Weekend<PatIdPatNo>(PatIdPatNo.class);
//        weekend.weekendCriteria().andEqualTo(PatIdPatNo::getPatID, patId);
        PatIdPatNo example = new PatIdPatNo();
        example.setPatID(patId);
        return patIdPatNoMapper.select(example);
    }


    /**
     * 根据就诊信息的患者编号和就诊流水号，匹配卫宁推送数据
     *
     * @param patId
     * @param regNo
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public WeiNingDto getWeiNingDto(Long patId, Long regNo) {
        return patientMapper.getWeiNingDto(patId, regNo);
    }

}
