package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubItemexedept;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.HashMap;
import java.util.List;


@Mapper
public interface MdmPubItemexedeptMapper extends BaseMapper<MdmPubItemexedept>, ExampleMapper<MdmPubItemexedept> {
    List<HashMap<String,String>> getItemExecDeptItem(Integer itemCode, Integer useDeptId, String startTime, String endTime,Integer id);

    List<MdmPubItemexedept> getItemExecDeptByTime(@Param("itemCode") String itemCode,
                                                  @Param("curTime") String curTime,
                                                  @Param("curDeptId") Integer curDeptId,
                                                  @Param("curHospitalId") Integer curHospitalId);


    List<MdmPubItemexedept> getItemExecDeptByTimeItemCodeList(@Param("itemCodeList") List<Integer> itemCodeList,
                                                  @Param("curTime") String curTime,
                                                  @Param("curDeptId") Integer curDeptId,
                                                  @Param("curHospitalId") Integer curHospitalId);

    List<MdmPubItemexedept> getExecDeptList(@Param("curTime") String curTime,
                                            @Param("curDeptId") Integer curDeptId,
                                            @Param("curHospitalId") Integer curHospitalId);
}
