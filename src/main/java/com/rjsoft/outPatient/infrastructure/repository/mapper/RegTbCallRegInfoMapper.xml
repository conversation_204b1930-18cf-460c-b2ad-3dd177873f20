<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.RegTbCallRegInfoMapper">
  <resultMap id="BaseResultMap" type="com.rjsoft.outPatient.infrastructure.repository.entity.RegTbCallRegInfo">
    <!--@Table Reg_Tb_CallRegInfo-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="reg_no" jdbcType="BIGINT" property="regNo" />
    <result column="call_reg_no" jdbcType="VARCHAR" property="callRegNo" />
    <result column="hospital_id" jdbcType="INTEGER" property="hospitalId" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, reg_no, call_reg_no, hospital_id, create_by, create_time
  </sql>

  <insert id="insertBatchNew">
    insert into Reg_Tb_CallRegInfo (id, reg_no, call_reg_no, hospital_id, create_by, create_time)
    values
    <foreach collection="list" item="item" separator=",">
       (#{item.id}, #{item.regNo}, #{item.callRegNo}, #{item.hospitalId}, #{item.createBy}, #{item.createTime})
    </foreach>
  </insert>

</mapper>