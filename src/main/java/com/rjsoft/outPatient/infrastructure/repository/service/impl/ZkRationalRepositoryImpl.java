package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.config.dto.DoctorInfoResponse;
import com.rjsoft.outPatient.domain.recipe.dto.HlyyDoctor;
import com.rjsoft.outPatient.infrastructure.repository.entity.Worker;
import com.rjsoft.outPatient.infrastructure.repository.entity.WorkerContrast;
import com.rjsoft.outPatient.infrastructure.repository.entity.ZkIsNewRecipe;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.WorkerRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.ZkRationalRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 招康合理用药
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class ZkRationalRepositoryImpl implements ZkRationalRepository {

    ZkIsNewRecipeMapper zkIsNewRecipeMapper;
    SysFunctionMapper sysFunctionMapper;


    @Override
    public List<ZkIsNewRecipe> getZkIsNewRecipe(Integer recipeNo, Integer hospitalCode) {
        return zkIsNewRecipeMapper.getZkIsNewRecipe(recipeNo, hospitalCode);
    }

    @Override
    public boolean saveZkIsNewRecipe(ZkIsNewRecipe zkIsNewRecipe) {
        boolean success;
        boolean hasData = zkIsNewRecipeMapper.existsWithPrimaryKey(zkIsNewRecipe);
        if (hasData) {
            success = zkIsNewRecipeMapper.updateByPrimaryKeySelective(zkIsNewRecipe) > 0;
        } else {
            zkIsNewRecipe.setCreateDate(sysFunctionMapper.getDate());
            success = zkIsNewRecipeMapper.insert(zkIsNewRecipe) > 0;
        }
        return success;
    }


}
