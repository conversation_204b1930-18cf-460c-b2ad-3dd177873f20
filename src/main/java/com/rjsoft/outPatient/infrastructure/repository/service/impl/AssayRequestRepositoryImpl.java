package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.AssayRequest;
import com.rjsoft.outPatient.infrastructure.repository.entity.AssayRequestDetail;
import com.rjsoft.outPatient.infrastructure.repository.mapper.AssayRequestDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.AssayRequestMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.AssayRequestRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 化验申请单
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class AssayRequestRepositoryImpl implements AssayRequestRepository {
    AssayRequestMapper assayRequestMapper;
    AssayRequestDetailMapper assayRequestDetailMapper;

    /**
     * 保存化验申请单
     *
     * @param assayRequest
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean saveAssayRequest(AssayRequest assayRequest) {
        if (assayRequest == null) {
            return true;
        }
        if (assayRequest.getOpFlag() == null || assayRequest.getOpFlag() == 0) {
            assayRequestMapper.insert(assayRequest);
        } else if (assayRequest.getOpFlag() == -1) {
            assayRequestMapper.deleteByPrimaryKey(assayRequest);
        } else {
            assayRequestMapper.updateByPrimaryKeySelective(assayRequest);
        }

        List<AssayRequestDetail> insertList = new ArrayList<>();
        List<AssayRequestDetail> delList = new ArrayList<>();
        List<AssayRequestDetail> updateList = new ArrayList<>();
        for (AssayRequestDetail requestDetail : assayRequest.getRequestDetailList()) {
            if (requestDetail.getOpFlag() == null || requestDetail.getOpFlag() == 0) {
                insertList.add(requestDetail);
            } else if (requestDetail.getOpFlag() == -1) {
                delList.add(requestDetail);
            } else {
                updateList.add(requestDetail);
            }
        }
        if(insertList.size() >0) {
            assayRequestDetailMapper.batchInsert(insertList);
        }
        //以下两个方法还没有实现，先关注insert
        //以下两个方法还没有实现，先关注insert
        if(delList.size() >0) {
            assayRequestDetailMapper.deleteByIds(delList);
        }
        if(updateList.size() >0) {
            assayRequestDetailMapper.batchUpdate(updateList);
        }
        for (AssayRequestDetail requestDetail : delList) {
            assayRequestDetailMapper.deleteByPrimaryKey(requestDetail);
        }
        for (AssayRequestDetail requestDetail : updateList) {
           assayRequestDetailMapper.updateByPrimaryKeySelective(requestDetail);
        }
        return true;
    }

    @Override
    @DatabaseAnnotation
    public AssayRequest getAssayRequestByApplyIId(Integer applyId, Integer hospitalCode) {
        AssayRequest request = assayRequestMapper.getAssayRequestByApplyIId(applyId, hospitalCode);
        if (request == null) {
            return null;
        }
        List<AssayRequestDetail> requestDetailList = assayRequestDetailMapper.getRequestDetailByRequestNo(request.getRequestNo(), hospitalCode);
        request.setRequestDetailList(requestDetailList);
        return request;
    }

    /**
     * 根据预存流水号，加载申请单信息
     *
     * @param preSaveNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public AssayRequest getAssayRequestByPreNo(Long preSaveNo, Integer hospitalCode) {
        if (preSaveNo == null || hospitalCode == null) {
            return null;
        }
        AssayRequest request = assayRequestMapper.getAssayRequestByPreNo(preSaveNo, hospitalCode);
        if (request == null) {
            return null;
        }
        List<AssayRequestDetail> requestDetailList = assayRequestDetailMapper.getRequestDetailByRequestNo(request.getRequestNo(), hospitalCode);
        request.setRequestDetailList(requestDetailList);
        return request;
    }

    /**
     * 删除化验申请单
     *
     * @param requestNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean delAssayRequest(Long requestNo, Integer hospitalCode) {
        assayRequestDetailMapper.delRequestDetailByRequestNo(requestNo, hospitalCode);
        assayRequestMapper.delAssayRequestById(requestNo, hospitalCode);
        return true;
    }
}
