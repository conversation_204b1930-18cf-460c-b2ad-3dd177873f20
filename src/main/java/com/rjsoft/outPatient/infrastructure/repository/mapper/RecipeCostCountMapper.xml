<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeCostCountMapper">


    <select id="getZfPerCapitaCost" resultType="java.math.BigDecimal">
        SELECT CASE SUM(isnull(fybrc, 0))
                   WHEN 0 THEN 0
                   ELSE SUM(isnull(ZfDrugAmount, 0)) / SUM(isnull(fybrc, 0)) END avgzfdrug
        FROM Tbt_MdNewdocdata (nolock)
        WHERE Doctorid = #{doctorId}
          AND Optime BETWEEN #{start} AND #{end};
    </select>


</mapper>