package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList;
import com.rjsoft.outPatient.infrastructure.repository.entity.SciRegisterlist;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RegisterListMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SciRegisterlistMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.SciRegisterlistRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

@Service
@AllArgsConstructor
public class SciRegisterlistRepositoryImpl implements SciRegisterlistRepository {
    private SciRegisterlistMapper sciRegisterlistMapper;
    private RegisterListMapper registerListMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public boolean saveSciRegisterlist(SciRegisterlist sciRegisterlist) {
        return sciRegisterlistMapper.insertSelective(sciRegisterlist)>0;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public boolean saveRegRegisterlist(RegisterList registerList) {
        return registerListMapper.insertSelective(registerList)>0;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public SciRegisterlist getSciRegisterlist(Integer regNo) {
        Weekend<SciRegisterlist> weekend = new Weekend<>(SciRegisterlist.class);
        weekend.weekendCriteria().andEqualTo(SciRegisterlist::getRegno,regNo);
        return sciRegisterlistMapper.selectOneByExample(weekend);
    }
}
