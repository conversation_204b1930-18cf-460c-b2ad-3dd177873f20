package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.Application;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationControl;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationTemplate;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface ApplicationControlMapper extends BaseMapper<ApplicationControl>, ExampleMapper<ApplicationControl> {


    /**
     * 查询申请单明细对照信息
     *
     * @param templateNo
     * @param hospitalCode
     * @return
     */
    List<ApplicationControl> getApplicationControl(@Param("templateNo") String templateNo, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据流水号集合获取申请单主表
     *
     * @param ids
     * @param hospitalCode
     * @return
     */
    default List<ApplicationControl> getApplicationControlByIds(List<Integer> ids, Integer hospitalCode) {
        Weekend weekend = new Weekend(ApplicationControl.class);
        WeekendCriteria<ApplicationControl, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ApplicationControl::getId, ids);
        weekendCriteria.andEqualTo(ApplicationControl::getHospitalCode, hospitalCode);
        return selectByExample(weekend);
    }


    /**
     * 申请单保存时查询申请单对照信息，匹配收费项目
     *
     * @param templateNo
     * @param fromGroupNo
     * @param selectedPlace
     * @param hospitalCode
     * @return
     */
    default List<ApplicationControl> getApplicationControlByLoca(String templateNo, String fromGroupNo, String selectedPlace, Integer hospitalCode) {
        ApplicationControl applicationControl = new ApplicationControl();
        applicationControl.setTemplateNo(templateNo);
        applicationControl.setFromGroup(fromGroupNo);

        if (selectedPlace.isEmpty()) {
            selectedPlace = null;
        }
        if (selectedPlace != null) {
            applicationControl.setSelectedPlace(selectedPlace);
        }
        applicationControl.setHospitalCode(hospitalCode);
        return select(applicationControl);
    }

    /**
     * 根据组号获取申请单主表
     *
     * @param fromGroupNo
     * @param hospitalCode
     * @return
     */
    default List<ApplicationControl> getApplicationControlByGroupNo(String fromGroupNo, Integer hospitalCode) {
        ApplicationControl applicationControl = new ApplicationControl();
        applicationControl.setFromGroup(fromGroupNo);
        applicationControl.setHospitalCode(hospitalCode);
        return select(applicationControl);
    }


}
