<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ItemSetMapper">

    <select id="getItemSetDtoByHospitalId" resultType="com.rjsoft.outPatient.domain.packages.dto.ItemSetDto">
        SELECT distinct itemcode packageId,
        itemName packageName,
        hospitalid hospitalCode,
        isFixed
        FROM
        System_Tv_PubItems
        WHERE
        isSet = 1
        AND Stopped = 0
        AND ItemCategory = #{itemCategory}
        AND useRange IN ( 0, 1 )
        AND hospitalid = #{hospitalId}
        <if test="itemNameOrInputCode != null and itemNameOrInputCode !='' ">
            and (ItemName like concat('%',#{itemNameOrInputCode},'%') or InputCode1 like concat('%',#{itemNameOrInputCode},'%') )
        </if>
        ORDER BY
        itemcode
    </select>

</mapper>