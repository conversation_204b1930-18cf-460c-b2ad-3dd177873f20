package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ChiefComplaint;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

public interface ChiefComplaintMapper extends BaseMapper<ChiefComplaint> {

    /**
     * 查询主诉信息
     *
     * @param patId
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    default List<ChiefComplaint> getPatChiefComplaint(Integer patId, Integer receptionNo, Integer hospitalCode) {
        ChiefComplaint chiefComplaint = new ChiefComplaint();
        chiefComplaint.setPatId(patId);
        chiefComplaint.setReceptionNo(receptionNo);
        chiefComplaint.setIsDelete(0);
        chiefComplaint.setHospitalCode(hospitalCode);
        return select(chiefComplaint);
    }

    /**
     * 查询主诉信息
     *  @param receptionNo
     *  @param hospitalCode
     */
    default List<ChiefComplaint> getChiefComplaint( Integer receptionNo, Integer hospitalCode) {
        ChiefComplaint chiefComplaint = new ChiefComplaint();
        chiefComplaint.setReceptionNo(receptionNo);
        chiefComplaint.setIsDelete(0);
        chiefComplaint.setHospitalCode(hospitalCode);
        return select(chiefComplaint);
    }

}
