package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.utils.TyMapUtil;
import com.rjsoft.outPatient.common.consts.PreLoad;
import com.rjsoft.outPatient.common.utils.IgniteUtil;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.domain.preload.PreLoadCache;
import com.rjsoft.outPatient.infrastructure.repository.entity.Department;
import com.rjsoft.outPatient.infrastructure.repository.entity.HisDictionary;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DepartmentMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.HisDictionaryMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DepartmentRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 部门信息
 *
 * <AUTHOR>
 * @create 2021/11/24 11:27
 */
@AllArgsConstructor
@Service
public class DepartmentRepositoryImpl implements DepartmentRepository {
    private DepartmentMapper departmentMapper;
    private HisDictionaryMapper hisDictionaryMapper;
    PreLoadCache preLoadCache;

    /**
     * 根据部门编码获取部门信息
     *
     * @param deptId
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Department getDepartmentByDeptId(Integer deptId, Integer hospitalCode) {
        if (deptId == null || hospitalCode == null) {
            return null;
        }
        return departmentMapper.getDepartmentByDeptId(deptId, hospitalCode);
    }

    /**
     * 根据部门编码获取部门信息
     *
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Map<Integer,Department> getAllDepartmentByHospitalCode(Integer hospitalCode) {
        if (hospitalCode == null) {
            return null;
        }
        List<Department> allDepartmentByHospitalCode = departmentMapper.getAllDepartmentByHospitalCode(hospitalCode);

        return TyMapUtil.listToMap(allDepartmentByHospitalCode, "getDeptId");
    }

    /**
     * 根据ID获取处方明细
     *
     * @param deptId
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public Department getDepartmentByDeptIdPreLoad(Integer deptId, Integer hospitalCode) {
        Map<Integer,Department>  allDepartmentByHospitalCodeMap = preLoadCache.getOrLoad(
                PreLoad.ALL_DEPARTMENT_BY_HOSPITAL_CODE + "_"+hospitalCode,()->{
                    return null;
                });
        if(allDepartmentByHospitalCodeMap == null){
            return getDepartmentByDeptId(deptId, hospitalCode);
        }
        Department department = allDepartmentByHospitalCodeMap.get(deptId);
        if(department == null){
            getDepartmentByDeptId(deptId, hospitalCode);
        }
        if (department.getHospitalId() != hospitalCode) {
            return null;
        }
        return department;
    }

    @Override
    public Department getDepartmentByDeptIdFIgnite(Integer deptId, Integer hospitalCode) {
        if (deptId == null || hospitalCode == null) {
            return null;
        }
        StringBuilder sqlSB = StrUtil.builder();
        sqlSB.append("SELECT\n" +
                        "\tDeptId,\n" +
                        "\tHospitalId,\n" +
                        "\tDeptCode,\n" +
                        "\tDeptName,\n" +
                        "\tParentId,\n" +
                        "\tInputCode1,\n" +
                        "\tInputCode2,\n" +
                        "\tInsureDeptCode,\n" +
                        "\tRecordDeptCode,\n" +
                        "\tIsVisit,\n" +
                        "\tDeptPhone,\n" +
                        "\tDeptClassCode,\n" +
                        "\tIsSpecial,\n" +
                        "\tIsVirtual,\n" +
                        "\tSID,\n" +
                        "\tStatus,\n" +
                        "\tRemark,\n" +
                        "\tDrugType,\n" +
                        "\tCreatorUserId,\n" +
                        "\tCreateOn,\n" +
                        "\tUpdateUserId,\n" +
                        "\tUpdateOn,\n" +
                        "\tLang \n" +
                        "FROM\n" +
                        "\tSystem_Tb_Department_4 ")
                .append(" WHERE ")
                .append(" DeptId = ").append(deptId)
                .append(" and HospitalId = ").append(hospitalCode)
                .append(" Status = 1 ");
        Object query = IgniteUtil.query(sqlSB.toString());
        if (query != null) {
            List<HashMap> list = JSONUtil.toList(query.toString(), HashMap.class);
            if (ObjectUtil.isNotEmpty(list)) {
                HashMap map = list.get(0);
                // FIXME: 2024/4/18 alex:这里字段匹配太多了，后面做个beancopy类，忽略大小写和类型的那种
                Department department = new Department();
                department.setDeptId(Integer.parseInt(map.get("DEPTID").toString()));
                department.setHospitalId(Integer.parseInt(map.get("HOSPITALID").toString()));
                department.setDeptCode(map.get("DEPTCODE").toString());
                department.setDeptName(map.get("DEPTNAME").toString());
                department.setParentId(Integer.parseInt(map.get("PARENTID").toString()));
                department.setInputCode1(map.get("INPUTCODE1").toString());
                department.setInputCode2(map.get("INPUTCODE2").toString());
                department.setInsureDeptCode(Integer.parseInt(map.get("INSUREDEPTCODE").toString()));
                department.setRecordDeptCode(Integer.parseInt(map.get("RECORDDEPTCODE").toString()));
                return department;
            }
        }
        return null;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Department> getDepartmentByDeptIdList(List<Integer> deptIds, Integer hospitalCode) {
        Weekend<Department> weekend = new Weekend<>(Department.class);
        weekend.weekendCriteria().andEqualTo(Department::getHospitalId, hospitalCode)
                .andIn(Department::getDeptId, deptIds);
        List<Department> departmentList = departmentMapper.selectByExample(weekend);
        return departmentList;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Department> getDepartmentList(List<Integer> hospitalCodeList) {
        Weekend<Department> weekend = new Weekend<>(Department.class);
        weekend.weekendCriteria().andIn(Department::getHospitalId, hospitalCodeList);
        return departmentMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Department> getDepartmentList(Integer hospitalId) {
        Weekend<Department> weekend = new Weekend<>(Department.class);
        weekend.weekendCriteria().andEqualTo(Department::getHospitalId, hospitalId);
        return departmentMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Department> getMzDepartmentList(Integer hospitalId, String type) {
        List<HisDictionary> hisDictionaryList = hisDictionaryMapper.getDictionaryByTypes(Arrays.asList(51), hospitalId);
        String deptClassCode = null;
        for (HisDictionary hisDictionary : hisDictionaryList) {
            if (type.equals(hisDictionary.getInputCode1().trim())) {
                deptClassCode = hisDictionary.getHisDictionaryCode();
            }
        }
        Weekend<Department> weekend = new Weekend<>(Department.class);
        weekend.weekendCriteria().andEqualTo(Department::getHospitalId, hospitalId).andEqualTo(Department::getDeptClassCode, deptClassCode);
        return departmentMapper.selectByExample(weekend);
    }
}
