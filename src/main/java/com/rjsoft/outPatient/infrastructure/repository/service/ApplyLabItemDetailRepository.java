package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.ApplyLabItemDetail;

import java.util.List;

/**
 * 化验指标
 *
 * <AUTHOR>
public interface ApplyLabItemDetailRepository {


    /**
     * 查询化验指标明细
     *
     * @param hospitalCode
     * @return
     */
    List<ApplyLabItemDetail> getApplyLabItemDetail(Integer hospitalCode);


}
