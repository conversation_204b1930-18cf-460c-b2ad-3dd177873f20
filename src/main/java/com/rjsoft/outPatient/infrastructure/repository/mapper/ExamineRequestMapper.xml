<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ExamineRequestMapper">


    <select id="getApplicationProjectList"
            resultType="com.rjsoft.outPatient.domain.applicationForm.dto.ApplicationProjectDto">
        select a.jzlsh receptionNo, a.jclsh examineNo, b.sqdbm templateNo, b.sqdmc templateName
        from MZYS_TB_JCSQZB a
                 inner join MZYS_TB_JCSQD b on b.sqdbm = a.sqdbm
        where a.jzlsh = #{receptionNo}
          and a.sqys = #{doctorId}
          and b.type = #{applyType}
    </select>


    <!--    查询已开申请单明细-->
    <select id="getApplicationProjectDetail"
            resultType="com.rjsoft.outPatient.domain.applicationForm.dto.ApplicationProjectListDto">
        select c.xmmc itemName, c.xmsl itemQuantity, c.cfje itemAmount, c.zt status
        from MZYS_TB_JCSQZB a
                 inner join MZYS_TB_JCSQMX b on b.jclsh = a.jclsh
                 inner join MZYS_TB_MZCFMX c on c.cfmxlsh = b.cfmxlsh
        where a.jclsh = #{examineNo}
    </select>


    <select id="getApplicationProjectDetailS" resultType="java.util.HashMap">
        select distinct jclxmc, c.xmmc, c.xmsl, c.cfje, d.cfmxlsh
        from MZYS_TB_MZCFMX c
                 inner join MZYS_TB_JCSQMX d on c.cfmxlsh = d.cfmxlsh
                 inner join MZYS_TB_JCSQZB a on a.jclsh = d.InspectSerialNo
        where d.InspectSerialNo = #{examineNo}
    </select>

    <select id="getApplicationItems"
            resultType="com.rjsoft.outPatient.domain.applicationForm.dto.ApplicationProjectListDto">
        select c.xmmc itemName, d.xmsl itemQuantity, d.cfje itemAmount, d.zt status
        from MZYS_TB_JCSQDNR a
                 inner join MZYS_TB_JCSQZB b on a.id = jclsh
                 inner join MZYS_TB_JCSQMX c on c.jclsh = b.jclsh
                 inner join MZYS_TB_MZCFMX d on d.cfmxlsh = c.cfmxlsh
        where a.bdbm = #{formId}
          and yybm = #{hospitalCode}


    </select>

</mapper>
