package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbMonthFeeRation;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

public interface MzysTbMonthFeeRationMapper extends Mapper<MzysTbMonthFeeRation> {
    default MzysTbMonthFeeRation getMonthFeeRationByWorker(String workerNo, String month) {
        Weekend<MzysTbMonthFeeRation> weekend = Weekend.of(MzysTbMonthFeeRation.class);
        WeekendCriteria<MzysTbMonthFeeRation, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andEqualTo(MzysTbMonthFeeRation::getWorkerNo, workerNo)
                .andEqualTo(MzysTbMonthFeeRation::getStatMonth, month);
        return this.selectOneByExample(weekend);
    }

}