package com.rjsoft.outPatient.infrastructure.repository.strategy.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.utils.ObjectUtils;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeItem;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugFrequency;
import com.rjsoft.outPatient.infrastructure.repository.entity.PackageDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.PreRecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugFrequencyMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.PackageDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SysFunctionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ChargeItemRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.CustomPackageRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.PreRecipeDetailRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeRepository;
import com.rjsoft.outPatient.infrastructure.repository.strategy.PreRecipeStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component("PreRecipeByCustomId")
@AllArgsConstructor
public class PreRecipeByCustomId implements PreRecipeStrategy {

    @Resource
    RecipeRepository recipeRepository;

    CustomPackageRepository customPackageRepository;
    ChargeItemRepository chargeItemRepository;
    PreRecipeDetailRepository preRecipeDetailRepository;


    PackageDetailMapper packageDetailMapper;
    SysFunctionMapper sysFunctionMapper;
    DrugFrequencyMapper drugFrequencyMapper;


    @Override
    @DatabaseAnnotation
    public List<PreRecipeDetail> getPreRecipeDetails(CheckRecipeDto checkDto, Map errInfo) {
        List<PreRecipeDetail> details = getPreRecipeDetailsByCustomId(checkDto);
        return details;
    }

    /**
     * 获取处方预存明细(根据自定义套餐ID)
     *
     * @param checkDto
     * @return
     */
    @DatabaseAnnotation
    private List<PreRecipeDetail> getPreRecipeDetailsByCustomId(CheckRecipeDto checkDto) {
        List<PreRecipeDetail> res = new ArrayList<>();
        List<PackageDetail> details;
        String packageName = "";
        //根据自定义套餐加载明细
        if (7 == checkDto.getOperatingType()) {
            details = packageDetailMapper.getPackageDetailsByPackageDetailId(Converter.toInt32(checkDto.getOperatingId()), checkDto.getHospitalCode());
        } else {
            details = packageDetailMapper.getPackageDetailsByPackageId(Converter.toInt32(checkDto.getOperatingId()), checkDto.getHospitalCode());
            packageName = customPackageRepository.getCustomPackageNameById(Converter.toInt32(checkDto.getOperatingId()), checkDto.getHospitalCode());
            //如果预存流水号不为空，检查一下预存表数据，如果预存表数据中不存在此收费项目，则从集合中移除(化验套餐中可能有项目已经通过申请单开立了，因此要移除掉，避免重复开设)
            if (checkDto.getPreSaveNo() != null) {
                List<PreRecipeDetail> preRecipeDetailList = preRecipeDetailRepository.getPreRecipeDetailsBySaveNo(checkDto.getPreSaveNo(), checkDto.getDoctorId(), checkDto.getHospitalCode());
                final long dataFrom = preRecipeDetailList.stream().filter(p -> ObjectUtils.compare(p.getPreSaveType(), 1)).count();
                if (dataFrom > 0) {
                    //保证至少走过一次前置校验，不然通过输入码录入的信息，套餐明细无法保存
                    List<Integer> hasItems = preRecipeDetailList.stream().map(PreRecipeDetail::getItemCode).collect(Collectors.toList());
                    details.removeIf(p -> !hasItems.contains(p.getItemCode()));
                }
            }
        }
        if (details == null || details.isEmpty()) {
            return res;
        }
        List<Integer> itemCodes = details.stream().map(PackageDetail::getItemCode).collect(Collectors.toList());

        DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
        List<ChargeItem> chargeItems = chargeItemRepository.getChargeItemByIdsAndInitSpec(itemCodes, checkDto.getHospitalCode());

        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);

        long preSaveNo = 0L;
        if (checkDto.getPreSaveNo() != null && checkDto.getPreSaveNo() > 0L) {
            Log.info("自定义套餐使用现有预存id,不用重新生成");
            preSaveNo = checkDto.getPreSaveNo();
        } else {
            preSaveNo = sysFunctionMapper.getGetSequences(SequenceEnum.PRE_SAVE_NO);
        }
        for (ChargeItem chargeItem : chargeItems) {
            PreRecipeDetail recipeDetail = new PreRecipeDetail();
            for (PackageDetail detail : details) {
                if (detail.getItemCode().equals(chargeItem.getItemCode())) {
                    recipeDetail.initFromCustomPackage(chargeItem, detail);
                    recipeDetail.conversionDoseUnit(detail.getDosageUnit(), chargeItem);
                    final DrugFrequency frequency = ItemCategoryEnum.isDrug(recipeDetail.getFeeCategory()) ?
                            drugFrequencyMapper.getDrugFrequencyById(detail.getFrequency(), detail.getHospitalCode()) : null;
                    recipeDetail.calculateQuantity(frequency, chargeItem, 7, RoundingMode.DOWN);
                }
            }
            recipeDetail.setApplicationForm(packageName);
            recipeDetail.setPreSaveNo(preSaveNo);
            recipeDetail.setRecipeDetailNo(sysFunctionMapper.getGetSequences(SequenceEnum.RECIPE_DETAIL_NO));
            recipeDetail.setOpCode(checkDto.getDoctorId());
            recipeDetail.setOpFlag(0);
            res.add(recipeDetail);
        }
        return res;
    }
}
