package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ClinicNewPatient;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

public interface ClinicNewPatientMapper extends BaseMapper<ClinicNewPatient>, ExampleMapper<ClinicNewPatient> {


    /**
     * 根据身份证号+大类名称获取是否已经上报
     *
     * @param idNo   身份证号
     * @param sbName 大类名称
     * @return
     */
    Integer getInfoByIdNoSbname(@Param("idNo") String idNo, @Param("sbName") String sbName, @Param("receptionNo") String receptionNo, @Param("hospitalCode") Integer hospitalCode);


    /**
     * 检查是否存在定稿新病人
     *
     * @param reportDiseaseId
     * @param idNo
     * @param sbCode
     * @param hospitalCode
     * @return
     */
    Integer getFinalizedInfoByIdNoSbname(@Param("reportDiseaseId") Integer reportDiseaseId, @Param("idNo") String idNo, @Param("sbCode") String sbCode, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据身份证号+大类名称+就诊流水号获取报告信息
     *
     * @param idNo
     * @param sbname
     * @param receptionNo
     * @return
     */
    ClinicNewPatient getInfoByIdNoSbname2(@Param("idNo") String idNo, @Param("sbname") String sbname, @Param("receptionNo") String receptionNo, @Param("hospitalCode") Integer hospitalCode);
}
