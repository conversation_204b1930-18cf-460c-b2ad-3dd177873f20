<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DiagnoseOldMapper">

    <select id="getDiagnoseOldByRegNo" resultType="java.util.HashMap">
       select a.zdbm,d.zdmc
        from mzys.dbo.MZYS_Tv_MZYSZD a (nolock)
        inner join mzys.dbo.MZYS_TV_KZJL b (nolock) on a.jzlsh=b.jzlsh
        inner join mzys.dbo.GHSF_TB_MZGH_ALL c (nolock) on c.ghlsh=b.ghlsh
        inner join mzys.dbo.XTPZ_TB_ZDXX d (nolock) on a.zdbm=d.zdbm
        where c.regno=#{regNo}
        and (a.zdbm like 'F%' or a.zdbm like 'G30%' or a.zdbm like 'G31%' or a.zdbm like 'G47%' or a.zdbm like 'A52.104%' or a.zdbm like 'R06.501%'
            or a.zdbm = 'Z63.800x001' or a.zdbm = 'Z03.900' or a.zdbm = 'Z91.500' or a.zdbm = 'R45.700x002' or a.zdbm = 'Z03.200' or a.zdbm = 'Z86.400')
    </select>

    <select id="existsWithDiseaseProofIdAndDiagnosticCode" resultType="java.lang.Integer">
        select count(*) from MZYS_TB_JBZM_ZD where MainId = #{diseaseProofId} and zdbm = #{diagnosticCode}
    </select>

    <select id="getDictionaryCondition" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.DictionaryCondition">
        select zdbm as dictionaryCode,zdmc as dictionaryTypeName,idcbm as dictionaryIDC from MZYS_TB_ZYSQDZD
    </select>

</mapper>