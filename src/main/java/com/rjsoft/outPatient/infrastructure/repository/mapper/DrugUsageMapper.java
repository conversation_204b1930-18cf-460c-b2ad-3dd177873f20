package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.DrugUsage;
import com.rjsoft.outPatient.infrastructure.repository.entity.PrvNameResult;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

/**
 * 药品给药途径
 * <AUTHOR>
public interface DrugUsageMapper extends BaseMapper<DrugUsage> , ExampleMapper<DrugUsage> {

    /**
     * 根据药品项目ID查询用法
     * @param drugId
     * @param hospitalCode
     * @return
     */
    default List<DrugUsage> getDrugUsageById(Integer drugId,Integer hospitalCode) {
        Weekend weekend = new Weekend(DrugUsage.class);
        WeekendCriteria<DrugUsage, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(DrugUsage::getDrugId, drugId);
        weekendCriteria.andEqualTo(DrugUsage::getHospitalCode, hospitalCode);
        weekend.setOrderByClause("sid");
        return selectByExample(weekend);
    }

    List<PrvNameResult> selectPrvName(@Param("itemCode") String itemCode, @Param("hospId") Integer hospId);
}
