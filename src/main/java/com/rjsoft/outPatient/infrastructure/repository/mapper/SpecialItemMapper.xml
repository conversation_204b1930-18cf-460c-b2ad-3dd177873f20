<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.SpecialItemMapper">
    <select id="querySpecialItemByIdList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.SpecialItems">
        select *
        from System_Tb_SpecialItems
        where  itemCode in
        <foreach collection="itemCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and hospitalCode=#{hospitalCode}
    </select>
    <select id="querySpecialItemByIdListAndHospitalCodeList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.SpecialItems">
        select *
        from System_Tb_SpecialItems
        where  itemCode in
        <foreach collection="itemCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and hospitalCode in
        <foreach collection="hospitalCodeList" index="index" item="hospitalCode" open="(" separator="," close=")">
            #{hospitalCode}
        </foreach>
    </select>
</mapper>
