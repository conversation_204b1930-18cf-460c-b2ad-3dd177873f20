package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 申请单部位内容
 */
@Data
@Table(name = "MZYS_TB_JCSQDNRBW")
public class ApplicationContentLoca  implements Serializable {

    @Id
    @Column(name = "Id", insertable = false, updatable = false)
    private Integer locationId;

    @Column(name = "nrid")
    private Integer contentId;

    @Column(name = "bwfz")
    private Integer fromGroupNo;

    @Column(name = "xzbw")
    private Integer selectedPlace;

    @Column(name = "yybm")
    private Integer hospitalCode;

    public ApplicationContentLoca() {
    }

    public ApplicationContentLoca(Integer contentId, Integer hospitalCode) {
        this.contentId = contentId;
        this.hospitalCode = hospitalCode;
    }
}
