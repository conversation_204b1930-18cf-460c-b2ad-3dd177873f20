package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.OldReceptionRecord;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/4 - 10:30
 */
public interface OldReceptionRecordMapper extends BaseMapper<OldReceptionRecord>, ExampleMapper<OldReceptionRecord> {

    /**
     * 根据挂号流水号获取接诊记录
     *
     * @param regNos
     */
    List<OldReceptionRecord> getReceptionByRegNo(List<String> regNos);

    /**
     * 根据挂号流水号获取老系统接诊记录
     *
     * @param regNos
     * @param hospitalCode
     * @return
     */
    default List<OldReceptionRecord> getOldReceptionByRegNo(List<String> regNos, Integer hospitalCode, Date startTime, Date endTime) {
        List<OldReceptionRecord> records = new ArrayList<>();
        OldReceptionRecord record = new OldReceptionRecord();
        if (regNos == null || regNos.size() == 0) {
            return records;
        }
        Weekend<OldReceptionRecord> weekend = new Weekend<>(OldReceptionRecord.class);
        WeekendCriteria<OldReceptionRecord, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andIn(OldReceptionRecord::getRegNo, regNos);
        if(startTime!=null&&endTime!=null) {
            keywordCriteria.andBetween(OldReceptionRecord::getFirstDate, startTime, endTime);
        }
        records = selectByExample(weekend);
        return records;
    }

}
