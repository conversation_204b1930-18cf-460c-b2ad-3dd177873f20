package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegTbSpecialsourcerelation;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RegTbSpecialsourcerelationMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RegTbSpecialsourcerelationRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

@AllArgsConstructor
@Service
public class RegTbSpecialsourcerelationRepositoryImpl implements RegTbSpecialsourcerelationRepository {
    private final RegTbSpecialsourcerelationMapper regTbSpecialsourcerelationMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public RegTbSpecialsourcerelation getRegTbSpecialsourcerelation(Long regNo, Integer hospitalCode) {
        Weekend<RegTbSpecialsourcerelation> weekend = new Weekend<>(RegTbSpecialsourcerelation.class);
        WeekendCriteria<RegTbSpecialsourcerelation, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(RegTbSpecialsourcerelation::getHospitalCode,hospitalCode).andEqualTo(RegTbSpecialsourcerelation::getSpecialRegNo,regNo).andEqualTo(RegTbSpecialsourcerelation::getStatus,0);
        WeekendCriteria<RegTbSpecialsourcerelation, Object> weekendCriteria1 = weekend.weekendCriteria();
        weekendCriteria1.andEqualTo(RegTbSpecialsourcerelation::getHospitalCode,hospitalCode).andEqualTo(RegTbSpecialsourcerelation::getDispensingRegNo,regNo).andEqualTo(RegTbSpecialsourcerelation::getStatus,0);
        weekend.or(weekendCriteria1);
        return regTbSpecialsourcerelationMapper.selectOneByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<RegTbSpecialsourcerelation> getRegTbSpecialsourcerelationList(List<Long> regNos, Integer hospitalCode) {
        Weekend<RegTbSpecialsourcerelation> weekend = new Weekend<>(RegTbSpecialsourcerelation.class);
        WeekendCriteria<RegTbSpecialsourcerelation, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(RegTbSpecialsourcerelation::getHospitalCode,hospitalCode).andIn(RegTbSpecialsourcerelation::getSpecialRegNo,regNos).andEqualTo(RegTbSpecialsourcerelation::getStatus,0);
        WeekendCriteria<RegTbSpecialsourcerelation, Object> weekendCriteria1 = weekend.weekendCriteria();
        weekendCriteria1.andEqualTo(RegTbSpecialsourcerelation::getHospitalCode,hospitalCode).andIn(RegTbSpecialsourcerelation::getDispensingRegNo,regNos).andEqualTo(RegTbSpecialsourcerelation::getStatus,0);
        weekend.or(weekendCriteria1);
        return regTbSpecialsourcerelationMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public RegTbSpecialsourcerelation getRegTbSpecialsourcerelationBySpecial(long regNo, int hospitalCode) {
        Weekend<RegTbSpecialsourcerelation> weekend = new Weekend<>(RegTbSpecialsourcerelation.class);
        weekend.weekendCriteria().andEqualTo(RegTbSpecialsourcerelation::getHospitalCode,hospitalCode).andEqualTo(RegTbSpecialsourcerelation::getSpecialRegNo,regNo).andEqualTo(RegTbSpecialsourcerelation::getStatus,0);
        return regTbSpecialsourcerelationMapper.selectOneByExample(weekend);
    }
}
