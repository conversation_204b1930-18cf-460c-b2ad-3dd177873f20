package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugUsage;
import com.rjsoft.outPatient.infrastructure.repository.entity.PrvNameResult;

import java.util.List;

/**
 * 药品给药途径
 * <AUTHOR>
public interface DrugUsageRepository {

    /**
     * 根据药品ID查询给药途径
     * @param drugId
     * @param hospitalCode
     * @return
     */
    List<DrugUsage> getDrugUsageById(Integer drugId, Integer hospitalCode);

    /**
     * 根据药品ID查询住院给药途径
     * @param itemCode
     * @param hospId
     * @return
     */
    public List<PrvNameResult> selectPrvName(String itemCode, Integer hospId);

}
