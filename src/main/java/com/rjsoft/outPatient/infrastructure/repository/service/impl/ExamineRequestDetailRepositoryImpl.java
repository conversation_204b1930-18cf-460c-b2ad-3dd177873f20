package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.ExamineRequest;
import com.rjsoft.outPatient.infrastructure.repository.entity.ExamineRequestDetail;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ExamineRequestDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ExamineRequestDetailRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

/**
 * <AUTHOR>
 * @since 2021/9/8-5:34 下午
 */
@Service
@AllArgsConstructor
public class ExamineRequestDetailRepositoryImpl implements ExamineRequestDetailRepository {

    private final ExamineRequestDetailMapper examineRequestDetailMapper;

    @Override
    @DatabaseAnnotation
    public void deleteByExamineNoAndRecipeDetailIdAndReceptionNo(Integer examineNo, Long recipeDetailId, Long receptionNo) {
        final ExamineRequestDetail entity = new ExamineRequestDetail();
        entity.setDeleteFlag(true);
        Weekend weekend = new Weekend(ExamineRequestDetail.class);
        WeekendCriteria<ExamineRequestDetail, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ExamineRequestDetail::getExamineNo, examineNo);
        weekendCriteria.andEqualTo(ExamineRequestDetail::getRecipeDetailId, recipeDetailId);
        weekendCriteria.andEqualTo(ExamineRequestDetail::getReceptionNo, receptionNo);
        examineRequestDetailMapper.updateByExampleSelective(entity, weekend);
    }
}
