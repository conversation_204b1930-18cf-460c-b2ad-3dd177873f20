package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.rjsoft.common.SystemNo;
import com.rjsoft.common.configuration.SysConfig;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.utils.ObjectUtils;
import com.rjsoft.common.utils.TyMapUtil;
import com.rjsoft.common.utils.TyMdc;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.consts.PreLoad;
import com.rjsoft.outPatient.common.consts.SysConfigKey;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.common.enums.RecipeStatusEnum;
import com.rjsoft.outPatient.common.enums.RecipeTypeEnum;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.item.ChargeItemDomain;
import com.rjsoft.outPatient.domain.item.vo.ItemExeDeptReqVO;
import com.rjsoft.outPatient.domain.item.vo.ItemExeDeptRespVO;
import com.rjsoft.outPatient.domain.preload.PreLoadCache;
import com.rjsoft.outPatient.domain.prescriptionAudit.dto.AuditPrescriptionDto;
import com.rjsoft.outPatient.domain.reception.dto.InsuranceOutRecipeDto;
import com.rjsoft.outPatient.domain.recipe.constant.TyKey;
import com.rjsoft.outPatient.domain.recipe.dto.*;
import com.rjsoft.outPatient.domain.recipe.util.PreRecipeUtil;
import com.rjsoft.outPatient.domain.recipe.vo.DoctorFeeQuotaVo;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.*;
import com.rjsoft.outPatient.macomm.massert.MAssert;
import com.rjsoft.outPatient.macomm.ret.RetMsg;
import javafx.util.Pair;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.StopWatch;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.rjsoft.outPatient.common.enums.ApplyFormFlagEnum.NOT_APPLY_FORM;

/**
 * 门诊处方
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class RecipeRepositoryImpl implements RecipeRepository {
//    RedisTemplate<String, Object> rdTemplate;
    PreLoadCache preLoadCache;

    ChargeItemDomain chargeItemDomain;

    ChargeItemRepository chargeItemRepository;
    RegisterListRepository registerListRepository;
    RecipeDetailRepository recipeDetailRepository;
    SystemTbPubItemsRepository systemTbPubItemsRepository;

    //这样不好，直接调到下面了。
    SystemTvIteminfodeptRepository systemTvIteminfodeptRepository;

    RecipeMapper recipeMapper;
    RecipeDetailMapper recipeDetailMapper;
    SysFunctionMapper sysFunctionMapper;
    DrugFrequencyMapper drugFrequencyMapper;
    CurrencyMapper currencyMapper;
    RisItemDjMapper risItemDjMapper;
    ApplicationContentMapper applicationContentMapper;
    AppointmentPatientMapper appointmentPatientMapper;
    WechatPushTemplateMapper wechatPushTemplateMapper;
    ApplicationTemplateMapper applicationTemplateMapper;
    ProtectionMapper protectionMapper;
    MainPackageMapper mainPackageMapper;
    ApplyListMapper applyListMapper;
    ApplyItemDetailMapper applyItemDetailMapper;
    CaCheckListMapper caCheckListMapper;
    DrugRestrictionMapper drugRestrictionMapper;
    InfuseGroupDetailMapper infuseGroupDetailMapper;
    OldRecipeDetailMapper oldRecipeDetailMapper;
    AssayReportItemMapper assayReportItemMapper;
    DrugToHospitalMapper drugToHospitalMapper;
    PsychotherapyItemMapper psychotherapyItemMapper;
    PsychotherapyApplyListMapper psychotherapyApplyListMapper;
    PsychotherapyApplyDetailMapper psychotherapyApplyDetailMapper;
    RecipeContactInfoMapper recipeContactInfoMapper;
    OldRecipeMapper oldRecipeMapper;
    WorkerMapper workerMapper;
    DepartmentMapper departmentMapper;
    WardTbAdvMapper wardTbAdvMapper;

    SysConfig sysConfig;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Autowired
    private MdmPubItemexedeptRepository mdmPubItemexedeptRepository;

    @Autowired
    private MdmPubFeecategoryexedeptRepository mdmPubFeecategoryexedeptRepository;

    @Override
    @DatabaseAnnotation
    public Date getDate() {
        return sysFunctionMapper.getDate();
    }

    @Override
    @DatabaseAnnotation
    public long getGzdMaxId() {
        return currencyMapper.getGzdMaxId();
    }

    @Override
    @DatabaseAnnotation
    public Integer deleteRecipeByIds(List<Long> ids, Integer hospitalCode) {
        final Weekend<Recipe> weekend = new Weekend<>(Recipe.class);
        weekend.weekendCriteria()
                .andIn(Recipe::getRecipeId, ids)
                .andEqualTo(Recipe::getHospitalCode, hospitalCode);
        return recipeMapper.deleteByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public Integer deleteRecipeById(Long recipeId, Integer hospitalCode) {
        final Weekend<Recipe> weekend = new Weekend<>(Recipe.class);
        weekend.weekendCriteria()
                .andEqualTo(Recipe::getRecipeId, recipeId)
                .andEqualTo(Recipe::getHospitalCode, hospitalCode);
        return recipeMapper.deleteByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public Boolean deleteRecipeByReceptionNoAndRecipeCategory(Long receptionNo, Integer hospitalCode, Integer recipeCategory) {
        final Weekend<Recipe> weekend = new Weekend<>(Recipe.class);
        weekend.weekendCriteria().andEqualTo(Recipe::getReceptionNo, receptionNo)
                .andEqualTo(Recipe::getRecipeCategory, recipeCategory)
                .andEqualTo(Recipe::getHospitalCode, hospitalCode);
        return recipeMapper.deleteByExample(weekend) > 0;
    }

    @Override
    @DatabaseAnnotation
    public Recipe getRecipeByReceptionNoAndRecipeCategory(Long receptionNo, Integer hospitalCode, Integer recipeCategory) {
        final Weekend<Recipe> weekend = new Weekend<>(Recipe.class);
        weekend.weekendCriteria().andEqualTo(Recipe::getReceptionNo, receptionNo)
                .andEqualTo(Recipe::getRecipeCategory, recipeCategory).andEqualTo(Recipe::getHospitalCode, hospitalCode);
        return recipeMapper.selectOneByExample(weekend);
    }

    /**
     * 根据ID查询处方
     *
     * @param recipeNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public Recipe getRecipeById(Long recipeNo, Integer hospitalCode) {
        Recipe recipe = recipeMapper.getRecipeById(recipeNo, hospitalCode, null);
        if (recipe == null) {
            recipe = recipeMapper.getRecipeById(recipeNo, hospitalCode, "MZYS_TB_MZCF_DATA");
            if (recipe != null) {
                recipe.setDataFlag(2);
            }
        } else {
            recipe.setDataFlag(1);
        }
        return recipe;
    }

    @Override
    @DatabaseAnnotation
    public List<Recipe> getRecipeByIds(List<Long> recipeIds, Integer hospitalCode) {
        final Weekend<Recipe> weekend = new Weekend<>(Recipe.class);
        weekend.weekendCriteria().andIn(Recipe::getRecipeId, recipeIds)
                .andEqualTo(Recipe::getHospitalCode, hospitalCode);
        return recipeMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<Recipe> getRecipeByReceptionNoAndFirstDoctorId(Integer operateType, Long receptionNo, Integer firstDoctorId, Integer hospitalCode) {
        List<String> feeCategoryList = new ArrayList<>();
        if (operateType.equals(1)) {
            feeCategoryList = Arrays.asList("12", "13", "6");
        } else {
            feeCategoryList = Arrays.asList("12", "13");
        }
        Weekend<Recipe> weekend = new Weekend<>(Recipe.class);
        weekend.weekendCriteria().andEqualTo(Recipe::getReceptionNo, receptionNo)
                .andEqualTo(Recipe::getFirstDoctorId, firstDoctorId)
                .andEqualTo(Recipe::getHospitalCode, hospitalCode)
                .andIn(Recipe::getFeeCategory, feeCategoryList);
        return recipeMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<Recipe> getRecipeByType(Long receptionNo, Integer feeCategory, Integer recipeCategory, Integer hospitalCode) {
        List<Recipe> recipes = new ArrayList<>();
        //非药品
        if (feeCategory != null) {
            recipes = recipeMapper.getRecipeByType(receptionNo, feeCategory, recipeCategory, hospitalCode, null);
        }
        //药品
        else {
            recipes = recipeMapper.getRecipeByTypeBy(receptionNo, recipeCategory, hospitalCode);
        }
        for (Recipe recipe : recipes) {
            recipe.setDataFlag(1);
        }
        return recipes;
    }

    @Override
    @DatabaseAnnotation
    @Transactional(rollbackFor = {Exception.class})
    public boolean saveRecipe(Recipe recipe) {
        boolean success = recipeMapper.saveRecipe(recipe);
        if (!success) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        if (recipe.getRecipeDetails() != null) {
            for (RecipeDetail recipeDetail : recipe.getRecipeDetails()) {
                /**
                 //通过主从表方式保存时，使从表中相关字段与主表保持一致
                 recipeDetail.setRecipeNo(recipe.getRecipeNo());
                 recipeDetail.setRecipeId(recipe.getRecipeId());
                 recipeDetail.setReceptionNo(recipe.getReceptionNo());
                 recipeDetail.setRegNo(recipe.getRegNo());
                 recipeDetail.setFirstDate(recipe.getFirstDate());
                 recipeDetail.setFirstDoctorId(recipe.getFirstDoctorId());
                 recipeDetail.setLastDate(recipe.getLastDate());
                 recipe.setLastDoctorId(recipe.getLastDoctorId());
                 if (recipeDetail.getItemNo() == null) {
                 Integer maxSerialNumber = getRecipeMaxSerialNumberNext(recipeDetail.getFeeCategory(), recipeDetail.getReceptionNo(), recipeDetail.getHospitalCode());
                 recipeDetail.setItemNo(maxSerialNumber);
                 }
                 */
                //这里查一下，用于前面逻辑不正确时的防护。有必要时，一起查出来
                RecipeDetail recipeDetail1 = recipeDetailMapper.selectByPrimaryKey(recipeDetail);
                if (recipeDetail1 != null) {
                    recipeDetail.setDataFlag(1);
                }

                success = recipeDetailMapper.saveRecipeDetail(recipeDetail);
                if (!success) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return false;
                }
            }
            //处方外配，数据处理，插入上传中间表 Start
            if(sysConfig.getConfig(SystemNo.OUTPATIENT, SysConfigKey.JZWP, String.valueOf(recipe.getHospitalCode())).equals("1")
                    && recipe.getRecipeCategory().equals(RecipeTypeEnum.TAKE_OUT.getRecipeType())
                    && ItemCategoryEnum.isDrug(recipe.getRecipeDetails().get(0).getFeeCategory())){
                try{
                    int count = this.recipeMapper.getInsuranceOutRecipeCount(
                            String.valueOf(recipe.getReceptionNo())
                            , String.valueOf(recipe.getRecipeNo())
                            , recipe.getHospitalCode());
                    if(count <= 0){
                        RegisterList registerList = registerListRepository
                                .getRegisterByRegNo(recipe.getRegNo(),recipe.getHospitalCode());
                        InsuranceOutRecipeDto pInsuranceOutRecipeDto = new InsuranceOutRecipeDto();
                        pInsuranceOutRecipeDto.setReceptionNo(String.valueOf(recipe.getReceptionNo()));
                        pInsuranceOutRecipeDto.setCardNo(registerList != null ? registerList.getCardNo() : "");
                        pInsuranceOutRecipeDto.setPatId(registerList != null ? String.valueOf(registerList.getPatID()) : "");
                        Worker pWorker =  workerMapper.getWorkerById(recipe.getFirstDoctorId(),recipe.getHospitalCode());
                        pInsuranceOutRecipeDto.setDoctorName(pWorker != null ? pWorker.getName() : "");
                        pInsuranceOutRecipeDto.setDoctorId(recipe.getFirstDoctorId());
                        pInsuranceOutRecipeDto.setDeptId(registerList != null ? registerList.getDeptID() : 0);
                        Department pDepartment = null;
                        if(registerList != null){
                            pDepartment = departmentMapper.getDepartmentByDeptId(registerList.getDeptID(),recipe.getHospitalCode());
                        }
                        pInsuranceOutRecipeDto.setDeptName(pDepartment != null ? pDepartment.getDeptName() : "");
                        pInsuranceOutRecipeDto.setRecipeNo(String.valueOf(recipe.getRecipeNo()));
                        pInsuranceOutRecipeDto.setRecipeStatus(0);
                        pInsuranceOutRecipeDto.setStatus(0);
                        pInsuranceOutRecipeDto.setCreDoctorId(recipe.getFirstDoctorId());
                        pInsuranceOutRecipeDto.setCreTime(recipe.getFirstDate());
                        pInsuranceOutRecipeDto.setHospitalCode(recipe.getHospitalCode());
                        int retNum = recipeMapper.insertInsuranceOutRecipeDto(pInsuranceOutRecipeDto);
                        if(retNum <= 0){
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return false;
                        }
                    }
                }catch (Exception ex){
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return false;
                }
            }
            //处方外配，数据处理，插入上传中间表 End
        }

        return true;
    }

    @Override
    @DatabaseAnnotation
    @Transactional(rollbackFor = {Exception.class})
    public boolean saveRecipes(List<Recipe> recipe) {
        boolean success = false;
        for (Recipe entity : recipe) {
            success = saveRecipe(entity);
            if (!success) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return false;
            }
        }
        return success;
    }

    @Override
    public DoctorFeeQuotaVo getDoctorDayFeeQuota(Integer doctorId, Integer hospitalCode) {
        Date date = sysFunctionMapper.getDate();
        DoctorFeeQuotaVo doctorDayFeeQuota = recipeMapper.getDoctorDayFeeQuota(doctorId, date, hospitalCode);
        doctorDayFeeQuota = Optional.ofNullable(doctorDayFeeQuota).orElse(new DoctorFeeQuotaVo());
        doctorDayFeeQuota.setDefaultValue();

        BigDecimal drugAmountRatio = recipeMapper.getDoctorBasicDrugFeeQuota(doctorId, date, 0, hospitalCode);
        doctorDayFeeQuota.setDrugAmountRatio(drugAmountRatio == null ? BigDecimal.ZERO : drugAmountRatio.setScale(4, RoundingMode.HALF_UP));
        return doctorDayFeeQuota;
    }

    @Override
    public DoctorFeeQuotaVo getDoctorMonthFeeQuota(Integer doctorId, Integer hospitalCode) {
        Date date = sysFunctionMapper.getDate();
        DoctorFeeQuotaVo doctorDayFeeQuota = recipeMapper.getDoctorMonthFeeQuota(doctorId, date, hospitalCode);
        doctorDayFeeQuota = Optional.ofNullable(doctorDayFeeQuota).orElse(new DoctorFeeQuotaVo());
        doctorDayFeeQuota.setDefaultValue();

        BigDecimal drugAmountRatio = recipeMapper.getDoctorBasicDrugFeeQuota(doctorId, date, 1, hospitalCode);
        doctorDayFeeQuota.setDrugAmountRatio(drugAmountRatio == null ? BigDecimal.ZERO : drugAmountRatio.setScale(4, RoundingMode.HALF_UP));
        return doctorDayFeeQuota;
    }

    @Override
    @DatabaseAnnotation
    public List<Recipe> getRecipeByReceptionNo(Long receptionNo, Integer itemCategory, Integer hospitalCode) {
        final Recipe entity = new Recipe();
        entity.setReceptionNo(receptionNo);
        entity.setHospitalCode(hospitalCode);
        if (itemCategory != null && itemCategory > 0) {
            entity.setFeeCategory(itemCategory);
        }
        return recipeMapper.select(entity);
    }

    @Override
    @DatabaseAnnotation
    public List<Recipe> getRecipeByReceptionNoTwo(Set<Long> receptionNo, List<Integer> itemCategoryList, Integer hospitalCode) {
        Weekend<Recipe> weekend = new Weekend<>(Recipe.class);
        WeekendCriteria<Recipe, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(Recipe::getReceptionNo, receptionNo)
                .andEqualTo(Recipe::getHospitalCode, hospitalCode);
        if (itemCategoryList.size() > 0) {
            weekendCriteria.andIn(Recipe::getFeeCategory, itemCategoryList);
        }
        return recipeMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<WardTbAdv> getHistoryWardAdv(Long regNo, Integer hospitalCode) {
        Weekend<WardTbAdv> weekend = new Weekend<>(WardTbAdv.class);
        WeekendCriteria<WardTbAdv, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andEqualTo(WardTbAdv::getHospitalId,hospitalCode).andEqualTo(WardTbAdv::getRegNo,regNo)
                .andEqualTo(WardTbAdv::getAdvKind,"出院带药")
                .andNotEqualTo(WardTbAdv::getUpstatus,2);
        List<WardTbAdv> wardTbAdvList = wardTbAdvMapper.selectByExample(weekend);
        return wardTbAdvList;
    }

    @Override
    @DatabaseAnnotation
    public WardTbAdv getHistoryWardAdvById(Integer advId, Integer hospitalCode) {
        WardTbAdv wardTbAdv = wardTbAdvMapper.selectByPrimaryKey(advId);
        return wardTbAdv;
    }

    @Override
    @DatabaseAnnotation
    public Long getSequence(SequenceEnum sequenceEnum) {
        //手动创建事务
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        //获取事务状态
        TransactionStatus status = platformTransactionManager.getTransaction(def);
        //避免调用时生成主键重复问题
        Long getSequences = sysFunctionMapper.getGetSequences(sequenceEnum);
        //提交事务
        platformTransactionManager.commit(status);
        return getSequences;
    }

    @Override
    @DatabaseAnnotation
    public List<Long> getSequenceList(Integer num, SequenceEnum sequenceEnum) {
        return sysFunctionMapper.getGetSequencesList(num,sequenceEnum);
    }

    @Override
    @DatabaseAnnotation
    public Integer getRecipeMaxGroupNo(Long receptionNo, Integer hospitalCode) {
        Integer groupNo = recipeDetailMapper.getRecipeMaxGroupNo(receptionNo, hospitalCode, "");
        if (groupNo == -1) {
            //如果主表没有数据，查看数据是否被转移到迁移表
            groupNo = recipeDetailMapper.getRecipeMaxGroupNo(receptionNo, hospitalCode, "MZYS_TB_MZCFMX_DATA");
        }
        if (groupNo == -1) {
            groupNo = 0;
        }
        return groupNo;
    }

    @Override
    @DatabaseAnnotation
    public Integer getRecipeMaxChangeRecipeFlag(Long receptionNo, Integer hospitalCode) {
        Integer ChangeRecipeFlag = recipeDetailMapper.getRecipeMaxChangeRecipeFlag(receptionNo, hospitalCode, "");
        if (ChangeRecipeFlag == -1) {
            //如果主表没有数据，查看数据是否被转移到迁移表
            ChangeRecipeFlag = recipeDetailMapper.getRecipeMaxChangeRecipeFlag(receptionNo, hospitalCode, "MZYS_TB_MZCFMX_DATA");
        }
        if (ChangeRecipeFlag == -1) {
            ChangeRecipeFlag = 0;
        }
        return ChangeRecipeFlag;
    }


    /**
     * 计算药品处方
     *
     * @param recipe
     * @param preRecipe
     * @param updateProperty
     */
    @Override
    @DatabaseAnnotation
    public void calcDrugRecipe(PreRecipeDetail recipe, PreRecipeDto preRecipe, SystemTbPubItems dic, String updateProperty) {
        if (ItemCategoryEnum.notDrug(dic.getItemCategory())) {
            return;
        }
        DrugFrequency freq = drugFrequencyMapper.getDrugFrequencyById(recipe.getFrequency(), recipe.getHospitalCode());
        Integer days = recipe.getDays();
        boolean qtyFlag = false;
        boolean dayFlag = false;
        //标记是否特殊用法
        boolean specialUsageFlag = recipe.whetherSpecialUsage();

        //每天使用剂量
        BigDecimal qty = null;
        //修改剂量，频率、如果天数大于0， 需要重新计算数量
        //修改剂量单位，计算需要重新计算剂量
        //修改数量，需要重新计算天数
        switch (updateProperty) {
            case "剂量":
            case "频率":
            case "天数":
                qtyFlag = true;
                break;
            case "数量":
                dayFlag = true;
                break;
            case "剂量单位":
                //如果剂量单位不为空，且剂量不为0则重新计算剂量
                BigDecimal dose;
                // 精总开具药品，切换单位自动计算剂量开关 1打开 0关闭 【目前默认关闭】
                if ("1".equals(sysConfig.getConfig(SystemNo.OUTPATIENT, SysConfigKey.UNIT_FLAG, String.valueOf(recipe.getHospitalCode())))) {
                    if (!specialUsageFlag) {
                        //如果是特殊用法，则切换剂量单位不用重新计算剂量
                        dose = Converter.toDecimal(recipe.getDose());
                        if (!StringUtils.isEmpty(recipe.getDoseUnit()) && !ObjectUtils.compare(dose, new BigDecimal("0"))) {

                            if (recipe.getDoseUnit().equals(dic.getDosageUnit()) && preRecipe.getDoseUnit().equals(StringUtils.trim(dic.getWardUnit()))) {
                                //剂量单位转病区单位
                                dose = dose.divide(dic.getDosage(), 1, RoundingMode.CEILING);
                            } else if (recipe.getDoseUnit().equals(dic.getWardUnit()) && preRecipe.getDoseUnit().equals(StringUtils.trim(dic.getDosageUnit()))) {
                                //病区单位转剂量单位
                                dose = dose.multiply(dic.getDosage());
                            }
                            recipe.setDose(Converter.toString(dose));
                            dayFlag = true;
                        }
                    }
                }
                qtyFlag = true;
                recipe.setDoseUnit(preRecipe.getDoseUnit());
                break;
            default:
                return;
        }

        //按照当前剂量计算平均每天使用数量
        //如果是特殊用法，则拆分用法
        if (specialUsageFlag) {
            BigDecimal dose = recipe.specialUsageSum();
            qty = dose;
            if (ObjectUtils.compare(dose, BigDecimal.ZERO)) {
                throw new IllegalArgumentException("请输入有效的剂量!");
            }
            recipe.setFrequency(-1);
        } else {
            if (recipe.getFrequency() != null && recipe.getFrequency() == -1) {
                recipe.setFrequency(null);
            }
            if (freq == null) {
                return;
            }
            qty = Converter.toDecimal(recipe.getDose())
                    .multiply(Converter.toDecimal(freq.getCount()))
                    .divide(Converter.toDecimal(freq.getType()), 7, RoundingMode.DOWN);
        }

        if (!specialUsageFlag) {
            if (freq == null || days == null || days == 0) {
                return;
            }
        } else {
            if (days == null || days == 0) {
                return;
            }
        }

        //换算为门诊包装
        if (!StringUtils.isEmpty(recipe.getDoseUnit()) && StringUtils.trim(recipe.getDoseUnit()).equals(StringUtils.trim(dic.getDosageUnit()))) {
            qty = qty.divide(Converter.toDecimal(dic.getDosage()), 7, RoundingMode.DOWN)
                    .divide(Converter.toDecimal(dic.getClinicQty()), 7, RoundingMode.DOWN);
        } else if (!StringUtils.isEmpty(recipe.getDoseUnit()) && StringUtils.trim(recipe.getDoseUnit()).equals(StringUtils.trim(dic.getWardUnit()))) {
            qty = qty.divide(Converter.toDecimal(dic.getClinicQty()), 7, RoundingMode.DOWN);
        }

        //修改修改总量字段
        if (qtyFlag && days > 0) {
            //总量
            qty = qty.multiply(Converter.toDecimal(days));
            //门诊包装向上取整
            BigDecimal quantity = qty.setScale(0, RoundingMode.UP);
            recipe.setQuantity(quantity);
            recipe.setClincUnitNum(-1);
        }
        /*需要修改天数
        if (dayFlag) {
            //计算
            Integer day = recipe.getQuantity().divide(qty, 6, RoundingMode.DOWN).setScale(0, RoundingMode.UP).intValue();
            recipe.setDays(day);
        }*/
    }

    //<editor-fold desc="处方前置校验，整理处方">



    @Override
    public void calcRecipeQty(PreRecipeDetail recipe, ChargeItem dic)
    {
        if (ItemCategoryEnum.notDrug(dic.getItemCategory())) {
            return;
        }
        Log.info("开始重新计算处方数量" + recipe.getItemName());
        Integer hospitalCode=1;
        if(recipe.getHospitalCode()!=null) hospitalCode = recipe.getHospitalCode();
        DrugFrequency freq = drugFrequencyMapper.getDrugFrequencyById(recipe.getFrequency(), hospitalCode);
        Integer days = recipe.getDays();
        boolean qtyFlag = false;
        boolean dayFlag = false;
        //标记是否特殊用法
        boolean specialUsageFlag = recipe.whetherSpecialUsage();
        //每天使用剂量
        BigDecimal qty = null;
        BigDecimal dose;
        // 精总开具药品，切换单位自动计算剂量开关 1打开 0关闭 【目前默认关闭】
        if ("1".equals(sysConfig.getConfig(SystemNo.OUTPATIENT, SysConfigKey.UNIT_FLAG, String.valueOf(hospitalCode)))) {
            if (!specialUsageFlag) {
                //如果是特殊用法，则切换剂量单位不用重新计算剂量
                dose = Converter.toDecimal(recipe.getDose());
                if (!StringUtils.isEmpty(recipe.getDoseUnit()) && !ObjectUtils.compare(dose, new BigDecimal("0"))) {

                    if (recipe.getDoseUnit().equals(dic.getDosageUnit()) && recipe.getDoseUnit().equals(StringUtils.trim(dic.getWardUnit()))) {
                        //剂量单位转病区单位
                        dose = dose.divide(dic.getDosage(), 1, RoundingMode.CEILING);
                    } else if (recipe.getDoseUnit().equals(dic.getWardUnit()) && recipe.getDoseUnit().equals(StringUtils.trim(dic.getDosageUnit()))) {
                        //病区单位转剂量单位
                        dose = dose.multiply(dic.getDosage());
                    }
                    recipe.setDose(Converter.toString(dose));
                    dayFlag = true;
                }
            }
        }
        qtyFlag = true;
        //按照当前剂量计算平均每天使用数量
        //如果是特殊用法，则拆分用法
        if (specialUsageFlag) {
            BigDecimal doseSpecial = recipe.specialUsageSum();
            qty = doseSpecial;
            if (ObjectUtils.compare(doseSpecial, BigDecimal.ZERO)) {
                throw new IllegalArgumentException("请输入有效的剂量!");
            }
            recipe.setFrequency(-1);
        } else {
            if (recipe.getFrequency() != null && recipe.getFrequency() == -1) {
                recipe.setFrequency(null);
            }
            if (freq == null) {
                return;
            }
            qty = Converter.toDecimal(recipe.getDose())
                    .multiply(Converter.toDecimal(freq.getCount()))
                    .divide(Converter.toDecimal(freq.getType()), 7, RoundingMode.DOWN);
        }
        if (freq == null || days == null || days == 0) {
            return;
        }

        try {
            //换算为门诊包装
            if (!StringUtils.isEmpty(recipe.getDoseUnit()) && StringUtils.trim(recipe.getDoseUnit()).equals(StringUtils.trim(dic.getDosageUnit()))) {
                qty = qty.divide(Converter.toDecimal(dic.getDosage()), 7, RoundingMode.DOWN)
                        .divide(Converter.toDecimal(dic.getClinicQty()), 7, RoundingMode.DOWN);
            } else if (!StringUtils.isEmpty(recipe.getDoseUnit()) && StringUtils.trim(recipe.getDoseUnit()).equals(StringUtils.trim(dic.getWardUnit()))) {
                qty = qty.divide(Converter.toDecimal(dic.getClinicQty()), 7, RoundingMode.DOWN);
            }
        }catch (Exception ex){
            if(ex.getMessage().indexOf("zero") > -1 || ex.getMessage().indexOf("零") > -1){
                throw new RuntimeException(recipe.getItemName() + "计量单位换算失败");
            }
        }

        //修改修改总量字段
        if (qtyFlag && days > 0) {
            //总量
            qty = qty.multiply(Converter.toDecimal(days));
            //门诊包装向上取整
            BigDecimal quantity = qty.setScale(0, RoundingMode.UP);
            Log.info("结束重新计算处方数量" + quantity);
            recipe.setQuantity(quantity);
            recipe.setClincUnitNum(-1);
        }
    }



    public void batchChargeItemDealBigList(Integer hospitalCode, RetMsg noRecipeRet, Long preSaveNoNew, Date date, List<PreRecipeDetail> res) {

        MAssert.massertListNotNull(res, noRecipeRet);
        // XXX: yutao 2023/4/18 本查询只用到了 res中元素的 itemCode和ExeDept
        List<Pair<String, SystemTbPubItems>> systemTbPubItems = systemTbPubItemsRepository.queryChargeItemByItemCodeList(res, hospitalCode, null);
        MAssert.massertListNotNull(systemTbPubItems, noRecipeRet);

        MAssert.massertEqual(systemTbPubItems.size(), res.size(), noRecipeRet);

        Map<String, SystemTbPubItems> systemTbPubItemsMap = new HashMap<>();
        for (Pair<String, SystemTbPubItems> stp : systemTbPubItems) {
            systemTbPubItemsMap.put(stp.getKey(), stp.getValue());
        }
        List<Integer> resItemNoList = res.stream().map(PreRecipeDetail::getItemNo).collect(Collectors.toList());

        List<SpecialItems> specialItemList = systemTbPubItemsRepository.querySpecialItemByIdList(resItemNoList, hospitalCode);
        Map<String, SpecialItems> specialItemMap = new HashMap<>();
        specialItemList.forEach(p -> specialItemMap.put(p.getItemCode() + "_" + p.getHospitalCode(), p));

        // xxx: yutao 2023/4/19 导入时，有没有相同科室开了多次同一种药的情况？或者不同科室开了相同的药，是不是会有问题？
        for (PreRecipeDetail preRecipeDetail : res) {
            preRecipeDetail.setPreSaveNo(preSaveNoNew);
            DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
            fillNewPreData(SequenceEnum.RECIPE_DETAIL_NO, preRecipeDetail, date);

            //SystemTbPubItems chargeItem = systemTbPubItemsRepository.getChargeItemById(preRecipeDetail.getItemCode(),hospitalCode,null);
            SystemTbPubItems chargeItem = systemTbPubItemsMap.get(preRecipeDetail.getItemCode() + "_" + preRecipeDetail.getExecDept());
            MAssert.massertNotNull(chargeItem, noRecipeRet);
//            ChargeItem chargeItem = chargeItemRepository.getChargeItemById(preRecipeDetail.getItemCode(), hospitalCode);


            // XXX: yutao 2023/4/19 下面没有查询了，从上面生成的map中直接取
            chargeItem = systemTbPubItemsRepository.chargeItemBatchTrim(chargeItem, specialItemMap);
            preRecipeDetail.ChangeChargeItemPart(chargeItem);
            ChargeItem calcItem = new ChargeItem();
            BeanUtils.copyProperties(chargeItem, calcItem);
            calcRecipeQty(preRecipeDetail, calcItem);
        }
    }

    public void batchChargeItemDeal(Integer hospitalCode, RetMsg noRecipeRet, Long preSaveNoNew, Date date, List<PreRecipeDetail> res) {
        for (PreRecipeDetail preRecipeDetail : res) {
            preRecipeDetail.setPreSaveNo(preSaveNoNew);
            DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
            fillNewPreData(SequenceEnum.RECIPE_DETAIL_NO, preRecipeDetail, date);

            //SystemTbPubItems chargeItem = systemTbPubItemsRepository.getChargeItemById(preRecipeDetail.getItemCode(),hospitalCode,null);
            // FIXME: yutao 2023/4/16 NOW! 这里会有很大影响，循环中多表关联sql
            SystemTbPubItems chargeItem = systemTbPubItemsRepository.getChargeItemByIdIsStopped(preRecipeDetail.getItemCode(), preRecipeDetail.getExecDept(), hospitalCode, null);
            MAssert.massertNotNull(chargeItem, noRecipeRet);
//            ChargeItem chargeItem = chargeItemRepository.getChargeItemById(preRecipeDetail.getItemCode(), hospitalCode);


            // FIXME: yutao 2023/4/16 循环中单表sql
            chargeItem = systemTbPubItemsRepository.chargeItemTrim(chargeItem); //这实现..坑
            preRecipeDetail.ChangeChargeItemPart(chargeItem);
            ChargeItem calcItem = new ChargeItem();
            BeanUtils.copyProperties(chargeItem, calcItem);
            calcRecipeQty(preRecipeDetail, calcItem);
        }
    }

    private void fillNewPreData(SequenceEnum recipeDetailNo, PreRecipeDetail preRecipeDetail, Date date) {
        preRecipeDetail.setRecipeDetailNo(sysFunctionMapper.getGetSequences(recipeDetailNo));
        preRecipeDetail.setRecipeNo(0L);
        preRecipeDetail.setRecipeStatus(RecipeStatusEnum.UNREVIEWED.getCode());
        preRecipeDetail.setOpFlag(0);
        preRecipeDetail.setGroupNo(0);
        preRecipeDetail.setOpTime(date);
    }

    private DrugToHospital queryOneDrugToHospital(Integer oldRecipeDetail, RetMsg commRet, Integer hospitalCode) {
        DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
        List<DrugToHospital> drugToHospitalList = drugToHospitalMapper.getDrugToHospital(oldRecipeDetail);
        MAssert.massertNotTrue(drugToHospitalList.size() <= 0, commRet);

        List<DrugToHospital> drugToHospitalListHosp = drugToHospitalList.stream().filter(p -> p.getHospitalId().equals(hospitalCode)).collect(Collectors.toList());
        MAssert.massertNotTrue(drugToHospitalListHosp.size() <= 0, commRet);
        return drugToHospitalListHosp.get(0);
    }



    /**
     * 根据申请单加载处方明细
     *
     * @param record
     * @param applyList
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailsByApply(ReceptionRecord record, ApplyList applyList) {
        List<RecipeDetail> res = new ArrayList<>();
        Date date = sysFunctionMapper.getDate();
        //根据检查流水号加载出此申请单所有对应的明细
        List<RecipeDetail> recipeDetailList = recipeDetailRepository.getRecipeDetailByExamineNo(applyList.getId(), applyList.getHospitalId());
        Integer execDept = 0;
        if (StrUtil.isNotBlank(applyList.getExeDeptId())) {
            execDept = Arrays.stream(applyList.getExeDeptId().split(",")).mapToInt(Converter::toInt32).findFirst().orElse(0);
        }
        for (ApplyDetail applyDetail : applyList.getApplyDetailList()) {
            //匹配结果集，如果传进来的申请单中没有这条明细，标记为删除，如果不存在，则新增
            if (applyDetail.getApplyDetailCostList() == null) {
                applyDetail.setApplyDetailCostList(new ArrayList<>());
            }
            recipeDetailList.stream().filter(recipeDetail -> ObjectUtils.compare(recipeDetail.getExamineDetailNo(), applyDetail.getId())).forEach(recipeDetail -> {
                long count = applyDetail.getApplyDetailCostList().stream().filter(p -> ObjectUtils.compare(p.getId(), recipeDetail.getExamineItemDetailNo())).count();
                if (count == 0) {
                    recipeDetail.setDataFlag(-1);
                }
            });

            List<Integer> itemIds = applyDetail.getApplyDetailCostList().stream().map(ApplyDetailCost::getPayItemId).collect(Collectors.toList());
            List<ChargeItem> chargeItems = chargeItemRepository.getChargeItemByIdsAndInitSpec(itemIds, applyList.getHospitalId());

            Integer firstDoctor = applyList.getApplyUserId();
            Date firstDate = date;
            if (CollUtil.isNotEmpty(recipeDetailList)) {
                firstDate = recipeDetailList.get(0).getFirstDate();
                firstDoctor = recipeDetailList.get(0).getFirstDoctorId();
            }

            for (ApplyDetailCost applyDetailCost : applyDetail.getApplyDetailCostList()) {
                //检查库中已经存在数据，如果存在修改，否则新增
                List<RecipeDetail> list = recipeDetailList.stream().filter(p -> ObjectUtils.compare(p.getExamineItemDetailNo(), applyDetailCost.getId())).collect(Collectors.toList());
                ChargeItem chargeItem = chargeItems.stream().filter(p -> ObjectUtils.compare(applyDetailCost.getPayItemId(), p.getItemCode())).findFirst().orElse(null);
                if (ObjectUtil.isEmpty(chargeItem)) {
                    Log.info("检查申请明细关联的收费项目不存在");
                    continue;
                }
                if (CollUtil.isEmpty(list)) {
                    //根据明细，生成处方信息
                    DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
                    RecipeDetail recipeDetail = new RecipeDetail();
                    recipeDetail.setRecipeDetailId(sysFunctionMapper.getGetSequences(SequenceEnum.RECIPE_DETAIL_NO));
                    recipeDetail.setRecipeDetailNo(recipeDetail.getRecipeDetailId());
                    recipeDetail.ChangeChargeItem(chargeItem);
                    recipeDetail.setApplicationForm(applyList.getBigForm().getName());
                    recipeDetail.setExamineNo(Converter.toString(applyList.getId()));
                    recipeDetail.setExamineDetailNo(applyDetail.getId());
                    recipeDetail.setExamineItemDetailNo(applyDetailCost.getId());
                    recipeDetail.setPreSaveNo(new Long("-1"));
                    recipeDetail.setExamineCode(applyDetail.getItemCode());
                    recipeDetail.setExamineName(applyDetail.getItemName());
                    recipeDetail.setFirstDoctorId(firstDoctor);
                    recipeDetail.setFirstDate(firstDate);
                    recipeDetail.setLastDoctorId(applyList.getApplyUserId());
                    recipeDetail.setLastDate(date);
                    recipeDetail.setDataFlag(0);
                    recipeDetail.setStatus(0);
                    recipeDetail.setExamineFlag(1);
                    //执行科室
                    recipeDetail.setExecDept(execDept);
                    if (chargeItem.getIsSet() != null && chargeItem.getIsSet() == 1) {
                        recipeDetail.setPackageFlag(1);
                    }
                    // 药品属性
                    if (Stream.of(12, 13).collect(Collectors.toList()).contains(recipeDetail.getFeeCategory())) {
                        recipeDetail.setFrequency(3);
                        recipeDetail.setUsage(applyDetailCost.getUsage());
                        recipeDetail.setDays(1);
                        recipeDetail.setRecipeStatus(0);
                        recipeDetail.setUnit(chargeItem.getClinicUnit());
                        recipeDetail.setDose(String.valueOf(chargeItem.getDosage()));
                        recipeDetail.setDoseUnit(chargeItem.getDosageUnit());

                        //药品执行药房设置 默认门诊西药房
                        recipeDetail.setExecDept(1064);
                        Log.info("检查申请明细关联的药品设置执行科室");
                        String curTime = new SimpleDateFormat("HH:mm:ss").format(new Date());
                        //获取收费项目设置的执行科室
                        List<MdmPubItemexedept> itemExeDepts = mdmPubItemexedeptRepository.getItemExecDeptByTime(applyDetailCost.getPayItemCode(),
                                curTime, applyList.getApplyDeptId(), applyList.getHospitalId());
                        if (CollUtil.isNotEmpty(itemExeDepts)) {
                            Log.info("获取收费项目设置的执行科室：{}", itemExeDepts);
                            recipeDetail.setExecDept(itemExeDepts.get(0).getExecDept());
                        } else {
                            List<MdmPubFeecategoryexedept> feeCategoryExeDepts = mdmPubFeecategoryexedeptRepository.getFeeCategoryExecDeptByTime(recipeDetail.getFeeCategory(),
                                    curTime, applyList.getApplyDeptId(), applyList.getHospitalId());
                            if (CollUtil.isNotEmpty(feeCategoryExeDepts)) {
                                Log.info("获取收费类型设置的执行科室：{}", feeCategoryExeDepts);
                                recipeDetail.setExecDept(feeCategoryExeDepts.get(0).getExecDept());
                            }
                        }
                    }
                    recipeDetail.setQuantity(applyDetailCost.getNum().multiply(applyDetail.getNum()));
                    //等于2需要向下取整
                    if(ObjectUtil.equal(2,applyDetailCost.getChargeType())){
                        recipeDetail.setAmount(recipeDetail.getQuantity().multiply(recipeDetail.getPrice()).setScale(0, RoundingMode.DOWN));
                    }else {
                        recipeDetail.setAmount(recipeDetail.getQuantity().multiply(recipeDetail.getPrice()).setScale(2, RoundingMode.HALF_UP));
                    }
                    res.add(recipeDetail);
                } else {
                    for (RecipeDetail recipeDetail : list) {
                        recipeDetail.setQuantity(applyDetailCost.getNum().multiply(applyDetail.getNum()));
                        recipeDetail.setAmount(recipeDetail.getQuantity().multiply(recipeDetail.getPrice()).setScale(2, RoundingMode.HALF_UP));
                        recipeDetail.setDataFlag(1);
                        //执行科室
                        recipeDetail.setExecDept(execDept);
                    }
                    res.addAll(list);
                }

            }
            for (RecipeDetail recipeDetail : res) {
                recipeDetail.setReceptionNo(record.getReceptionNo());
                recipeDetail.setRegNo(record.getRegNo());
                recipeDetail.setHospitalCode(record.getHospitalCode());
                recipeDetail.setApplyGroupId(applyList.getGroupId());
            }
        }
        return res;
    }
    @Override
    @DatabaseAnnotation
    public List<AuditPrescriptionDto> getRecipeInfoByReceptionNo(String receptionNo, Integer doctorId, String hospitalCode) {
        return recipeMapper.getRecipeInfoByReceptionNo(receptionNo, doctorId, hospitalCode);
    }


    /**
     * 直接取同一就诊流水号的所有药，然后，java中判定，不走sql
     * @param receptionNo  就诊流水号
     * @param itemCategory 收费类型
     * @param dispensing   配药途径
     * @param hospitalCode 医院编码
     * @param itemCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public Integer getRecipeDetailCountOutOfSql(Long receptionNo, List<Integer> itemCategory, Integer dispensing, Integer hospitalCode, Integer itemCode) {
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
//        weekend.setDistinct(true);
        weekend.selectProperties("itemCode");
        weekend.weekendCriteria().andEqualTo(RecipeDetail::getReceptionNo, receptionNo);
        List<RecipeDetail> recipeDetails = recipeDetailMapper.selectByExample(weekend);
        if(recipeDetails==null||recipeDetails.size()==0){
            return 0;
        }
        int count = 0;
        for (RecipeDetail rd : recipeDetails) {
            if(rd.getItemCode()==itemCode){
                continue;
            }
            if(rd.getDispensing()!=dispensing||rd.getHospitalCode()!=hospitalCode){
                continue;
            }
            Integer feeCategory = rd.getFeeCategory();
            Boolean inCategory = false;
            for(Integer category : itemCategory){
                if(feeCategory==category){
                    inCategory = true;
                }
            }
            if(!inCategory){
                continue;
            }
            if("MECT".equals(rd.getApplicationForm())){
                continue;
            }
            count++;
        }

        return count;
    }

    @Override
    public Integer getRecipeMaxSerialNumberNext(Integer feeCategory, Long receptionNo, Integer hospitalCode) {
        Integer itemNo = recipeMapper.getRecipeMaxSerialNumberNext(feeCategory, receptionNo, hospitalCode);
        itemNo = itemNo == null ? 0 : itemNo;
        return itemNo + 1;
    }
    @Override
    @DatabaseAnnotation
    @Cacheable(cacheNames = "RisItemDj", unless = "#result == null")
    public RisItemDj getRisItemByCode(Integer itemCode, Integer hospitalCode) {
        return risItemDjMapper.getRisItemByCode(itemCode, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public Map<Integer,RisItemDj> getRisItemByCodeList(List<Integer> itemCodeList, Integer hospitalCode) {
        Weekend<RisItemDj> weekend = new Weekend<>(RisItemDj.class);
        WeekendCriteria<RisItemDj, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(RisItemDj::getHospitalCode, hospitalCode)
                .andIn(RisItemDj::getItemCode, itemCodeList);
        List<RisItemDj> risItemDjs = risItemDjMapper.selectByExample(weekend);
        Map<Integer, RisItemDj> map = new HashMap<>();
        for (RisItemDj dj : risItemDjs) {
            if (map.get(dj.getItemCode()) != null) {
                map.put(dj.getItemCode(), dj);
            }
        }
        return map;
    }

    @Override
    @DatabaseAnnotation
    public Map<Integer,RisItemDj> getAllRisItem() {
        Weekend<RisItemDj> weekend = new Weekend<>(RisItemDj.class);
        List<RisItemDj> risItemDjs = risItemDjMapper.selectByExample(weekend);
        Map<Integer, RisItemDj> map = new HashMap<>();
        for (RisItemDj dj : risItemDjs) {
            if (map.get(dj.getItemCode()) != null) {
                map.put(dj.getItemCode(), dj);
            }
        }
        return map;
    }

    /**
     * 获取未审方药品处方数量
     *
     * @param receptionNo
     */
    @Override
    @DatabaseAnnotation
    public int getRecipeCount(Long receptionNo) {
        return recipeMapper.getRecipeCount(receptionNo);
    }

    /**
     * 获取就诊流水号对应的病历数据数量
     *
     * @param receptionNo
     * @param hospId
     */
    @Override
    @DatabaseAnnotation
    public int getBlCount(Long receptionNo, Integer hospId) {
        return recipeMapper.getBlCount(receptionNo, hospId);
    }


    /**
     * 微信推送数据
     *
     * @param content
     * @param telephone
     */
    @Override
    public int insertTemplate(String content, String telephone, Integer doctorId) {
        return wechatPushTemplateMapper.insertTemplate(content, telephone, doctorId);
    }

    /**
     * 根据处方流水号获取处方类型和处方名称
     *
     * @param recipeNo
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public RecipeTypeResponse getRecipeType(Long recipeNo, Integer hospitalCode) {
        return recipeMapper.getRecipeType(recipeNo, hospitalCode);
    }

    /**
     * 根据套餐项目编码获取套餐下详情编码
     *
     * @param itemCode
     */
    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    public List<Integer> getItemCodeTail(Integer itemCode) {
        return recipeMapper.getItemCodeTail(itemCode);
    }

    /**
     * 根据套餐项目编码获取套餐下详情编码,List入参版
     *
     * @param itemCodeList
     */
    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    public Map<Integer, List<Integer>> getItemCodeTailByItemCodeList(List<Integer> itemCodeList) {
        Map<Integer, List<Integer>> map = new HashMap<>();
        List<ItemCodeRelation> itemCodeTailByItemCodeList = recipeMapper.getItemCodeTailByItemCodeList(itemCodeList);
        for (ItemCodeRelation r : itemCodeTailByItemCodeList) {
            List<Integer> integerList = null;
            if(r!=null){
                integerList = map.get(r.getCode());
            }else{
                continue;
            }

            if (integerList == null) {
                map.put(r.getCode(),new ArrayList<>());
            }
            integerList.add(r.getSubCode());
        }
        return map;
    }

    /**
     * 根据套餐项目编码获取套餐下详情编码,所有,
     * 先判断preload中有没有，没有（/超时）的话，再实际去查
     *
     */
    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    public Map<Integer, List<Integer>> getAllItemCodeTail() {
        Map<Integer, List<Integer>> allItemCodeTail = preLoadCache.getOrLoad(PreLoad.ALL_ITEM_CODE_TAIL,()->{
            Map<Integer, List<Integer>> map = new HashMap<>();
            List<ItemCodeRelation> itemCodeTailByItemCodeList = recipeMapper.getAllItemCodeTail();
            for (ItemCodeRelation r : itemCodeTailByItemCodeList) {
                List<Integer> integerList = null;
                if(r!=null){
                    integerList = map.get(r.getCode());
                }else{
                    continue;
                }

                if (integerList == null) {
                    map.put(r.getCode(),new ArrayList<>());
                }
                integerList.add(r.getSubCode());
            }
            return map;
        });
        return allItemCodeTail;
    }

    /**
     * 根据套餐项目编码获取套餐下详情编码,List入参版
     *
     * @param itemCodeList
     */
    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    public Map<Integer, List<Integer>> getItemCodeTailByItemCodeListInPreLoad(List<Integer> itemCodeList) {
        // TODO: yutao 2024/6/20 注意测试一下有值的情况
        // XXX: yutao 2024/6/20 注意防止过Map过大，占用内存的问题
        Map<Integer, List<Integer>> allItemCodeTail = preLoadCache.get(PreLoad.ALL_ITEM_CODE_TAIL);
        if(allItemCodeTail==null) {
            //直接db中查
            Map<Integer, List<Integer>> map = new HashMap<>();
            // XXX: yutao 2024/6/20 可以尝试在这里也全量加载，并写入preLoad
            List<ItemCodeRelation> itemCodeTailByItemCodeList = recipeMapper.getItemCodeTailByItemCodeList(itemCodeList);
            for (ItemCodeRelation r : itemCodeTailByItemCodeList) {
                List<Integer> integerList = null;
                if (r != null) {
                    integerList = map.get(r.getCode());
                } else {
                    continue;
                }

                if (integerList == null) {
                    map.put(r.getCode(), new ArrayList<>());
                }
                integerList.add(r.getSubCode());
            }
            return map;
        }
        Map<Integer, Integer> itemCodeMap = TyMapUtil.listToMap(itemCodeList,"this");
        Map<Integer, List<Integer>> outMap = new HashMap<>();
        for(Map.Entry et:allItemCodeTail.entrySet()){
            Integer code = (Integer) et.getKey();
            if(itemCodeMap.get(code) == null){
                continue;
            }
            outMap.put(code, (List<Integer>) et.getValue());
        }
        return outMap;
    }

    /**
     * 保存约束保护
     *
     * @param protection
     * @return
     */
    @Override
    @DatabaseAnnotation
    public Boolean saveProtection(Protection protection) {
        Date date = sysFunctionMapper.getDate();
        protection.setCreateDate(date);
        return protectionMapper.saveProtection(protection);
    }

    /**
     * 查询约束保护
     *
     * @param protection
     * @return
     */
    @Override
    @DatabaseAnnotation
    public Protection getProtection(Protection protection) {
        return protectionMapper.selectOne(protection);
    }

    /**
     * 根据套餐Id获取套餐记录
     *
     * @param itemCodes
     */
    @Override
    @DatabaseAnnotation
    public List<MainPackage> getMainPackageByPackageId(List<Integer> itemCodes) {
        return mainPackageMapper.getMainPackageByPackageId(itemCodes);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.CA)
    public boolean AddCaCheckInfo(List<CaCheckList> checkList) {
        for (CaCheckList item : checkList) {
            caCheckListMapper.insert(item);
        }
        return true;
    }

    /**
     * 根据开药项目编码获取是否是麻醉类药品
     *
     * @param itemCode     项目编码
     * @param hospitalCode 医院编码
     */
    @Override
    @DatabaseAnnotation
    public boolean isNarcotics(Integer itemCode, Integer hospitalCode, Integer restrictCode) {
        return drugRestrictionMapper.isNarcotics(itemCode, hospitalCode, restrictCode) > 0;
    }

    /**
     * 根据开药项目编码获取计数，用于判断是否是麻醉类药品
     *
     * @param itemCodeList   项目编码
     * @param hospitalCode 医院编码
     */
    @Override
    @DatabaseAnnotation
    public List<ItemCountDto> listIsNarcoticCount(List<Integer> itemCodeList, Integer hospitalCode, Integer restrictCode) {
        if (itemCodeList == null || itemCodeList.size() == 0|| itemCodeList.isEmpty()){
            return new ArrayList<>();
        }
        return drugRestrictionMapper.listIsNarcoticCount(itemCodeList, hospitalCode, restrictCode) ;
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> listDoctorRecipePartDetail(Integer hospitalCode, Integer doctorId, Long receptionNo) {
        StopWatch yusw = new StopWatch();
        yusw.start("redis init");
        yusw.stop();
        yusw.start("redis test");
        String name = (String) preLoadCache.get(PreLoad.TEST);
        yusw.stop();
        yusw.start("redis load");
//        List<RecipeDetail> recipeDetailList = (List<RecipeDetail>) preLoadCache
//                .get(PreLoad.RECIPE_DETAIL_LIST +"_"+receptionNo);

        List<RecipeDetail> recipeDetailList = preLoadCache.get(PreLoad.RECIPE_DETAIL_LIST+"_"+receptionNo);

        yusw.stop();


        List<RecipeDetail> drugRestrictionList = null;
        if(recipeDetailList == null) {
            yusw.start("load from db");
            final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
            final WeekendCriteria<RecipeDetail, Object> criteria = weekend.weekendCriteria();

            weekend.selectProperties("itemCode", "dispensing", "feeCategory", "recipeCategory", "applicationForm");
            criteria.andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                    .andEqualTo(RecipeDetail::getLastDoctorId, doctorId)
                    .andEqualTo(RecipeDetail::getReceptionNo, receptionNo);
            drugRestrictionList = recipeDetailMapper.selectByExample(weekend);
        }else{
            yusw.start("filter from redis");
            List<RecipeDetail> filteredList = recipeDetailList.stream()
                    .filter(detail -> detail.getLastDoctorId().equals(doctorId) &&
                            detail.getHospitalCode().equals(hospitalCode))
                    .collect(Collectors.toList());

            drugRestrictionList =  filteredList;
        }
        yusw.stop();
        String yuswName = "listDoctorRecipePartDetail";
        Log.info(yuswName + yusw.prettyPrint());
        Log.info(yuswName + yusw.shortSummary());
        Log.info(yuswName + "任务总耗时：" + yusw.getTotalTimeMillis());
        return drugRestrictionList;
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> listRecipePartDetail(Long receptionNo) {
            final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
            final WeekendCriteria<RecipeDetail, Object> criteria = weekend.weekendCriteria();

            weekend.selectProperties("itemCode", "dispensing", "feeCategory", "recipeCategory", "applicationForm",
                    "lastDoctorId","hospitalCode");
            criteria.andEqualTo(RecipeDetail::getReceptionNo, receptionNo);
            List<RecipeDetail> drugRestrictionList = recipeDetailMapper.selectByExample(weekend);

            return drugRestrictionList;
    }

    /**
     * 添加输液分组明细
     *
     * @param params
     */
    @Override
    @DatabaseAnnotation
    public void insertInfuseGroup(List<InfuseGroupDetail> params) {
        infuseGroupDetailMapper.insertInfuseGroup(params);
    }

    /**
     * 根据处方明细id获取输液分组信息
     *
     * @param recipeDetailNos
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public List<InfuseGroupDetail> getInfuseGroup(Set<Long> recipeDetailNos, Integer hospitalCode) {
        return infuseGroupDetailMapper.getInfuseGroup(recipeDetailNos, hospitalCode);
    }

    /**
     * 根据挂号流水号和医院编码修改打印标记为【已打印】
     *
     * @param recipeDetailNo
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public void updatePrintStatus(List<Long> recipeDetailNo, Integer hospitalCode) {
        recipeDetailMapper.updatePrintStatus(recipeDetailNo, hospitalCode);
    }

    /**
     * 获取收费项目对应标本信息
     *
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<AssayReportItem> getAssayReportItemByItemCode(Integer itemCode, Integer hospitalCode) {
        return assayReportItemMapper.getAssayReportItemByItemCode(itemCode, hospitalCode);
    }

    /**
     * 获取院区下所有收费项目对应标本信息
     *
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    @Cacheable(cacheNames = "gwAssayReportItemByHospitalcode1", unless = "#result == null")
    public List<AssayReportItem> getAssayReportItemByHospitalCode(Integer hospitalCode) {
        return assayReportItemMapper.getAssayReportItemByHospitalCode(hospitalCode);
    }
    @Override
    @DatabaseAnnotation
    public void savePrescription(String receptionNo, Long caId) {
        recipeMapper.savePrescription(receptionNo, caId);
    }

    @Override
    @DatabaseAnnotation
    public List<Integer> getCaPrescriptionId(Long caId) {
        return recipeMapper.getCaPrescriptionId(caId);
    }

    @Override
    @DatabaseAnnotation
    public List<Recipe> getRecipeInfobyReceptionNos(List<Long> receptionNos, Integer feeCategory, Integer hospitalCode) {
        if (receptionNos == null || receptionNos.size() == 0) {
            return null;
        }
        Weekend<Recipe> weekend = new Weekend<>(Recipe.class);
        weekend.weekendCriteria().andIn(Recipe::getReceptionNo, receptionNos)
                .andEqualTo(Recipe::getFeeCategory, feeCategory)
                .andEqualTo(Recipe::getHospitalCode, hospitalCode);
        weekend.orderBy("firstDate").desc();
        return recipeMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public Recipe getRecipeInfobyRecipeNo(Long recipeNo, Integer feeCategory, Integer hospitalCode) {
        Weekend<Recipe> weekend = new Weekend<>(Recipe.class);
        weekend.weekendCriteria().andEqualTo(Recipe::getRecipeNo, recipeNo).andEqualTo(Recipe::getFeeCategory, feeCategory).andEqualTo(Recipe::getHospitalCode, hospitalCode);
        return recipeMapper.selectOneByExample(weekend);
    }



    @Override
    public boolean deleteLinkagebyRrecipeNo(Long recipeNo, Integer feeCategory, Integer hospitalCode, Long receptionNo) {
        List<SystemTbPubItems> chargeItems = systemTbPubItemsRepository.getSystemTbPubItems("ZYBZLZ", hospitalCode);

        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        Weekend<Recipe> checkWeekend = new Weekend<>(Recipe.class);
        checkWeekend.weekendCriteria().andEqualTo(Recipe::getReceptionNo, receptionNo)
                .andEqualTo(Recipe::getFeeCategory, feeCategory)
                .andEqualTo(Recipe::getHospitalCode, hospitalCode);
        List<Recipe> recipeList = recipeMapper.selectByExample(checkWeekend);

        if (recipeList == null || recipeList.size() == 0) {
            Weekend<RecipeDetail> weekend1 = new Weekend<>(RecipeDetail.class);
            WeekendCriteria<RecipeDetail, Object> weekendCriteria = weekend1.weekendCriteria();
            if (chargeItems != null && chargeItems.size() > 0) {
                SystemTbPubItems systemTbPubItems = chargeItems.get(0);
                weekendCriteria.andEqualTo(RecipeDetail::getReceptionNo, receptionNo)
                        .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                        .andEqualTo(RecipeDetail::getRecipeCategory, ItemCategoryEnum.ChineseHerbMed.getCategoryCode());
                weekendCriteria.andEqualTo(RecipeDetail::getItemCode, systemTbPubItems.getItemCode());
                int countDetail = recipeDetailMapper.deleteByExample(weekend1);
                if (countDetail > 0) {
                    return true;
                }
            }
        } else {
            return false;
        }
        return false;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public void updateRecipeDetailExedept(Long recipeDetailNo, Integer exeDept, Integer exeHospitalId, Integer hospitalCode) {
        recipeMapper.updateRecipeDetailExedept(recipeDetailNo, exeDept, exeHospitalId, hospitalCode);
    }

    @Override
    public void setDefaultExeDept(PreRecipeDetail preRecipeDetail, Integer hospId, Integer itemCode, Integer feeCategory) {
        Log.info("开始设置默认执行科室: " + JSON.toJSONString(preRecipeDetail));

        List<ItemExeDeptRespVO> defaultExeDepts = queryDefaultExeDept(itemCode.toString(), feeCategory.toString(), NOT_APPLY_FORM.getCode());
        PreRecipeUtil.fillDetailExeDeptAndExehospId(preRecipeDetail, defaultExeDepts, hospId);
    }


    @Override
    public List<ItemExeDeptRespVO> queryDefaultExeDept(String itemCode, String itemCategory, Integer applyFlag) {
        ItemExeDeptReqVO itemExeDeptReqVO = new ItemExeDeptReqVO();
        itemExeDeptReqVO.setItemCode(itemCode);
        itemExeDeptReqVO.setFeeCategory(itemCategory);
        itemExeDeptReqVO.setApplyFlag(applyFlag);
        return chargeItemDomain.getItemExeDept(itemExeDeptReqVO);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public List<PrescriptionCommentDto> getPrescriptionCommentByDoctorId(Integer doctorId, String startDate, String endDate) {
        return this.recipeMapper.getPrescriptionCommentByDoctorId(doctorId, startDate, endDate);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Recipe> getRecipeByRegNo(Long regNo, Integer hospitalCode, String tableName) {
        Weekend<Recipe> weekend = new Weekend<>(Recipe.class);
        if(StrUtil.isNotBlank(tableName)){
            weekend.setTableName(tableName);
        }
        WeekendCriteria<Recipe, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andEqualTo(Recipe::getRegNo, regNo)
                .andEqualTo(Recipe::getHospitalCode, hospitalCode);
        return recipeMapper.selectByExample(weekend);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public boolean updateRecipeRegno(Long regNo, Long receptionNo, List<Long> recipeNos, Integer hospitalCode, Integer doctorId) {
        Weekend<Recipe> weekend = new Weekend<>(Recipe.class);
        WeekendCriteria<Recipe, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andIn(Recipe::getRecipeNo,recipeNos).andEqualTo(Recipe::getHospitalCode,hospitalCode);
        Recipe recipe = new Recipe();
        recipe.setRegNo(regNo);
        recipe.setReceptionNo(receptionNo);
        return recipeMapper.updateByExampleSelective(recipe,weekend)>0;
    }




    /**
     * 根据入参查询并组装itemExecDeptByTimeItemCodeMap，写入tyMdc,相同查询，可以重复查询并写入，有拦截器拦截相同查询，对性能没有影响
     * @param itemCodeList
     * @param keyName
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public void fillItemExecDeptByTimeItemCodeMap2TyMdc(List<Integer> itemCodeList, String keyName) {
        //尽量保证curTime的一致性
        String curTime = TyMdc.get(TyKey.CUR_TIME);
        if(curTime == null) {
            curTime = new SimpleDateFormat("HH:mm:ss").format(new Date());
        }
        List<MdmPubItemexedept> itemExecDeptByTimeItemCodeList = chargeItemDomain.getItemExecDeptByTimeItemCodeList(itemCodeList, curTime);
        //转为结果map,并额发入TyMdc
        Map<String, List<MdmPubItemexedept>> itemExecDeptByTimeItemCodeMap = new HashMap<>();
        for (MdmPubItemexedept mpi : itemExecDeptByTimeItemCodeList) {
            List<MdmPubItemexedept> list = itemExecDeptByTimeItemCodeMap.get(String.valueOf(mpi.getItemCode()));
            if (list == null) {
                list = new ArrayList<>();
                itemExecDeptByTimeItemCodeMap.put(String.valueOf(mpi.getItemCode()), list);
            }
            list.add(mpi);
        }
        TyMdc.put(keyName, itemExecDeptByTimeItemCodeMap);
    }


    /**
     * 删除处方外配往中间表数据
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public int deleteInsuranceOutRecipeByRecipeNo(String RecipeNo, String ReceptionNo, Integer HospitalCode){
        return recipeMapper.deleteInsuranceOutRecipeByRecipeNo(RecipeNo,ReceptionNo,HospitalCode);
    }

    /**
     * 根据条件查询外配中间表记录是否可删除
     *
     * @param ReceptionNo
     * @param RecipeNo
     * @param HospitalCode
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public int getInsuranceOutRecipeByStatus(String ReceptionNo,String RecipeNo,Integer HospitalCode){
        return recipeMapper.getInsuranceOutRecipeByStatus(ReceptionNo,RecipeNo,HospitalCode);
    }


}
