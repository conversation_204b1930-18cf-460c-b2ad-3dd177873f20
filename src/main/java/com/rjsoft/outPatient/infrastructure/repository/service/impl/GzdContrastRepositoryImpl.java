package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.GzdContrast;
import com.rjsoft.outPatient.infrastructure.repository.mapper.GzdContrastMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.GzdContrastRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 告知单对照
 * <AUTHOR>
@Service
@AllArgsConstructor
public class GzdContrastRepositoryImpl implements GzdContrastRepository {

    GzdContrastMapper gzdContrastMapper;

    /**
     * 查询告知单项目类型对照设置
     * @param gzdType
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<GzdContrast> getGzdContrastByType(Integer gzdType, Integer hospitalCode) {
        return gzdContrastMapper.getGzdContrastByType(gzdType, hospitalCode);
    }

    /**
     * 保存告知单对照
     * @param gzdContrast
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean saveGzdContrast(GzdContrast gzdContrast) {
        boolean success;
        if (gzdContrast.getId() == null || gzdContrast.getId() == 0) {
            success = gzdContrastMapper.insert(gzdContrast) > 0;
        } else {
            success = gzdContrastMapper.updateByPrimaryKeySelective(gzdContrast) > 0;
        }
        return success;
    }

    /**
     * 删除告知单对照
     * @param id
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean delGzdContrast(Integer id) {
        GzdContrast entity = new GzdContrast();
        entity.setId(id);
        return gzdContrastMapper.deleteByPrimaryKey(entity) > 0;
    }

    /**
     * 根据itemId查询告知单类型
     *
     * @param itemId
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public GzdContrast getGzdContrast(Integer itemId,Integer hospitalCode) {
        GzdContrast gzdContrast = new GzdContrast();
        gzdContrast.setItemId(itemId);
        gzdContrast.setHospitalCode(hospitalCode);
        return gzdContrastMapper.selectOne(gzdContrast);
    }
}
