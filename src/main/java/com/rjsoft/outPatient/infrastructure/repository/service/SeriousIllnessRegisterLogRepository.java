package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.SeriousIllnessRegisterLog;

import java.util.List;

/**
 * 大病登记日志
 *
 * <AUTHOR>
public interface SeriousIllnessRegisterLogRepository {

    /**
     * 插入大病登记日志
     *
     */
    Integer insertLog(SeriousIllnessRegisterLog seriousIllnessRegisterLog);
    List<SeriousIllnessRegisterLog> getSeriousIllnessRegisterLog(List<Integer> serId ,List<Integer> stateList,Integer oldFlag);
}
