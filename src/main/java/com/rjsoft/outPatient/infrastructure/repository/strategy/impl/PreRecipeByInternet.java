package com.rjsoft.outPatient.infrastructure.repository.strategy.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.log.Log;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.common.enums.RecipeStatusEnum;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeItem;
import com.rjsoft.outPatient.infrastructure.repository.entity.PreRecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.SystemTbPubItems;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.*;
import com.rjsoft.outPatient.infrastructure.repository.strategy.PreRecipeStrategy;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component("PreRecipeByInternet")
@AllArgsConstructor
public class PreRecipeByInternet implements PreRecipeStrategy {

    @Resource
    RecipeRepository recipeRepository;

    SystemTbPubItemsRepository systemTbPubItemsRepository	;
    SystemTvIteminfodeptRepository systemTvIteminfodeptRepository	;
    VirtualInventoryRepository virtualInventoryRepository	;
    ChargeItemRepository chargeItemRepository	;

    PreRecipeDetailMapper preRecipeDetailMapper	;
    RecipeDetailMapper recipeDetailMapper	;
    OldRecipeDetailMapper oldRecipeDetailMapper	;
    DrugToHospitalMapper drugToHospitalMapper	;
    SysFunctionMapper sysFunctionMapper	;

    @Override
    public List<PreRecipeDetail> getPreRecipeDetails(CheckRecipeDto checkDto, Map errInfo) {
        List<PreRecipeDetail> details = getPreRecipeDetailsByInternet(checkDto.getReceptionNo().toString(),
                checkDto.getPreSaveNo(), Converter.toInt32(checkDto.getOperatingId()), checkDto.getHospitalCode(),
                checkDto.getImportFlag(), errInfo);
        return details;
    }

    /**
     * 获取互联网处方预存明细
     */
    @DatabaseAnnotation
    private List<PreRecipeDetail> getPreRecipeDetailsByInternet(String receptionNo, Long preSaveNo, Integer presId, Integer hospitalCode, int importFlag,Map errInfo) {
        List<RecipeDetail> recipeDetails;
        List<PreRecipeDetail> res = new ArrayList<>();

        if (preSaveNo != null) {
            PreRecipeDetail entity = new PreRecipeDetail();
            entity.setPreSaveNo(preSaveNo);
            entity.setHospitalCode(hospitalCode);
            List<PreRecipeDetail> preRecipeDetails = preRecipeDetailMapper.select(entity);
            for (PreRecipeDetail preRecipeDetail : preRecipeDetails) {
                preRecipeDetail.setOpFlag(1);
                res.add(preRecipeDetail);
            }
            return res;
        }

        if (hospitalCode.equals(HospitalClassify.GENERAL.getHospitalCode())) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
            recipeDetails = recipeDetailMapper.getInternetRecipeByReceptionNo(receptionNo, presId, importFlag);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
            recipeDetails = recipeDetailMapper.getInternetRecipeByReceptionNo(receptionNo, presId, importFlag);
        }

        for (RecipeDetail recipeDetail : recipeDetails) {
            res.add(new PreRecipeDetail(recipeDetail));
        }


        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        Long preSaveNos = sysFunctionMapper.getGetSequences(SequenceEnum.PRE_SAVE_NO);
        Date date = sysFunctionMapper.getDate();
        for (PreRecipeDetail preRecipeDetail : res) {
            preRecipeDetail.setPreSaveNo(preSaveNos);
            Long preRecipeDetailNo = sysFunctionMapper.getGetSequences(SequenceEnum.PRE_SAVE_NO);
            preRecipeDetail.setRecipeDetailNo(preRecipeDetailNo);
            preRecipeDetail.setRecipeNo(0L);
            preRecipeDetail.setRecipeStatus(RecipeStatusEnum.UNREVIEWED.getCode());
            preRecipeDetail.setOpFlag(0);
            preRecipeDetail.setGroupNo(0);
            preRecipeDetail.setOpTime(date);

            //药品直接获取当前配置的执行科室
            Integer feeCategory = preRecipeDetail.getFeeCategory();
            if (feeCategory!=null && ItemCategoryEnum.isDrug(feeCategory)) {
                recipeRepository.setDefaultExeDept(preRecipeDetail, hospitalCode, preRecipeDetail.getItemCode(), feeCategory);
            }

            SystemTbPubItems chargeItem = systemTbPubItemsRepository.getChargeItemByIdIsStopped(preRecipeDetail.getItemCode(), preRecipeDetail.getExecDept(), hospitalCode,null);
            //ChargeItem chargeItem = chargeItemRepository.getChargeItemById(preRecipeDetail.getItemCode(), hospitalCode);
            if(chargeItem == null){
                Log.info(preRecipeDetail.getItemName() + "无匹配药品或未启用，无法进行处方导入");
                errInfo.put("errInfo", preRecipeDetail.getItemName().trim() + "无匹配药品或未启用，无法进行处方导入");
                return res;
            }
            chargeItem = systemTbPubItemsRepository.chargeItemTrim(chargeItem); //这实现..坑
            preRecipeDetail.ChangeChargeItemPart(chargeItem);
            ChargeItem calcItem = new ChargeItem();
            BeanUtils.copyProperties(chargeItem, calcItem);
            recipeRepository.calcRecipeQty(preRecipeDetail, calcItem);
        }
        return res;
    }

}
