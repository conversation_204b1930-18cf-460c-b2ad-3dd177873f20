package com.rjsoft.outPatient.infrastructure.repository.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatBlParam;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatientDTO;
import com.rjsoft.outPatient.domain.diagnose.dto.GeneralDiagnoseDto;
import com.rjsoft.outPatient.domain.diagnose.dto.MajorIoDiagnoseDto;
import com.rjsoft.outPatient.domain.diagnose.dto.MajorRegNoDto;
import com.rjsoft.outPatient.domain.reception.dto.ReceptionRegisterDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList;
import com.ruijing.code.util.DateUtil;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 挂号记录
 *
 * <AUTHOR>
public interface RegisterListMapper extends BaseMapper<RegisterList>, ExampleMapper<RegisterList> {

    /**
     * 获取挂号记录
     *
     * @return
     */
    List<RegisterList> getPageRegisterListByDoctor(@Param("startTime") Date startTime,
                                                   @Param("endTime") Date endTime,
                                                   @Param("hospitalCode") Integer hospitalCode,
                                                   @Param("workerId") String workerId,
                                                   @Param("deptId") String deptId,
                                                   @Param("cardNo") String cardNo,
                                                   @Param("fzFlag") Integer fzFlag,
                                                   @Param("selectRange") Integer selectRange,
                                                   @Param("sortField") String sortField,
                                                   @Param("list") List<Integer> cloudDeptIds);

    /**
     * 获取挂号记录
     *
     * @return
     */
    List<RegisterList> getPageRegisterAllListByDoctor(@Param("startTime") Date startTime,
                                                      @Param("endTime") Date endTime,
                                                      @Param("hospitalCode") Integer hospitalCode,
                                                      @Param("workerId") String workerId,
                                                      @Param("deptId") String deptId,
                                                      @Param("cardNo") String cardNo,
                                                      @Param("fzFlag") Integer fzFlag,
                                                      @Param("selectRange") Integer selectRange,
                                                      @Param("sortField") String sortField,
                                                      @Param("list") List<Integer> cloudDeptIds);

    /**
     * 获取挂号总数
     *
     * @return
     */
    HashMap<String, Integer> getRegisterAllCountByDoctor(@Param("startTime") Date startTime,
                                                         @Param("endTime") Date endTime,
                                                         @Param("hospitalCode") Integer hospitalCode,
                                                         @Param("workerId") String workerId,
                                                         @Param("deptId") String deptId,
                                                         @Param("cardNo") String cardNo,
                                                         @Param("fzFlag") Integer fzFlag,
                                                         @Param("selectRange") Integer selectRange,
                                                         @Param("list") List<Integer> cloudDeptIds);

    /**
     * 获取挂号总数
     *
     * @return
     */
    HashMap<String, Integer> getRegisterCountByDoctor(@Param("startTime") Date startTime,
                                                      @Param("endTime") Date endTime,
                                                      @Param("hospitalCode") Integer hospitalCode,
                                                      @Param("workerId") String workerId,
                                                      @Param("deptId") String deptId,
                                                      @Param("cardNo") String cardNo,
                                                      @Param("fzFlag") Integer fzFlag,
                                                      @Param("selectRange") Integer selectRange,
                                                      @Param("list") List<Integer> cloudDeptIds);

    /**
     * 根据病历卡号获取挂号记录
     *
     * @param hisCardNo
     * @param hospitalCode
     * @return
     */
    RegisterList getRegisterByHisCardNo(String hisCardNo, Integer hospitalCode);

    /**
     * 根据挂号流水号获取挂号记录
     *
     * @param regNo
     * @param hospitalCode
     * @param tableName
     * @return
     */
    default RegisterList getRegisterByRegNo(Long regNo, Integer hospitalCode, String tableName) {
        Weekend<RegisterList> weekend = new Weekend(RegisterList.class);
        if (!StringUtils.isEmpty(tableName)) {
            weekend.setTableName(tableName);
        }
        WeekendCriteria<RegisterList, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(RegisterList::getRegNo, regNo);
        weekendCriteria.andEqualTo(RegisterList::getHospitalCode, hospitalCode);
        weekendCriteria.andEqualTo(RegisterList::getIsDelete, false);
        return selectOneByExample(weekend);
    }

    /**
     * 根据挂号流水号列表获取挂号记录列表
     *
     * @param regNoList
     * @param hospitalCode
     * @param tableName
     * @return
     */
    default List<RegisterList> getRegisterByRegNoList(List<Long> regNoList, Integer hospitalCode, String tableName) {
        if (regNoList==null || regNoList.size()==0) {
            return new ArrayList<>();
        }
        Weekend<RegisterList> weekend = new Weekend(RegisterList.class);
        if (!StringUtils.isEmpty(tableName)) {
            weekend.setTableName(tableName);
        }
        WeekendCriteria<RegisterList, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(RegisterList::getRegNo, regNoList);
        weekendCriteria.andEqualTo(RegisterList::getHospitalCode, hospitalCode);
        weekendCriteria.andEqualTo(RegisterList::getIsDelete, false);
        return selectByExample(weekend);
    }

    PatientDTO queryPatientInfo(String regNo);

    /**
     * 根据挂号流水号[guid],获取患者相关信息
     */
    PatientDTO queryOldPatientInfo(String regNo);

    /**
     * 根据病历卡号查询挂号记录
     *
     * @param patIds
     * @param startTime
     * @param endDate
     * @param hospitalCode
     * @param tableName
     * @return
     */
    default List<RegisterList> getRegisterByPatIds(List<Integer> patIds, Date startTime, Date endDate, Integer hospitalCode, String tableName) {
        if (patIds == null || patIds.size() == 0) {
            return new ArrayList<>();
        }
        Weekend<RegisterList> weekend = new Weekend<>(RegisterList.class);
        if (!StringUtils.isEmpty(tableName)) {
            weekend.setTableName(tableName);
        }
        if (endDate != null) {
            endDate = DateUtils.addDays(endDate, 1);
        }
        WeekendCriteria<RegisterList, Object> keywordCriteria = weekend.weekendCriteria();
        if(startTime!=null) {
            keywordCriteria.andGreaterThanOrEqualTo(RegisterList::getRegistTime, startTime);
        }
        keywordCriteria.andLessThan(RegisterList::getRegistTime, endDate);
        keywordCriteria.andIn(RegisterList::getPatID, patIds);
        keywordCriteria.andEqualTo(RegisterList::getStatus, 0);
        if (ObjectUtil.isNotEmpty(hospitalCode)) {
            keywordCriteria.andEqualTo(RegisterList::getHospitalCode, hospitalCode);
        }
        return selectByExample(weekend);
    }

    default List<RegisterList> getRegisterByCardNo(String cardNo, Date startTime, Date endDate, Integer hospitalCode, String tableName) {
        Weekend<RegisterList> weekend = new Weekend<>(RegisterList.class);
        if (!StringUtils.isEmpty(tableName)) {
            weekend.setTableName(tableName);
        }
        WeekendCriteria<RegisterList, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(RegisterList::getStatus, 0);
        criteria.andNotEqualTo(RegisterList::getChargeType, 10);
        criteria.andEqualTo(RegisterList::getCardNo, cardNo);
        criteria.andLessThan(RegisterList::getRegistTime, endDate);
        criteria.andEqualTo(RegisterList::getHospitalCode, hospitalCode);
        criteria.andGreaterThanOrEqualTo(RegisterList::getRegistTime, startTime);
        return selectByExample(weekend);
    }

    /**
     * 根据挂号流水号+医生编码+医院编码获取挂号信息
     *
     * @param regNo
     * @param doctorId
     * @param hospCode
     * @return
     */
    default RegisterList getRegisterListByRegNoAndDocId(Long regNo, Integer doctorId, Integer hospCode) {
        //RegisterList registerList = new RegisterList();
        //registerList.setRegNo(regNo);
        //registerList.setDoctorID(doctorId);
        //registerList.setHospitalCode(hospCode);
        //registerList.setIsDelete(false);
        //return selectOne(registerList);

        Weekend<RegisterList> weekend = new Weekend<>(RegisterList.class);
        weekend.weekendCriteria().andEqualTo(RegisterList::getRegNo, regNo)
                .andEqualTo(RegisterList::getDoctorID, doctorId)
                .andEqualTo(RegisterList::getHospitalCode, hospCode)
                .andEqualTo(RegisterList::getIsDelete, false);
        List<RegisterList> list = selectByExample(weekend);
        return list.size() > 0 ? list.get(0) : null;
    }

    /**
     * 根据挂号日期+医生编码+医院编码获取当天挂号信息
     *
     * @param registTime 挂号日期
     * @param doctorId   医生编码
     * @param hospCode   医院编码
     * @return
     */
    default List<RegisterList> getDayRegisterInfo(Date registTime, Integer doctorId, Integer hospCode) {
        Date startDate = DateUtil.parseDate(new SimpleDateFormat("yyyy-MM-dd").format(registTime), "yyyy-MM-dd");
        Date endDate = DateUtil.addDays(startDate, 1);
        Weekend<RegisterList> weekend = new Weekend<>(RegisterList.class);
        weekend.weekendCriteria().andEqualTo(RegisterList::getDoctorID, doctorId)
                .andEqualTo(RegisterList::getHospitalCode, hospCode)
                .andEqualTo(RegisterList::getIsDelete, false)
                .andBetween(RegisterList::getRegistTime, startDate, endDate);
        weekend.setOrderByClause("RegistTime desc");
        return selectByExample(weekend);
    }


    /**
     * 查询当前挂号同一个医生同一个患者挂号信息
     *
     * @param registTime
     * @param patId
     * @param doctorId
     * @param hospCode
     * @return
     */
    default List<RegisterList> getRegisterListByDay(Date registTime, Integer patId, Integer doctorId, Integer hospCode, String tbName) {
        Date startDate = Converter.toDate(new SimpleDateFormat("yyyy-MM-dd").format(registTime), "yyyy-MM-dd");
        Date endDate = DateUtil.addDays(startDate, 1);
        Weekend<RegisterList> weekend = new Weekend<>(RegisterList.class);
        if (!StringUtils.isEmpty(tbName)) {
            weekend.setTableName(tbName);
        }
        weekend.weekendCriteria().andEqualTo(RegisterList::getDoctorID, doctorId)
                .andEqualTo(RegisterList::getPatID, patId)
                .andEqualTo(RegisterList::getHospitalCode, hospCode)
                .andEqualTo(RegisterList::getIsDelete, false)
                .andEqualTo(RegisterList::getStatus, 0)
                .andBetween(RegisterList::getRegistTime, startDate, endDate);
        weekend.setOrderByClause(" RegistTime desc");
        return selectByExample(weekend);
    }

    /**
     * 根据挂号时间区间，加载出所有挂号记录，用户匹配是否存在当日挂号记录
     *
     * @param minDate
     * @param maxDate
     * @param patId
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    default List<RegisterList> getRegisterListByDay(Date minDate, Date maxDate, List<Long> patId, List<Integer> doctorId, Integer hospitalCode) {
        Date startDate = Converter.toDate(new SimpleDateFormat("yyyy-MM-dd").format(minDate), "yyyy-MM-dd");
        Date endDate = Converter.toDate(new SimpleDateFormat("yyyy-MM-dd").format(maxDate), "yyyy-MM-dd");
        endDate = DateUtil.addDays(startDate, 1);
        Weekend<RegisterList> weekend = new Weekend(RegisterList.class);
        weekend.selectProperties("regNo", "doctorID", "patID", "deptID");
        weekend.weekendCriteria()
                .andGreaterThanOrEqualTo(RegisterList::getRegistTime, startDate)
                .andLessThan(RegisterList::getRegistTime, endDate)
                .andIn(RegisterList::getDoctorID, doctorId)
                .andIn(RegisterList::getPatID, patId)
                .andEqualTo(RegisterList::getHospitalCode, hospitalCode)
                .andEqualTo(RegisterList::getStatus, 0)
                .andEqualTo(RegisterList::getIsDelete, false)
                .andBetween(RegisterList::getRegistTime, startDate, endDate);
        return selectByExample(weekend);
    }

    /**
     * 根据患者编号列表获取所有患者信息
     *
     * @param patIds
     * @param hospitalId
     * @param tableName
     * @return
     */
    default List<RegisterList> getRegisterListByPatIds(List<Integer> patIds, Integer hospitalId, String tableName) {
        Weekend<RegisterList> weekend = new Weekend<>(RegisterList.class);
        weekend.setTableName(tableName);
        WeekendCriteria<RegisterList, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(RegisterList::getPatID, patIds)
                .andEqualTo(RegisterList::getStatus, 0)
                .andEqualTo(RegisterList::getHospitalCode, hospitalId);
        weekend.setOrderByClause("RegNo desc");
        return selectByExample(weekend);
    }

    /**
     * 通过身份证号查询所有挂号信息
     *
     * @param certificateNo 身份证
     * @return regNo
     */
    List<Long> getAllRegNoByCertificateNo(@Param("certificateNo") String certificateNo);

    /**
     * 通过身份证号查询所有挂号信息
     *
     * @param patId 身份证
     * @return regNo
     */
    List<Long> getAllRegNoByPatId(@Param("patId") Integer patId);

    /**
     * 通过患者ID查询所有挂号信息
     *
     * @param patIds 患者ID
     * @return regNo
     */
    List<Long> queryAllRegNoByPatIds(@Param("patIds") List<Integer> patIds);

    /**
     * 通过身份证号查询所有挂号时间
     *
     * @param certificateNo 身份证
     * @return RegisterTime
     */
    Date getAllRegistTimeByCertificateNo(@Param("certificateNo") String certificateNo);

    /**
     * 查询所有挂号信息
     *
     * @param patId
     * @return regNo
     */
    List<MajorRegNoDto> getMajorAllRegNoByPatId(@Param("patId") Integer patId);

    /**
     * 查询所有挂号信息
     *
     * @param patNo
     */
    List<MajorRegNoDto> getMajorAllRegNoFromOldData(@Param("patNo") Integer patNo);

    /**
     * 查询所有挂号信息
     *
     * @param patNos
     */
    List<MajorRegNoDto> queryMajorAllRegNoFromOldData(@Param("patNos") List<Integer> patNos);


    /**
     * 查询所有挂号信息
     *
     * @param patId
     */
    List<Long> getIOAllRegNosByPatId(@Param("patId") Integer patId);

    /**
     * 通过挂号流水号查询查房信息
     *
     * @param regNo
     */
    HashMap<String, Integer> getIOCheckInfoByRegNo(@Param("regNo") Long regNo);

    /**
     * 通过挂号流水号查询诊断信息
     *
     * @param regNo
     */
    List<MajorIoDiagnoseDto> getIODiagnoseInfoByRegNo(@Param("regNo") Long regNo);

    /**
     * 通过身份证号查询所有挂号信息
     *
     * @param certificateNo 身份证
     * @return regNo
     */
    List<String> getAllRegNoFromOldData(@Param("certificateNo") String certificateNo);

    /**
     * 通过patId查询所有挂号信息
     *
     * @param patId
     * @return regNo
     */
    List<String> getAllRegNoFromOldDataByPatId(@Param("patId") Integer patId);

    /**
     * 通过patIds查询所有挂号信息
     *
     * @param patIds
     * @return regNo
     */
    List<String> getAllRegNoFromOldDataByPatIds(@Param("patIds") List<Integer> patIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 根据挂号流水号获取挂号信息
     *
     * @param regNo
     * @return
     */
    ReceptionRegisterDto getRegTvRegisterList(@Param("regNo") Long regNo);

    /**
     * 根据挂号流水号列表，查询是否有已删除挂号信息
     *
     * @param regNos
     */
    List<Long> selectInvalidRegNos(@Param("list") Set<Long> regNos);

    /**
     * 根据身份证号获取患者诊断记录[总院 106 ]
     *
     * @param certificateNo
     */
    List<GeneralDiagnoseDto> queryDiagnoses(String certificateNo);

    /**
     * 根据挂号流水号获取相关信息
     * 【病历补写】
     *
     * @param regNos
     */
    List<PatBlDto> getPatBlInfoByRegNos(@Param("list") List<String> regNos, @Param("hisCardNo") String hisCardNo, @Param("patName") String patName);

    /**
     * 获取未提交患者病历列表
     * 【病历补写】
     *
     * @param param
     */
    List<PatBlDto> getNoFilledPatBlInfo(PatBlParam param);

    /**
     * 获取未提交患者病历列表
     * 【病历补写】
     *
     * @param param
     */
    List<PatBlDto> getNoCommittedPatBlInfo(PatBlParam param);

    /**
     * 获取未提交患者病历列表
     * 【病历补写】
     *
     * @param param
     */
    List<PatBlDto> getNoSignedPatBlInfo(PatBlParam param);

    /**
     * 根据挂号流水号修改对应看诊医生和复诊标记
     *
     * @param doctorId
     * @param fzFlag
     * @param regNo
     */
    int updateOldRegisterList(@Param("doctorId") Integer doctorId, @Param("fzFlag") Integer fzFlag, @Param("regNo") Long regNo);

    /**
     * 根据挂号流水号和医院编码查询挂号信息
     *
     * @param regNo
     * @param hospitalCode
     */
    RegisterList getRegisterInfoByRegNo(@Param("regNo") Long regNo, @Param("hospitalCode") Integer hospitalCode);


    /**
     * 根据挂号流水号和医院编码查询挂号信息
     *
     * @param regNo
     * @param hospitalCode
     */
    RegisterList getAllRegisterInfoByRegNo(@Param("regNo") Long regNo, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据patid查询患者挂号列表
     *
     * @param startTime
     * @param endTime
     * @param hospitalCode
     * @param patId
     * @return
     */
    List<RegisterList> getPageRegisterListByPatid(@Param("startTime") Date startTime,
                                                  @Param("endTime") Date endTime,
                                                  @Param("hospitalCode") Integer hospitalCode,
                                                  @Param("patId") String patId);

    /**
     * 查询老系统所有挂号信息
     *
     * @param certificateNo
     * @return
     */
    List<RegisterList> getRegisterListFromOldData(@Param("certificateNo") String certificateNo,
                                                  @Param("startTime") Date startTime,
                                                  @Param("endTime") Date endTime);


    /**
     * 查询老系统所有挂号信息
     *
     * @param patNo
     * @param startTime
     * @param endTime
     * @return
     */
    List<RegisterList> getRegisterListOldDataByPatNo(@Param("patNo") Integer patNo,
                                                     @Param("startTime") Date startTime,
                                                     @Param("endTime") Date endTime);

    int updateRegistOrderByRegNo(Long regNo, Integer hospitalCode, Integer registOrder);

    int updateRegistVipFlag(Long regNo, Integer hospitalCode);

    /**
     * 根据患者编号列表获取指定时间段内所有患者信息
     *
     * @param patIds 患者ID集合
     * @param hospitalId 医院编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tableName 查询表对象
     * @return
     */
    default List<RegisterList> getRegisterListByPatIds(List<Integer> patIds, Integer hospitalId, Date startTime, Date endTime, String tableName) {
        Weekend<RegisterList> weekend = new Weekend<>(RegisterList.class);
        weekend.setTableName(tableName);
        WeekendCriteria<RegisterList, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(RegisterList::getPatID, patIds)
                .andEqualTo(RegisterList::getStatus, 0)
                .andEqualTo(RegisterList::getHospitalCode, hospitalId);
        if (endTime != null) {
            criteria.andLessThanOrEqualTo(RegisterList::getCreateTime, endTime);
        }
        if (startTime != null) {
            criteria.andGreaterThanOrEqualTo(RegisterList::getCreateTime, startTime);
        }
        weekend.setOrderByClause("RegNo desc");
        return selectByExample(weekend);
    }

    /**
     * 根据患者编号列表获取指定时间段内所有患者信息
     *
     * <AUTHOR>
     * @param regNo 挂号流水号
     * @param hospitalCode 医院编码
     * @param insuType 险种类型
     * @return
     */
    Integer verifyIsJMYB(@Param("regNo") Integer regNo,
                         @Param("hospitalCode") Integer hospitalCode,
                         @Param("insuType") String insuType);

    /**
     * 获取挂号记录
     *
     * @return
     */
    List<RegisterList> getRegisterListPageByDoctor(@Param("startTime") Date startTime,
                                                   @Param("endTime") Date endTime,
                                                   @Param("hospitalCode") Integer hospitalCode,
                                                   @Param("workerId") String workerId,
                                                   @Param("deptId") String deptId,
                                                   @Param("cardNo") String cardNo,
                                                   @Param("fzFlag") Integer fzFlag,
                                                   @Param("selectRange") Integer selectRange,
                                                   @Param("sortField") String sortField,
                                                   @Param("list") List<Integer> cloudDeptIds,
                                                   @Param("paperNum") Integer paperNum,
                                                   @Param("paperSize") Integer paperSize);
    /**
     * 获取个人挂号记录
     *
     * @return
     */
    List<RegisterList> getRegisterListByDoctor(@Param("hospitalCode") Integer hospitalCode,
                                               @Param("workerId") String workerId,
                                               @Param("deptId") String deptId,
                                               @Param("selectRange") Integer selectRange,
                                               @Param("regNo") Long regNo,
                                               @Param("list") List<Integer> cloudDeptId);

    /**
     * 获取挂号记录
     *
     * @return
     */
    List<RegisterList> getRegisterAllListPageByDoctor(@Param("startTime") Date startTime,
                                                      @Param("endTime") Date endTime,
                                                      @Param("hospitalCode") Integer hospitalCode,
                                                      @Param("workerId") String workerId,
                                                      @Param("deptId") String deptId,
                                                      @Param("cardNo") String cardNo,
                                                      @Param("fzFlag") Integer fzFlag,
                                                      @Param("selectRange") Integer selectRange,
                                                      @Param("sortField") String sortField,
                                                      @Param("list") List<Integer> cloudDeptIds,
                                                      @Param("paperNum") Integer paperNum,
                                                      @Param("paperSize") Integer paperSize);

    /**
     * 获取单个挂号记录
     *
     * @return
     */
    List<RegisterList> getRegisterAllListByDoctor(@Param("hospitalCode") Integer hospitalCode,
                                                  @Param("workerId") String workerId,
                                                  @Param("deptId") String deptId,
                                                  @Param("selectRange") Integer selectRange,
                                                  @Param("regNo") Long regNo,
                                                  @Param("list") List<Integer> cloudDeptIds);

    /**
     * 根据病历卡号查询患者
     *
     * @param hospitalCode 医院编码
     * @param doctorId     医生编码
     * @param deptId    科室编码
     * @param outPatientNo    病历卡号
     */
    List<RegisterList> getByOutPatientNo(Integer hospitalCode, Integer doctorId, Integer deptId, String outPatientNo);

    List<RegisterList> listRegisterByRegNo(@Param("hospitalCode") Integer hospitalCode, @Param("regNoList") List<Long> regNoList);

    List<RegisterList> queryRegisterDtoByRegNoList(@Param("regNoList") List<Long> regNoList, @Param("hospitalCode") Integer hospitalCode);

}
