package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.OverdoseDrugs;

import java.util.List;
import java.util.Map;

/**
 * 药品超量
 *
 * <AUTHOR>
 * @since 2021/9/27-3:49 下午
 */
public interface OverdoseDrugsRepository {

    /**
     * 根据药品编码、科室编码查询药品超量记录
     *
     * @param drugId       药品编码
     * @param deptId       科室编码
     * @param hospitalCode 医院编码
     * @return record
     */
    OverdoseDrugs getOverdoseDrugsByDrugIdAndDeptId(Integer drugId, Integer deptId, Integer hospitalCode);

    @DatabaseAnnotation
    OverdoseDrugs tyMdcGetOverdoseDrugsByDrugIdAndDeptId(Integer drugId, Integer deptId, Integer hospitalCode);

    @DatabaseAnnotation
    Map<String,List<OverdoseDrugs>> listOverdoseDrugsByDrugIdAndHosptailCode(List<Integer> itemCodeList, Integer hospitalCode);
}
