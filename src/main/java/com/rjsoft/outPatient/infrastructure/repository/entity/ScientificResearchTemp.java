package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.xml.soap.Detail;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2021/8/6 10:01
 * @description 科研模板
 **/
@Data
@Table(name = "MZYS_TB_KYMBKS")
public class ScientificResearchTemp implements Serializable {


    @Id
    @Column(name = "Id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer researchTempId;

    /**
     * 科研课题id
     */
    @Column(name = "ProjectId")
    private String projectId;

    /**
     * 科研课题名称
     */
    @Column(name = "ProjectName")
    private String projectName;

    /**
     * 元素编码   关联MZYS_TB_MBINFO表的ysbm
     */
    @Column(name = "Mbid")
    private Integer tempId;

    /**
     * 状态
     */
    @Column(name = "Status")
    private Integer status;

    /**
     * 創建人
     */
    @Column(name = "CreDoctor")
    private Integer creDoctor;

    /**
     * 創建日期
     */
    @Column(name = "CreTime")
    private Date creTime;

    @Column(name = "hosp_code")
    private Integer hospCode;
}
