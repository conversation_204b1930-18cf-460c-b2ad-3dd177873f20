package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Agent;
import com.rjsoft.outPatient.infrastructure.repository.entity.DiseaseReportCard;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

public interface DiseaseReportCardMapper extends BaseMapper<DiseaseReportCard>, ExampleMapper<DiseaseReportCard> {

    /**
     * 根据身份证号和大类名称判断是否上报过
     * @param idNo 身份证
     * @param sbname 大类名称
     * @return
     */
    int getInfoByIdNoSbname(@Param("idNo") String idNo,@Param("sbname") String sbname);
}
