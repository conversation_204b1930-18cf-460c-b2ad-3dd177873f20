package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.NotificationDetails;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

/**
 * 告知单明细
 *
 * <AUTHOR>
 * @since 2021/7/19 - 14:14
 */
public interface NotificationDetailsMapper extends BaseMapper<NotificationDetails>, ExampleMapper<NotificationDetails> {

    /**
     * 常用条件
     * @return
     */
    default WeekendCriteria<NotificationDetails, Object> commonConditionsByGzdName() {
        Weekend<NotificationDetails> weekend = new Weekend<>(NotificationDetails.class);
        WeekendCriteria<NotificationDetails, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.orEqualTo(NotificationDetails::getGZDName, "自愿住院知情同意书")
                .orEqualTo(NotificationDetails::getGZDName, "自愿住院通知书")
                .orEqualTo(NotificationDetails::getGZDName, "非自愿住院知情同意书")
                .orEqualTo(NotificationDetails::getGZDName, "非自愿住院通知书")
                .orEqualTo(NotificationDetails::getGZDName, "紧急观察住院知情同意书")
                .orEqualTo(NotificationDetails::getGZDName, "紧急观察住院通知书");
        return weekendCriteria;
    }

   }
