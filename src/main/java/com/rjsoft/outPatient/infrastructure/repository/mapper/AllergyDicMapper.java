package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.AllergyDic;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface AllergyDicMapper extends BaseMapper<AllergyDic>, ExampleMapper<AllergyDic> {


    /**
     * 获取过敏源
     *
     * @param inputCode
     * @param hospitalCode
     * @return
     */
    default List<AllergyDic> getAllergyType(String inputCode, Integer hospitalCode) {
        final Weekend<AllergyDic> weekend = new Weekend<>(AllergyDic.class);
        final WeekendCriteria<AllergyDic, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(AllergyDic::getHospitalId, hospitalCode);

        if (StringUtils.isNotBlank(inputCode)) {
            inputCode = "%" + inputCode + "%";
            final WeekendCriteria<AllergyDic, Object> criteria1 = weekend.weekendCriteria();
            criteria1.andLike(AllergyDic::getName, inputCode).orLike(AllergyDic::getInputCode, inputCode);
            weekend.and(criteria1);
        }

        return selectByExample(weekend);
    }

}
