package com.rjsoft.outPatient.infrastructure.repository.strategy;

import com.rjsoft.common.log.Log;
import com.rjsoft.outPatient.common.enums.PreOpType;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.PreRecipeDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class PreRecipeStrategyContext {
    private final Map<String, PreRecipeStrategy> strategyMap;

    @Autowired
    public PreRecipeStrategyContext(Map<String, PreRecipeStrategy> strategyMap) {
        this.strategyMap = strategyMap;
    }

    public List<PreRecipeDetail> getPreRecipeDetails(Integer typeCode, CheckRecipeDto checkDto, Map errInfo) {
        PreOpType preOpType = PreOpType.getTypeByCode(typeCode);
        if (preOpType == null) {
            throw new RuntimeException("preOpType不存在!");
        }
        List<PreRecipeDetail> preRecipeDetails = null;
        Log.info("trategySelect:"+checkDto.getReceptionNo()+" : "+preOpType.getStrategyName());
        PreRecipeStrategy strategy = strategyMap.get(preOpType.getStrategyName());
        if (strategy != null) {
            preRecipeDetails = strategy.getPreRecipeDetails(checkDto, errInfo);
        } else {
            throw new RuntimeException("预存表查询策略不存在!");
        }
        return preRecipeDetails;
    }
}
