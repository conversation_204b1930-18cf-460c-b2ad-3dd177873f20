package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.outPatient.infrastructure.repository.entity.ApplyDetailCost;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.List;

/**
 * 申请单费用明细
 * <AUTHOR>
public interface ApplyDetailCostMapper extends BaseMapper<ApplyDetailCost>, ExampleMapper<ApplyDetailCost> {
    /**
     * 根据ID获取申请单明细
     * @param param 项目入参顺序：ID
     * @return
     */
    default ApplyDetailCost getApplyDetailById(Object... param){
        ApplyDetailCost entity=new ApplyDetailCost();
        entity.setId(Converter.toInt32(param[0]));
        return selectByPrimaryKey(entity);
    }

    /**
     * 根据申请根据申请单查询明细
     * @param listId
     * @param detailID
     * @return
     */
    default List<ApplyDetailCost> getApplyDetailsByDetailId( Integer listId,Integer detailID){
        ApplyDetailCost entity=new ApplyDetailCost();
        entity.setListId(listId);
        entity.setDetlId(detailID);
        return select(entity);
    }

    /**
     * 根据申请根据申请单查询明细
     * @param listId
     * @return
     */
    default List<ApplyDetailCost> getApplyDetailsByListId(List< Integer> listId){
        if(listId==null||listId.size()==0){
            return new ArrayList<>();
        }
        Weekend weekend=new Weekend(ApplyDetailCost.class);
        WeekendCriteria<ApplyDetailCost,Object>weekendCriteria=weekend.weekendCriteria();
        weekendCriteria.andIn(ApplyDetailCost::getListId,listId);
        return selectByExample(weekend);
    }
}
