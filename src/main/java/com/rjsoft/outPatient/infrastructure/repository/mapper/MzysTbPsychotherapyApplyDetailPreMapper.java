package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbPsychotherapyApplyDetailPre;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface MzysTbPsychotherapyApplyDetailPreMapper extends Mapper<MzysTbPsychotherapyApplyDetailPre> {

    @DatabaseAnnotation
    default List<MzysTbPsychotherapyApplyDetailPre> selectByApplyId(Long applyId) {
        Weekend<MzysTbPsychotherapyApplyDetailPre> weekend = new Weekend<>(MzysTbPsychotherapyApplyDetailPre.class);
        WeekendCriteria<MzysTbPsychotherapyApplyDetailPre, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(MzysTbPsychotherapyApplyDetailPre::getApplyId, applyId);
        return selectByExample(weekend);
    }

}