package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.utils.TyMapUtil;
import com.rjsoft.outPatient.domain.recipe.dto.DrugYYYDto;
import com.rjsoft.outPatient.domain.recipe.vo.DrugUseInfoVO;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugUseInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.PreRecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugUseInfoMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DrugInfomationRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.DrugUseInfoRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeDetailRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeRepository;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class DrugUseInfoRepositoryImpl implements DrugUseInfoRepository {

    DrugUseInfoMapper drugUseInfoMapper;
    RecipeRepository recipeRepository;
    RecipeDetailRepository recipeDetailRepository;

    @Override
    @DatabaseAnnotation
    public Boolean isExistsDrugUseInfo(Long receptionNo, Long recipeDetailNo, Integer itemCode, Integer hospitalCode) {
        final Weekend<DrugUseInfo> weekend = new Weekend<>(DrugUseInfo.class);
        weekend.weekendCriteria()
                .andEqualTo(DrugUseInfo::getReceptionNo, receptionNo)
                .andEqualTo(DrugUseInfo::getRecipeDetailNo, recipeDetailNo)
                .andEqualTo(DrugUseInfo::getItemCode, itemCode)
                .andEqualTo(DrugUseInfo::getHospitalId, hospitalCode);
        return drugUseInfoMapper.selectOneByExample(weekend) != null;
    }



    @Override
    @DatabaseAnnotation
    public boolean saveDrugUseInfo(DrugUseInfo drugUseInfo) {
        boolean flag = drugUseInfoMapper.insert(drugUseInfo) > 0;
        return flag;
    }

    @Override
    @DatabaseAnnotation
    public boolean saveDrugUseInfo(DrugUseInfoVO drugUseInfoVO) {
//        PreRecipeDetail preRecipeDetail = recipeRepository
//                .getPreRecipeById(drugUseInfoVO.preSaveNo,drugUseInfoVO.recipeDetailNo,drugUseInfoVO.hospitalCode);
        RecipeDetail recipeDetail = recipeDetailRepository.getRecipeDetailById(drugUseInfoVO.recipeDetailNo,drugUseInfoVO.hospitalCode);
        Date createTime = recipeRepository.getDate();
        if(recipeDetail != null){
            DrugUseInfo drugUseInfo = new DrugUseInfo();
            drugUseInfo.setRecipeDetailNo(recipeDetail.getRecipeDetailNo());
            drugUseInfo.setRecipeDetailId(recipeDetail.getRecipeDetailId());
            drugUseInfo.setReceptionNo(recipeDetail.getReceptionNo());
            drugUseInfo.setRecipeNo(recipeDetail.getRecipeNo());
            drugUseInfo.setRecipeId(recipeDetail.getRecipeId());
            drugUseInfo.setRegNo(recipeDetail.getRegNo());
            drugUseInfo.setItemCode(recipeDetail.getItemCode());
            drugUseInfo.setItemName(recipeDetail.getItemName());
            drugUseInfo.setDoseUnit(recipeDetail.getDoseUnit());
            drugUseInfo.setUnit(recipeDetail.getUnit());
            drugUseInfo.setSpecification(recipeDetail.getSpecification());
            drugUseInfo.setDrugUseInfo(drugUseInfoVO.drugUseInfo);
            drugUseInfo.setHospitalId(drugUseInfoVO.hospitalCode);
            drugUseInfo.setCreateTime(createTime);
            return drugUseInfoMapper.insert(drugUseInfo) > 0;
        }

        return false;
    }
}
