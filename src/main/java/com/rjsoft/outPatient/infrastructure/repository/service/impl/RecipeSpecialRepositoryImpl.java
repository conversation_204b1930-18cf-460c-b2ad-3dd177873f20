package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.Recipe;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetailSpecial;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeSpecial;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeSpecialMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeSpecialRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@AllArgsConstructor
public class RecipeSpecialRepositoryImpl implements RecipeSpecialRepository {
    private RecipeSpecialMapper recipeSpecialMapper;

    @Override
    @DatabaseAnnotation
    public boolean addRecipeSpecial(List<Recipe> recipeList, Integer doctorId) {
        if(recipeList==null||recipeList.size()==0||doctorId==null){
            return false;
        }
        List<RecipeSpecial> recipeSpecialList = new ArrayList<>();
        Date date = new Date();
        for(Recipe recipe:recipeList) {
            RecipeSpecial recipeSpecial = new RecipeSpecial();
            BeanUtils.copyProperties(recipe,recipeSpecial);
            recipeSpecial.setCreateTime(date);
            recipeSpecial.setDoctorId(doctorId);
            recipeSpecialList.add(recipeSpecial);
        }
        for(RecipeSpecial recipeSpecial:recipeSpecialList){
            recipeSpecialMapper.insertSelective(recipeSpecial);
        }
        return true;
    }
}
