package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 处方限制类型
 * MZYS_NEW..MZYS_TB_CFXZLX
 *
 * <AUTHOR>
 * @since 2021/9/27-2:04 下午
 */
@Data
@Table(name = "MZYS_TB_CFXZLX")
public class RecipeRestrictionType implements Serializable {


    @Column(name = "id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;
    /**
     * 唯一标识符
     * 处方限制类型编码
     */
    @Id
    @Column(name = "cfxzlx")
    private Integer recipeRestriction;
    /**
     * 处方限制类型名称
     */
    @Column(name = "cfxzms")
    private String name;
    /**
     * 备注
     */
    @Column(name = "bz")
    private String remark;
    /**
     * 创建日期
     */
    @Column(name = "cjrq")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    /**
     * 医院编码
     */
    @Column(name = "yybm")
    private Integer hospitalCode;

}
