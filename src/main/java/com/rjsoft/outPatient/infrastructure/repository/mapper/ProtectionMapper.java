package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Protection;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

/**
 * 约束保护
 * <AUTHOR>
public interface ProtectionMapper extends BaseMapper<Protection>, ExampleMapper<Protection> {

    /**
     * 保存约束保护
     * @param protection
     * @return
     */
    default boolean saveProtection(Protection protection) {
        if (protection == null || protection.getRecipeDetailNo() == null) {
            throw new RuntimeException("处方明细流水为空!");
        }
        Protection entity = new Protection();
        entity.setRecipeDetailNo(protection.getRecipeDetailNo());
        delete(entity);
        insert(protection);
        return true;
    }
}
