package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.AssayResult;
import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.AssayStateResult;
import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.OutPatientInvoiceTimeResult;

import java.util.List;

public interface AdmissionApplicationFormRepository {
    List<AssayResult> getAssay(Long recipeDetailId);

    List<AssayStateResult> getAssayState(Long recipeDetailId);

    OutPatientInvoiceTimeResult getOutPatient(Long regNo, Integer hospitalCode);
}
