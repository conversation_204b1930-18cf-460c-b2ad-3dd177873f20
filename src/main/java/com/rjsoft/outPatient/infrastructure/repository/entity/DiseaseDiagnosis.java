package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.rjsoft.outPatient.domain.dictionary.dto.CommonDicEntity;
import com.rjsoft.outPatient.domain.dictionary.vo.CommonDicVo;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 疾病诊断
 *
 * <AUTHOR>
 * @since 2021/8/30-11:09 上午
 */
@Data
@Table(name = "MZYS_TB_DBDJDic")
public class DiseaseDiagnosis extends CommonDicEntity  implements Serializable {

    /**
     * 编码
     */
    @Column(name = "DisCode")
    private String disCode;

    /**
     * 名称
     */
    @Column(name = "DisName")
    private String name;

    /**
     * 医院编码
     */
    @Column(name = "hospitalCode")
    private Integer hospitalCode;

    @Override
    public CommonDicVo conversionFill() {
        super.setCode(Integer.valueOf(this.disCode));
        return new CommonDicVo(this);
    }
}
