package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
    * 门诊病案首页上传日志
    */
@Data
@Table(name = "MZYS_Tb_MedicalUploadLog")
public class MzysTbMedicalUploadLog implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 病案信息ID
     */
    @Column(name = "medical_id")
    private Long medicalId;

    /**
     * 挂号流水号
     */
    @Column(name = "reg_no")
    private Long regNo;

    /**
     * 院区ID
     */
    @Column(name = "hospital_id")
    private Integer hospitalId;

    /**
     * 请求入参
     */
    @Column(name = "req_info")
    private String reqInfo;

    /**
     * 请求出参
     */
    @Column(name = "res_info")
    private String resInfo;

    /**
     * 上传结果状态码
     */
    @Column(name = "upload_status")
    private Integer uploadStatus;

    /**
     * 上传结果信息
     */
    @Column(name = "upload_msg")
    private String uploadMsg;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @Column(name = "create_by")
    private Integer createBy;

    private static final long serialVersionUID = 1L;
}