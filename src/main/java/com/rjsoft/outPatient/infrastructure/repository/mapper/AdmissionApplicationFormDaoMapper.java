package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.*;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbMzCfMx;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegisterListResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdmissionApplicationFormDaoMapper {

    /**
     * 根据patId查询患者信息
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    RetPatientResult getPatient(@Param("regNo") Integer regNo, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 查询病区信息
     *
     * @param params
     * @return
     */
    List<DeptsResult> listWards(AdmissionApplicationFormParams params);


    /**
     * 查询科室下病区信息
     *
     * @param params
     * @return
     */
    List<DeptsResult> listDeptWards(AdmissionApplicationFormParams params);

    /**
     * 查询病区下科室信息
     *
     * @param hospitalCode
     * @return
     */
    List<DeptsResult> listWardDepts(Integer hospitalCode,Integer wardId);

    /**
     * 病区床位查询汇总信息
     *
     * @param params
     * @return
     */
    WardCountResult getWardCount(AdmissionApplicationFormParams params);

    /**
     * 病区床位床号查询
     *
     * @param params
     * @return
     */
    List<BedsResult> listBeds(AdmissionApplicationFormParams params);


    /**
     * 查询检查结果
     *
     * @param params
     * @return
     */
    InspectionResult getTypeAndID(AdmissionApplicationFormParams params);

    /**
     * 查询化验结果
     *
     * @param recipeDetailId
     * @return
     */
    List<AssayResult> getAssay(Long recipeDetailId);


    /**
     * 查询化验状态
     *
     * @param recipeDetailId
     * @return
     */
    List<AssayStateResult> getAssayState(Long recipeDetailId);

    /**
     * 查询历史信息诊断
     *
     * @param regNo
     * @return
     */
    List<HistoryDiagnoseResult> listHistoryDiagnose(Long regNo);

    /**
     * 查询患者挂号信息
     *
     * @param outPatientNo
     * @param hospitalCode
     * @return
     */
    List<RegisterListResult> listOutPatientNo(@Param("outPatientNo") String outPatientNo, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据ghlsh=regNo查询患者的账户标志Reg_Tb_OutpatientInvoice_Time表的AccountFlag
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    OutPatientInvoiceTimeResult getOutPatient(@Param("regNo") Long regNo, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据就诊流水号查询处方明细信息
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    MzysTbMzCfMx getTopMzCfMx(@Param("receptionNo") Long receptionNo, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 查询有效期内的处方明细
     *
     * @param jzlsh
     * @param months
     * @param hospitalCode
     * @return
     */
    MzysTbMzCfMx listMzCfMx(@Param("jzlsh") Long jzlsh, @Param("months") Integer months, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 查询患者住院信息
     *
     * @param params
     * @return
     */
    List<InPatientResult> listInPatients(AdmissionApplicationFormParams params);

    /**
     * 查询门诊处方明细信息
     *
     * @param cfmxlsh
     * @param hospitalCode
     * @return
     */
    MzysTbMzCfMx getRecipeByRecipeDetailId(@Param("cfmxlsh") Long cfmxlsh, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据就诊流水号，套餐名称，医院id查询患者是否存在入院前套餐
     *
     * @param jzlsh
     * @param tcmc
     * @param hospitalCode
     * @return
     */
    int getOldRecipeDetail(@Param("jzlsh") String jzlsh, @Param("tcmc") String tcmc, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 修改套餐开立状态
     *
     * @param jzlsh
     * @param hospitalCode
     * @param issueInspectStatus
     * @return
     */
    int changeIssueInspectStatus(@Param("jzlsh") Long jzlsh, @Param("hospitalCode") Integer hospitalCode, @Param("issueInspectStatus") Boolean issueInspectStatus);
}
