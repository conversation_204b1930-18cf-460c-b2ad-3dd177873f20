<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.GateOfficeMapper">

    <select id="queryApplyList" resultType="com.rjsoft.outPatient.domain.gateOffice.dto.ApplyInfoDto">
        select a.ID,a.PatName,a.Phone,c.typeName multipleTypeName,a.CreateTime submitTime,
        ISNULL(d.ReservationTime,'-') reservationTime,a.State,RTRIM(LTRIM(b.ShowValue)) stateName,a.SMSSendFlag sendFlag,
        ISNULL(RTRIM(LTRIM(e.ShowValue)),'未发送') sendFlagText,
        CASE when contactFlag!=1 then 0 when contactFlag!=1 or d.serialNo is null then 1 else 2 END isComplete,
        a.HospitalId hospId,f.SimpleName hospName
        from MDM_Pub_Application a
        inner join (
            select b.ValueCode,b.ShowValue,a.UseSpace
            from DP_MDM_CodeSystem a
            inner join DP_MDM_ValueSets b on a.CodeId=b.CodeId and a.Code='DRJCM009'
        ) b on a.State=b.ValueCode and exists(select * from Fun_SplitStr(b.UseSpace,',') where Result=a.HospitalId)
        left join MDM_Pub_Multidisciplinary c on a.MulDisciplID=c.uniqueID and a.HospitalId=c.hospitalId
        left join MDM_Pub_Reservation d on a.ID=d.ApplyId and a.HospitalId=d.HospitalId
        inner join (
            select b.ValueCode,b.ShowValue,a.UseSpace
            from DP_MDM_CodeSystem a
            inner join DP_MDM_ValueSets b on a.CodeId=b.CodeId and a.Code='DRJCM010'
        ) e on a.SMSSendFlag=e.ValueCode and exists(select * from Fun_SplitStr(e.UseSpace,',') where Result=a.HospitalId)
        inner join Tb_Hospital f on a.HospitalId=f.HOSPITALCODE
        where a.hospitalId=#{hospId}
        <if test="patName!=null and patName!=''">
            and a.PatName like '%'+#{patName}+'%'
        </if>
        <if test="state!=null and state!=''">
            and a.state=#{state}
        </if>
        <if test="beginTime!=null">
            and a.CreateTime &gt;= #{beginTime}
        </if>
        <if test="endTime!=null">
            and a.CreateTime &lt; DATEADD(dd,1,#{endTime})
        </if>
        <if test="sendFlag!=null and sendFlag!=''">
            and a.SMSSendFlag=#{sendFlag}
        </if>
        order by a.createTime desc
    </select>

    <select id="queryApplyInfo" resultType="com.rjsoft.outPatient.domain.gateOffice.dto.ApplyInfoDto">
        select a.ID,a.PatName,a.Phone,a.Address,a.MulDisciplID multipleTypeId,c.typeName multipleTypeName,
               a.State,RTRIM(LTRIM(b.ShowValue)) stateName,a.visitFlag,a.contactFlag,ISNULL(a.remark,'-') remark,
               CASE a.contactFlag when 0 then '否' when 1 then '是' else '-' END contactFlagText,
               CASE a.visitFlag when 1 then '初诊' when 2 then '复诊' else '异常' END visitFlagText,
               a.HospitalId hospId,d.SimpleName hospName,e.ReservationDate,e.ReservationBeginTime,e.ReservationEndTime
        from MDM_Pub_Application a
        inner join (
            select b.ValueCode,b.ShowValue,a.UseSpace
            from DP_MDM_CodeSystem a
            inner join DP_MDM_ValueSets b on a.CodeId=b.CodeId and a.Code='DRJCM009'
        ) b on a.State=b.ValueCode and exists(select * from Fun_SplitStr(b.UseSpace,',') where Result=a.HospitalId)
        left join MDM_Pub_Multidisciplinary c on a.MulDisciplID=c.uniqueID and a.HospitalId=c.hospitalId
        inner join Tb_Hospital d on a.HospitalId=d.HOSPITALCODE
        left join MDM_Pub_Reservation e on a.ID=e.ApplyId and a.HospitalId=e.HospitalId
        where a.hospitalId=#{hospId} and a.ID=#{id}
    </select>

    <update id="editApplyInfo">
        update
            MDM_Pub_Application
        set
            contactFlag=#{contactFlag},
            remark=#{remark},
            state=CASE when #{contactFlag}=1 then '02' when #{contactFlag}=0 then '01' else state END
        where Id=#{id} and hospitalId=#{hospId}
    </update>

    <update id="cancelApply">
        update
            MDM_Pub_Application
        set
            state='04'
        where
        Id in (
            <foreach collection="idList" item="id" separator=",">
                #{id}
            </foreach>
        )
        and hospitalId=#{hospId}
    </update>

    <select id="sendMsg" resultType="java.lang.Integer">
        exec Usp_MDM_SendMessage #{ids},#{code},#{UId},#{hospId}
    </select>

    <select id="selectMessage" resultType="com.rjsoft.outPatient.domain.gateOffice.dto.SyncResult">
        select
            top 1
        SystemNo,
                GroupDescription,
             [Source],
            Target,
            Produce,
            contentType
        from
            System_tb_DataSyncConfig
        where
            HospitalId=#{hospId}
          and GroupNo =#{id}
    </select>

    <update id="updateSendFlag">
        update
        MDM_Pub_Application
        set
        SMSSendFlag='01'
        where
        Id in (
        <foreach collection="idList" item="id" separator=",">
            #{id}
        </foreach>
        )
        and hospitalId=#{hospId}
    </update>

    <select id="getReservationInfo" resultType="java.lang.Integer">
        select COUNT(*) from MDM_Pub_Reservation where applyId=#{applyId} and hospitalId=#{hospId}
    </select>


    <insert id="saveReservationInfo">
        insert into MDM_Pub_Reservation(ApplyId,ReservationDate,ReservationBeginTime,ReservationEndTime,
                ReservationTime,CreateUserId,CreateTime,HospitalId)
        values(
            #{applyId},#{reservationDate},#{reservationBeginTime},#{reservationEndTime},
            CONVERT(varchar(10),#{reservationDate},23)+' '+#{reservationBeginTime}+'-'+#{reservationEndTime},
            #{createUserId},GETDATE(),#{hospId}
        )
    </insert>


    <update id="updateReservationInfo">
        update
            MDM_Pub_Reservation
        set ReservationDate=#{reservationDate},
            ReservationBeginTime=#{reservationBeginTime},
            ReservationEndTime=#{reservationEndTime},
            ReservationTime=CONVERT(varchar(10),#{reservationDate},23)+' '+#{reservationBeginTime}+'-'+#{reservationEndTime},
            UpdateUserId=#{updateUserId},
            UpdateTime=GETDATE()
        where ApplyId=#{applyId}
          and HospitalId=#{hospId}
    </update>

    <update id="editReservationState">
        update
            MDM_Pub_Application
        set
            state='03'
        where Id=#{id} and hospitalId=#{hospId}
    </update>

    <insert id="saveReservationDoctor">
        delete from MDM_Pub_ReservationDoctor where ApplyId=#{applyId} and HospitalId=#{hospId}

        insert into MDM_Pub_ReservationDoctor
        Values
            <foreach collection="list" separator="," item="list">
                (#{applyId},#{list.workerId},#{list.hospId},#{hospId})
            </foreach>
    </insert>

    <select id="queryDoctorInfo" resultType="com.rjsoft.outPatient.domain.gateOffice.dto.DoctorWorkerDto">
        select
            distinct
            a.WorkerId,
            rtrim(a.Name) Name,
            a.WorkerNo,
            g.DisplayName HospRelation,
            a.HospitalId hospId
        from  TB_User u
        inner join System_Tb_Worker a on u.WORKERID=a.WorkerId and a.HospitalId=a.HospitalId and a.Status=0
        inner join System_Tb_UserDepatmentRelation f on u.USERID=f.UserID and a.HospitalId=f.HosptlID
        inner join Tb_Hospital g on a.HospitalId=g.HOSPITALCODE and g.IsDeleted=0
        where u.ISDELETED=0  and a.StaffType =1 and a.Status='0'
        <if test="inputCode!=null and inputCode!=''">
            and (a.WorkerNo like '%'+#{inputCode}+'%'
                 or a.InputCode1 like '%'+#{inputCode}+'%'
                 or a.InputCode2 like '%'+#{inputCode}+'%'
                 or a.Name like '%'+#{inputCode}+'%')
        </if>
    </select>

    <select id="queryDoctorList" resultType="com.rjsoft.outPatient.domain.gateOffice.dto.DoctorInfoDto">
        select WorkerId,WorkerHospId hospId from MDM_Pub_ReservationDoctor where ApplyId=#{applyId} and HospitalId=#{hospId}
    </select>

</mapper>