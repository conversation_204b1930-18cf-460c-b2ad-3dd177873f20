package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ApplicationControlItems;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Example;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

public interface ApplicationControlItemMapper extends BaseMapper<ApplicationControlItems>, ExampleMapper<ApplicationControlItems> {


    /**
     * 查询申请单明细对照项目信息
     *
     * @param applicationConNo
     * @param hospitalCode
     * @return
     */
    List<ApplicationControlItems> getApplicationControlItem(@Param("applicationConNo") String applicationConNo, @Param("hospitalCode") Integer hospitalCode);


}
