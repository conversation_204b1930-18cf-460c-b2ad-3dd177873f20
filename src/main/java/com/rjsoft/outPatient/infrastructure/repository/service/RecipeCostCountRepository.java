package com.rjsoft.outPatient.infrastructure.repository.service;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2021/7/28 - 9:21
 */
public interface RecipeCostCountRepository {

    /**
     * 根据医生编码，日期范围，查询自费病人人均次费用
     *
     * @param doctorId 医生编码
     * @param start    开始日期
     * @param end      结束日期
     * @return 病人人均次费用
     */
    BigDecimal getZfPerCapitaCost(int doctorId, LocalDate start, LocalDate end);

}
