package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.domain.doctorElemMain.constant.HospitalIdEnum;
import com.rjsoft.outPatient.domain.config.dto.DoctorDeptDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.OftenClincDoctor;
import com.rjsoft.outPatient.infrastructure.repository.mapper.OftenClincDoctorMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.OftenClincRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 常门诊
 *
 * <AUTHOR>
@AllArgsConstructor
@Service
public class OftenClincRepositoryImpl implements OftenClincRepository {

    OftenClincDoctorMapper oftenClincDoctorMapper;


    @Override
    @DatabaseAnnotation
    public List<OftenClincDoctor> getDoctorDeptById(Integer doctorId,Integer hospitalCode) {
        return oftenClincDoctorMapper.getDoctorDeptById(doctorId,hospitalCode);
    }

    /**
     * 根据医生id和医院编码获取医生信息
     *
     * @param doctorId
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public List<OftenClincDoctor> getOftenDoctorById(Integer doctorId, Integer hospitalCode) {
        return oftenClincDoctorMapper.getOftenDoctorById(doctorId, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public List<OftenClincDoctor> getDoctorDeptByScheduling(Integer hospitalCode, Integer doctorId) {

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String time = df.format(new Date());

        HashMap parm = new HashMap();
        parm.put("hospitalCode", hospitalCode);
        parm.put("doctorId", doctorId);
        parm.put("begindate", time);
        parm.put("enddate", time);

        if (HospitalIdEnum.GENERAL.getCode().equals(hospitalCode)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER);
        }else if(HospitalIdEnum.BRANCH.getCode().equals(hospitalCode)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.RJCONFIGER3);
        }
        return oftenClincDoctorMapper.getDoctorDeptByScheduling(parm);
    }

    @Override
    @DatabaseAnnotation
    public boolean saveOftenClincDoctor(OftenClincDoctor entity) {
        return oftenClincDoctorMapper.insert(entity) > 0;
    }

    @Override
    @DatabaseAnnotation
    public boolean delOftenClincDoctor(Integer doctorId, Integer deptId, Integer hospitalCode) {
        OftenClincDoctor entity = new OftenClincDoctor();
        entity.setDoctorId(doctorId);
        entity.setHospitalCode(hospitalCode);
        if (deptId != null && deptId > 0) {
            entity.setDeptId(deptId);
        }
        return oftenClincDoctorMapper.delete(entity) > 0;
    }

    /**
     * 获取常门诊医生去重数据
     *
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public List<DoctorDeptDto> getDistinctDoctorAndDeptId(Integer hospitalCode) {
        return oftenClincDoctorMapper.getDistinctDoctorAndDeptId(hospitalCode);
    }

    /**
     * 根据医生列表获取实体类
     *
     * @param doctors
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public List<OftenClincDoctor> getInfoByDoctors(List<Integer> doctors, Integer hospitalCode) {
        return oftenClincDoctorMapper.getInfoByDoctors(doctors,hospitalCode);
    }

}
