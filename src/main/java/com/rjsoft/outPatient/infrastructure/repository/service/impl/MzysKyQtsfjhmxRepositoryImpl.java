package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.domain.research.dto.ExtraVisitPlanSaveInputDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysKyQtsfjhmx;
import com.rjsoft.outPatient.infrastructure.repository.mapper.MzysKyQtsfjhmxMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.MzysKyQtsfjhmxRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

@AllArgsConstructor
@Service
public class MzysKyQtsfjhmxRepositoryImpl implements MzysKyQtsfjhmxRepository {
    private MzysKyQtsfjhmxMapper mzysKyQtsfjhmxMapper;

    @Override
    public boolean saveExtraVisitPlan(ExtraVisitPlanSaveInputDto **********************) {

        return false;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<MzysKyQtsfjhmx> getExtraVisitPlanDetail(Integer id) {
        Weekend<MzysKyQtsfjhmx> weekend = new Weekend<>(MzysKyQtsfjhmx.class);
        weekend.weekendCriteria().andEqualTo(MzysKyQtsfjhmx::getFsid,id);
        return mzysKyQtsfjhmxMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public boolean insertMzysKyQtsfjhmxList(List<MzysKyQtsfjhmx> mzysKyQtsfjhmxList) {
        for(MzysKyQtsfjhmx mzysKyQtsfjhmx:mzysKyQtsfjhmxList){
            mzysKyQtsfjhmx.setId(null);
            mzysKyQtsfjhmxMapper.insertSelective(mzysKyQtsfjhmx);
        }
        return true;
    }
}
