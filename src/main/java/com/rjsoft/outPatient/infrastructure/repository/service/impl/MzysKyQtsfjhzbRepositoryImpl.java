package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.domain.research.dto.VisitPlanDto;
import com.rjsoft.outPatient.domain.research.dto.VisitPlanInputDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysKyQtsfjhmx;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysKyQtsfjhzb;
import com.rjsoft.outPatient.infrastructure.repository.mapper.MzysKyQtsfjhmxMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.MzysKyQtsfjhzbMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.MzysKyQtsfjhzbRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

@AllArgsConstructor
@Service
public class MzysKyQtsfjhzbRepositoryImpl implements MzysKyQtsfjhzbRepository {
    private MzysKyQtsfjhzbMapper mzysKyQtsfjhzbMapper;
    private MzysKyQtsfjhmxMapper mzysKyQtsfjhmxMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.RDR_RESHISEXCHANGE)
    public List<VisitPlanDto> visitPlan(VisitPlanInputDto visitPlanInputDto) {
        return mzysKyQtsfjhzbMapper.visitPlan(visitPlanInputDto);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<MzysKyQtsfjhzb> getMzysKyQtsfjhzbList(Integer patientId, Integer projectId, Long fplanId) {
        Weekend<MzysKyQtsfjhzb> weekend = new Weekend<>(MzysKyQtsfjhzb.class);
        weekend.weekendCriteria().andEqualTo(MzysKyQtsfjhzb::getPatientId,patientId).andEqualTo(MzysKyQtsfjhzb::getProjectId,projectId).andEqualTo(MzysKyQtsfjhzb::getPid,fplanId);
        return mzysKyQtsfjhzbMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Integer insertMzysKyQtsfjhzb(MzysKyQtsfjhzb mzysKyQtsfjhzb,List<MzysKyQtsfjhmx> mzysKyQtsfjhmxList,List<Integer> delIds) {
        if(mzysKyQtsfjhzb.getId()==null){
            boolean flag = mzysKyQtsfjhzbMapper.insertSelective(mzysKyQtsfjhzb)>0;
            int flagNum = 0;
            for(MzysKyQtsfjhmx mzysKyQtsfjhmx:mzysKyQtsfjhmxList){
                mzysKyQtsfjhmx.setFsid(mzysKyQtsfjhzb.getId());
                mzysKyQtsfjhmxMapper.insertSelective(mzysKyQtsfjhmx);
                flagNum++;
            }
            if(flag&&flagNum==mzysKyQtsfjhmxList.size()){
                return mzysKyQtsfjhzb.getId();
            }else{
                return 0;
            }
        }else{
            boolean flag = mzysKyQtsfjhzbMapper.updateByPrimaryKeySelective(mzysKyQtsfjhzb)>0;
            for(MzysKyQtsfjhmx mzysKyQtsfjhmx:mzysKyQtsfjhmxList){
                if(mzysKyQtsfjhmx.getId()==null){
                    mzysKyQtsfjhmx.setFsid(mzysKyQtsfjhzb.getId());
                    mzysKyQtsfjhmxMapper.insertSelective(mzysKyQtsfjhmx);
                }else{
                    mzysKyQtsfjhmxMapper.updateByPrimaryKeySelective(mzysKyQtsfjhmx);
                }
            }
            if(delIds!=null&&delIds.size()>0){
                for(Integer id:delIds){
                    mzysKyQtsfjhmxMapper.deleteByPrimaryKey(id);
                }
            }
            if(flag){
                return mzysKyQtsfjhzb.getId();
            }else{
                return 0;
            }
        }
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)

    public MzysKyQtsfjhzb getById(Integer id) {
        return mzysKyQtsfjhzbMapper.selectByPrimaryKey(id);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public String submitExtraVisitPlan(Integer id) {
        return mzysKyQtsfjhzbMapper.submitExtraVisitPlan(String.valueOf(id));
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public boolean insertMzysKyQtsfjhzb(MzysKyQtsfjhzb mzysKyQtsfjhzb) {
        return mzysKyQtsfjhzbMapper.insertSelective(mzysKyQtsfjhzb)>0;
    }
}
