<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ItemInfoDeptMapper">

    <select id="getDrugInfoDept" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ItemInfoDept">
        select GlobalId,DrugId,HospitalId,UseDeptRange,DeptId,Stopped
        from System_Tv_ItemInfoDept
        where DrugId = #{drugId} and HospitalId = #{hospitalId}
          and UseDeptRange = #{useDeptRange} and (DeptId = #{deptId} or DeptId = -1)
            and Stopped = 0
    </select>

    <select id="getItemInfoHospital" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ItemInfoDept">
        select b.<PERSON>d,b.<PERSON>d,b.<PERSON>,b.UseD<PERSON>R<PERSON>e,b.DeptId,b.Stopped
        from System_Tv_ItemInfoDept a
        inner join System_Tv_ItemInfoDept b
        on a.GlobalId = b.GlobalId and b.UseDeptRange = #{useDeptRange}
        where a.Stopped = 0 and a.HospitalId = #{hospitalId} and a.UseDeptRange = #{useDeptRange} and a.DrugId = #{drugId}
    </select>

</mapper>