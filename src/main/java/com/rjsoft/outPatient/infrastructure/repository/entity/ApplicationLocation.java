package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 申请单部位信息
 */
@Data
@Table(name = "MZYS_TB_JCSQDBW")
public class ApplicationLocation  implements Serializable {

    @Id
    @Column(name = "bwlsh")
    private Integer locationId;

    @Column(name = "zhlsh")
    private Integer fromGroupNo;

    @Column(name = "bwmc")
    private String locationName;

    @Column(name = "yybm")
    private Integer hospitalCode;

}


