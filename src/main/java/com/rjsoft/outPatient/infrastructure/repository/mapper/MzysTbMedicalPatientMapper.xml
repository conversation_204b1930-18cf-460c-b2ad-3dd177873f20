<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.MzysTbMedicalPatientMapper">

  <select id="queryReceptionByRegNoList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ReceptionRecord">
    select t.ghlsh regNo, t.jzlsh receptionNo, t.ysbm doctorId, t.ksbm deptId, t.sckzrq firstDate
    from MZYS_TV_KZJL t (nolock)
    where t.hospitalCode=#{hospitalCode}
    and  t.ghlsh in
    <foreach item="item" collection="regNoList" separator="," close=")" open="(" index="">
      #{item}
    </foreach>
  </select>

  <select id="queryMDTWorkRegNoList" resultType="java.lang.Long">
    select RegNo
    from Reg_Tb_RegisterDoctor_Time (nolock)
    where HospitalCode = #{hospitalCode} and Status=0
    and RegNo in
    <foreach item="item" collection="regNoList" separator="," close=")" open="(" index="">
      #{item}
    </foreach>
  </select>

  <select id="queryChronicRegNoList" resultType="java.lang.Long">
    select a.regNo
    from Reg_TV_OutpatientInvoice a (nolock)
    left join Reg_GjYiBao_Charge b (nolock) on a.ChargeNo=b.ChargeNo
    where a.HospitalCode=#{hospitalCode} and b.medtype like '%140101%'
    and a.regNo in
    <foreach item="item" collection="regNoList" separator="," close=")" open="(" index="">
      #{item}
    </foreach>
  </select>

  <select id="queryEnterHospitalRequisitionList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.EnterHospitalRequisition">
    select a.ghlsh regNo, t.createTime,t.CZR opCode
    from MZYS_TB_MZZYSQD t (nolock)
    inner join MZYS_TB_KZJL a (nolock) on t.JZLSH=a.jzlsh and t.HospitalCode=a.hospitalCode
    where a.hospitalCode=#{hospitalCode} and t.status not in (1,6)
    and a.ghlsh in
    <foreach item="item" collection="regNoList" separator="," close=")" open="(" index="">
      #{item}
    </foreach>
  </select>

  <select id="queryMedicalRegisterList" resultType="com.rjsoft.outPatient.domain.medicalInsurance.vo.QueryMedicalRegisterVO">
    SELECT
      t.HospitalCode,
      t.RegNo,
      t.PatID,
      a.PatName,
      a.HospNo OutPatientNo,
      a.Sex,
      t.RegistTime,
      t.DeptID,
      t.DoctorID,
      a.CertificateType,
      rtrim(a.CertificateNo) certificateNo,
      isnull(b.upload_status,0) uploadStatus,
      b.upload_msg uploadMsg
    FROM Reg_Tv_RegisterList t
    INNER JOIN Reg_Tb_PatientList a ON t.PatID= a.PatID
    LEFT JOIN MZYS_Tb_MedicalPatient b ON t.RegNo= b.reg_no AND t.HospitalCode= b.hospital_id AND b.status= 1
    WHERE t.HospitalCode=#{param.hospitalCode} and t.Status=0 and t.fzflag = 4
    <if test="param.startTime != null">
      and t.RegistTime &gt;= #{param.startTime}
    </if>
    <if test="param.endTime != null">
      and t.RegistTime &lt;= #{param.endTime}
    </if>
    <if test="param.deptId != null">
      and t.DeptID = #{param.deptId}
    </if>
    <if test="param.uploadStatus != null">
      and isnull(b.upload_status, 0) = #{param.uploadStatus}
    </if>
    order by t.RegistTime desc
  </select>

  <select id="queryExportMedicalRegisterList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList">
    SELECT
      t.RegNo AS regNo,
      t.ReturnRegNo AS returnRegNo,
      t.PatID AS patID,
      t.NewPatID AS newPatID,
      t.CardNo AS cardNo,
      t.OutPatientNo AS outPatientNo,
      t.HospNo AS hospNo,
      t.PatName AS patName,
      t.CourseID AS courseID,
      t.CourseName AS courseName,
      t.DeptID AS deptID,
      t.DoctorID AS doctorID,
      t.DoctorLevel AS doctorLevel,
      t.ChargeType AS chargeType,
      t.BlanceWay AS blanceWay,
      t.RegistType AS registType,
      t.AppointmentWay AS appointmentWay,
      t.AppointmentNo AS appointmentNo,
      t.RegistOrder AS registOrder,
      t.registMode AS RegistMode,
      t.VisitTime AS visitTime,
      t.Status AS status,
      t.FzFlag AS fzFlag,
      t.RegistTime AS registTime,
      t.ComputerNo AS computerNo,
      t.OpCode AS opCode,
      t.CreateTime AS createTime,
      t.CureCode AS cureCode,
      t.VisitFlag AS visitFlag,
      t.ReferralFlag AS referralFlag,
      t.DeptKind AS deptKind,
      t.HospitalCode AS hospitalCode,
      t.UnitNo AS unitNo,
      t.GhDoctor AS ghDoctor
    FROM Reg_Tv_RegisterList t (nolock)
    INNER JOIN Reg_Tb_PatientList a (nolock) ON t.PatID= a.PatID
    WHERE t.HospitalCode=#{param.hospitalCode} and t.Status=0 and t.fzflag = 4
    <if test="param.startTime != null">
      and t.RegistTime &gt;= #{param.startTime}
    </if>
    <if test="param.endTime != null">
      and t.RegistTime &lt;= #{param.endTime}
    </if>
    <if test="param.deptId != null">
      and t.DeptID = #{param.deptId}
    </if>
    order by t.RegNo desc
  </select>


  <insert id="insertMedicalPatientBatch">
    INSERT INTO MZYS_Tb_MedicalPatient (
    id, reg_no, org_code, org_name, medical_no,
    patient_id, patient_name, sex, birthday, marry,
    nationality, nation, certifier_type, certifier_no, address,
    relationship_tel, allergy_flag, allergy_drug, allergy_other_flag, allergy_other,
    hospital_id, upload_status, upload_msg, status, create_by,
    create_time, update_by, update_time
    ) VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.id}, #{item.regNo}, #{item.orgCode}, #{item.orgName}, #{item.medicalNo},
      #{item.patientId}, #{item.patientName}, #{item.sex}, #{item.birthday,jdbcType=TIMESTAMP}, #{item.marry},
      #{item.nationality}, #{item.nation}, #{item.certifierType}, #{item.certifierNo}, #{item.address},
      #{item.relationshipTel}, #{item.allergyFlag}, #{item.allergyDrug}, #{item.allergyOtherFlag}, #{item.allergyOther},
      #{item.hospitalId}, #{item.uploadStatus}, #{item.uploadMsg}, #{item.status}, #{item.createBy},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateBy}, #{item.updateTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>

  <insert id="insertMedicalReceptionBatch">
    INSERT INTO MZYS_Tb_MedicalReception (
    id, reg_no, register_time, arrival_time, reception_time,
    reception_dept, reception_doctor, reception_doctor_title, reception_type, visit_flag,
    infusion_flag, chronic_flag, emergency_level, emergency_destination, admission_register_time,
    chief_complaint, hospital_id
    ) VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.id}, #{item.regNo}, #{item.registerTime,jdbcType=TIMESTAMP}, #{item.arrivalTime,jdbcType=TIMESTAMP}, #{item.receptionTime,jdbcType=TIMESTAMP},
      #{item.receptionDept}, #{item.receptionDoctor}, #{item.receptionDoctorTitle}, #{item.receptionType}, #{item.visitFlag},
      #{item.infusionFlag}, #{item.chronicFlag}, #{item.emergencyLevel}, #{item.emergencyDestination}, #{item.admissionRegisterTime,jdbcType=TIMESTAMP},
      #{item.chiefComplaint}, #{item.hospitalId}
      )
    </foreach>
  </insert>

  <insert id="insertMedicalDiagBatch">
    INSERT INTO MZYS_Tb_MedicalDiag (id, reg_no, diag_code, diag_name, main_flag, order_no, hospital_id )
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.regNo}, #{item.diagCode}, #{item.diagName}, #{item.mainFlag}, #{item.orderNo}, #{item.hospitalId})
    </foreach>
  </insert>

  <insert id="insertMedicalFeeBatch">
    INSERT INTO MZYS_Tb_MedicalFee (
    id, reg_no, total_fee, out_pocket_fee, commonly_serve_fee, commonly_treat_fee,
    nursing_fee, other_serve_fee, pathology_diag_fee, laboratory_diag_fee, imaging_diag_fee, clinical_diag_fee,
    no_operation_fee, clinical_treat_fee, operation_treat_fee, anaesthesia_fee, operation_fee, recovery_fee,
    chinese_treat_fee, western_drug_fee, antibiosis_drug_fee, chinese_drug_fee, chinese_herbal_fee, blood_fee,
    albumin_fee, globins_Fee, coagula_factors_fee, cell_factor_fee, inspect_disposable_fee, treat_disposable_fee,
    operation_disposable_fee, other_fee, hospital_id
    ) VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.id}, #{item.regNo}, #{item.totalFee}, #{item.outPocketFee}, #{item.commonlyServeFee}, #{item.commonlyTreatFee},
      #{item.nursingFee}, #{item.otherServeFee}, #{item.pathologyDiagFee}, #{item.laboratoryDiagFee}, #{item.imagingDiagFee}, #{item.clinicalDiagFee},
      #{item.noOperationFee}, #{item.clinicalTreatFee}, #{item.operationTreatFee}, #{item.anaesthesiaFee}, #{item.operationFee}, #{item.recoveryFee},
      #{item.chineseTreatFee}, #{item.westernDrugFee}, #{item.antibiosisDrugFee}, #{item.chineseDrugFee}, #{item.chineseHerbalFee}, #{item.bloodFee},
      #{item.albuminFee}, #{item.globinsFee}, #{item.coagulaFactorsFee}, #{item.cellFactorFee}, #{item.inspectDisposableFee}, #{item.treatDisposableFee},
      #{item.operationDisposableFee}, #{item.otherFee}, #{item.hospitalId}
      )
    </foreach>
  </insert>

</mapper>