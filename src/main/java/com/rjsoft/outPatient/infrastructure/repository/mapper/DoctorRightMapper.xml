<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DoctorRightMapper">

    <select id="getDoctorRightList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.DoctorRight">
        select * from MZYS_TB_DoctorRight
        <where>
            <foreach collection="list" index="index" item="item">
                or (WorkerID=#{item.doctorId} and Type=#{item.typeId} and hospitalCode=#{item.hospitalCode})
            </foreach>
        </where>
    </select>
    <delete id="delDoctorRight">
        DELETE FROM MZYS_TB_DoctorRight WHERE WorkerID in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
                 and Type=#{typeId} and hospitalCode = #{hospitalCode}
    </delete>
</mapper>