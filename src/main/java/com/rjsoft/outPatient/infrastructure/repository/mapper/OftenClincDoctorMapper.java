package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.config.dto.DoctorDeptDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.OftenClincDoctor;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Date;
import java.util.HashMap;
import java.util.List;


/**
 * 常门诊医生
 *
 * <AUTHOR>
public interface OftenClincDoctorMapper extends Mapper<OftenClincDoctor> {

    /**
     * 根据医生编码获取常门诊医生科室
     *
     * @param hospitalCode
     * @param doctorId
     * @return
     */
    default List<OftenClincDoctor> getDoctorDeptById(Integer doctorId,Integer hospitalCode) {
        OftenClincDoctor oftenClincDoctor = new OftenClincDoctor();
        if (doctorId != null && doctorId > 0) {
            oftenClincDoctor.setDoctorId(doctorId);
        }
        oftenClincDoctor.setHospitalCode(hospitalCode);
        return select(oftenClincDoctor);
    }

    /**
     * 根据医生id和医院编码获取医生信息
     * @param doctorId
     * @param hospitalCode
     */
    default List<OftenClincDoctor> getOftenDoctorById(Integer doctorId,Integer hospitalCode){
        OftenClincDoctor doctor = new OftenClincDoctor();
        doctor.setDoctorId(doctorId);
        doctor.setHospitalCode(hospitalCode);
        return select(doctor);
    }


    /**
     * 获取医生当天排班科室
     *
     * @param parameter
     * @return
     */
    List<OftenClincDoctor> getDoctorDeptByScheduling(HashMap parameter);

    /**
     * 获取常门诊医生去重数据
     */
    List<DoctorDeptDto> getDistinctDoctorAndDeptId(Integer hospitalCode);

    /**
     *  根据医生列表获取实体类
     */
    default List<OftenClincDoctor> getInfoByDoctors(List<Integer> doctors,Integer hospitalCode){
        Weekend<OftenClincDoctor> weekend = new Weekend<OftenClincDoctor>(OftenClincDoctor.class);
        WeekendCriteria<OftenClincDoctor, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(OftenClincDoctor::getDoctorId,doctors).andEqualTo(OftenClincDoctor::getHospitalCode,hospitalCode);
        return selectByExample(weekend);
    }

}




