package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 门诊处方
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class outpatientRepositoryImpl implements OutpatientRepository {
    OutpatientInvoiceMapper outpatientInvoiceMapper;
    OutpatientInvoiceViewMapper outpatientInvoiceViewMapper;
    ChargeDetailViewMapper chargeDetailViewMapper;

    /**
     * 根据就诊流水号获取门诊发票
     *
     * @param regNo
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public OutpatientInvoice getOutpatientInvoiceByRegNo(Long regNo, Integer hospitalCode) {
        return outpatientInvoiceMapper.getInvoice(regNo, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<OutpatientInvoiceView> getOutpatientInvoiceByRegNoList(List<Long> regNoList, Integer hospitalCode) {
        return outpatientInvoiceViewMapper.getOutpatientInvoiceByRegNoList(regNoList, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<ChargeDetailView> getChargeDetailByChargeNoList(List<Long> chargeNoList, Integer hospitalCode) {
        return chargeDetailViewMapper.getChargeDetailByChargeNoList(chargeNoList, hospitalCode);
    }

}
