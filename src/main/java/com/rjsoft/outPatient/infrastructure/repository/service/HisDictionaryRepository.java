package com.rjsoft.outPatient.infrastructure.repository.service;


import com.rjsoft.outPatient.infrastructure.repository.entity.DictionaryType;
import com.rjsoft.outPatient.infrastructure.repository.entity.HisDictionary;

import java.util.List;

/**
 * 医院字典
 * <AUTHOR>
public interface HisDictionaryRepository {
    /**
     * 根据字典类型集合获取系统字典
     * @param types
     * @param hospitalCode
     * @return
     */
    List<HisDictionary> getDictionaryByTypes(List<Integer> types, Integer hospitalCode);

    /**
     * 根据类型集合查询字典类型
     * @param types
     * @return
     */
    List<DictionaryType> getDictionaryTypeByIds(List<Integer>types);
}
