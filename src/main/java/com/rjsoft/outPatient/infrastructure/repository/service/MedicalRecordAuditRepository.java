package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.param.SearchParam;
import com.rjsoft.outPatient.domain.caseHistory.dto.*;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 病历审核信息
 */
public interface MedicalRecordAuditRepository {

    /**
     * 根据病历号获取病历审核信息
     *
     * @param blId
     * @return
     */
    MedicalRecordAudit getMedicalRecordAuditByBlId(String blId);

    /**
     * 查询科室审核病历列表
     *
     * @param param
     * @return
     */
    List<MedicalRecordAudit> getDeptMedicalRecordAuditList(SearchParam param);

    /**
     * 查询病历审核抽查列表
     *
     * @param param
     * @return
     */
    List<MedicalRecordAudit> getSpotCheckList(SearchParam param);

    /**
     * 查询病历审核结果列表
     *
     * @param param
     * @return
     */
    List<MedicalRecordAudit> getCheckList(SearchParam param);

    /**
     * 查询审核历史
     *
     * @param medicalRecordAuditId
     * @param hospitalCode
     * @return
     */
    List<MedicalRecordAuditRecord> getMedicalRecordAuditHistory(Integer medicalRecordAuditId, Integer hospitalCode);

    /**
     * 保存病历审核意见
     *
     * @param medicalRecordAuditRecord
     * @param blId
     * @return
     */
    boolean saveMedicalRecordAudit(MedicalRecordAuditRecord medicalRecordAuditRecord, Integer blId);

    /**
     * 添加到抽查列表
     *
     * @param params
     */
    void addMedicalRecordAudit(Map<String, Object> params);

    /**
     * 查询审核病历数据
     *
     * @param medicalRecordAuditId
     * @param hospitalCode
     * @return
     */
    MedicalRecordAuditRecord getCheckCaseInfo(Integer medicalRecordAuditId, Integer hospitalCode);

    /**
     * 保存病历审核信息
     *
     * @param medicalRecordAuditRecord
     * @return
     */
    boolean updateMedicalRecordAudit(MedicalRecordAuditRecord medicalRecordAuditRecord);

    /**
     * 保存病历操作日志
     *
     * @param blId
     * @param hospitalCode
     * @return
     */
    boolean saveCaseHistoryLogByType(Integer blId, Integer doctorId, String doctorName, Integer hospitalCode);

}
