package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.utils.TyMapUtil;
import com.rjsoft.common.utils.TyMdc;
import com.rjsoft.outPatient.domain.recipe.constant.TyKey;
import com.rjsoft.outPatient.infrastructure.repository.entity.OverdoseDrugs;
import com.rjsoft.outPatient.infrastructure.repository.entity.OverdoseDrugsDept;
import com.rjsoft.outPatient.infrastructure.repository.mapper.OverdoseDrugsDeptMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.OverdoseDrugsMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.OverdoseDrugsRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/27-3:49 下午
 */
@Service
@AllArgsConstructor
public class OverdoseDrugsRepositoryImpl implements OverdoseDrugsRepository {

    private final OverdoseDrugsMapper overdoseDrugsMapper;
    private final OverdoseDrugsDeptMapper overdoseDrugsDeptMapper;

    @Override
    @DatabaseAnnotation
    public OverdoseDrugs getOverdoseDrugsByDrugIdAndDeptId(Integer drugId, Integer deptId, Integer hospitalCode) {
        final Weekend<OverdoseDrugsDept> overdoseDrugsDeptWeekend = new Weekend<>(OverdoseDrugsDept.class);
        overdoseDrugsDeptWeekend.weekendCriteria()
                .andEqualTo(OverdoseDrugsDept::getDeptId, deptId)
                .andEqualTo(OverdoseDrugsDept::getHospitalCode, hospitalCode);
        final List<OverdoseDrugsDept> overdoseDrugsDeptList = overdoseDrugsDeptMapper.selectByExample(overdoseDrugsDeptWeekend);
        final List<Integer> ids = overdoseDrugsDeptList.parallelStream().map(OverdoseDrugsDept::getId).collect(Collectors.toList());
        if (ids.size() <= 0) {
            return null;
        }

        final Weekend<OverdoseDrugs> overdoseDrugsWeekend = new Weekend<>(OverdoseDrugs.class);
        overdoseDrugsWeekend.weekendCriteria()
                .andEqualTo(OverdoseDrugs::getDrugId, drugId)
                .andEqualTo(OverdoseDrugs::getHospitalCode, hospitalCode)
                .andIn(OverdoseDrugs::getId, ids);
        List<OverdoseDrugs> list = overdoseDrugsMapper.selectByExample(overdoseDrugsWeekend);
        if (list.size() > 0) {
            return list.get(0);
        } else {
            return null;
        }

    }

    @Override
    @DatabaseAnnotation
    public OverdoseDrugs tyMdcGetOverdoseDrugsByDrugIdAndDeptId(Integer drugId, Integer deptId, Integer hospitalCode) {

        final Weekend<OverdoseDrugsDept> overdoseDrugsDeptWeekend = new Weekend<>(OverdoseDrugsDept.class);
        overdoseDrugsDeptWeekend.weekendCriteria()
                .andEqualTo(OverdoseDrugsDept::getDeptId, deptId)
                .andEqualTo(OverdoseDrugsDept::getHospitalCode, hospitalCode);
        final List<OverdoseDrugsDept> overdoseDrugsDeptList = overdoseDrugsDeptMapper.selectByExample(overdoseDrugsDeptWeekend);
        final List<Integer> ids = overdoseDrugsDeptList.parallelStream().map(OverdoseDrugsDept::getId).collect(Collectors.toList());
        if (ids.size() <= 0) {
            return null;
        }

        Map<String,List<OverdoseDrugs>> overdoseDrugsByDrugIdMap = TyMdc.get(TyKey.OVERDOSE_DRUGS_BY_DRUG_ID_MAP);
        if(overdoseDrugsByDrugIdMap == null){
            final Weekend<OverdoseDrugs> overdoseDrugsWeekend = new Weekend<>(OverdoseDrugs.class);
            overdoseDrugsWeekend.weekendCriteria()
                    .andEqualTo(OverdoseDrugs::getDrugId, drugId)
                    .andEqualTo(OverdoseDrugs::getHospitalCode, hospitalCode)
                    .andIn(OverdoseDrugs::getId, ids);
            List<OverdoseDrugs> list = overdoseDrugsMapper.selectByExample(overdoseDrugsWeekend);
            if (list.size() > 0) {
                return list.get(0);
            } else {
                return null;
            }
        }else{
            List<OverdoseDrugs> overdoseDrugList = overdoseDrugsByDrugIdMap.get(drugId);
            if(overdoseDrugList == null||overdoseDrugList.size()==0){
                return null;
            }
            return overdoseDrugList.get(0) ;
        }


    }

    @Override
    @DatabaseAnnotation
    public Map<String,List<OverdoseDrugs>> listOverdoseDrugsByDrugIdAndHosptailCode(List<Integer> itemCodeList, Integer hospitalCode) {
        if (itemCodeList.size() == 0 || hospitalCode == null){
            return new HashMap<>();
        }
        final Weekend<OverdoseDrugs> overdoseDrugsWeekend = new Weekend<>(OverdoseDrugs.class);
        overdoseDrugsWeekend.weekendCriteria()
                .andIn(OverdoseDrugs::getDrugId, itemCodeList)
                .andEqualTo(OverdoseDrugs::getHospitalCode, hospitalCode);
        List<OverdoseDrugs> list = overdoseDrugsMapper.selectByExample(overdoseDrugsWeekend);
        Map<String,List<OverdoseDrugs>> overdoseDrugsByDrugIdMap = TyMapUtil.listToListMapMul(list, "getHospitalCode","getDrugId");
        return overdoseDrugsByDrugIdMap;
    }
}
