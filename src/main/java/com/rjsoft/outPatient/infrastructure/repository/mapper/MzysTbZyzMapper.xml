<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.MzysTbZyzMapper" >
  <resultMap id="BaseResultMap" type="com.rjsoft.outPatient.domain.hospitalProve.pojo.MzysTbZyz" >
    <id column="jzlsh" property="jzlsh" jdbcType="CHAR" />
    <result column="lczd" property="lczd" jdbcType="VARCHAR" />
    <result column="ys" property="ys" jdbcType="VARCHAR" />
    <result column="bscw" property="bscw" jdbcType="VARCHAR" />
    <result column="glffcb" property="glffcb" jdbcType="VARCHAR" />
    <result column="glffhx" property="glffhx" jdbcType="VARCHAR" />
    <result column="yszl" property="yszl" jdbcType="VARCHAR" />
    <result column="hszysx" property="hszysx" jdbcType="VARCHAR" />
    <result column="mzcl" property="mzcl" jdbcType="VARCHAR" />
    <result column="ysqz" property="ysqz" jdbcType="INTEGER" />
    <result column="qzsj" property="qzsj" jdbcType="TIMESTAMP" />
    <result column="ysxm" property="ysxm" jdbcType="VARCHAR" />
    <result column="hzjg" property="hzjg" jdbcType="VARCHAR" />
    <result column="hzmz" property="hzmz" jdbcType="VARCHAR" />
    <result column="ksbm" property="ksbm" jdbcType="INTEGER" />
    <result column="CreateUserId" property="createUserId" jdbcType="INTEGER" />
    <result column="CreateOn" property="createOn" jdbcType="TIMESTAMP" />
    <result column="HospitalId" property="hospitalId" jdbcType="INTEGER" />
    <result column="UpdateUserId" property="updateUserId" jdbcType="INTEGER" />
    <result column="UpdateOn" property="updateOn" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    jzlsh, lczd, ys, bscw, glffcb, glffhx, yszl, hszysx, mzcl, ysqz, qzsj, ysxm, hzjg, 
    hzmz, ksbm, CreateUserId, CreateOn, HospitalId, UpdateUserId, UpdateOn
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.rjsoft.outPatient.domain.hospitalProve.pojo.MzysTbZyzExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from MZYS_TB_ZYZ
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from MZYS_TB_ZYZ
    where jzlsh = #{jzlsh,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from MZYS_TB_ZYZ
    where jzlsh = #{jzlsh,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.rjsoft.outPatient.domain.hospitalProve.pojo.MzysTbZyzExample" >
    delete from MZYS_TB_ZYZ
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.rjsoft.outPatient.domain.hospitalProve.pojo.MzysTbZyz" >
    insert into MZYS_TB_ZYZ (jzlsh, lczd, ys, 
      bscw, glffcb, glffhx, 
      yszl, hszysx, mzcl, 
      ysqz, qzsj, ysxm, 
      hzjg, hzmz, ksbm, CreateUserId, 
      CreateOn, HospitalId, UpdateUserId, 
      UpdateOn)
    values (#{jzlsh,jdbcType=CHAR}, #{lczd,jdbcType=VARCHAR}, #{ys,jdbcType=VARCHAR}, 
      #{bscw,jdbcType=VARCHAR}, #{glffcb,jdbcType=VARCHAR}, #{glffhx,jdbcType=VARCHAR}, 
      #{yszl,jdbcType=VARCHAR}, #{hszysx,jdbcType=VARCHAR}, #{mzcl,jdbcType=VARCHAR}, 
      #{ysqz,jdbcType=INTEGER}, #{qzsj,jdbcType=TIMESTAMP}, #{ysxm,jdbcType=VARCHAR}, 
      #{hzjg,jdbcType=VARCHAR}, #{hzmz,jdbcType=VARCHAR}, #{ksbm,jdbcType=INTEGER}, #{createUserId,jdbcType=INTEGER}, 
      #{createOn,jdbcType=TIMESTAMP}, #{hospitalId,jdbcType=INTEGER}, #{updateUserId,jdbcType=INTEGER}, 
      #{updateOn,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.rjsoft.outPatient.domain.hospitalProve.pojo.MzysTbZyz" >
    insert into MZYS_TB_ZYZ
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="jzlsh != null" >
        jzlsh,
      </if>
      <if test="lczd != null" >
        lczd,
      </if>
      <if test="ys != null" >
        ys,
      </if>
      <if test="bscw != null" >
        bscw,
      </if>
      <if test="glffcb != null" >
        glffcb,
      </if>
      <if test="glffhx != null" >
        glffhx,
      </if>
      <if test="yszl != null" >
        yszl,
      </if>
      <if test="hszysx != null" >
        hszysx,
      </if>
      <if test="mzcl != null" >
        mzcl,
      </if>
      <if test="ysqz != null" >
        ysqz,
      </if>
      <if test="qzsj != null" >
        qzsj,
      </if>
      <if test="ysxm != null" >
        ysxm,
      </if>
      <if test="hzjg != null" >
        hzjg,
      </if>
      <if test="hzmz != null" >
        hzmz,
      </if>
      <if test="ksbm != null" >
        ksbm,
      </if>
      <if test="createUserId != null" >
        CreateUserId,
      </if>
      <if test="createOn != null" >
        CreateOn,
      </if>
      <if test="hospitalId != null" >
        HospitalId,
      </if>
      <if test="updateUserId != null" >
        UpdateUserId,
      </if>
      <if test="updateOn != null" >
        UpdateOn,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="jzlsh != null" >
        #{jzlsh,jdbcType=CHAR},
      </if>
      <if test="lczd != null" >
        #{lczd,jdbcType=VARCHAR},
      </if>
      <if test="ys != null" >
        #{ys,jdbcType=VARCHAR},
      </if>
      <if test="bscw != null" >
        #{bscw,jdbcType=VARCHAR},
      </if>
      <if test="glffcb != null" >
        #{glffcb,jdbcType=VARCHAR},
      </if>
      <if test="glffhx != null" >
        #{glffhx,jdbcType=VARCHAR},
      </if>
      <if test="yszl != null" >
        #{yszl,jdbcType=VARCHAR},
      </if>
      <if test="hszysx != null" >
        #{hszysx,jdbcType=VARCHAR},
      </if>
      <if test="mzcl != null" >
        #{mzcl,jdbcType=VARCHAR},
      </if>
      <if test="ysqz != null" >
        #{ysqz,jdbcType=INTEGER},
      </if>
      <if test="qzsj != null" >
        #{qzsj,jdbcType=TIMESTAMP},
      </if>
      <if test="ysxm != null" >
        #{ysxm,jdbcType=VARCHAR},
      </if>
      <if test="hzjg != null" >
        #{hzjg,jdbcType=VARCHAR},
      </if>
      <if test="hzmz != null" >
        #{hzmz,jdbcType=VARCHAR},
      </if>
      <if test="ksbm != null" >
        #{ksbm,jdbcType=INTEGER},
      </if>
      <if test="createUserId != null" >
        #{createUserId,jdbcType=INTEGER},
      </if>
      <if test="createOn != null" >
        #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="hospitalId != null" >
        #{hospitalId,jdbcType=INTEGER},
      </if>
      <if test="updateUserId != null" >
        #{updateUserId,jdbcType=INTEGER},
      </if>
      <if test="updateOn != null" >
        #{updateOn,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.rjsoft.outPatient.domain.hospitalProve.pojo.MzysTbZyzExample" resultType="java.lang.Integer" >
    select count(*) from MZYS_TB_ZYZ
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update MZYS_TB_ZYZ
    <set >
      <if test="record.jzlsh != null" >
        jzlsh = #{record.jzlsh,jdbcType=CHAR},
      </if>
      <if test="record.lczd != null" >
        lczd = #{record.lczd,jdbcType=VARCHAR},
      </if>
      <if test="record.ys != null" >
        ys = #{record.ys,jdbcType=VARCHAR},
      </if>
      <if test="record.bscw != null" >
        bscw = #{record.bscw,jdbcType=VARCHAR},
      </if>
      <if test="record.glffcb != null" >
        glffcb = #{record.glffcb,jdbcType=VARCHAR},
      </if>
      <if test="record.glffhx != null" >
        glffhx = #{record.glffhx,jdbcType=VARCHAR},
      </if>
      <if test="record.yszl != null" >
        yszl = #{record.yszl,jdbcType=VARCHAR},
      </if>
      <if test="record.hszysx != null" >
        hszysx = #{record.hszysx,jdbcType=VARCHAR},
      </if>
      <if test="record.mzcl != null" >
        mzcl = #{record.mzcl,jdbcType=VARCHAR},
      </if>
      <if test="record.ysqz != null" >
        ysqz = #{record.ysqz,jdbcType=INTEGER},
      </if>
      <if test="record.qzsj != null" >
        qzsj = #{record.qzsj,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ysxm != null" >
        ysxm = #{record.ysxm,jdbcType=VARCHAR},
      </if>
      <if test="record.hzjg != null" >
        hzjg = #{record.hzjg,jdbcType=VARCHAR},
      </if>
      <if test="record.hzmz != null" >
        hzmz = #{record.hzmz,jdbcType=VARCHAR},
      </if>
      <if test="record.ksbm != null" >
        ksbm = #{record.ksbm,jdbcType=INTEGER},
      </if>
      <if test="record.createUserId != null" >
        CreateUserId = #{record.createUserId,jdbcType=INTEGER},
      </if>
      <if test="record.createOn != null" >
        CreateOn = #{record.createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="record.hospitalId != null" >
        HospitalId = #{record.hospitalId,jdbcType=INTEGER},
      </if>
      <if test="record.updateUserId != null" >
        UpdateUserId = #{record.updateUserId,jdbcType=INTEGER},
      </if>
      <if test="record.updateOn != null" >
        UpdateOn = #{record.updateOn,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update MZYS_TB_ZYZ
    set jzlsh = #{record.jzlsh,jdbcType=CHAR},
      lczd = #{record.lczd,jdbcType=VARCHAR},
      ys = #{record.ys,jdbcType=VARCHAR},
      bscw = #{record.bscw,jdbcType=VARCHAR},
      glffcb = #{record.glffcb,jdbcType=VARCHAR},
      glffhx = #{record.glffhx,jdbcType=VARCHAR},
      yszl = #{record.yszl,jdbcType=VARCHAR},
      hszysx = #{record.hszysx,jdbcType=VARCHAR},
      mzcl = #{record.mzcl,jdbcType=VARCHAR},
      ysqz = #{record.ysqz,jdbcType=INTEGER},
      qzsj = #{record.qzsj,jdbcType=TIMESTAMP},
      ysxm = #{record.ysxm,jdbcType=VARCHAR},
      hzjg = #{record.hzjg,jdbcType=VARCHAR},
      hzmz = #{record.hzmz,jdbcType=VARCHAR},
      ksbm = #{record.ksbm,jdbcType=INTEGER},
      CreateUserId = #{record.createUserId,jdbcType=INTEGER},
      CreateOn = #{record.createOn,jdbcType=TIMESTAMP},
      HospitalId = #{record.hospitalId,jdbcType=INTEGER},
      UpdateUserId = #{record.updateUserId,jdbcType=INTEGER},
      UpdateOn = #{record.updateOn,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.rjsoft.outPatient.domain.hospitalProve.pojo.MzysTbZyz" >
    update MZYS_TB_ZYZ
    <set >
      <if test="lczd != null" >
        lczd = #{lczd,jdbcType=VARCHAR},
      </if>
      <if test="ys != null" >
        ys = #{ys,jdbcType=VARCHAR},
      </if>
      <if test="bscw != null" >
        bscw = #{bscw,jdbcType=VARCHAR},
      </if>
      <if test="glffcb != null" >
        glffcb = #{glffcb,jdbcType=VARCHAR},
      </if>
      <if test="glffhx != null" >
        glffhx = #{glffhx,jdbcType=VARCHAR},
      </if>
      <if test="yszl != null" >
        yszl = #{yszl,jdbcType=VARCHAR},
      </if>
      <if test="hszysx != null" >
        hszysx = #{hszysx,jdbcType=VARCHAR},
      </if>
      <if test="mzcl != null" >
        mzcl = #{mzcl,jdbcType=VARCHAR},
      </if>
      <if test="ysqz != null" >
        ysqz = #{ysqz,jdbcType=INTEGER},
      </if>
      <if test="qzsj != null" >
        qzsj = #{qzsj,jdbcType=TIMESTAMP},
      </if>
      <if test="ysxm != null" >
        ysxm = #{ysxm,jdbcType=VARCHAR},
      </if>
      <if test="hzjg != null" >
        hzjg = #{hzjg,jdbcType=VARCHAR},
      </if>
      <if test="hzmz != null" >
        hzmz = #{hzmz,jdbcType=VARCHAR},
      </if>
      <if test="ksbm != null" >
        ksbm = #{ksbm,jdbcType=INTEGER},
      </if>
      <if test="createUserId != null" >
        CreateUserId = #{createUserId,jdbcType=INTEGER},
      </if>
      <if test="createOn != null" >
        CreateOn = #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="hospitalId != null" >
        HospitalId = #{hospitalId,jdbcType=INTEGER},
      </if>
      <if test="updateUserId != null" >
        UpdateUserId = #{updateUserId,jdbcType=INTEGER},
      </if>
      <if test="updateOn != null" >
        UpdateOn = #{updateOn,jdbcType=TIMESTAMP},
      </if>
    </set>
    where jzlsh = #{jzlsh,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.rjsoft.outPatient.domain.hospitalProve.pojo.MzysTbZyz" >
    update MZYS_TB_ZYZ
    set lczd = #{lczd,jdbcType=VARCHAR},
      ys = #{ys,jdbcType=VARCHAR},
      bscw = #{bscw,jdbcType=VARCHAR},
      glffcb = #{glffcb,jdbcType=VARCHAR},
      glffhx = #{glffhx,jdbcType=VARCHAR},
      yszl = #{yszl,jdbcType=VARCHAR},
      hszysx = #{hszysx,jdbcType=VARCHAR},
      mzcl = #{mzcl,jdbcType=VARCHAR},
      ysqz = #{ysqz,jdbcType=INTEGER},
      qzsj = #{qzsj,jdbcType=TIMESTAMP},
      ysxm = #{ysxm,jdbcType=VARCHAR},
      hzjg = #{hzjg,jdbcType=VARCHAR},
      hzmz = #{hzmz,jdbcType=VARCHAR},
      ksbm = #{ksbm,jdbcType=INTEGER},
      CreateUserId = #{createUserId,jdbcType=INTEGER},
      CreateOn = #{createOn,jdbcType=TIMESTAMP},
      HospitalId = #{hospitalId,jdbcType=INTEGER},
      UpdateUserId = #{updateUserId,jdbcType=INTEGER},
      UpdateOn = #{updateOn,jdbcType=TIMESTAMP}
    where jzlsh = #{jzlsh,jdbcType=CHAR}
  </update>
</mapper>