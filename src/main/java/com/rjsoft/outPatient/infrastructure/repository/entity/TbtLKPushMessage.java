package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table( name ="tbt_LK_PushMessage" )
public class TbtLKPushMessage {
    @Id
    @Column(name = "ID", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    @Column(name = "PushType")
    private String pushType;

    @Column(name = "CardNo")
    private String cardNo;

    @Column(name = "PatName")
    private String patName;

    @Column(name = "OpDate")
    private Date opDate;

    @Column(name = "DeptNameOrContent")
    private String deptNameOrContent;

    @Column(name = "Content")
    private String content;

    @Column(name = "Status")
    private Integer status;

    @Column(name = "SendDate")
    private Date sendDate;
}
