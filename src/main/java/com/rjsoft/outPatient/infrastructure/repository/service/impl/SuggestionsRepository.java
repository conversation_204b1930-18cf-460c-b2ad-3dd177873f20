package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.outPatient.domain.caseHistory.dto.CheckBlDetail;
import com.rjsoft.outPatient.domain.caseHistory.dto.UpdateCheckedBlParam;
import com.rjsoft.outPatient.infrastructure.repository.entity.Allergy;
import com.rjsoft.outPatient.infrastructure.repository.entity.AllergyDic;

import java.util.List;
import java.util.Set;

/**
 * 病历审核信息
 */
public interface SuggestionsRepository {

    /**
     * 根据病历id获取病历审核信息
     * @param blId
     */
    List<CheckBlDetail> getCheckBls(Set<String> blId,Integer hospitalCode);

    /**
     * 已审核病历提交
     * @param suggestionId
     * @param hospitalCode
     */
    void commitBl(Integer suggestionId,Integer hospitalCode);

    /**
     * 已审核病历修改保存
     * @param param
     */
    void updateBl(UpdateCheckedBlParam param);

}
