package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.config.HisConfig;
import com.rjsoft.outPatient.domain.caseHistory.dto.CheckBlDetail;
import com.rjsoft.outPatient.domain.caseHistory.dto.UpdateCheckedBlParam;
import com.rjsoft.outPatient.infrastructure.repository.entity.Allergy;
import com.rjsoft.outPatient.infrastructure.repository.entity.AllergyDic;
import com.rjsoft.outPatient.infrastructure.repository.mapper.AllergyDicMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.AllergyMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SuggestionsMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SysFunctionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.AllergyRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 过敏信息
 */
@AllArgsConstructor
@Service
public class SuggestionsRepositoryImpl implements SuggestionsRepository {

    private final SuggestionsMapper suggestionsMapper;
    HisConfig hisConfig;


    /**
     * 根据病历id获取病历审核信息
     *
     * @param blId
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.MZEMR)
    public List<CheckBlDetail> getCheckBls(Set<String> blId, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZEMR);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZEMR3);
        }
        return suggestionsMapper.getCheckBls(blId);
    }

    /**
     * 已审核病历提交
     *
     * @param suggestionId
     * @param hospitalCode
     */
    @Override
    public void commitBl(Integer suggestionId, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZEMR);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZEMR3);
        }
        suggestionsMapper.commitBl(suggestionId);
    }

    /**
     * 已审核病历修改保存
     *
     * @param param
     */
    @Override
    public void updateBl(UpdateCheckedBlParam param) {
        if (param.getHospitalCode().equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZEMR);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZEMR3);
        }

        suggestionsMapper.updateBl(param);
    }
}
