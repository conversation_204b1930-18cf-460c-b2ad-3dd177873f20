<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.AssayRequestDetailMapper">

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO MZYS_TB_HYSQMX (sqlsh,sqmxlsh,jzlsh,ghlsh,
        xmbm,xmmc,sqys,zxks,yybm)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.requestNo}, #{item.requestDetailNo}, #{item.receptionNo}, #{item.regNo},
            #{item.itemCode},#{item.itemName},#{item.applyDoctor},#{item.execDept},
            #{item.hospitalCode}
            )
        </foreach>
    </insert>


    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM MZYS_TB_HYSQMX WHERE sqmxlsh IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item.requestDetailNo}
        </foreach>
    </delete>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE MZYS_TB_HYSQMX
            SET sqlsh = #{item.requestNo}, jzlsh = #{item.receptionNo},
            ghlsh = #{item.regNo}, xmbm = #{item.itemCode},
            xmmc = #{item.itemName}, sqys = #{item.applyDoctor},
            zxks = #{item.execDept}, yybm = #{item.hospitalCode}
            WHERE sqmxlsh = #{item.requestDetailNo}
        </foreach>
    </update>
</mapper>
