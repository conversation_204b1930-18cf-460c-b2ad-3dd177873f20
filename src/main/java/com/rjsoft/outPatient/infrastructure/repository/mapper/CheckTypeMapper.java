package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.CheckType;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

public interface CheckTypeMapper extends BaseMapper<CheckType> {

    /**
     * 查询申请单检查类型
     *
     * @return
     */
    default List<CheckType> getCheckType() {
        CheckType checkType = new CheckType();
        return select(checkType);
    }
}
