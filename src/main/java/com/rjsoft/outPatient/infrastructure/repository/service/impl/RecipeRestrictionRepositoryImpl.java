package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugRestriction;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeRestriction;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeRestrictionMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeRestrictionTypeMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeRestrictionRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/9/27-2:11 下午
 */
@Service
@AllArgsConstructor
public class RecipeRestrictionRepositoryImpl implements RecipeRestrictionRepository {

    private final RecipeRestrictionMapper recipeRestrictionMapper;
    private final RecipeRestrictionTypeMapper recipeRestrictionTypeMapper;

    @Override
    @DatabaseAnnotation
    public Integer getRecipeRestrictionCount(Integer doctorId, Integer deptId, Integer itemCode, Integer hospitalCode) {
        final Weekend<RecipeRestriction> weekend = new Weekend<>(RecipeRestriction.class);
        final WeekendCriteria<RecipeRestriction, Object> criteria = weekend.weekendCriteria();

        criteria.andEqualTo(RecipeRestriction::getItemCode, itemCode)
                .andEqualTo(RecipeRestriction::getHospitalCode, hospitalCode)
                .andEqualTo(RecipeRestriction::getStatus, 0);

        if (doctorId != null) {
            criteria.andEqualTo(RecipeRestriction::getDoctorId, doctorId);
        }
        if (deptId != null) {
            criteria.andEqualTo(RecipeRestriction::getDeptId, deptId);
        }
        return recipeRestrictionMapper.selectCountByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public Integer getRecipeRestrictionCode(Integer itemCode, Integer hospitalCode) {

        final Weekend<RecipeRestriction> weekend = new Weekend<>(RecipeRestriction.class);
        weekend.selectProperties("recipeRestriction");
        final WeekendCriteria<RecipeRestriction, Object> criteria = weekend.weekendCriteria();

        criteria.andEqualTo(RecipeRestriction::getItemCode, itemCode)
                .andEqualTo(RecipeRestriction::getHospitalCode, hospitalCode)
                .andEqualTo(RecipeRestriction::getStatus, 0);

        final RecipeRestriction entity = Optional.ofNullable(recipeRestrictionMapper.selectOneByExample(weekend)).orElse(new RecipeRestriction());

        return Optional.ofNullable(entity.getRecipeRestriction()).orElse(-1);
    }

    @Override
    @DatabaseAnnotation
    public Map<Integer,Integer> listRecipeRestrictionCode2Map(List<Integer> itemCodeList, Integer hospitalCode) {
        if (itemCodeList==null||itemCodeList.size()==0){
            return new HashMap<>();
        }
        final Weekend<RecipeRestriction> weekend = new Weekend<>(RecipeRestriction.class);
        weekend.selectProperties("recipeRestriction","itemCode");
        final WeekendCriteria<RecipeRestriction, Object> criteria = weekend.weekendCriteria();

        criteria.andIn(RecipeRestriction::getItemCode, itemCodeList)
                .andEqualTo(RecipeRestriction::getHospitalCode, hospitalCode)
                .andEqualTo(RecipeRestriction::getStatus, 0);

        final List<RecipeRestriction> entityList = recipeRestrictionMapper.selectByExample(weekend);
        Map<Integer,Integer> itemCodeRestrictionMap = new HashMap<>();
        if(entityList==null||entityList.size()==0){
            return itemCodeRestrictionMap;
        }
        for(RecipeRestriction restriction : entityList) {
            itemCodeRestrictionMap.put(restriction.getItemCode(),
                    Optional.ofNullable(restriction.getRecipeRestriction()).orElse(-1));
        }

        return itemCodeRestrictionMap;
    }
}
