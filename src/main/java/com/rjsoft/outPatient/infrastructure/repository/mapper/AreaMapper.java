package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Area;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

public interface AreaMapper extends BaseMapper<Area>, ExampleMapper<Area> {
    /**
     * 查询地区根据parentId
     *
     * @param parentId
     * @return
     */
    public List<Area> getAreaByParentId(String parentId, String code);
}
