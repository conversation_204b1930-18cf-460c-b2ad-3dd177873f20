package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 诊断病情
 *
 * <AUTHOR>
 * @since 2021/8/2 - 15:06
 */
@Data
@Table(name = "MZYS_TB_ZDWZQK")
public class DiagnoseCondition  implements Serializable {

    /**
     * 诊断编码
     */
    @Column(name = "zdbm")
    private String diagnoseCode;
    
    /**
     * 诊断名称
     */
    @Column(name = "zdmc")
    private String diagnoseName;
    
    /**
     * 微重情况 1 重症 0 轻症
     */
    @Column(name = "wzqk")
    private Integer condition;

    /**
     * 医院编码
     */
    @Column(name = "hospitalCode")
    private Integer hospitalCode;

}
