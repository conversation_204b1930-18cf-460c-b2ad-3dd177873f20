package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 电子病历审核表
 */
@Data
@Table(name = "MZYS_TB_DZBLSH")
public class MedicalRecordAudit implements Serializable {

    @Id
    @Column(name = "Id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    @Column(name = "RegNo")
    private Integer regNo;

    @Column(name = "PatName")
    private String patName;

    @Column(name = "OutPatientNo")
    private String outPatientNo;

    @Column(name = "Blid")
    private Integer blId;

    @Column(name = "CheckSort")
    private Integer checkSort;

    @Column(name = "CreDoctorId")
    private Integer creDoctorId;

    @Column(name = "CreTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creTime;

    @Column(name = "HospitalCode")
    private Integer hospitalCode;

    @Transient
    @Column(insertable = false, updatable = false)
    private String blCardNo;

    @Transient
    @Column(insertable = false, updatable = false)
    private String blHtml;

    @Transient
    @Column(insertable = false, updatable = false)
    private Integer caseHistoryDoctorId;

    @Transient
    @Column(insertable = false, updatable = false)
    private String receptionNo;

    @Transient
    @Column(insertable = false, updatable = false)
    private Date firstDate;

    @Transient
    @Column(insertable = false, updatable = false)
    private Integer receptionDoctorId;

    @Transient
    @Column(insertable = false, updatable = false)
    private Integer receptionDeptId;

    @Transient
    @Column(insertable = false, updatable = false)
    private Integer printFlag;

}
