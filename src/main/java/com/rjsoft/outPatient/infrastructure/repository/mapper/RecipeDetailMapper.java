package com.rjsoft.outPatient.infrastructure.repository.mapper;


import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.common.enums.RecipeTypeEnum;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DrugInfo;
import com.rjsoft.outPatient.domain.diseaseReport.dto.ReceptionDrugDTO;
import com.rjsoft.outPatient.domain.prescriptionAudit.vo.AuditMedicine;
import com.rjsoft.outPatient.domain.recipe.dto.HistoryRecipeDetailDto;
import com.rjsoft.outPatient.domain.recipe.dto.TreatDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.Recipe;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 门诊处方明细
 *
 * <AUTHOR>
public interface RecipeDetailMapper extends Mapper<RecipeDetail> {

    /**
     * 根据处方流水号和费用类别获取处方明细
     *
     * @param receptionNo  处方流水号
     * @param hospitalCode 医院编码
     * @param feeCategory  费用类别
     * @param dispensing
     * @return List<RecipeDetail>
     */
    default List<RecipeDetail> getRecipeByReceptionNoAndFeeCategory(Long receptionNo, Integer hospitalCode, List<Integer> feeCategory, Integer dispensing) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        WeekendCriteria<RecipeDetail, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andEqualTo(RecipeDetail::getReceptionNo, receptionNo);
        keywordCriteria.andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        keywordCriteria.andEqualTo(RecipeDetail::getDispensing, dispensing);
        keywordCriteria.andIn(RecipeDetail::getFeeCategory, feeCategory);
        return selectByExample(weekend);
    }


    /**
     * 根据就诊流水号获取处方药品处方明细
     *
     * @param regNo
     * @param hospitalCode
     * @param tableName
     * @return
     */
    default List<RecipeDetail> getDrugRecipeByRegNo(Long regNo, Integer hospitalCode, String tableName) {
        List<Integer> feeCategory = ItemCategoryEnum.getDrugTypeCodeList();

        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.setTableName(tableName);
        weekend.weekendCriteria()
                .andEqualTo(RecipeDetail::getRegNo, regNo)
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                .andIn(RecipeDetail::getFeeCategory, feeCategory);

        WeekendCriteria<RecipeDetail, Object> keywordCriteria1 = weekend.weekendCriteria();
        keywordCriteria1.andNotEqualTo(RecipeDetail::getApplicationForm, "MECT").orIsNull(RecipeDetail::getApplicationForm);

        weekend.and(keywordCriteria1);
        return selectByExample(weekend);
    }

    /**
     * 根据处方流水号获取处方明细
     *
     * @param recipeNo
     * @param hospitalCode
     * @param tableName
     * @return
     */
    default List<RecipeDetail> getRecipeByRecipeNo(Long recipeNo, Integer hospitalCode, String tableName) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.setTableName(tableName);
        WeekendCriteria<RecipeDetail, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andEqualTo(RecipeDetail::getRecipeNo, recipeNo);
        keywordCriteria.andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        return selectByExample(weekend);
    }

    /**
     * 根据处方流水号获取处方明细
     *
     * @param receptionNo
     * @param hospitalCode
     * @param tableName
     * @return
     */
    default List<RecipeDetail> getRecipeByReceptionNo(Long receptionNo, Integer hospitalCode, String tableName) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.setTableName(tableName);
        WeekendCriteria<RecipeDetail, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andEqualTo(RecipeDetail::getReceptionNo, receptionNo).andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        WeekendCriteria<RecipeDetail, Object> keywordCriteria1 = weekend.weekendCriteria();
        keywordCriteria1.andNotEqualTo(RecipeDetail::getApplicationForm, "MECT").orIsNull(RecipeDetail::getApplicationForm);
        weekend.and(keywordCriteria1);
        return selectByExample(weekend);
    }

    /**
     * 根据处方流水号获取处方互联网处方明细
     *
     * @param receptionNo
     * @return
     */
    List<RecipeDetail> getInternetRecipeByReceptionNo(@Param("receptionNo") String receptionNo, @Param("presId") Integer presId, @Param("importFlag") int importFlag);


    /**
     * 根据ID 集合获取处方明细
     *
     * @param ids
     * @param hospitalCode
     * @param tableName
     * @return
     */
    default List<RecipeDetail> getRecipeDetailByIds(List<Long> ids, Integer hospitalCode, String tableName) {
        if (ids == null || ids.stream().count() == 0) {
            return new ArrayList<>();
        }
        Weekend weekend = new Weekend(RecipeDetail.class);
        weekend.setTableName(tableName);
        WeekendCriteria<RecipeDetail, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andIn(RecipeDetail::getRecipeDetailNo, ids);
        keywordCriteria.andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        return selectByExample(weekend);
    }

    /**
     * 获取药品处方数量
     *
     * @param receptionNo
     * @param hospitalCode
     * @param tableName
     * @return
     */
    default Integer getDrugRecipeCountById(Long receptionNo, Integer hospitalCode, String tableName) {
        Weekend weekend = new Weekend(RecipeDetail.class);
        weekend.setTableName(tableName);
        List<Integer> feeCategory = ItemCategoryEnum.getDrugTypeCodeList();
        WeekendCriteria<RecipeDetail, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andEqualTo(RecipeDetail::getReceptionNo, receptionNo);
        keywordCriteria.andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        keywordCriteria.andIn(RecipeDetail::getFeeCategory, feeCategory);
        keywordCriteria.andNotEqualTo(RecipeDetail::getRecipeCategory, RecipeTypeEnum.TAKE_OUT.getRecipeType());
        return selectCountByExample(weekend);
    }


    /**
     * 保存处方明细
     *
     * @param recipeDetail
     * @return
     */
    default boolean saveRecipeDetail(RecipeDetail recipeDetail) {
        recipeDetail.setRecipeId(recipeDetail.getRecipeNo());
        recipeDetail.setRecipeDetailId(recipeDetail.getRecipeDetailNo());
        if(recipeDetail.getRecipeNo()==null||recipeDetail.getRecipeDetailNo() == null){
            return false;
        }
        if (recipeDetail.getDataFlag() == 0) {
            return insertSelective(recipeDetail) > 0;
        } else if (recipeDetail.getDataFlag() == 1) {
            return updateByPrimaryKeySelective(recipeDetail) > 0;
        } else if (recipeDetail.getDataFlag() == 2) {
            Weekend weekend = new Weekend(RecipeDetail.class);
            weekend.setTableName("MZYS_TB_MZCFMX_DATA");
            return updateByExampleSelective(recipeDetail, weekend) > 0;
        } else if (recipeDetail.getDataFlag() == -1) {
            return deleteByPrimaryKey(recipeDetail) > 0;
        }
        else {
            return false;
        }
    }

    /**
     * 获取最大组号
     *
     * @param receptionNo
     * @param hospitalCode
     * @param tableName
     * @return
     */
    default Integer getRecipeMaxGroupNo(Long receptionNo, Integer hospitalCode, String tableName) {
        Weekend weekend = new Weekend(RecipeDetail.class);
        WeekendCriteria<RecipeDetail, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(RecipeDetail::getReceptionNo, receptionNo);
        weekendCriteria.andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        weekend.setOrderByClause("zh desc");
        weekend.selectProperties("groupNo");
        weekend.setTableName(tableName);
        List<RecipeDetail> recipeDetail = selectByExample(weekend);
        if (recipeDetail == null || recipeDetail.stream().count() == 0) {
            return -1;
        } else if (recipeDetail.get(0).getGroupNo() == null) {
            return 0;
        } else {
            return recipeDetail.get(0).getGroupNo();
        }
    }

    /**
     * 获取最大换方标记
     *
     * @param receptionNo
     * @param hospitalCode
     * @param tableName
     * @return
     */
    default Integer getRecipeMaxChangeRecipeFlag(Long receptionNo, Integer hospitalCode, String tableName) {
        Weekend weekend = new Weekend(RecipeDetail.class);
        WeekendCriteria<RecipeDetail, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(RecipeDetail::getReceptionNo, receptionNo);
        weekendCriteria.andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        weekend.setOrderByClause("hfbj desc");
        weekend.selectProperties("changeRecipeFlag");
        weekend.setTableName(tableName);
        List<RecipeDetail> recipeDetail = selectByExample(weekend);
        if (recipeDetail == null || recipeDetail.stream().count() == 0) {
            return -1;
        } else if (recipeDetail.get(0).getChangeRecipeFlag() == null) {
            return 0;
        } else {
            return recipeDetail.get(0).getChangeRecipeFlag();
        }
    }

    default List<RecipeDetail> getDrugInfoByRegNo(Long regNo) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        List<Integer> typeList = Stream.of(12, 13, 14).collect(Collectors.toList());
        weekend.weekendCriteria().andEqualTo(RecipeDetail::getRegNo, regNo)
                .andIn(RecipeDetail::getFeeCategory, typeList);
        return selectByExample(weekend);
    }

    List<ReceptionDrugDTO> getRecipeByReceptionFromMzysnew(@Param("receptionNo") Long receptionNo);

    List<DrugInfo> getDrugInfo(@Param("itemCode") Integer itemCode,
                               @Param("dose") String dose,
                               @Param("frequency") Integer frequency);

    List<AuditMedicine> getRecipeDetailByRecipeNo(@Param("recipeNo") String recipeNo,
                                                  @Param("hospitalCode") String hospitalCode);

    /**
     * 查询
     *
     * @param recipeDetailNo 就诊流水号
     * @param hospitalCode   医院编码
     * @return
     */
    default RecipeDetail getRecipeDetailByDetailNo(Long recipeDetailNo, Integer hospitalCode) {
        return selectOneByExample(new Example.Builder(RecipeDetail.class)
                .where(WeekendSqls.<RecipeDetail>custom()
                        .andEqualTo(RecipeDetail::getRecipeDetailNo, recipeDetailNo)
                        .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)).build());
    }

    /**
     * 根据就诊流水号和处方明细状态获取处方明细
     *
     * @param receptionNo
     */
    default List<RecipeDetail> getRecipeDetailByStatus(Long receptionNo) {
        RecipeDetail detail = new RecipeDetail();
        detail.setReceptionNo(receptionNo);
        detail.setStatus(0);
        return select(detail);
    }


    /**
     * 从老系统获取处方明细
     *
     * @param receptionNo
     * @return
     */
    List<ReceptionDrugDTO> getRecipeFromOld(@Param("receptionNo") String receptionNo);

    /**
     * 查询治疗单列表
     *
     * @param regNo
     * @param type
     * @param hospitalCode
     */
    List<TreatDto> getTreatDto(@Param("regNo") Long regNo, @Param("type") Integer type, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据挂号流水号和医院编码修改打印标记为【已打印】
     *
     * @param recipeDetailNo
     * @param hospitalCode
     */
    default void updatePrintStatus(List<Long> recipeDetailNo, Integer hospitalCode) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        WeekendCriteria<RecipeDetail, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(RecipeDetail::getRecipeDetailNo, recipeDetailNo)
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        RecipeDetail detail = new RecipeDetail();
        detail.setPrintStatus(1);
        updateByExampleSelective(detail, weekend);
    }

    /**
     * 根据就诊流水号获取处方明细信息[只获取申请单数据]
     *
     * @param receptionNo
     * @param hospitalCode
     */
    default List<RecipeDetail> getRecipeDetailsByReceptionNo(Long receptionNo, Integer hospitalCode) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.selectProperties("examineNo");
        WeekendCriteria<RecipeDetail, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(RecipeDetail::getReceptionNo, receptionNo)
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                .andEqualTo(RecipeDetail::getStatus, 0)
                .andEqualTo(RecipeDetail::getExamineFlag, 1)
                .andIsNotNull(RecipeDetail::getExamineNo);
        return selectByExample(weekend);
    }

    /**
     * 根据就诊流水号和医院编码获取未审核通过的门诊处方明细记录
     * 查询出当前需要打印的处方
     *
     * @param receptionNo
     * @param hospitalCode
     */
    default List<RecipeDetail> getNoCheckedRecipeStatus(Integer operateType, Long receptionNo, Integer firstDoctorId, Integer hospitalCode) {
        // 医生只允许打印自己开具的处方
        List<String> feeCategoryList = new ArrayList<>();
        if (operateType.equals(1)) {
            feeCategoryList = Arrays.asList("12", "13", "6");
        } else {
            feeCategoryList = Arrays.asList("12", "13");
        }
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.selectProperties("recipeStatus");
        WeekendCriteria<RecipeDetail, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(RecipeDetail::getReceptionNo, receptionNo)
                .andEqualTo(RecipeDetail::getFirstDoctorId, firstDoctorId)
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                .andIn(RecipeDetail::getFeeCategory, feeCategoryList);
//                .andIn(RecipeDetail::getRecipeStatus, RecipeStatusEnum.getNoCheckedRecipeStatus());
        return selectByExample(weekend);
    }


    /**
     * 根据挂号流水号查询互联网药品处方
     *
     * @param regNo
     * @return
     */
    List<HistoryRecipeDetailDto> getInternetDrugRecipeByRegNo(@Param("regNo") String regNo);


    /**
     * 查询患者处方信息
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    List<RecipeDetail> hasRecipeDetailByReceptionNo(@Param("receptionNo") Long receptionNo, @Param("hospitalCode") Integer hospitalCode);

}
