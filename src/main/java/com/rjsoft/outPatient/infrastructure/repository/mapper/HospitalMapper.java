package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.HisDictionary;
import com.rjsoft.outPatient.infrastructure.repository.entity.Hospital;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.common.Mapper;

/**
 * <AUTHOR>
 * @create 2021/9/17 16:52
 */

public interface HospitalMapper extends Mapper<Hospital>, ExampleMapper<Hospital> {
    /**
     * 根据医院编号获取hospital
     * @param hospitalCode
     * @return com.rjsoft.outPatient.infrastructure.repository.entity.Hospital
     * <AUTHOR>
     **/
    default Hospital queryHospitalByHospitalCode(String hospitalCode){
        Hospital hospital = new Hospital();
        hospital.setHospitalCode(hospitalCode);
        return selectOne(hospital);
    }
}
