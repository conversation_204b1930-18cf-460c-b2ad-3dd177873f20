package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.AllergyDic;
import com.rjsoft.outPatient.infrastructure.repository.entity.SeriousIllnessRegisterLog;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

/**
 * 大病登记日志
 *
 * <AUTHOR>
public interface SeriousIllnessRegisterLogMapper extends BaseMapper<SeriousIllnessRegisterLog>, ExampleMapper<SeriousIllnessRegisterLog> {

    /**
     * 获取大病等级日志
     *
     * @param serIdList
     * @return
     */
    default List<SeriousIllnessRegisterLog> getSeriousIllnessRegisterLog(List<Integer> serIdList ,List<Integer> stateList,Integer oldFlag) {
        final Weekend<SeriousIllnessRegisterLog> weekend = new Weekend<>(SeriousIllnessRegisterLog.class);
        final WeekendCriteria<SeriousIllnessRegisterLog, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(SeriousIllnessRegisterLog::getSerID, serIdList);
        criteria.andIn(SeriousIllnessRegisterLog::getOperateType, stateList);
        return selectByExample(weekend);
    }
}
