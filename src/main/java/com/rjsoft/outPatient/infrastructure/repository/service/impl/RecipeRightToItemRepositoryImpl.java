package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.InHospitalInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDoctorRight;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeRightToItem;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeRightToItemMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeRightToItemRepository;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/23 - 17:10
 */
@Service
@AllArgsConstructor
public class RecipeRightToItemRepositoryImpl implements RecipeRightToItemRepository {

    private final RecipeRightToItemMapper recipeRightToItemMapper;

    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    public Integer getRecipeRightToItemCount(@NonNull Integer doctorId, @NonNull Integer rightCode, @NonNull Boolean isRightCodeEq) {
        final Weekend<RecipeRightToItem> weekend = new Weekend<>(RecipeRightToItem.class);
        final WeekendCriteria<RecipeRightToItem, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(RecipeRightToItem::getDrugCode, doctorId);
        if (isRightCodeEq) {
            weekendCriteria.andEqualTo(RecipeRightToItem::getRightCode, rightCode);
        } else {
            weekendCriteria.andNotEqualTo(RecipeRightToItem::getRightCode, rightCode);
        }
        return recipeRightToItemMapper.selectCountByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = "ZXHIS")
    public List<RecipeRightToItem> getRecipeRightToItemByDrugCodeAndRightCode(@NonNull Integer drugCode, @NonNull List<Integer> rightCodes) {
        final Weekend<RecipeRightToItem> weekend = new Weekend<>(RecipeRightToItem.class);
        weekend.weekendCriteria()
                .andEqualTo(RecipeRightToItem::getDrugCode, drugCode);
        final WeekendCriteria<RecipeRightToItem, Object> weekendCriteria = weekend.weekendCriteria();
        for (Integer rightCode : rightCodes) {
            weekendCriteria.orEqualTo(RecipeRightToItem::getRightCode, rightCode);
        }
        weekend.and(weekendCriteria);
        return recipeRightToItemMapper.selectByExample(weekend);
    }
}
