package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.MectItem;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;
import java.util.Map;

/**
 * Mect项目配置
 *
 * <AUTHOR>
public interface MectItemRepository {

    /**
     * 根据类型获取Mect项目
     *
     * @param type
     * @param hospitalCode
     * @return
     */
    List<MectItem> getMectItemsByType(String type, Integer hospitalCode);

    /**
     * 保存Mect项目
     *
     * @param entity
     * @return
     */
    boolean saveMectItems(MectItem entity);

    /**
     * 判断项目是否为 MECT 项目
     *
     * @param itemCode     项目编码
     * @param hospitalCode 医院编码
     * @return
     */
    boolean isMectItem(Integer itemCode, Integer hospitalCode);

//    @DatabaseAnnotation
//    @Cacheable(cacheNames = "isMectItem", unless = "#result == null")
    Map<Integer, Integer> mectItemMulCount(List<Integer> itemCodeList, Integer hospitalCode);

    /**
     * 删除Mect项目
     *
     * @param itemCode
     * @param hospitalCode
     * @param type
     * @return
     */
    boolean delMectItems(Integer itemCode, Integer hospitalCode, String type);
}
