package com.rjsoft.outPatient.infrastructure.repository.strategy.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.log.Log;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.PreRecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.SystemTbPubItems;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SysFunctionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.SystemTbPubItemsRepository;
import com.rjsoft.outPatient.infrastructure.repository.strategy.PreRecipeStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
public class PreRecipeByItemCodeBase {

    @Resource
    RecipeRepository recipeRepository;

    @Resource
    SystemTbPubItemsRepository systemTbPubItemsRepository;

    @Resource
    SysFunctionMapper sysFunctionMapper;

    /**
     * 获取处方预存明细(根据itemCode)
     *
     * @param checkDto
     * @return
     */
    @DatabaseAnnotation(name = "HISDB")
    protected List<PreRecipeDetail> getPreRecipeDetailsByItemCode(CheckRecipeDto checkDto) {
        List<PreRecipeDetail> res = new ArrayList<>();

        final Long itemCode = Converter.toInt64(checkDto.getOperatingId());
        final Integer hospitalCode = checkDto.getHospitalCode();
        SystemTbPubItems chargeItem = systemTbPubItemsRepository.getChargeItemById(Math.toIntExact(itemCode), hospitalCode, 0);
//        final ChargeItem chargeItem = chargeItemRepository.getChargeItemById(Math.toIntExact(itemCode), hospitalCode);
        Optional.ofNullable(chargeItem).orElseThrow(() -> new IllegalArgumentException("未找到收费项目"));
        PreRecipeDetail preRecipeDetail = new PreRecipeDetail();
        preRecipeDetail.setOpFlag(0);
        preRecipeDetail.ChangeChargeItem(chargeItem);
        // 不是药品，数量默认值 ： 1
        if (ItemCategoryEnum.notDrug(preRecipeDetail.getFeeCategory()) || preRecipeDetail.getQuantity() == null) {
            preRecipeDetail.setQuantity(BigDecimal.ONE);
        }

        final Integer days = preRecipeDetail.getDays();
        if (ItemCategoryEnum.isDrug(preRecipeDetail.getFeeCategory()) && (days == null || days == 0)) {
            preRecipeDetail.setDays(BigDecimal.ONE.intValue());
        }

        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);

        if (ItemCategoryEnum.notDrug(preRecipeDetail.getFeeCategory())) {
            Log.info("非药品系统套餐单个录入判断是否需要生成新预存流水号");
            if (checkDto.getPreSaveNo() != null) {
                preRecipeDetail.setPreSaveNo(checkDto.getPreSaveNo());
            } else {
                Log.info("非药品系统套餐单个录入生成新预存流水号");
                preRecipeDetail.setPreSaveNo(sysFunctionMapper.getGetSequences(SequenceEnum.PRE_SAVE_NO));
            }
        } else {
            preRecipeDetail.setPreSaveNo(sysFunctionMapper.getGetSequences(SequenceEnum.PRE_SAVE_NO));
        }
        preRecipeDetail.setRecipeDetailNo(sysFunctionMapper.getGetSequences(SequenceEnum.RECIPE_DETAIL_NO));
        preRecipeDetail.setOpCode(checkDto.getDoctorId());

        res.add(preRecipeDetail);
        return res;
    }




}
