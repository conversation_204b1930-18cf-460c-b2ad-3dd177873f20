<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DpMdmCodeSystemMapper">


    <select id="getDpMdmCodeSystemList" resultType="com.rjsoft.outPatient.common.dto.DpMdmCodeSystemVo">
        select a.code code,b.ValueCode valueCode,b.ShowValue showValue
        from DP_MDM_CodeSystem a (nolock)
            inner join DP_MDM_ValueSets b (nolock) on a.CodeId = b.CodeId and a.HospitalId = b.HospitalId
        <where>
            a.code in
            <foreach item="item" collection="codeList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </where>
    </select>

</mapper>