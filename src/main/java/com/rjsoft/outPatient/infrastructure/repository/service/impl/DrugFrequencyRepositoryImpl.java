package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugFrequency;
import com.rjsoft.outPatient.infrastructure.repository.entity.FrequencyResult;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DictionaryTbFrequencyMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugFrequencyMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DrugFrequencyRepository;
import com.ruijing.code.api.HeadInfo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

/**
 * 药品频次
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class DrugFrequencyRepositoryImpl implements DrugFrequencyRepository {

    DrugFrequencyMapper drugFrequencyMapper;
    DictionaryTbFrequencyMapper dictionaryTbFrequencyMapper;

    @Override
    @DatabaseAnnotation
    public List<DrugFrequency> getDrugFrequency(Integer hospitalCode, String inputCode) {
        final Weekend<DrugFrequency> weekend = new Weekend<>(DrugFrequency.class);
        weekend.weekendCriteria().andEqualTo(DrugFrequency::getHospitalCode, hospitalCode)
                .andEqualTo(DrugFrequency::getStatus, 0);
        if (!StringUtils.isEmpty(inputCode)) {
            final WeekendCriteria<DrugFrequency, Object> weekendCriteria = weekend.weekendCriteria();
            weekendCriteria.andLike(DrugFrequency::getCode, "%" + inputCode + "%");
            weekend.and(weekendCriteria);
        }
        weekend.setOrderByClause("sxh");
        return drugFrequencyMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<FrequencyResult> selFrequency(Integer hospitalCode, String inputCode) {
        List<FrequencyResult> list =  dictionaryTbFrequencyMapper.selFrequency(null,hospitalCode,null);
        return list;
    }

    @Override
    @DatabaseAnnotation
    public FrequencyResult selFrequencyById(Integer hospitalCode, Integer id) {
        List<FrequencyResult> list =  dictionaryTbFrequencyMapper.selFrequency(null,hospitalCode,id);
        if(list!=null&&list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /**
     * 根据医院编码加载所有频次
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<DrugFrequency> getDrugFrequency(Integer hospitalCode) {
        DrugFrequency entity = new DrugFrequency();
        entity.setHospitalCode(hospitalCode);
        entity.setStatus(0);
        return drugFrequencyMapper.select(entity);
    }

    @Override
    @DatabaseAnnotation
    public DrugFrequency getDrugFrequencyById(Integer id, Integer hospitalCode) {
        DrugFrequency entity = new DrugFrequency();
        entity.setHospitalCode(hospitalCode);
        entity.setStatus(0);
        entity.setId(id);
        return drugFrequencyMapper.selectByPrimaryKey(entity);
    }

    @Override
    @DatabaseAnnotation
    public DrugFrequency getDrugFrequencyByCode(String code, Integer hospitalCode) {
        Weekend<DrugFrequency> weekend = new Weekend<>(DrugFrequency.class);
        weekend.weekendCriteria().andEqualTo(DrugFrequency::getCode,code).andEqualTo(DrugFrequency::getHospitalCode,hospitalCode);
        List<DrugFrequency> drugFrequencyList = drugFrequencyMapper.selectByExample(weekend);
        if(drugFrequencyList!=null&&drugFrequencyList.size()>0){
            return drugFrequencyList.get(0);
        }
        return null;
    }
}
