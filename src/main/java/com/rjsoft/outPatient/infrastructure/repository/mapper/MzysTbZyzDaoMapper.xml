<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.MzysTbZyzDaoMapper" >

  <sql id="Base_Column_List" >
    jzlsh, lczd, ys, bscw, glffcb, glffhx, yszl, hszysx, mzcl, ysqz, qzsj, ysxm, hzjg,
    hzmz, ksbm, CreateUserId, CreateOn, HospitalId, UpdateUserId, UpdateOn
  </sql>
  <select id="getMzysTbZyz" resultType="com.rjsoft.outPatient.domain.hospitalProve.dto.MzysTbZyzResult" >
    select
    <include refid="Base_Column_List" />
    from MZYS_TB_ZYZ
    where jzlsh = #{jzlsh}
  </select>
  <select id="getMdDig" resultType="com.rjsoft.outPatient.domain.hospitalProve.dto.MdDigResult">
    select DigitalSignatures from tbt_MdDigitalSignatures where workerId=#{workerId}
  </select>
</mapper>