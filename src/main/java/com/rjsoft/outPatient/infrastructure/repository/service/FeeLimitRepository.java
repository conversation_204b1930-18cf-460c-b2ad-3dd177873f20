package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.FeeLimit;

import java.util.List;

/**
 * 费用限制
 * <AUTHOR>
public interface FeeLimitRepository {

    /**
     * 查询费用限制
     * @param patientType
     * @param hospitalCode
     * @return
     */
    List<FeeLimit> getFeeLimitByPatType(Integer patientType, Integer hospitalCode);

    /**
     * 保存费用限制
     * @param feeLimit
     * @return
     */
    boolean saveFeeLimit(FeeLimit feeLimit);

    /**
     * 删除费用限制
     * @param limitCode
     * @return
     */
    boolean delFeeLimit(Integer limitCode);

}
