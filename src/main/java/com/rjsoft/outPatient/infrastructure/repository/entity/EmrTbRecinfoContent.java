package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
* 
* 患者住院病历内容表
*/
@Data
@Table(name = "EMR_Tb_RecInfo_Content")
public class EmrTbRecinfoContent implements Serializable {

    /**
    * 文书内容ID
    */
    @Id
    @Column(name = "ContentId")
    private Integer contentId;
    /**
    * 病历文书ID
    */
    @Column(name = "RecId")
    private Integer recId;
    /**
    * 
    */
    @Column(name = "Content")
    private Object content;
    /**
    * 创建人
    */
    @Column(name = "CreatorId")
    private Integer creatorId;
    /**
    * 创建时间
    */
    @Column(name = "CreateDate")
    private Date createDate;
    /**
    * 医院编码
    */
    @Column(name = "HospitalId")
    private Integer hospitalId;
    /**
    * rtf文件流
    */
    @Column(name = "RecencryText")
    private Object recencryText;
    /**
    * 
    */
    @Column(name = "ContentHtml")
    private String contentHtml;

}
