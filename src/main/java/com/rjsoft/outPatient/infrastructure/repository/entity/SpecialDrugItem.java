package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 特殊药品
 *
 * <AUTHOR>
 * @since 2022/08/19-2:27 下午
 */
@Data
@Table(name = "System_Tv_ItemToUsage")
public class SpecialDrugItem implements Serializable {

    private static final long serialVersionUID = -8849258621944966959L;

    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "Id")
    private Integer id;
    /**
     * 配置主表id
     */
    @Column(name = "UsageId")
    private Integer usageId;
    /**
     * 给药途径名称
     */
    @Column(name = "itemCode")
    private Integer itemCode;
    /**
     * 是否删除
     */
    @Column(name = "isDel")
    private Integer isDel;
    /**
     * 是否启用
     */
    @Column(name = "EnableFlag")
    private Integer enableFlag;
    /**
     * 数量
     */
    @Column(name = "Qty")
    private Float qty;
    /**
     * 创建人 id
     */
    @Column(name = "Createor")
    private Integer createor;
    /**
     * 删除标志
     */
    @Column(name = "DelFlag")
    private Integer delFlage;

    /**
     * 创建日期
     */
    @Column(name = "CreateOn")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createOn;

    /**
     * 修改日期
     */
    @Column(name = "UpdateOn")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateOn;

    /**
     * 修改人id
     */
    @Column(name = "Updator")
    private Integer updator;

    /**
     * 医院编码
     */
    @Column(name = "HospId")
    private Integer hospitalCode;

    /**
     * 描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 给药途径名称
     */
    @Column(name = "UsageName")
    private String usageName;

    /**
     * 给药途径编码
     * 关联
     * 数据库: HISDB 架构: dbo 表: SYSTEM_TB_USAGECONFIG
     */
    @Column(name = "Usage")
    private Integer usage;

    /**
     * 适用范围
     */
    @Column(name = "UseRange")
    private Integer useRange;

    /**
     * 维护类型
     */
    @Column(name = "Type")
    private Integer type;

    /**
     * 代煎代配代码
     */
    @Column(name = "Decoct")
    private Integer Decoct;

    /**
     * 代煎代配描述
     */
    @Column(name = "DecoctName")
    private String DecoctName;

    /**
     * 联动次数
     */
    @Column(name = "interacteNums")
    private Integer interacteNums;

    /**
     * 联动数量
     */
    @Column(name = "interacteQuantity")
    private Integer interacteQuantity;

    /**
     * 剂型
     */
    @Column(name = "dosage")
    private Integer dosage;

    /**
     * 剂型名称
     */
    @Column(name = "dosageName")
    private String dosageName;
}
