<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.SeriousIllnessRegisterMapper">
    <select id="getNewList" resultType="com.rjsoft.outPatient.domain.seriousIllness.vo.SeriousIllnessRegisterVo">
        select
        Id as id,
        regno as regNo,
        cardNo as cardNo,
        hisCardNo as hisCardNo,
        Name as patName,
        SFZ as patSfz,
        Phone as phone,
        Adress as address,
        Diagnosis as diagnosis,
        DiagnosisId as diagnosisId,
        TreatmentItem as treatmentItem,
        (select top 1 name from MZYS_TB_ZLXM a where a.code = treatmentItem) as treatmentItemName,
        MedicalInstitution as medicalInstitution,
        Doctor as doctor,
        SignTime as signTime,
        <PERSON><PERSON><PERSON><PERSON> as sealDoctorId,
        Seal as seal,
        SealTime as sealTime,
        Consignor as consignor,
        Consignor<PERSON><PERSON> as consignorSF<PERSON>,
        RegisterTime as registerTime,
        Trustees as trustees,
        State as state,
        UseLift as useLift,
        ybSerialNo as ybSerialNo,
        TransfersOutName as transfersOutName,
        Credoctorid as creDoctorId,
        CreName as creName,
        CreTime as creTime,
        CancelTime as cancelTime,
        Upddoctorid as updDoctorId,
        UpdName as updName,
        UpdTime as updTime,
        DelFlag as delFlag,
        hospitalCode as hospitalCode,
        ValStartTime as valStartTime,
        ValEndTime as valEndTime,
        Source as source,
        TrtDclaDetlSn as TrtDclaDetlSn,
        0 as oldFlag
        from MZYS_TB_DBDJD
        where hospitalCode = #{hospitalCode} and DelFlag = 0
        and state in
        <foreach collection="stateList" index="index" item="state" separator="," close=")" open="(">
          #{state}
        </foreach>
        <if test="cardNo != null and cardNo != ''">
           and cardNo = #{cardNo}
        </if>
        <if test="hisCardNo != null and hisCardNo != ''">
           and hisCardNo = #{hisCardNo}
        </if>
        <if test="patName != null and patName != ''">
           and Name like concat('%',#{patName},'%')
        </if>
        <if test="startTime != null">
            and CreTime >= #{startTime}
        </if>
        <if test="endTime != null">
            and CreTime <![CDATA[<=]]> #{endTime}
        </if>
        <if test="patSfz != null and patSfz != ''">
            and SFZ = #{patSfz}
        </if>
    </select>

</mapper>