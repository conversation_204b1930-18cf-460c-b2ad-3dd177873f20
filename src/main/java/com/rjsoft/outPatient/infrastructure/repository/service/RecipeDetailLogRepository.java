package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;

import java.util.List;

public interface RecipeDetailLogRepository {

    void saveDeleteRecipeDetailLog(RecipeDetail recipeDetail);

    void saveDeleteRecipeDetailLogList(List<RecipeDetail> recipeDetailList);

    void saveDeleteRecipeDetailLogByIds(List<Long> recipeDetailIds);

    void saveDeleteRecipeDetailLogBApplyIds(List<Integer> applyIds);

    void saveDeleteRecipeDetailLogByRecipeId(Long recipeId);

}
