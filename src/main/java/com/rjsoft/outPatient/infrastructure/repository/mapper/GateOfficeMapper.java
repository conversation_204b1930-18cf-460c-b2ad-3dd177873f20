package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.gateOffice.dto.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description：
 * @Author：dmyl
 * @Date：2023/4/19 11:49
 **/
public interface GateOfficeMapper {

    List<ApplyInfoDto> queryApplyList(QueryApplyParam param);

    ApplyInfoDto queryApplyInfo(ApplyInfoParam param);

    Integer editApplyInfo(ApplyInfoParam param);

    Integer cancelApply(ApplyInfoParam param);

    Integer sendMsg(MsgParam param);

    SyncResult selectMessage(@Param("hospId") Integer hospId,@Param("id") Integer id);

    Integer updateSendFlag(ApplyInfoParam param);

    Integer getReservationInfo(@Param("applyId")Integer applyId,@Param("hospId")Integer hospId);

    Integer saveReservationInfo(ReservationInfoParam param);

    Integer updateReservationInfo(ReservationInfoParam param);

    Integer editReservationState(ApplyInfoParam param);

    Integer saveReservationDoctor(@Param("list") List<DoctorInfoDto> list,@Param("applyId")Integer applyId,@Param("hospId")Integer hospId);

    List<DoctorWorkerDto> queryDoctorInfo(@Param("inputCode")String inputCode);

    List<DoctorInfoDto> queryDoctorList(@Param("applyId")Integer applyId,@Param("hospId")Integer hospId);
}
