package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 心理治疗申请主表
 */
@Data
@Table(name = "MZYS_TB_PsychotherapyApplyList")
public class PsychotherapyApplyList implements Serializable {

    @Id
    @Column(name = "ApplyId", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    @Column(name = "RegNo")
    private Integer regNo;

    @Column(name = "RecipeNo")
    private Integer recipeNo;

    @Column(name = "VisitNo")
    private String visitNo;

    @Column(name = "PatNo")
    private Integer patNo;

    @Column(name = "PatName")
    private String patName;

    @Column(name = "CardNo")
    private String cardNo;

    @Column(name = "HisCardNo")
    private String hisCardNo;

    @Column(name = "ApplyTime")
    private Date applyTime;

    @Column(name = "ApplyDoctor")
    private Integer applyDoctor;

    @Column(name = "Status")
    private Integer status;

    @Column(name = "CreateUserId")
    private Integer createUserId;

    @Column(name = "CreateOn")
    private Date createOn;

    @Column(name = "RisId")
    private Integer risId;

    @Column(name = "RegDate")
    private Date regDate;

    @Column(name = "HospitalId")
    private Integer hospitalId;

}