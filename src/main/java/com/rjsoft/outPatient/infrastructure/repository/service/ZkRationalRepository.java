package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.config.dto.DoctorInfoResponse;
import com.rjsoft.outPatient.domain.recipe.dto.HlyyDoctor;
import com.rjsoft.outPatient.infrastructure.repository.entity.Worker;
import com.rjsoft.outPatient.infrastructure.repository.entity.ZkIsNewRecipe;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 招康合理用药
 *
 * <AUTHOR>
public interface ZkRationalRepository {

    /**
     * 查询招康合理用药处方记录
     *
     * @param recipeNo
     * @param hospitalCode
     * @return
     */
    List<ZkIsNewRecipe> getZkIsNewRecipe(Integer recipeNo, Integer hospitalCode);


    /**
     * 保存招康合理用药处方记录
     *
     * @param zkIsNewRecipe
     * @return
     */
    boolean saveZkIsNewRecipe(ZkIsNewRecipe zkIsNewRecipe);
}
