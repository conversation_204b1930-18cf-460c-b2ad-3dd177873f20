package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.InHospitalInfo;

/**
 * 住院信息
 *
 * <AUTHOR>
 * @since 2021/7/12 - 10:22
 */
public interface InHospitalInfoRepository {

    /**
     * 根据挂号流水号查询住院信息
     *
     * @param certificateNo
     * @param hospitalCode
     * @return
     */
    InHospitalInfo getInHospitalInfoByRegNo(String certificateNo, Integer hospitalCode);

}
