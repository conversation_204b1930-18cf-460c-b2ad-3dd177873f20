package com.rjsoft.outPatient.infrastructure.repository.mapper;

import cn.hutool.core.collection.CollUtil;
import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeDetailView;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Collections;
import java.util.List;

public interface ChargeDetailViewMapper extends Mapper<ChargeDetailView> {

    default List<ChargeDetailView> getChargeDetailByChargeNoList(List<Long> chargeNoList, Integer hospitalCode) {
        if (CollUtil.isEmpty(chargeNoList)) {
            return Collections.emptyList();
        }
        Weekend<ChargeDetailView> weekend = new Weekend<>(ChargeDetailView.class);
        WeekendCriteria<ChargeDetailView, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ChargeDetailView::getChargeno, chargeNoList)
                .andEqualTo(ChargeDetailView::getHospitalcode, hospitalCode);
        return selectByExample(weekend);
    }

}