<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.OldSeriousIllnessRegisterMapper">
    <select id="getSFZ" resultType="java.lang.String">
        select distinct b.CertificateNo
        from Reg_Tb_PatientCard a (nolock)
        inner join Reg_Tb_PatientList b (nolock) on a.PatId = b.PatId
        where b.Is<PERSON>elete = 0
          <if test="cardNo !=null and cardNo != ''">
            and a.CardNo = #{cardNo}
          </if>
        <if test="hisCardNo !=null and hisCardNo != ''">
            and a.HisCardNo = #{hisCardNo}
        </if>
    </select>

</mapper>