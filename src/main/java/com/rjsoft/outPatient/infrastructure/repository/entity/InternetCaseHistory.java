package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class InternetCaseHistory implements Serializable {

    private Integer blId;

    private String regNo;

    private Date creDate;

    private String deptName;

    private String doctorName;

    private Integer dataSource;

    private String dataSourceStr;

    private Integer offsetFlag;

    private Integer hospitalCode;

    private String hospitalName;

    private Integer internetFlag;
}
