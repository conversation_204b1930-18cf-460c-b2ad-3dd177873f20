package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.ExecDeptTypeEnum;
import com.rjsoft.outPatient.domain.execDept.dto.FeeCategoryExecDeptAddDto;
import com.rjsoft.outPatient.domain.execDept.dto.FeeCategoryExecDeptSelectDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubFeecategoryexedept;
import com.rjsoft.outPatient.infrastructure.repository.mapper.MdmPubFeecategoryexedeptMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.MdmPubFeecategoryexedeptRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service
@AllArgsConstructor
public class MdmPubFeecategoryexedeptRepositoryImpl implements MdmPubFeecategoryexedeptRepository {
    private MdmPubFeecategoryexedeptMapper mdmPubFeecategoryexedeptMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public PageInfo<MdmPubFeecategoryexedept> getFeeCategoryExecDeptItemList(FeeCategoryExecDeptSelectDto feeCategoryExecDeptDto) {
        Weekend<MdmPubFeecategoryexedept> weekend = new Weekend<>(MdmPubFeecategoryexedept.class);
        WeekendCriteria<MdmPubFeecategoryexedept, Object> weekendCriteria = weekend.weekendCriteria();
        if(feeCategoryExecDeptDto.getFeeCategoryCode()!=null){
            weekendCriteria.andEqualTo(MdmPubFeecategoryexedept::getFeeCategoryCode,feeCategoryExecDeptDto.getFeeCategoryCode());
        }
        if(feeCategoryExecDeptDto.getDeptId()!=null){
            weekendCriteria.andEqualTo(MdmPubFeecategoryexedept::getUseDept,feeCategoryExecDeptDto.getDeptId());
        }
        if(!StringUtils.isEmpty(feeCategoryExecDeptDto.getInputFeeCategoryStr())){
            weekendCriteria.andLike(MdmPubFeecategoryexedept::getFeeCategoryName,feeCategoryExecDeptDto.getInputFeeCategoryStr());
        }
        if(!StringUtils.isEmpty(feeCategoryExecDeptDto.getInputDeptStr())){
            weekendCriteria.andLike(MdmPubFeecategoryexedept::getUseDeptName,feeCategoryExecDeptDto.getInputDeptStr());
        }
        PageHelper.startPage(feeCategoryExecDeptDto.getPageNum(), feeCategoryExecDeptDto.getPageSize());
        List<MdmPubFeecategoryexedept> mdmPubFeecategoryexedeptList = mdmPubFeecategoryexedeptMapper.selectByExample(weekend);
        PageInfo<MdmPubFeecategoryexedept> pageInfo = new PageInfo<>(mdmPubFeecategoryexedeptList);
        return pageInfo;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<HashMap<String,String>> getFeeCategoryExecDept(FeeCategoryExecDeptAddDto feeCategoryExecDeptAddDto,Integer id) {
        return mdmPubFeecategoryexedeptMapper.getFeeCategoryExecDept(feeCategoryExecDeptAddDto.getFeeCategoryCode(),feeCategoryExecDeptAddDto.getUseDept(),feeCategoryExecDeptAddDto.getStartTime(),feeCategoryExecDeptAddDto.getEndTime(),id,feeCategoryExecDeptAddDto.getHospitalId());
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public int addCategory(FeeCategoryExecDeptAddDto feeCategoryExecDeptAddDto) {
        MdmPubFeecategoryexedept mdmPubFeecategoryexedept = new MdmPubFeecategoryexedept();
        BeanUtils.copyProperties(feeCategoryExecDeptAddDto,mdmPubFeecategoryexedept);
        Date date = new Date();
        Integer userId = feeCategoryExecDeptAddDto.getUserId();
        mdmPubFeecategoryexedept.setType(ExecDeptTypeEnum.OUTPATIENT_TYPE.getType());
        mdmPubFeecategoryexedept.setCreateTime(date);
        mdmPubFeecategoryexedept.setUpdateTime(date);
        mdmPubFeecategoryexedept.setCreateUserId(userId);
        mdmPubFeecategoryexedept.setUpdateUserId(userId);
        return mdmPubFeecategoryexedeptMapper.insert(mdmPubFeecategoryexedept);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public int updateCategory(FeeCategoryExecDeptAddDto feeCategoryExecDeptAddDto) {
        MdmPubFeecategoryexedept mdmPubFeecategoryexedept = new MdmPubFeecategoryexedept();
        BeanUtils.copyProperties(feeCategoryExecDeptAddDto,mdmPubFeecategoryexedept);
        Date date = new Date();
        Integer userId = feeCategoryExecDeptAddDto.getUserId();
        mdmPubFeecategoryexedept.setUpdateTime(date);
        mdmPubFeecategoryexedept.setUpdateUserId(userId);
        return mdmPubFeecategoryexedeptMapper.updateByPrimaryKeySelective(mdmPubFeecategoryexedept);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public int deleteFeeCategory(Integer id) {
        return mdmPubFeecategoryexedeptMapper.deleteByPrimaryKey(id);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<MdmPubFeecategoryexedept> getFeeCategoryExecDeptByTime(Integer feeCategory, String curTime, Integer curDeptId, Integer curHospitalId) {
        return mdmPubFeecategoryexedeptMapper.getFeeCategoryExecDeptByTime(feeCategory, curTime, curDeptId, curHospitalId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<MdmPubFeecategoryexedept> getExecDeptList(String curTime, Integer curDeptId, Integer curHospitalId) {
        return mdmPubFeecategoryexedeptMapper.getExecDeptList(curTime, curDeptId, curHospitalId);
    }
}
