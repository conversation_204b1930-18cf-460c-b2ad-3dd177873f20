package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.config.dto.DischargeDrugDoctorDTO;
import com.rjsoft.outPatient.infrastructure.repository.entity.IoTbInpatient;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

public interface IoTbInpatientMapper extends BaseMapper<IoTbInpatient>, ExampleMapper<IoTbInpatient> {
    List<IoTbInpatient> queryInpatientInfos(@Param("patId") int patId, @Param("status") Integer status, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<IoTbInpatient> getInpatientListByPatids(@Param("patIds") List<Integer> patIds, @Param("status") Integer status, @Param("startTime") String startTime, @Param("endTime") String endTime);

    DischargeDrugDoctorDTO getDischargeDrugDoctor(@Param("regNo") Long regNo);

}
