<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeDetailResearchMapper">
    <select id="getByReceptionNoAndItemCodes"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetailResearch">
        select a.ID id,
            jzlsh receptionNo,
            cflsh recipeNo,
            cfmxlsh recipeDetailNo,
            xmmc itemName,
            xmbm itemCode,
            FPlanId fPlanId,
            PrimaryKey primaryKey,
            State state,
            Type type,
            ReportState reportState,
            sqh sqh,
            CreateID createId,
            CreateDate createDate,
            RemovedID removedId,
            RemovedDate removedDate,
            IsDeleted isDeleted,
            OutsidePlan outsidePlan,
            ProjectID projectId,
            <PERSON>Name projectName,
            <PERSON><PERSON><PERSON> patNo,
            RegNo regNo,
            hospitalCode hospitalCode
        from MZYS_TB_MZCFMXKY a(nolock)
        inner join System_Tb_PubItems b(nolock) on a.xmbm = b.ItemCode and a.hospitalCode = b.HospitalId
        <where>
            and a.jzlsh = #{receptionNo}
            and a.hospitalCode = #{hospitalCode}
            and a.IsDeleted = 0
            and b.MzItemId in
            <foreach collection="itemCodeList" index="index" item="itemCode" separator="," close=")" open="(">
                #{itemCode}
            </foreach>
        </where>
    </select>

    <update id="updateRecipeDetailResearch" >
        update MZYS_TB_MZCFMXKY set IsDeleted = 1
        WHERE jzlsh = #{receptionNo}
            and hospitalCode = #{hospitalCode}
            and cfmxlsh in
            <foreach collection="recipeDetailIds" index="index" item="recipeDetailId" separator="," close=")" open="(">
                #{recipeDetailId}
            </foreach>
    </update>

</mapper>