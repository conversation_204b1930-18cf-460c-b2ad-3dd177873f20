package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ApplyBigForm;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

/**
 * 申请大类
 * <AUTHOR>
public interface ApplyBigFormMapper extends BaseMapper<ApplyBigForm>, ExampleMapper<ApplyBigForm> {

    /**
     * 根据ID，加载大类信息
     * @param id
     * @return
     */
    default ApplyBigForm getBigFormById(Integer id) {
        ApplyBigForm entity = new ApplyBigForm();
        entity.setId(id);
        return selectByPrimaryKey(entity);
    }

    /**
     * 根据申请单类别查询分类
     * @param type
     * @return
     */
    default List<ApplyBigForm> getBigFormByType(Integer type) {
        ApplyBigForm entity = new ApplyBigForm();
        entity.setBigFormType(type);
        return select(entity);
    }

    /**
     * 根据ID，加载大类信息
     * @param ids
     * @return
     */
    default List<ApplyBigForm> queryBigFormListByIds(List<Integer> ids) {
        Weekend<ApplyBigForm> weekend = new Weekend<>(ApplyBigForm.class);
        WeekendCriteria<ApplyBigForm, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ApplyBigForm::getId, ids);
        return selectByExample(weekend);
    }

}
