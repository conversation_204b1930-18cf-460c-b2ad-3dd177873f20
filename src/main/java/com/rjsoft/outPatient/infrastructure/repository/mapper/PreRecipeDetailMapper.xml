<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.PreRecipeDetailMapper">

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO MZYS_TB_MZCFMXPre (
        jzlsh, ghlsh, cflsh, xmbm, xmmc, sflx, cflx, jl, jldw, yf, gg, gytj, tsyf, ts, xmsl, dj, dw, mzdwsl,
        cfje, zh, pytjbm, yszt, zt, zxks, hfbj, tcbj, tcmxlsh, sqd, jclsh, jcmxlsh, tcid, cfzt, hospitalCode,
        preSaveNo, cfmxlsh, hybb, preSaveType, opCode, opTime, flmc, jclxbm, jclxmc, jcbwbh, xmxh,
        isOtherHospital, itemHospitalCode, exeHospitalId, GlobalId
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.receptionNo}, #{item.regNo}, #{item.recipeNo}, #{item.itemCode}, #{item.itemName}, #{item.feeCategory},
            #{item.recipeCategory}, #{item.dose}, #{item.doseUnit}, #{item.frequency}, #{item.specification},
            #{item.usage}, #{item.specialUsage}, #{item.days}, #{item.quantity}, #{item.price}, #{item.unit},
            #{item.clincUnitNum}, #{item.amount}, #{item.groupNo}, #{item.dispensing}, #{item.doctorEntrust},
            #{item.status}, #{item.execDept}, #{item.changeRecipeFlag}, #{item.packageFlag}, #{item.packageDetailNo},
            #{item.applicationForm}, #{item.examineNo}, #{item.examineDetailNo}, #{item.packageId}, #{item.recipeStatus},
            #{item.hospitalCode}, #{item.preSaveNo}, #{item.recipeDetailNo}, #{item.urgent}, #{item.preSaveType},
            #{item.opCode}, #{item.opTime}, #{item.assayCategoryName}, #{item.examineCode}, #{item.examineName},
            #{item.examinePartNo}, #{item.itemNo}, #{item.isOtherHospital}, #{item.itemHospitalCode},
            #{item.exeHospitalId}, #{item.globalId})
        </foreach>
    </insert>
</mapper>
