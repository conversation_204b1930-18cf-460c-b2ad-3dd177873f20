package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.RegTbSpecialsourcerelation;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

public interface RegTbSpecialsourcerelationMapper extends BaseMapper<RegTbSpecialsourcerelation>, ExampleMapper<RegTbSpecialsourcerelation> {
    /**
     * 获取特需号挂号表信息
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    RegTbSpecialsourcerelation getRegTbSpecialsourcerelation(Long regNo, Integer hospitalCode);
}
