package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;
import org.apache.logging.log4j.util.Strings;
import tk.mybatis.mapper.entity.IDynamicTableName;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存表
 *
 * <AUTHOR>
 * @since 2021/9/8-10:46 上午
 */
@Data
@Table(name = "Drug_Tb_StoreChangeOutPatientVirtual")
public class VirtualInventory  implements IDynamicTableName, Serializable {

    /**
     *
     */
    @Id
    @Column(name = "Id")
    private Integer id;
    /**
     * 科室编码
     */
    @Column(name = "DeptId")
    private Integer deptId;
    /**
     * 药品编码
     */
    @Column(name = "DrugId")
    private Integer drugId;
    /**
     *
     */
    @Column(name = "DrugNewId")
    private Integer drugNewId;
    /**
     * 批号
     */
    @Column(name = "BatchNo")
    private String batchNo;
    /**
     * 数量
     */
    @Column(name = "Quantity")
    private BigDecimal quantity;
    /**
     * 包装系数
     */
    @Column(name = "PackFactor")
    private Integer packFactor;
    /**
     * 包装单位
     */
    @Column(name = "PackUnit")
    private Integer packUnit;
    /**
     * 零售价
     */
    @Column(name = "RetailPrice")
    private BigDecimal retailPrice;
    /**
     * 交易价格
     */
    @Column(name = "TradePrice")
    private BigDecimal tradePrice;
    /**
     * 更改类型
     */
    @Column(name = "ChangeType")
    private Integer changeType;

    public Integer getChangeType() {
        // 15 去库存、16 加库存
        return changeType = quantity != null && quantity.compareTo(BigDecimal.ZERO) > 0 ? 16 : 15;
    }

    /**
     * 源列表 ID
     */
    @Column(name = "SourceListId")
    private Integer sourceListId;
    /**
     * 源详细信息 ID
     */
    @Column(name = "SourceDetailId")
    private Integer sourceDetailId;
    /**
     * 创建于
     */
    @Column(name = "CreateOn")
    private Date createOn;
    /**
     * 创建用户 ID
     */
    @Column(name = "CreateUserId")
    private Integer createUserId;
    /**
     * 创建用户名
     */
    @Column(name = "CreateUserName")
    private String createUserName;
    /**
     * 医院编码
     */
    @Column(name = "HospitalId")
    private Integer hospitalId;
    /**
     * 库存
     */
    @Transient
    private BigDecimal inventory;

    /**
     * 动态表名
     */
    @Transient
    private String tableName;

    @Override
    public String getDynamicTableName() {
        return tableName;
    }


    public VirtualInventory() {
    }

    public VirtualInventory(Integer drugId, Integer deptId, Integer hospitalId) {
        this.drugId = drugId;
        this.deptId = deptId;
        this.hospitalId = hospitalId;
    }

    public VirtualInventory(RecipeDetail recipeDetail, ChargeItem chargeItem, Long virtualInventoryId) {
        //药房、项目编码、DrugNewId、BatchNo、项目数量、包装系数、包装单位、零售价、交易价、ChangeType、
        // SourceListId（处方 id）、SourceDetailId（处方明细 id）、创建时间、创建人id、创建人姓名、医院编码
        this.batchNo = Strings.EMPTY;
        this.id = Math.toIntExact(virtualInventoryId);
        this.deptId = recipeDetail.getExecDept();
        this.drugId = recipeDetail.getItemCode();
        this.quantity = recipeDetail.getQuantity();
        this.packFactor = chargeItem.getClinicQty();
        this.tradePrice = chargeItem.getTradePrice();
        this.retailPrice = chargeItem.getRetailPrice();
        this.hospitalId = recipeDetail.getHospitalCode();
        this.createUserId = recipeDetail.getLastDoctorId();
        this.sourceDetailId = Math.toIntExact(recipeDetail.getRecipeDetailNo());
    }
}
