package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.DctAddNumDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubMultidisciplinary;
import com.rjsoft.outPatient.infrastructure.repository.mapper.MultidisciplinaryMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.MultidisciplinaryRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

@AllArgsConstructor
@Service
public class MultidisciplinaryRepositoryImpl implements MultidisciplinaryRepository {
    private MultidisciplinaryMapper multidisciplinaryMapper;

    @DatabaseAnnotation(name = DatasourceName.HISDB)
    @Override
    public PageInfo getMultidisciplinaryList(String name, Integer hospitalId, String state, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<MdmPubMultidisciplinary> list = multidisciplinaryMapper.getMultidisciplinaryList(name,hospitalId,state);
        PageInfo pageInfo = new PageInfo<>(list);
        return pageInfo;
    }

    @DatabaseAnnotation(name = DatasourceName.HISDB)
    @Override
    public MdmPubMultidisciplinary getMultidisciplinaryByUniqueID(Long uniqueID) {
        return multidisciplinaryMapper.selectByPrimaryKey(uniqueID);
    }

    @DatabaseAnnotation(name = DatasourceName.HISDB)
    @Override
    public List<MdmPubMultidisciplinary> getMultidisciplinaryByTypeName(String typeName, Integer hospitalId) {
        Weekend<MdmPubMultidisciplinary> weekend = new Weekend<>(MdmPubMultidisciplinary.class);
        weekend.weekendCriteria().andEqualTo(MdmPubMultidisciplinary::getTypeName,typeName).andEqualTo(MdmPubMultidisciplinary::getHospitalId,hospitalId);
        return multidisciplinaryMapper.selectByExample(weekend);
    }

    @DatabaseAnnotation(name = DatasourceName.HISDB)
    @Override
    public boolean updateMultidisciplinaryState(Long uniqueID, String state) {
        MdmPubMultidisciplinary multidisciplinary = new MdmPubMultidisciplinary();
        multidisciplinary.setUniqueID(uniqueID);
        multidisciplinary.setState(state);
        return multidisciplinaryMapper.updateByPrimaryKeySelective(multidisciplinary)>0;
    }

    @DatabaseAnnotation(name = DatasourceName.HISDB)
    @Override
    public boolean addMultidisciplinary(MdmPubMultidisciplinary multidisciplinary) {
        return multidisciplinaryMapper.insertSelective(multidisciplinary)>0;
    }

    @DatabaseAnnotation(name = DatasourceName.HISDB)
    @Override
    public boolean updateMultidisciplinary(MdmPubMultidisciplinary multidisciplinary) {
        return multidisciplinaryMapper.updateByPrimaryKey(multidisciplinary)>0;
    }
}
