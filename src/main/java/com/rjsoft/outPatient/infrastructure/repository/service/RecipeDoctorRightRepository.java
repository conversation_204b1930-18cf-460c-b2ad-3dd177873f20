package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDoctorRight;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/23 - 17:58
 */
public interface RecipeDoctorRightRepository {

    /**
     * 根据医生id和权限代码查询医生开药权限
     *
     * @param doctorId 医生id
     * @param rightCodes 权限代码
     * @return
     */
    List<RecipeDoctorRight> getRecipeDoctorRightByDoctorIdAndRightCode(Integer doctorId, List<Integer> rightCodes);

}
