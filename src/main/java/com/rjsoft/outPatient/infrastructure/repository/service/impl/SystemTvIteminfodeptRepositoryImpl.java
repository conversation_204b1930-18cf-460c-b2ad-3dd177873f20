package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.utils.TyMdc;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDeptDto;
import com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDeptDtoWithItemCode;
import com.rjsoft.outPatient.domain.recipe.constant.TyKey;
import com.rjsoft.outPatient.infrastructure.repository.entity.SystemTvIteminfodept;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SystemTvIteminfodeptMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.SystemTvIteminfodeptRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class SystemTvIteminfodeptRepositoryImpl implements SystemTvIteminfodeptRepository {
    SystemTvIteminfodeptMapper systemTvIteminfodeptMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<SystemTvIteminfodept> getSystemTbItemexecdept(Integer itemCode, Integer hospitalId) {
        return systemTvIteminfodeptMapper.getSystemTbItemexecdept(itemCode,hospitalId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<ChargeItemExecDeptDto> getItemsExecDept(Integer itemCode, String feeCategory) {
        Map<Integer,List<ChargeItemExecDeptDto> > defaultExecDeptMap = null;
        // FIXME: yutao 2024/7/6 前面put DEFAULT_EXEC_DEPT_MAP 时，有问题，会覆盖。先注释，以后再修改逻辑
//        defaultExecDeptMap = TyMdc.get(TyKey.DEFAULT_EXEC_DEPT_MAP);
        Integer feeCategoryInt = null;
        try {
            feeCategoryInt = Integer.valueOf(feeCategory);
        }catch (Exception e){
            feeCategoryInt = null;
        }
        if(defaultExecDeptMap==null) {
            if(feeCategoryInt==null) {
                return systemTvIteminfodeptMapper.getItemsExecDept(itemCode);
            }else if(ItemCategoryEnum.isDrug(feeCategoryInt)){
                return systemTvIteminfodeptMapper.getDrugsExecDept(itemCode);
            }else{
                return systemTvIteminfodeptMapper.getNotDrugsExecDept(itemCode);
            }
        }else{
            return defaultExecDeptMap.get(itemCode);
        }
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<ChargeItemExecDeptDtoWithItemCode> getItemsExecDeptByItemCodeList(List<Integer> itemCodeList) {
        return systemTvIteminfodeptMapper.getItemsExecDeptByItemCodeList(itemCodeList);
    }
}
