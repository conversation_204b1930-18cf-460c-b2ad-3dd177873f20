package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.bean.SpringBootBeanUtils;
import com.rjsoft.common.configuration.SysConfig;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.domain.config.dto.HisDictionaryDto;
import com.rjsoft.outPatient.domain.dictionary.Enum.DictionaryConfigEnum;
import com.rjsoft.outPatient.domain.dictionary.dto.CommonDicEntity;
import com.rjsoft.outPatient.domain.dictionary.dto.GeneralDicDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.AreaInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.DicInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.MenuByHisRight;
import com.rjsoft.outPatient.infrastructure.repository.entity.MenuByPrescription;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.DictionaryRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kj, liaoyy
 */
@Service
@AllArgsConstructor
public class DictionaryRepositoryImpl implements DictionaryRepository {

    DicInfoMapper dicInfoMapper;
    AreaInfoMapper areaInfoMapper;
    GeneralDicMapper generalDicMapper;
    MenuByPrescriptionMapper menuByPrescriptionMapper;
    MenuByHisRightMapper menuByHisRightMapper;
    SysConfig sysConfig;

    @Override
    @DatabaseAnnotation
    public List<CommonDicEntity> getDictionaryConfig(Integer type, Integer hospitalCode) {
        BaseMapper<? extends CommonDicEntity> baseMapper = SpringBootBeanUtils.getBean(DictionaryConfigEnum.getMapperClass(type));
        List<? extends CommonDicEntity> commonDicList = baseMapper.selectAll();
        return commonDicList.parallelStream().map(CommonDicEntity::conversionFill).collect(Collectors.toList());
    }

    @Override
    @DatabaseAnnotation
    public List<DicInfo> getDicInfo(String dicTypeSpell) {
        return dicInfoMapper.getDicInfo(dicTypeSpell);
    }

    @Override
    @DatabaseAnnotation
    public DicInfo getDicInfoByCode(String dicTypeSpell, String dicName, String dicCode) {
        return dicInfoMapper.getDicInfoByCode(dicTypeSpell, dicName, dicCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public List<AreaInfo> getAreaInfo(String parentCode, Integer level) {
        return areaInfoMapper.getAreaInfo(parentCode, level);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public AreaInfo getAreaInfoByCode(String parentCode, Integer level, String code) {
        return areaInfoMapper.getAreaInfoByCode(parentCode, level, code);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZYEMR)
    public GeneralDicDto getNationDic(String nation) {
        return generalDicMapper.getNationDicByCode(nation);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZYEMR)
    public GeneralDicDto getEducationDic(String education) {
        return generalDicMapper.getEducationDicByCode(education);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZYEMR)
    public GeneralDicDto getMaritalDic(String maritalStatus) {
        return generalDicMapper.getMaritalDicByCode(maritalStatus);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public HisDictionaryDto getGenaralDic(Integer dictionaryTypeID, String hisDictionaryCode, Integer hospId) {
        return generalDicMapper.getGenaralDic(dictionaryTypeID, hisDictionaryCode, hospId);
    }

    @Override
    @DatabaseAnnotation
    public List<MenuByPrescription> getPrescriptionMenu(Integer hospitalCode) {
        Weekend<MenuByPrescription> weekend = new Weekend<>(MenuByPrescription.class);
        weekend.weekendCriteria().andEqualTo(MenuByPrescription::getHospitalCode, hospitalCode)
                .andEqualTo(MenuByPrescription::getStatus, 1);
        weekend.setOrderByClause("cdxh");
        return menuByPrescriptionMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<MenuByHisRight> getHisRightMenu(Integer hospitalCode) {
        Weekend<MenuByHisRight> weekend = new Weekend<>(MenuByHisRight.class);
        weekend.weekendCriteria().andEqualTo(MenuByHisRight::getHospitalCode, hospitalCode)
                .andEqualTo(MenuByHisRight::getStatus, 1);
        weekend.setOrderByClause("cdxh");
        return menuByHisRightMapper.selectByExample(weekend);
    }

}
