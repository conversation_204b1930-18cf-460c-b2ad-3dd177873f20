package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.domain.recipe.dto.SystemTvPubItemsDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.PreRecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.SpecialItems;
import com.rjsoft.outPatient.infrastructure.repository.entity.SystemTbPubItems;
import javafx.util.Pair;

import java.util.List;
import java.util.Map;

public interface SystemTbPubItemsRepository {
    SystemTbPubItems getChargeItemById(Integer itemCode, Integer hospitalCode, Integer stopped);

    @DatabaseAnnotation(name = DatasourceName.HISDB)
    List<SystemTbPubItems> getChargeItemByIdList(List<Integer> itemCodeList, Integer hospitalCode, Integer stopped);

    SystemTbPubItems getChargeItemByMzItemId(Integer mzItemId, Integer hospitalCode, Integer stopped);

    List<SystemTbPubItems> getSystemTbPubItems(String inputCode, Integer hospitalCode);

    /**
     * 查询收费项目视图
     *
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    SystemTvPubItemsDto getChargeItemTv(Integer itemCode, Integer hospitalCode, Integer exeDept, Integer exeHospitalId, Integer stopped);

    SystemTbPubItems chargeItemTrim(SystemTbPubItems item);

    SystemTbPubItems getChargeItemByIdIsStopped(Integer itemCode, Integer deptId, Integer hospitalCode, Integer stopped);

    List<SpecialItems> querySpecialItemByIdList(List<Integer> itemCodeList, Integer hospitalId);

    SystemTbPubItems chargeItemBatchTrim(SystemTbPubItems item, Map<String, SpecialItems> specialItemMap);

    SystemTbPubItems tyMdcGetChargeItemByIdIsStopped(Integer itemCode, Integer deptId, Integer hospitalCode, Integer stopped);

    List<SystemTbPubItems> ListChargeItemByIdIsStopped(List<Integer> itemCodeList, List<Integer> exeDeptIdList, Integer hospitalCode, Integer stopped);

    // XXX: yutao 2023/4/17 add 4 重构
    SystemTbPubItems getChargeItemByIdIsStopped2(Integer itemCode, Integer deptId, Integer hospitalCode, Integer stopped);

    List<Pair<String,SystemTbPubItems>> queryChargeItemByItemCodeList(List<PreRecipeDetail>detailList, Integer hospitalCode, Integer stopped);

    List<SystemTbPubItems> queryChargeItemByIdList(List<Integer> itemCodeList, Integer hospitalCode);

}
