package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.outPatient.infrastructure.repository.entity.Agent;
import com.rjsoft.outPatient.infrastructure.repository.entity.AppointmentPatient;
import tk.mybatis.mapper.common.BaseMapper;

public interface AppointmentPatientMapper extends BaseMapper<AppointmentPatient> {

    /**
     * 根据电话和身份证号获取患者预约信息,判断有没有绑定微信
     * @param phone
     * @param certificate
     */
    default AppointmentPatient getPatientAppoint(String phone,String certificate){
        AppointmentPatient patient = new AppointmentPatient();
        patient.setTelephone(phone);
        patient.setCertificateNo(certificate);
        patient.setBindStatu(Converter.toInt64(1));
        patient.setIsUse(Converter.toInt64(1));
        patient.setIsDelete(Converter.toInt64(0));
        return selectOne(patient);
    }
    /**
     * 根据电话和身份证号获取患者预约信息，判断患者有没有认证
     * @param phone
     * @param certificate
     */
    default AppointmentPatient getPatientAppointment(String phone,String certificate){
        AppointmentPatient patient = new AppointmentPatient();
        patient.setTelephone(phone);
        patient.setCertificateNo(certificate);
        patient.setBindStatu(Converter.toInt64(1));
        patient.setIsUse(Converter.toInt64(1));
        patient.setIsDelete(Converter.toInt64(0));
        patient.setIsAuthentication(Converter.toInt64(1));
        return selectOne(patient);
    }

}
