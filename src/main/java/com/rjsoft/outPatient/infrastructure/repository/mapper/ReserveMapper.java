package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.RegAppointmentLog.dto.ChedulingInfo;
import com.rjsoft.outPatient.domain.reception.dto.VipInfoDto;
import com.rjsoft.outPatient.domain.reserve.dto.*;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 医院排班信息
 *
 * <AUTHOR>
 * @since 2021/8/11 - 9:01
 */
public interface ReserveMapper {

    /**
     * 查询医生排班信息
     *
     * @param doctorId  医生id
     * @param startDate 开始时间
     * @return List<DoctorSchedulingInfoDto>
     */
    List<DoctorSchedulingInfoDto> queryDoctorSchedulingInfoDto(@Param("doctorId") Integer doctorId, @Param("startDate") LocalDate startDate,@Param("endDate") LocalDate endDate,
                                                               @Param("deptId") Integer deptId, @Param("scheType") Integer scheType, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 查询时段
     * 执行 Usp_Hst_Get_Dic_TimeSpanExtNew 存储过程
     *
     * @param timeMs       上午/下午
     * @param scheduleDate 排班日期
     * @param deptId       科室编码
     * @param doctorId     医生编码
     * @return {@link List<QueryTimeSpanDto>}
     */
    List<QueryTimeSpanDto> queryTimeSpan(@Param("timeMs") String timeMs, @Param("scheduleDate") Date scheduleDate,
                                         @Param("deptId") Integer deptId, @Param("doctorId") Integer doctorId);

    /**
     * 查询
     * 调整排班页面医院号源列表
     *
     * @param adjustReserveTimeDTO {@link AdjustReserveTimeDTO}
     * @return {@link AdjustReserveTimeDTO} for list
     */
    List<AdjustReserveTimeDTO> getAdjustReserveView(AdjustReserveTimeDTO adjustReserveTimeDTO);

    /**
     * 根据科室id获取对应医生Id,去重
     *
     * @param deptCode 部门编号
     * @param hospId   医院Id
     */
    List<Integer> getReserveDctCode(@Param("deptCode") Integer deptCode, @Param("hospId") Integer hospId);

    /**
     * 根据排班科室查询排班医生列表
     *
     * @param inputCode    输入码
     * @param deptCode     科室编码
     * @param hospitalCode 医院编码
     */
    List<OnDutyDoctorDTO> queryShiftDoctorList(@Param("inputCode") String inputCode, @Param("deptCode") String deptCode, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据排班医生查询排班科室列表
     *
     * @param inputCode    输入码
     * @param doctorCode   医生编码
     * @param hospitalCode 医院编码
     */
    List<OnDutyDeptDTO> queryShiftDeptList(@Param("inputCode") String inputCode,
                                           @Param("doctorCode") String doctorCode,
                                           @Param("hospitalCode") Integer hospitalCode,
                                           @Param("startTime") String startTime,
                                           @Param("endTime") String endTime);

    /**
     * 查询预约信息
     * @param source
     * @param hospitalId
     * @param startTime
     * @param endTime
     */
    List<AppointmentInfo> findAppointmentInfo(@Param("source") String source, @Param("doctorId")Integer doctorId,
                                        @Param("hospitalId")Integer hospitalId,@Param("startTime")String startTime,
                                        @Param("endTime")String endTime,@Param("deptId")Integer deptId);


    /**
     * 医技预约
     *
     * @param time
     */
    List<Map<String, Object>> medicalAppointment(String time);

    /**
     * @param regSerialNo     挂号序号
     * @param appointmentOrderId  预约号
     * 根据挂号序号获取预约文字
     */
    String getReserve(@Param("regSerialNo") Integer regSerialNo,@Param("appointmentOrderId")String appointmentOrderId);

    List<VipInfoDto> getVipInfo(String doctID, String deptId);

    int getIncreaseTypeById(Integer id);

    ChedulingInfo getChedulingInfo(Integer deptId , Integer doctorId);
}
