<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.MzysKyQtsfjhzbMapper" >
  <resultMap id="VisitPlanMap" type="com.rjsoft.outPatient.domain.research.dto.VisitPlanDto" >
    <result column="Primary_Key" property="primaryKey" />
    <result column="Project_Id" property="projectId" />
    <result column="Project_Code" property="projectCode" />
    <result column="Project_Alert" property="projectAlert" />
    <result column="Patient_Id" property="patientId" />
    <result column="FPlanId" property="fplanId" />
    <result column="FollowupTitle" property="followupTitle" />
    <result column="FollowupDate" property="followupDate" />
    <result column="Type" property="type" />
    <result column="ItemGroupName" property="itemGroupName" />
    <result column="ItemGroupCode" property="itemGroupCode" />
    <result column="ItemName" property="itemName" />
    <result column="ItemCode" property="itemCode" />
    <result column="OPorIP" property="oporIP" />
    <result column="Dose" property="dose" />
    <result column="DoseUnit" property="doseUnit" />
    <result column="UsageCode" property="usageCode" />
    <result column="Quantity" property="quantity" />
    <result column="OtherUnitQuantity" property="otherUnitQuantity" />
    <result column="DrugSpec" property="drugSpec" />
    <result column="RouteOfMedicationCode" property="routeOfMedicationCode" />
    <result column="DaysOfDrugUse" property="daysOfDrugUse" />
    <result column="UtilityTime" property="utilityTime" />
    <result column="Price" property="price" />
    <result column="Total" property="total" />
    <result column="Status" property="status" />
  </resultMap>
  <select id="visitPlan" resultMap="VisitPlanMap" parameterType="com.rjsoft.outPatient.domain.research.dto.VisitPlanInputDto" >
    declare @Result nvarchar(200)
    EXEC UP_RetrievePatientFollowUpPlanNew #{visitPlanInputDto.userCode},#{visitPlanInputDto.idCard},#{visitPlanInputDto.projectId},#{visitPlanInputDto.fplanId},@Result
  </select>

  <select id="submitExtraVisitPlan" resultType="java.lang.String" parameterType="java.lang.String" >
    EXEC RDR_ResHisExchange #{id}
  </select>
</mapper>