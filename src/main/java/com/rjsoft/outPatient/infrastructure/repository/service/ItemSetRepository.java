package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.packages.dto.ItemSetDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.ItemSet;
import com.rjsoft.outPatient.infrastructure.repository.entity.ItemSetDetail;

import java.util.List;

/**
 * 查询套餐
 * <AUTHOR>
public interface ItemSetRepository {

    /**
     * 通过医院编码、项目名称（缩写）
     *
     * @param hospitalId 医院编码
     * @param itemNameOrInputCode 项目名称（缩写）
     * @return
     */
    List<ItemSetDto> getItemSetDtoByHospitalId(Integer itemCategory,Integer hospitalId, String itemNameOrInputCode);
}
