package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;

@Data
@Table(name = "Reg_Tb_DBConnection")
public class RegTbDBConnection {
    @Column(name = "DBName")
    private String dbName;
    @Column(name = "ConnectionString")
    private String connectionString;
    @Column(name = "HospitalCode")
    private String hospitalCode;

}
