package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ExamineRequestDetail;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

/**
 * 检查申请明细
 * <AUTHOR>
public interface ExamineRequestDetailMapper extends BaseMapper<ExamineRequestDetail>, ExampleMapper<ExamineRequestDetail> {

    /**
     * 根据检查流水号获取检查明细
     * @param examineNo
     * @param itemCode
     * @return
     */
    default ExamineRequestDetail getExamineRequestDetailByExamineNo(Integer examineNo,Integer itemCode) {
        ExamineRequestDetail entity = new ExamineRequestDetail();
        entity.setExamineNo(examineNo);
        entity.setItemCode(itemCode);
        List<ExamineRequestDetail>list=select(entity);
        if(list.stream().count()==0){
            return null;
        }
        return list.get(0);
    }
}
