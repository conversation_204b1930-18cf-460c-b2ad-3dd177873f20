<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DoctorDiseaseReportMapper">


    <select id="getDoctorReportDisease"
            resultType="com.rjsoft.outPatient.domain.diseaseReport.dto.DiseaseReportResponse">
        select
        a.Id id,
        case when a.ReportType = 'HIS' then 1 else 0 end isHisReport,
        a.UploadTypeId uploadTypeId,
        a.jzlsh receptionNo,
        a.diagnoseCode diagnoseCode,
        a.Name patName,
        a.IdNo certificateNo,
        a.CreateTime reportTime,
        a.Status status
        from Tbt_ZyEMR_Qpmzxbr a (nolock)
        where ( a.Updator = #{doctorId} or a.Creator = #{doctorId} )
        <if test="CertificateNo != null">
            and a.IdNo = #{CertificateNo}
        </if>
        <if test="status != -2">
            and a.Status = #{status}
        </if>
        <if test="uploadTypeId != 0">
            and a.UploadTypeId = #{uploadTypeId}
        </if>
        and a.HospitalCode=#{hospitalCode}
        and (CONVERT(date,a.CreateTime,20) between #{startTime} and DATEADD(day,1,#{endTime})
        or CONVERT(date,a.UpdateTime,20) between #{startTime} and DATEADD(day,1,#{endTime}))
    </select>


</mapper>