package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.EnterHospitalRequisitionEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.EnterHospitalRequisition;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzZySqdContent;
import com.rjsoft.outPatient.infrastructure.repository.entity.SQDTcZb;
import com.rjsoft.outPatient.infrastructure.repository.mapper.CurrencyMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.EnterHospitalRequisitionMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.MzZySqdContentMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SqdTcMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.EnterHospitalRequisitionRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/19 - 14:17
 */
@Service
@AllArgsConstructor
public class EnterHospitalRequisitionRepositoryImpl implements EnterHospitalRequisitionRepository {

    private final EnterHospitalRequisitionMapper enterHospitalRequisitionMapper;
    MzZySqdContentMapper mzZySqdContentMapper;
    CurrencyMapper currencyMapper;
    SqdTcMapper sqdTcMapper;

    @Override
    @DatabaseAnnotation
    public int getSqdMaxId() {
        return currencyMapper.getSqdMaxId();
    }

    @Override
    @DatabaseAnnotation
    public Integer save(EnterHospitalRequisition entity) {
        return enterHospitalRequisitionMapper.insertSelective(entity);
    }

    @Override
    @DatabaseAnnotation
    public Integer submitApply(EnterHospitalRequisition entity) {
        entity.setStatus(EnterHospitalRequisitionEnum.RECEIVED.getCode());
        return enterHospitalRequisitionMapper.updateByPrimaryKey(entity);
    }

    @Override
    @DatabaseAnnotation
    public Integer saveSqdContent(MzZySqdContent zySqdContent) {
        return mzZySqdContentMapper.insertSelective(zySqdContent);
    }

    @Override
    @DatabaseAnnotation
    public Integer updateContent(MzZySqdContent zySqdContent) {
        return mzZySqdContentMapper.updateByPrimaryKeySelective(zySqdContent);
    }

    @Override
    @DatabaseAnnotation
    public Integer update(EnterHospitalRequisition entity) {
        return enterHospitalRequisitionMapper.updateByPrimaryKey(entity);
    }

    @Override
    @DatabaseAnnotation
    public EnterHospitalRequisition getEnterHospitalRequisition(String receptionNo) {
        final EnterHospitalRequisition entity = new EnterHospitalRequisition();
        entity.setReceptionNo(Converter.toInt64(receptionNo));
        return enterHospitalRequisitionMapper.selectOne(entity);
    }

    @Override
    @DatabaseAnnotation
    public List<EnterHospitalRequisition> getEnterHospitalRequisitionList(String receptionNo) {
        final EnterHospitalRequisition entity = new EnterHospitalRequisition();
        entity.setReceptionNo(Converter.toInt64(receptionNo));
        return enterHospitalRequisitionMapper.select(entity);
    }

    @Override
    @DatabaseAnnotation
    public List<EnterHospitalRequisition> getEnterHospitalRequisitionByIds(List<Long> receptionNos) {
        if (receptionNos == null || receptionNos.isEmpty()) {
            return new ArrayList<>();
        }
        Weekend weekend = new Weekend(EnterHospitalRequisition.class);
        WeekendCriteria<EnterHospitalRequisition, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(EnterHospitalRequisition::getReceptionNo, receptionNos);
        return enterHospitalRequisitionMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public String importReport(String patName, String hisCardNo, String receptionNo) {
        return enterHospitalRequisitionMapper.importReport(patName, hisCardNo, receptionNo);
    }

    /**
     * 根据就诊流水号查询申请单信息
     *
     * @param jzlsh
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<EnterHospitalRequisition> getSqdByJzlsh(Long jzlsh, Integer hospitalCode) {
        EnterHospitalRequisition enterHospitalRequisition = new EnterHospitalRequisition();
        enterHospitalRequisition.setReceptionNo(jzlsh);
        enterHospitalRequisition.setHospitalCode(hospitalCode);
        return enterHospitalRequisitionMapper.select(enterHospitalRequisition);
    }

    /**
     * 根据申请单id,医院编码查询申请单内容
     *
     * @param sqdId
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public MzZySqdContent getSqdContent(Integer sqdId, Integer hospitalCode) {
        MzZySqdContent mzZySqdContent = new MzZySqdContent();
        mzZySqdContent.setSqdId(sqdId);
        mzZySqdContent.setHospitalCode(hospitalCode);
        return mzZySqdContentMapper.selectOne(mzZySqdContent);
    }

    /**
     * 查询配置套餐信息
     *
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<SQDTcZb> getSqdTc(Integer hospitalCode) {
        Weekend weekend = new Weekend(SQDTcZb.class);
        WeekendCriteria<SQDTcZb, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(SQDTcZb::getHospitalCode,hospitalCode);
         return sqdTcMapper.selectByExample(weekend);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Integer getFxpgStatus(Integer id, Integer hospitalCode) {
        //if (HospitalIdEnum.BRANCH.getCode().equals(hospitalCode)) {
        //    DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
        //} else {
        //    DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
        //}
        Integer fxpgStatus = enterHospitalRequisitionMapper.getFxpgStatus(Converter.toString(id), hospitalCode);
        if (fxpgStatus == null) {
            return 0;
        } else if (fxpgStatus == 0) {
            return 5;
        } else {
            return 3;
        }
    }

    @Override
    @DatabaseAnnotation
    public List<EnterHospitalRequisition> getInHospitalApply(Date startTime, Date endTime, Integer status, HashMap pageInfo, Integer signStatus) {
        String name = Converter.toString(pageInfo.get("name"));
        Weekend weekend = new Weekend(EnterHospitalRequisition.class);
        WeekendCriteria<EnterHospitalRequisition, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andGreaterThanOrEqualTo(EnterHospitalRequisition::getInHospTime, startTime);
        weekendCriteria.andLessThan(EnterHospitalRequisition::getInHospTime, endTime);
        if (status != null) {
            weekendCriteria.andEqualTo(EnterHospitalRequisition::getStatus, status);
        }
        if (signStatus != null) {
            weekendCriteria.andEqualTo(EnterHospitalRequisition::getRisSignStatus, signStatus);
            weekendCriteria.andEqualTo(EnterHospitalRequisition::getRiskStatus, 0);
        }
        if (!StringUtils.isEmpty(name)) {
            name = "%" + name + "%";
            WeekendCriteria<EnterHospitalRequisition, Object> keywordCriteria1 = weekend.weekendCriteria();
            keywordCriteria1.andLike(EnterHospitalRequisition::getName, name).
                    orLike(EnterHospitalRequisition::getCardNo, name)
                    .orLike(EnterHospitalRequisition::getCertificateNo, name);
            weekend.and(keywordCriteria1);
        }
        Integer pageSize = Converter.toInt32(pageInfo.get("pageSize"));
        Integer pageNum = Converter.toInt32(pageInfo.get("pageNum"));
        PageHelper.startPage(pageNum, pageSize, "createTime desc");
        List<EnterHospitalRequisition> enterHospitalRequisitions = enterHospitalRequisitionMapper.selectByExample(weekend);
        PageInfo page = new PageInfo<>(enterHospitalRequisitions);
        pageInfo.put("totalCount", page.getTotal());
        return enterHospitalRequisitions;
    }

    @Override
    @DatabaseAnnotation
    public List<EnterHospitalRequisition> getEnterHospitalRequisitionByPatId(Integer patId) {
        if (patId == null) {
            return null;
        }
        Weekend<EnterHospitalRequisition> weekend = new Weekend<>(EnterHospitalRequisition.class);
        weekend.weekendCriteria().andEqualTo(EnterHospitalRequisition::getPatientId, patId).andEqualTo(EnterHospitalRequisition::getStatus, EnterHospitalRequisitionEnum.RECEIVED.getCode());
        List<EnterHospitalRequisition> list = enterHospitalRequisitionMapper.selectByExample(weekend);
        return list;
    }

    @Override
    @DatabaseAnnotation
    public boolean cancelApply(Integer id) {
        EnterHospitalRequisition updateEnterHospitalRequisition = new EnterHospitalRequisition();
        updateEnterHospitalRequisition.setId(id);
        updateEnterHospitalRequisition.setSubmitStatus(0);
        updateEnterHospitalRequisition.setStatus(EnterHospitalRequisitionEnum.SAVE.getCode());
        return enterHospitalRequisitionMapper.updateByPrimaryKeySelective(updateEnterHospitalRequisition) > 0;
    }

    @Override
    @DatabaseAnnotation
    public EnterHospitalRequisition getApplyById(Integer id) {
        return enterHospitalRequisitionMapper.selectByPrimaryKey(id);
    }

    @Override
    @DatabaseAnnotation
    public boolean noticeStatus(Long receptionNo, Integer patId) {
        Weekend<EnterHospitalRequisition> weekend = new Weekend<>(EnterHospitalRequisition.class);
        weekend.weekendCriteria().andEqualTo(EnterHospitalRequisition::getReceptionNo, receptionNo).andEqualTo(EnterHospitalRequisition::getPatientId, patId);
        EnterHospitalRequisition updateEnterHospitalRequisition = new EnterHospitalRequisition();
        updateEnterHospitalRequisition.setNoticeStatus(true);
        return enterHospitalRequisitionMapper.updateByExampleSelective(updateEnterHospitalRequisition, weekend) > 0;
    }

    @Override
    public boolean riskStatus(Long receptionNo) {
        Weekend<EnterHospitalRequisition> weekend = new Weekend<>(EnterHospitalRequisition.class);
        weekend.weekendCriteria().andEqualTo(EnterHospitalRequisition::getReceptionNo, receptionNo);
        EnterHospitalRequisition updateEnterHospitalRequisition = new EnterHospitalRequisition();
        updateEnterHospitalRequisition.setRiskStatus(true);
        return enterHospitalRequisitionMapper.updateByExampleSelective(updateEnterHospitalRequisition, weekend) > 0;
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public boolean signStatus(String receptionNo, Boolean status) {
        return enterHospitalRequisitionMapper.updateByRecipeNo(receptionNo, status) ;
    }

    @Override
    public HashMap getSqhInfo(Integer id) {
        return enterHospitalRequisitionMapper.getSqhInfo(id);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public HashMap getpatPhone(Long patID) {
        return enterHospitalRequisitionMapper.getpatPhone(patID);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public boolean updateRiskReportStatusByRecipeNo(String receptionNo, Boolean riskStatus, Boolean riskSignStatus) {
        return enterHospitalRequisitionMapper.updateRiskReportStatusByRecipeNo(receptionNo,riskStatus,riskSignStatus);
    }


}
