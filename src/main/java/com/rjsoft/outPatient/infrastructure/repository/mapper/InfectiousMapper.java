package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Agent;
import com.rjsoft.outPatient.infrastructure.repository.entity.Infectious;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;
import java.util.Set;

public interface InfectiousMapper extends Mapper<Infectious> {

    /**
     * 根据传染病列表获取传染病
     * @param diagnoseCode
     */
    default List<Infectious>  getInfectiousList(String diagnoseCode){
        Weekend<Infectious> weekend = new Weekend<>(Infectious.class);
        WeekendCriteria<Infectious, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(Infectious::getZdCode,diagnoseCode);
        return selectByExample(weekend);
    }

}
