package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.EnterHospitalRequisition;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzZySqdContent;
import com.rjsoft.outPatient.infrastructure.repository.entity.SQDTcZb;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 住院申请单
 *
 * <AUTHOR>
 * @since 2021/7/19 - 14:17
 */
public interface EnterHospitalRequisitionRepository {

    int getSqdMaxId();

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    Integer save(EnterHospitalRequisition entity);

    Integer submitApply(EnterHospitalRequisition entity);

    Integer saveSqdContent(MzZySqdContent zySqdContent);

    Integer updateContent(MzZySqdContent zySqdContent);

    /**
     * 修改
     *
     * @param entity
     * @return
     */
    Integer update(EnterHospitalRequisition entity);

    /**
     * 查询
     *
     * @param receptionNo
     * @return
     */
    EnterHospitalRequisition getEnterHospitalRequisition(String receptionNo);

    List<EnterHospitalRequisition> getEnterHospitalRequisitionList(String receptionNo);

    /**
     * 根据就诊集合加载所有住院申请单
     *
     * @param receptionNos
     * @return
     */
    List<EnterHospitalRequisition> getEnterHospitalRequisitionByIds(List<Long> receptionNos);

    /**
     * 导入报告
     *
     * @param patName     患者姓名
     * @param hisCardNo   病历卡号
     * @param receptionNo 就诊流水号
     * @return String
     */
    String importReport(String patName, String hisCardNo, String receptionNo);

    /**
     * 根据就诊流水号查询申请单信息
     *
     * @param jzlsh
     * @param hospitalCode
     * @return
     */
    List<EnterHospitalRequisition> getSqdByJzlsh(Long jzlsh, Integer hospitalCode);

    /**
     * 根据申请单id,医院编码查询申请单内容
     *
     * @param sqdId
     * @param hospitalCode
     * @return
     */
    MzZySqdContent getSqdContent(Integer sqdId, Integer hospitalCode);

    /**
     * 查询配置套餐信息
     *
     * @return
     */
    List<SQDTcZb> getSqdTc(Integer hospitalCode);


    /**
     * 查询风险评估单完成状态
     *
     * @param id
     * @param hospitalCode
     * @return
     */
    Integer getFxpgStatus(Integer id, Integer hospitalCode);

    /**
     * 查询住院申请单列表
     *
     * @param startTime
     * @param endTime
     * @param status
     * @param pageInfo
     * @return
     */
    List<EnterHospitalRequisition> getInHospitalApply(Date startTime, Date endTime, Integer status, HashMap pageInfo,Integer signStatus);


    /**
     * 查询是否存在该患者是否有已提交的入院记录
     *
     * @param patId
     * @return
     */
    List<EnterHospitalRequisition> getEnterHospitalRequisitionByPatId(Integer patId);

    /**
     * 取消已申请的入院申请单
     *
     * @param id
     * @return
     */
    boolean cancelApply(Integer id);

    /**
     * 查询入院申请单
     *
     * @param id
     * @return
     */
    EnterHospitalRequisition getApplyById(Integer id);


    /**
     * 更新住院申请单告知单状态
     *
     * @param receptionNo
     * @param patId
     * @return
     */
    boolean noticeStatus(Long receptionNo,Integer patId);

    /**
     * 更新风险评估单告知单状态
     *
     * @param receptionNo
     * @return
     */
    boolean riskStatus(Long receptionNo);

    boolean signStatus(String receptionNo,Boolean status);

    HashMap getSqhInfo(Integer id);

    HashMap getpatPhone(Long patID);

    boolean updateRiskReportStatusByRecipeNo(String receptionNo,Boolean riskStatus,Boolean riskSignStatus);

}
