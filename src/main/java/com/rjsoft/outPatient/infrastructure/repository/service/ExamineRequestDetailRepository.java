package com.rjsoft.outPatient.infrastructure.repository.service;

/**
 * <AUTHOR>
 * @since 2021/9/8-5:34 下午
 */
public interface ExamineRequestDetailRepository {


    /**
     * 根据参数删除 检查申请明细
     *
     * @param examineNo      检查流水号
     * @param recipeDetailId 处方明细编码
     * @param receptionNo    就诊流水号
     */
    void deleteByExamineNoAndRecipeDetailIdAndReceptionNo(Integer examineNo, Long recipeDetailId, Long receptionNo);

}
