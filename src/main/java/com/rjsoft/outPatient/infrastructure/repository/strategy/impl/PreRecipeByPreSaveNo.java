package com.rjsoft.outPatient.infrastructure.repository.strategy.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.PreRecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.service.PreRecipeDetailRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeRepository;
import com.rjsoft.outPatient.infrastructure.repository.strategy.PreRecipeStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component("PreRecipeByPreSaveNo")
@AllArgsConstructor
public class PreRecipeByPreSaveNo implements PreRecipeStrategy {
    PreRecipeDetailRepository preRecipeDetailRepository;

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<PreRecipeDetail> getPreRecipeDetails(CheckRecipeDto checkDto, Map errInfo) {
        return preRecipeDetailRepository.getPreRecipeDetailsBySaveNo(checkDto.getPreSaveNo(), checkDto.getDoctorId(), checkDto.getHospitalCode());
    }

}
