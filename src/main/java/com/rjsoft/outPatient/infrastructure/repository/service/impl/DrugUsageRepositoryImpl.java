package com.rjsoft.outPatient.infrastructure.repository.service.impl;



import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugUsage;
import com.rjsoft.outPatient.infrastructure.repository.entity.PrvNameResult;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugUsageMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DrugUsageRepository;
import lombok.AllArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 药品给药途径
 * <AUTHOR>
@Service
@AllArgsConstructor
public class DrugUsageRepositoryImpl implements DrugUsageRepository {

    DrugUsageMapper drugUsageMapper;

    /**
     * 根据药品ID查询给药途径
     * @param drugId
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<DrugUsage> getDrugUsageById(Integer drugId, Integer hospitalCode) {
        return drugUsageMapper.getDrugUsageById(drugId, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<PrvNameResult> selectPrvName(String itemCode, Integer hospId) {
        return drugUsageMapper.selectPrvName(itemCode, hospId);
    }
}
