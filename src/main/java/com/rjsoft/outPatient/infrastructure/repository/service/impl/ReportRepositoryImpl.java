package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.domain.report.dto.*;
import com.rjsoft.outPatient.infrastructure.repository.entity.ReportXdt;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ReportMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ReportXdtMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ReportRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/9/11 10:53
 * @description
 **/
@Service
@AllArgsConstructor
public class ReportRepositoryImpl implements ReportRepository {

    ReportMapper reportMapper;
    ReportXdtMapper reportXdtMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public List<CrisisValueDto> getCrisisValue(String hisCardNo) {
        return reportMapper.getCrisisValue(hisCardNo);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.REPORTS)
    public RadiationReportDto getRadiationReport(String hisCardNo) {
        return reportMapper.getRadiationReport(hisCardNo);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public List<TestReportDetailDto> getTestReportDetail(String newSqdh, Integer mid, Date endTime, Integer hospitalCode) {
        String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.ZXHIS : DatasourceName.ZXHIS3;
        DataSourceSwitchAspect.changeDataSource(dataSourceName);
        return reportMapper.getTestReportDetail(newSqdh, mid, endTime);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public List<TestReportDto> getTestReportList(String patName, String patSfz, Date startTime, Date endTime) {
        //总院化验报告
        DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        List<TestReportDto> generalReportList = reportMapper.getHyMaterListGeneral(patName, patSfz, startTime, endTime);
        for (TestReportDto t : generalReportList) {
            t.setHospitalCode(HospitalClassify.GENERAL.getHospitalCode());
        }
        List<TestReportDto> reportList = new ArrayList<>(generalReportList);
        //分院化验报告
        DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        List<TestReportDto> branchReportList = reportMapper.getHyMaterList(patName, patSfz, startTime, endTime);
        for (TestReportDto t : branchReportList) {
            t.setHospitalCode(HospitalClassify.BRANCH.getHospitalCode());
        }
        reportList.addAll(branchReportList);
        return reportList;
    }

    /**
     * 模糊搜索化验项目列表
     *
     * @param code
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public List<TestTypeResponse> getTestType(String code, Integer hospId) {
        code = "%" + code + "%";
        return reportMapper.getTestType(code, hospId);
    }

    /**
     * 根据化验列表匹配单价和单位
     *
     * @param testTypeResponses
     * @param hospId
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<TestItemResponse> getTestItemList(List<TestTypeResponse> testTypeResponses, Integer hospId) {
        return reportMapper.getTestItemList(testTypeResponses, hospId);
    }

    /**
     * 根据挂号流水号获取心电图报告
     *
     * @param regNo
     * @param cardNo
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.REPORTS)
    public List<ReportXdt> getXdtReport(Long regNo, String cardNo) {
        return reportXdtMapper.getXdtReport(regNo, cardNo);
    }

    /**
     * 根据报告id获取心电图详情
     *
     * @param id
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.REPORTS)
    public ReportXdt getXdtReportDetail(Long id) {
        return reportXdtMapper.getXdtReportDetail(id);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.REPORTS)
    public List<CheckReportDto> getCheckReportList(String hisCardNo, Date startTime) {
        return reportMapper.getCheckReportList(hisCardNo,startTime);
    }


}
