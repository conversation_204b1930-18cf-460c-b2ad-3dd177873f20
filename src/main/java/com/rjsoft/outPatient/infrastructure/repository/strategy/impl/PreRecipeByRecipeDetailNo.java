package com.rjsoft.outPatient.infrastructure.repository.strategy.impl;

import com.alibaba.fastjson.JSON;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.utils.ObjectUtils;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.item.vo.ItemExeDeptRespVO;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.domain.recipe.util.PreRecipeUtil;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.ChargeItemRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeRepository;
import com.rjsoft.outPatient.infrastructure.repository.strategy.PreRecipeStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.rjsoft.outPatient.common.enums.ApplyFormFlagEnum.NOT_APPLY_FORM;

@Component("PreRecipeByRecipeDetailNo")
@AllArgsConstructor
public class PreRecipeByRecipeDetailNo implements PreRecipeStrategy {
    RecipeRepository recipeRepository;
    PreRecipeDetailMapper preRecipeDetailMapper;
    RecipeDetailMapper recipeDetailMapper;

    DrugToHospitalMapper drugToHospitalMapper;
    OldRecipeDetailMapper oldRecipeDetailMapper;

    SysFunctionMapper sysFunctionMapper;

    ChargeItemRepository chargeItemRepository;

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<PreRecipeDetail> getPreRecipeDetails(CheckRecipeDto checkDto, Map errInfo) {
        return getPreRecipeDetailsByRecipeDetailNo(checkDto.getOperatingId(), checkDto.getPreSaveNo(),
                checkDto.getDoctorId(), checkDto.getHospitalCode(), checkDto.getRecipeDataSources(),
                checkDto.getRecipeHospitalCode(), errInfo);
    }

    /**
     * 获取处方预存明细(根据预存流水号)
     *
     * @param preDetailNo
     * @param doctorId
     * @param hospitalCode
     * @param recipeDataSources
     * @param recipeHospitalCode
     * @return
     */
    private List<PreRecipeDetail> getPreRecipeDetailsByRecipeDetailNo(String preDetailNo, Long preSaveNo, Integer doctorId,
                                                                     Integer hospitalCode, String recipeDataSources,
                                                                     Integer recipeHospitalCode, Map errInfo) {
        List<PreRecipeDetail> res = new ArrayList<>();

        if (preSaveNo != null) {
            PreRecipeDetail entity = new PreRecipeDetail();
            entity.setPreSaveNo(preSaveNo);
            entity.setHospitalCode(hospitalCode);
            List<PreRecipeDetail> preRecipeDetails = preRecipeDetailMapper.select(entity);
            for (PreRecipeDetail preRecipeDetail : preRecipeDetails) {
                preRecipeDetail.setOpFlag(1);
                preRecipeDetail.setOpCode(doctorId);
                res.add(preRecipeDetail);
            }
            return res;
        }

        PreRecipeDetail preRecipeDetail = null;
        if (ObjectUtils.isNumber(preDetailNo)) {
            RecipeDetail recipeDetail = recipeDetailMapper.getRecipeDetailByDetailNo(Converter.toInt64(preDetailNo), recipeHospitalCode);
            if (recipeDetail == null) {
                return res;
            }
            //医院不同，需要查询药品主索引表，替换药品ID
            if (!hospitalCode.equals(recipeHospitalCode)) {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
                List<DrugToHospital> drugToHospitalList = drugToHospitalMapper.getDrugToHospital(recipeDetail.getItemCode());
                if (drugToHospitalList.size() <= 0) {
                    return res;
                }
                List<DrugToHospital> drugToHospitalListHosp = drugToHospitalList.stream().filter(p -> p.getHospitalId().equals(hospitalCode)).collect(Collectors.toList());
                if (drugToHospitalListHosp.size() <= 0) {
                    return res;
                }
                recipeDetail.setItemCode(drugToHospitalListHosp.get(0).getDrugId());
            }

            preRecipeDetail = new PreRecipeDetail(recipeDetail);
            preRecipeDetail.setExeHospitalId(recipeDetail.getExeHospitalId());
        } else {
            String dataSource = recipeHospitalCode.equals(HospitalClassify.GENERAL.getHospitalCode()) ? DatasourceName.MZYS : DatasourceName.MZYS3;
            DataSourceSwitchAspect.changeDataSource(dataSource);
            OldRecipeDetail oldRecipeDetail = oldRecipeDetailMapper.getOldRecipeDetailById(preDetailNo, null);
            if (oldRecipeDetail == null) {
                oldRecipeDetail = oldRecipeDetailMapper.getOldRecipeDetailById(preDetailNo, "MZYS_TB_MZCFMX_DATA");
            }
            if (oldRecipeDetail == null) {
                return res;
            }

            //医院不同，需要查询药品主索引表，替换药品ID
            if (!hospitalCode.equals(recipeHospitalCode)) {
                DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
                List<DrugToHospital> drugToHospitalList = drugToHospitalMapper.getDrugToHospital(oldRecipeDetail.getItemCode());
                if (drugToHospitalList.size() <= 0) {
                    return res;
                }
                List<DrugToHospital> drugToHospitalListHosp = drugToHospitalList.stream().filter(p -> p.getHospitalId().equals(hospitalCode)).collect(Collectors.toList());
                if (drugToHospitalListHosp.size() <= 0) {
                    return res;
                }
                oldRecipeDetail.setItemCode(drugToHospitalListHosp.get(0).getDrugId());
            }

            preRecipeDetail = new PreRecipeDetail(oldRecipeDetail);
        }
        if (preRecipeDetail == null) {
            return res;
        }
        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        Long preSaveNoNew = sysFunctionMapper.getGetSequences(SequenceEnum.PRE_SAVE_NO);
        preRecipeDetail.setPreSaveNo(preSaveNoNew);
        Long recipeDetailNo = sysFunctionMapper.getGetSequences(SequenceEnum.RECIPE_DETAIL_NO);
        preRecipeDetail.setRecipeDetailNo(recipeDetailNo);
        preRecipeDetail.setRecipeNo(0L);
        preRecipeDetail.setOpFlag(0);
        preRecipeDetail.setOpTime(sysFunctionMapper.getDate());

        //药品直接获取当前配置的执行科室
        Integer feeCategory = preRecipeDetail.getFeeCategory();
        if (feeCategory!=null && ItemCategoryEnum.isDrug(feeCategory)) {
            setDefaultExeDept(preRecipeDetail, hospitalCode, preRecipeDetail.getItemCode(), feeCategory);
        }

        //ChargeItem chargeItem = chargeItemRepository.getChargeItemById(preRecipeDetail.getItemCode(), hospitalCode);
        ChargeItem chargeItem = chargeItemRepository.getChargeItemByIdIsStopped(preRecipeDetail.getItemCode(), preRecipeDetail.getExecDept(), hospitalCode, 0);
        if(chargeItem == null){
            Log.info(preRecipeDetail.getItemName() + "收费项目不存在或已停用");
            errInfo.put("errInfo", preRecipeDetail.getItemName() + "收费项目不存在或已停用");
            return res;
        }
        preRecipeDetail.ChangeChargeItemPart(chargeItem);

        recipeRepository.calcRecipeQty(preRecipeDetail, chargeItem);
        res.add(preRecipeDetail);
        return res;
    }

    private void setDefaultExeDept(PreRecipeDetail preRecipeDetail, Integer hospId, Integer itemCode, Integer feeCategory) {
        Log.info("开始设置默认执行科室: " + JSON.toJSONString(preRecipeDetail));

        List<ItemExeDeptRespVO> defaultExeDepts = recipeRepository.queryDefaultExeDept(itemCode.toString(), feeCategory.toString(), NOT_APPLY_FORM.getCode());
        PreRecipeUtil.fillDetailExeDeptAndExehospId(preRecipeDetail, defaultExeDepts, hospId);
    }


}
