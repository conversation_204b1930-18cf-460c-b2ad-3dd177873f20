<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.MzysTbCfscjlMapper">
  <resultMap id="BaseResultMap" type="com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbCfscjl">
    <!--@mbg.generated generated on Wed Jul 16 10:36:22 CST 2025.-->
    <!--@Table MZYS_TB_CFSCJL-->
    <result column="DeleteDate" jdbcType="TIMESTAMP" property="deletedate" />
    <result column="cfmxlsh" jdbcType="BIGINT" property="cfmxlsh" />
    <result column="cfmxid" jdbcType="INTEGER" property="cfmxid" />
    <result column="jzlsh" jdbcType="BIGINT" property="jzlsh" />
    <result column="cflsh" jdbcType="BIGINT" property="cflsh" />
    <result column="cfid" jdbcType="INTEGER" property="cfid" />
    <result column="ghlsh" jdbcType="BIGINT" property="ghlsh" />
    <result column="sflx" jdbcType="SMALLINT" property="sflx" />
    <result column="xmxh" jdbcType="SMALLINT" property="xmxh" />
    <result column="xmbm" jdbcType="INTEGER" property="xmbm" />
    <result column="xmmc" jdbcType="VARCHAR" property="xmmc" />
    <result column="sckfys" jdbcType="SMALLINT" property="sckfys" />
    <result column="sckfsj" jdbcType="TIMESTAMP" property="sckfsj" />
    <result column="zhkfys" jdbcType="SMALLINT" property="zhkfys" />
    <result column="zhkfsj" jdbcType="TIMESTAMP" property="zhkfsj" />
    <result column="jl" jdbcType="VARCHAR" property="jl" />
    <result column="jldw" jdbcType="CHAR" property="jldw" />
    <result column="yf" jdbcType="SMALLINT" property="yf" />
    <result column="cffs" jdbcType="SMALLINT" property="cffs" />
    <result column="xmsl" jdbcType="INTEGER" property="xmsl" />
    <result column="dw" jdbcType="CHAR" property="dw" />
    <result column="gg" jdbcType="VARCHAR" property="gg" />
    <result column="gytj" jdbcType="SMALLINT" property="gytj" />
    <result column="ts" jdbcType="SMALLINT" property="ts" />
    <result column="zxks" jdbcType="SMALLINT" property="zxks" />
    <result column="sysj" jdbcType="TIMESTAMP" property="sysj" />
    <result column="yszt" jdbcType="VARCHAR" property="yszt" />
    <result column="dj" jdbcType="FLOAT" property="dj" />
    <result column="cfje" jdbcType="FLOAT" property="cfje" />
    <result column="mzssbm" jdbcType="INTEGER" property="mzssbm" />
    <result column="mzssbw" jdbcType="VARCHAR" property="mzssbw" />
    <result column="jcbwbh" jdbcType="INTEGER" property="jcbwbh" />
    <result column="zt" jdbcType="SMALLINT" property="zt" />
    <result column="mzdwsl" jdbcType="SMALLINT" property="mzdwsl" />
    <result column="jcdw" jdbcType="CHAR" property="jcdw" />
    <result column="dwbm" jdbcType="SMALLINT" property="dwbm" />
    <result column="tsyf" jdbcType="VARCHAR" property="tsyf" />
    <result column="zh" jdbcType="INTEGER" property="zh" />
    <result column="hybb" jdbcType="VARCHAR" property="hybb" />
    <result column="zkl" jdbcType="INTEGER" property="zkl" />
    <result column="hfbj" jdbcType="INTEGER" property="hfbj" />
    <result column="tcbj" jdbcType="INTEGER" property="tcbj" />
    <result column="tcmxlsh" jdbcType="BIGINT" property="tcmxlsh" />
    <result column="sqd" jdbcType="VARCHAR" property="sqd" />
    <result column="jclsh" jdbcType="VARCHAR" property="jclsh" />
    <result column="tcid" jdbcType="BIGINT" property="tcid" />
    <result column="cfzt" jdbcType="VARCHAR" property="cfzt" />
    <result column="pytjbm" jdbcType="INTEGER" property="pytjbm" />
    <result column="flmc" jdbcType="VARCHAR" property="flmc" />
    <result column="Exa" jdbcType="INTEGER" property="exa" />
    <result column="syzt" jdbcType="INTEGER" property="syzt" />
    <result column="hospitalCode" jdbcType="INTEGER" property="hospitalcode" />
    <result column="preSaveNo" jdbcType="BIGINT" property="presaveno" />
    <result column="cflx" jdbcType="INTEGER" property="cflx" />
    <result column="jcmxlsh" jdbcType="INTEGER" property="jcmxlsh" />
    <result column="bz2" jdbcType="VARCHAR" property="bz2" />
    <result column="jcmxxmlsh" jdbcType="INTEGER" property="jcmxxmlsh" />
    <result column="sqdbj" jdbcType="INTEGER" property="sqdbj" />
    <result column="sqdzh" jdbcType="INTEGER" property="sqdzh" />
    <result column="isOtherHospital" jdbcType="INTEGER" property="isotherhospital" />
    <result column="itemHospitalCode" jdbcType="INTEGER" property="itemhospitalcode" />
    <result column="exeHospitalId" jdbcType="INTEGER" property="exehospitalid" />
    <result column="compatibleType" jdbcType="VARCHAR" property="compatibletype" />
    <result column="GlobalId" jdbcType="INTEGER" property="globalid" />
    <result column="SkinTestRecpId" jdbcType="BIGINT" property="skintestrecpid" />
    <result column="SkinTestId" jdbcType="BIGINT" property="skintestid" />
    <result column="SkinTestType" jdbcType="VARCHAR" property="skintesttype" />
    <result column="sqdId" jdbcType="VARCHAR" property="sqdid" />
    <result column="DeleteBy" jdbcType="INTEGER" property="deleteby" />
    <result column="DeleteSource" jdbcType="INTEGER" property="deletesource" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated generated on Wed Jul 16 10:36:22 CST 2025.-->
    DeleteDate, cfmxlsh, cfmxid, jzlsh, cflsh, cfid, ghlsh, sflx, xmxh, xmbm, xmmc, sckfys, 
    sckfsj, zhkfys, zhkfsj, jl, jldw, yf, cffs, xmsl, dw, gg, gytj, ts, zxks, sysj, yszt, 
    dj, cfje, mzssbm, mzssbw, jcbwbh, zt, mzdwsl, jcdw, dwbm, tsyf, zh, hybb, zkl, hfbj, 
    tcbj, tcmxlsh, sqd, jclsh, tcid, cfzt, pytjbm, flmc, Exa, syzt, hospitalCode, preSaveNo, 
    cflx, jcmxlsh, bz2, jcmxxmlsh, sqdbj, sqdzh, isOtherHospital, itemHospitalCode, exeHospitalId, 
    compatibleType, GlobalId, SkinTestRecpId, SkinTestId, SkinTestType, sqdId, DeleteBy, 
    DeleteSource
  </sql>
</mapper>