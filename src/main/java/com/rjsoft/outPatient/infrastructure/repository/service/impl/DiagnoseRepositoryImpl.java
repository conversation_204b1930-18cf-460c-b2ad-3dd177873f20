package com.rjsoft.outPatient.infrastructure.repository.service.impl;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rjsoft.common.SystemNo;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.param.SearchParam;
import com.rjsoft.outPatient.common.SystemConfigUtils;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.consts.SysConfigKey;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.common.enums.YesOrNoEnum;
import com.rjsoft.outPatient.config.HisConfig;
import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.HistoryDiagnoseResult;
import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.InPatientResult;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatDianosisDTO;
import com.rjsoft.outPatient.domain.diagnose.dto.DiagnoseCodeName;
import com.rjsoft.outPatient.domain.diseaseReport.Enum.ReportStatus;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiseaseReportResponse;
import com.rjsoft.outPatient.domain.diseaseReport.dto.ReportStatusEnum;
import com.rjsoft.outPatient.domain.prescriptionAudit.vo.AuditDiagnose;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.DiagnoseRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.*;

/**
 * 诊断信息
 */
@Service
@AllArgsConstructor
public class DiagnoseRepositoryImpl implements DiagnoseRepository {

    DiagnosisProofMapper diagnosisProofMapper;
    DiagnosisProofLogMapper diagnosisProofLogMapper;
    DiagnoseMapper diagnoseMapper;
    SysFunctionMapper sysFunctionMapper;
    DiagnoseTypeDicMapper diagnoseTypeDicMapper;
    ChiefComplaintMapper chiefComplaintMapper;
    SpecialInfoMapper specialInfoMapper;
    SpecialInfoOldMapper specialInfoOldMapper;
    SpecialInfoMzysMapper specialInfoMzysMapper;
    ChiefComplaintConfigMapper chiefComplaintConfigMapper;
    SixClassMapper sixClassMapper;
    PsyNewPatientMapper psyNewPatientMapper;
    ClinicNewPatientMapper clinicNewPatientMapper;
    DiseaseReportCardMapper diseaseReportCardMapper;
    OldDiagnoseMapper oldDiagnoseMapper;
    OldDiagnoseViewMapper oldDiagnoseViewMapper;
    InfectiousMapper infectiousMapper;
    DoctorDiseaseReportMapper doctorDiseaseReportMapper;
    SystemConfigUtils systemConfigUtils;
    DiagnoseUploadTypeMapper diagnoseUploadTypeMapper;
    HisConfig hisConfig;

    @Override
    @DatabaseAnnotation
    public List<Diagnose> getDiagnoseList(Integer receptionNo, Integer hospitalCode, String table) {
        return diagnoseMapper.getDiagnoseList(receptionNo, hospitalCode, table);
    }

    @Override
    @DatabaseAnnotation
    public List<Diagnose> getDiagnoseListByReceptionNo(Integer receptionNo) {
        return diagnoseMapper.getDiagnoseListByReceptionNo(receptionNo);
    }

    @Override
    @DatabaseAnnotation
    public List<Diagnose> getDiagnoseList(Set<Long> receptionNo, Integer hospitalCode, String table) {
        return diagnoseMapper.getDiagnoseList(receptionNo, hospitalCode, table);
    }

    @Override
    @DatabaseAnnotation
    public Diagnose getDiagnose(Integer id, Integer hospitalCode) {
        final Diagnose entity = new Diagnose();
        entity.setId(id);
        entity.setHospitalCode(hospitalCode);
        return diagnoseMapper.selectByPrimaryKey(entity);
    }

    @Override
    @DatabaseAnnotation
    public List<Diagnose> getDiagnoseListByDiagnoseCode(Integer receptionNo, String diagnoseCode, Integer hospitalCode) {
        final Diagnose entity = new Diagnose();
        entity.setDiagnoseCode(diagnoseCode);
        entity.setHospitalCode(hospitalCode);
        entity.setReceptionNo(receptionNo);
        return diagnoseMapper.select(entity);
    }

    @Override
    @DatabaseAnnotation
    public List<Diagnose> getDiagnoseListByExample(Example example) {
        return diagnoseMapper.selectByExample(example);
    }


    @Override
    @DatabaseAnnotation
    public boolean saveDiagnose(Diagnose diagnose) {
        boolean success;
        boolean hasData = diagnoseMapper.existsWithPrimaryKey(diagnose);
        if (hasData) {
            diagnose.setUptDoctor(diagnose.getDoctorId());
            diagnose.setUptTime(sysFunctionMapper.getDate());
            success = diagnoseMapper.updateByPrimaryKeySelective(diagnose) > 0;
        } else {
            diagnose.setCreDoctor(diagnose.getDoctorId());
            diagnose.setCreTime(sysFunctionMapper.getDate());
            diagnose.setReportedState(0);
            diagnose.setDiagnoseMark(0);
            diagnose.setDiagnoseState(2);
            success = diagnoseMapper.insert(diagnose) > 0;
        }
        return success;
    }

    @Override
    @DatabaseAnnotation
    public void updateDiagnoseReported(Integer id, Integer status) {
        Diagnose entity = new Diagnose();
        entity.setId(id);
        entity.setReportedState(status);
        diagnoseMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    @DatabaseAnnotation
    public void updateDiagnoseReportedByCode(Integer receptionNo, String diagnoseCode, Integer status) {
        Diagnose entity = new Diagnose();
        entity.setReceptionNo(receptionNo);
        entity.setDiagnoseCode(diagnoseCode);
        List<Diagnose> list = diagnoseMapper.select(entity);
        for (Diagnose diagnose : list) {
            diagnose.setReportedState(status);
            diagnoseMapper.updateByPrimaryKeySelective(diagnose);
        }
    }

    @Override
    @DatabaseAnnotation
    public boolean delDiagnose(Integer id, Integer doctorId, Integer hospitalCode) {
        Diagnose diagnose = new Diagnose();
        diagnose.setId(id);
        diagnose.setHospitalCode(hospitalCode);
        diagnoseMapper.delete(diagnose);
        return true;
    }


    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<DiagnoseTypeDic> getDiagnoseListByType(SearchParam param) {
        Integer diagnoseType = Converter.toInt32(param.getKeys().get("diagnoseType"));
        String inputCode = Converter.toString(param.getKeys().get("inputCode"));
        Integer hospitalCode = Converter.toInt32(param.getHospitalCode());
        String sortField = "indes desc ";
        String inputType = systemConfigUtils.getSystemConfigByStr(SystemNo.OUTPATIENT, SysConfigKey.INPUT_SEACH_TYPE, hospitalCode);

        //西医诊断
        //if (DiagnoseTypeEnum.WESTERNMEDICINE.getCode().equals(diagnoseType)) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize(), sortField);
        List<DiagnoseTypeDic> list = diagnoseTypeDicMapper.getDiagnoseListByType(inputCode, inputType);
        PageInfo<DiagnoseTypeDic> pageInfo = new PageInfo<>(list);
        param.setTotalCount(Converter.toInt32(pageInfo.getTotal()));
        return list;
        //} else {
        //return null;
        //}
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public DiagnoseTypeDic getDiagnoseListByCode(String code, Integer hospitalCode) {
        final Weekend<DiagnoseTypeDic> weekend = new Weekend<>(DiagnoseTypeDic.class);
        weekend.weekendCriteria().andEqualTo(DiagnoseTypeDic::getDiagnoseCode, code)
                .andEqualTo(DiagnoseTypeDic::getIsStop, YesOrNoEnum.NO.isBoolValue())
                .andIn(DiagnoseTypeDic::getUseRange, Arrays.asList(0, 1, 2));
        return diagnoseTypeDicMapper.selectOneByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public boolean checkReviewDiagnose(Integer id, Integer receptionNo, Integer doctorId, Integer hospitalCode) {
        boolean success;
        Diagnose diagnose = new Diagnose();
        diagnose.setId(id);
        diagnose.setReceptionNo(receptionNo);
        diagnose.setHospitalCode(hospitalCode);
        boolean hasData = diagnoseMapper.existsWithPrimaryKey(diagnose);
        if (hasData) {
            diagnose.setIsReview(1);
            diagnose.setReviewDoctor(doctorId);
            success = diagnoseMapper.updateByPrimaryKeySelective(diagnose) > 0;
        } else {
            return false;
        }
        return success;
    }


    @Override
    @DatabaseAnnotation
    public List<ChiefComplaint> getPatChiefComplaint(Integer patId, Integer receptionNo, Integer hospitalCode) {
        return chiefComplaintMapper.getPatChiefComplaint(patId, receptionNo, hospitalCode);
    }


    @Override
    @DatabaseAnnotation
    public boolean savePatChiefComplaint(ChiefComplaint chiefComplaint) {
        boolean success;
        boolean hasData = chiefComplaintMapper.existsWithPrimaryKey(chiefComplaint);
        if (hasData) {
            chiefComplaint.setUptDoctor(chiefComplaint.getDoctorId());
            chiefComplaint.setUptTime(sysFunctionMapper.getDate());
            success = chiefComplaintMapper.updateByPrimaryKeySelective(chiefComplaint) > 0;
        } else {
            chiefComplaint.setCreDoctor(chiefComplaint.getDoctorId());
            chiefComplaint.setCreTime(sysFunctionMapper.getDate());
            chiefComplaint.setIsDelete(0);
            success = chiefComplaintMapper.insert(chiefComplaint) > 0;
        }
        return success;
    }


    @Override
    @DatabaseAnnotation
    public boolean delPatChiefComplaint(Integer id, Integer doctorId, Integer hospitalCode) {
        ChiefComplaint chiefComplaint = new ChiefComplaint();
        chiefComplaint.setId(id);
        chiefComplaint.setHospitalCode(hospitalCode);
        chiefComplaintMapper.delete(chiefComplaint);
        return true;
    }


    @Override
    @DatabaseAnnotation
    public List<ChiefComplaintConfig> getPatChiefComplaintConfig(Integer type, Integer hospitalCode) {
        return chiefComplaintConfigMapper.getPatChiefComplaintConfig(type, hospitalCode);
    }


    @Override
    @DatabaseAnnotation
    public SpecialInfo getPatientSpecialInfoOne(Integer id, Integer hospitalCod) {
        return specialInfoMapper.getPatientSpecialInfoOne(id, hospitalCod);
    }


    @Override
    @DatabaseAnnotation
    public List<SpecialInfo> getPatientSpecialInfo(Integer patId, Integer isRecent, Integer hospitalCode) {
        Weekend<SpecialInfo> weekend = new Weekend<>(SpecialInfo.class);
        WeekendCriteria<SpecialInfo, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andEqualTo(SpecialInfo::getPatId, patId);
        keywordCriteria.andEqualTo(SpecialInfo::getIsDelete, 0);
        keywordCriteria.andEqualTo(SpecialInfo::getHospitalCode, hospitalCode);
        weekend.setOrderByClause(" cjrq desc ");
        return specialInfoMapper.selectByExample(weekend);
    }

    @Override
    public List<SpecialInfoMzys> getPatientSpecialInfoMzys(List<String> lhospno, Integer isRecent, Integer hospitalCode) {
        Weekend<SpecialInfoMzys> weekend = new Weekend<>(SpecialInfoMzys.class);
        WeekendCriteria<SpecialInfoMzys, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andEqualTo(SpecialInfoMzys::getIsDelete, 0);
        keywordCriteria.andIsNotNull(SpecialInfoMzys::getCreTime);
        keywordCriteria.andIn(SpecialInfoMzys::getHisCardNo, lhospno);
        weekend.setOrderByClause(" cjrq desc ");
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }
        return specialInfoMzysMapper.selectByExample(weekend);
    }


    @Override
    @DatabaseAnnotation(name = "MZEMR")
    public List<SpecialInfoOld> getPatientSpecialInfoOld(String hisCardNo) {
        Weekend<SpecialInfoOld> weekendOld = new Weekend<>(SpecialInfoOld.class);
        WeekendCriteria<SpecialInfoOld, Object> keywordCriteriaold = weekendOld.weekendCriteria();
        keywordCriteriaold.andEqualTo(SpecialInfoOld::getHisCardNo, hisCardNo);
        keywordCriteriaold.andEqualTo(SpecialInfoOld::getIsDelete, 0);
        weekendOld.setOrderByClause(" OpDate desc ");
        return specialInfoOldMapper.selectByExample(weekendOld);
    }


    @Override
    @DatabaseAnnotation
    public boolean savePatientSpecialInfo(SpecialInfo specialInfo) {
        boolean success;
        boolean hasData = specialInfoMapper.existsWithPrimaryKey(specialInfo);
        if (hasData) {
            specialInfo.setInsertTime(sysFunctionMapper.getDate());
            success = specialInfoMapper.updateByPrimaryKeySelective(specialInfo) > 0;
        } else {
            specialInfo.setCreDoctor(specialInfo.getDoctorId());
            specialInfo.setCreTime(sysFunctionMapper.getDate());
            specialInfo.setInsertTime(sysFunctionMapper.getDate());
            specialInfo.setIsDelete(0);
            success = specialInfoMapper.insert(specialInfo) > 0;
        }
        return success;
    }


    @Override
    @DatabaseAnnotation
    public boolean delPatientSpecialInfo(Integer id, Integer doctorId, Integer hospitalCode) {
        SpecialInfo specialInfo = new SpecialInfo();
        specialInfo.setId(id);
        specialInfo.setIsDelete(1);
        specialInfo.setDelDoctor(doctorId);
        specialInfo.setDeleteTime(sysFunctionMapper.getDate());
        specialInfo.setHospitalCode(hospitalCode);
        return specialInfoMapper.updateByPrimaryKeySelective(specialInfo) > 0;
    }

    @Override
    @DatabaseAnnotation
    public List<PatDianosisDTO> getPatDianosis(Set<Long> receptionNoSet, Integer hospitalCode) {
        return diagnoseMapper.getPatDianosis(receptionNoSet, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public Diagnose getDiagnoseByDiagnoseNo(Integer diagnoseNo, Integer hospCode) {
        return diagnoseMapper.getDiagnoseBydiagnoseNo(diagnoseNo, hospCode);
    }

    /**
     * 根据就诊流水号清除本次就诊诊断
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    @Override
    public boolean delDiagnoseByReceptionNo(Long receptionNo, Integer hospitalCode) {
        if (receptionNo == null || hospitalCode == null) {
            return true;
        }
        Diagnose entity = new Diagnose();
        entity.setReceptionNo(Converter.toInt32(receptionNo));
        entity.setHospitalCode(hospitalCode);
        diagnoseMapper.delete(entity);
        return true;
    }

    @Override
    public SixClassCode getSixClass(String diagnoseCode, Integer hospCode) {
        if (hospCode == 1) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZYEMR);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZYEMR3);
        }
        return sixClassMapper.getSixClass(diagnoseCode);
    }

    /**
     * 根据诊断编码获取诊断名称
     *
     * @param diagnoseCodes
     * @param hospitalCode
     */
    @Override
    public List<SixClassCode> getSixClassByCodes(List<String> diagnoseCodes, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZYEMR);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZYEMR3);
        }
        return sixClassMapper.getSixClassByCodes(diagnoseCodes);
    }

    @Override
    public boolean judgePsyNewPatient(String idNo, String sbname, Integer hospCode) {
        if (hisConfig.getIsHistoryVersion().equals("true")) {
            return false;
        }
        if (hospCode == 1) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZYEMR);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZYEMR3);
        }
        return psyNewPatientMapper.getInfoByIdNoSbname(idNo, sbname) > 0;
    }


    @Override
    @DatabaseAnnotation
    public boolean judgeClinicNewPatient(String idNo, String sbName, String receptionNo, Integer hospCode) {
        return clinicNewPatientMapper.getInfoByIdNoSbname(idNo, sbName, receptionNo, hospCode) > 0;
    }

    @Override
    @DatabaseAnnotation
    public boolean judgeFinalizedClinicNewPatient(Integer reportDiseaseId, String idNo, String sbCode, Integer hospCode) {
        return clinicNewPatientMapper.getFinalizedInfoByIdNoSbname(reportDiseaseId, idNo, sbCode, hospCode) > 0;
    }


    @Override
    public boolean judgeDiseaseReportCard(String idNo, String sbname, Integer hospCode) {
        if (hisConfig.getIsHistoryVersion().equals("true")) {
            return false;
        }
        if (hospCode == 1) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZYEMR);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZYEMR3);
        }
        return diseaseReportCardMapper.getInfoByIdNoSbname(idNo, sbname) > 0;
    }

    @Override
    @DatabaseAnnotation
    public ClinicNewPatient getReportFromQpmzxbr(Integer reportDiseaseId, Integer receptionNo, String diagnoseCode, Integer hospCode) {
        if (reportDiseaseId == null) {
            Weekend<ClinicNewPatient> weekend = new Weekend<>(ClinicNewPatient.class);
            WeekendCriteria<ClinicNewPatient, Object> criteria = weekend.weekendCriteria();
            criteria.andEqualTo(ClinicNewPatient::getReceptionNo, String.valueOf(receptionNo))
                    .andEqualTo(ClinicNewPatient::getDiagnoseCode, diagnoseCode)
                    .andEqualTo(ClinicNewPatient::getHospitalCode, hospCode)
                    .andEqualTo(ClinicNewPatient::getReportType, "HIS");
            return clinicNewPatientMapper.selectOneByExample(weekend);
        } else {
            Weekend<ClinicNewPatient> weekend = new Weekend<>(ClinicNewPatient.class);
            WeekendCriteria<ClinicNewPatient, Object> criteria = weekend.weekendCriteria();
            criteria.andEqualTo(ClinicNewPatient::getReceptionNo, String.valueOf(receptionNo))
                    .andEqualTo(ClinicNewPatient::getDiagnoseCode, diagnoseCode)
                    .andEqualTo(ClinicNewPatient::getHospitalCode, hospCode)
                    .andEqualTo(ClinicNewPatient::getReportDiseaseId, reportDiseaseId);
            return clinicNewPatientMapper.selectOneByExample(weekend);
        }
    }


    @Override
    @DatabaseAnnotation
    public List<ClinicNewPatient> getReportFromQpmzxbrId(Integer id, Integer diagnoseNo, Integer receptionNo, String diagnoseCode, Integer hospCode) {
        ClinicNewPatient clinicNewPatient = new ClinicNewPatient();
        if (id != null) {
            clinicNewPatient.setId(id);
        }
        if (diagnoseNo != null) {
            clinicNewPatient.setDiagnoseNo(diagnoseNo);
        }
        clinicNewPatient.setReceptionNo(Converter.toString(receptionNo));
        clinicNewPatient.setDiagnoseCode(diagnoseCode);
        clinicNewPatient.setHospitalCode(hospCode);
        return clinicNewPatientMapper.select(clinicNewPatient);
    }


    @Override
    @DatabaseAnnotation
    public boolean addClinicNewPatient(ClinicNewPatient clinicNewPatient, Integer hospCode) {
        clinicNewPatient.setHospitalCode(hospCode);
        return clinicNewPatientMapper.insertSelective(clinicNewPatient) > 0;
    }

    @Override
    @DatabaseAnnotation
    public boolean updateClinicNewPatient(ClinicNewPatient clinicNewPatient, Integer hospCode) {
        Weekend<ClinicNewPatient> weekend = new Weekend<>(ClinicNewPatient.class);
        weekend.weekendCriteria().andEqualTo(ClinicNewPatient::getReceptionNo, clinicNewPatient.getReceptionNo())
                .andEqualTo(ClinicNewPatient::getDiagnoseCode, clinicNewPatient.getDiagnoseCode());
        return clinicNewPatientMapper.updateByExampleSelective(clinicNewPatient, weekend) > 0;
    }

    @Override
    @DatabaseAnnotation
    public boolean alterStatusForQpmzxbr(Integer id, Integer receptionNo, String diagnoseCode, String operator, Integer opType, Integer hospCode) {
        ClinicNewPatient clinicNewPatient = new ClinicNewPatient();
        clinicNewPatient.setReceptionNo(String.valueOf(receptionNo));
        clinicNewPatient.setDiagnoseCode(diagnoseCode);
        clinicNewPatient.setUpdator(operator);
        clinicNewPatient.setUpdateTime(new Date());

        if (opType < 2) {
            //定稿
            if (opType == 0) {
                clinicNewPatient.setStatus(String.valueOf(ReportStatusEnum.FINALIZED.getCode()));
            }
            //撤销定稿
            else if (opType == 1) {
                clinicNewPatient.setStatus(String.valueOf(ReportStatusEnum.ADD.getCode()));
            }
            Weekend<ClinicNewPatient> weekend = new Weekend<>(ClinicNewPatient.class);
            weekend.weekendCriteria()
                    .andEqualTo(ClinicNewPatient::getId, id)
                    .andEqualTo(ClinicNewPatient::getReceptionNo, String.valueOf(receptionNo))
                    .andEqualTo(ClinicNewPatient::getDiagnoseCode, diagnoseCode)
                    .andEqualTo(ClinicNewPatient::getHospitalCode, hospCode)
                    .andEqualTo(ClinicNewPatient::getReportType, "HIS")
                    .andNotEqualTo(ClinicNewPatient::getStatus, ReportStatus.BACKED.getStatus());
            boolean canNext = clinicNewPatientMapper.updateByExampleSelective(clinicNewPatient, weekend) > 0;
            if (canNext) {
                Integer status = opType == 0 ? 2 : 1;
                DataSourceSwitchAspect.changeDataSource("");
                updateDiagnoseReportedByCode(receptionNo, diagnoseCode, status);
            }
            return canNext;
        }
        //其他操作
        else {
            if (opType == 2) {
                clinicNewPatient.setStatus(String.valueOf(ReportStatus.SAVED.getStatus()));
            } else if (opType == 3) {
                clinicNewPatient.setStatus(String.valueOf(ReportStatus.FINAL.getStatus()));
            } else if (opType == 4) {
                clinicNewPatient.setStatus(String.valueOf(ReportStatus.CHECKED.getStatus()));
            } else if (opType == 5) {
                clinicNewPatient.setStatus(String.valueOf(ReportStatus.UPLOADED.getStatus()));
            }
            Weekend<ClinicNewPatient> weekend = new Weekend<>(ClinicNewPatient.class);
            weekend.weekendCriteria()
                    .andEqualTo(ClinicNewPatient::getId, id)
                    .andEqualTo(ClinicNewPatient::getDiagnoseCode, diagnoseCode)
                    .andEqualTo(ClinicNewPatient::getHospitalCode, hospCode);
            boolean canNext = clinicNewPatientMapper.updateByExampleSelective(clinicNewPatient, weekend) > 0;
            return canNext;
        }
    }

    @Override
    @DatabaseAnnotation
    public List<AuditDiagnose> getDiagnoseByReceptionNo(String receptionNo, String hospitalCode) {
        return diagnoseMapper.getDiagnoseByReceptionNo(receptionNo, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public void delDiagnoseByDiagnoseNo(Long diagnoseNo, Integer hospCode) {
        Diagnose diagnose = new Diagnose();
        diagnose.setId(Math.toIntExact(diagnoseNo));
        diagnose.setHospitalCode(hospCode);
        diagnoseMapper.delete(diagnose);
    }

    @Override
    @DatabaseAnnotation
    public ClinicNewPatient getReportFromQpmzxbr2(String patIdNo, String sbName, String receptionNo, Integer hospCode) {
        return clinicNewPatientMapper.getInfoByIdNoSbname2(patIdNo, sbName, receptionNo, hospCode);
    }

    /**
     * 根据患者编号列表，获取最后一次经复核的诊断信息
     *
     * @param regNos
     */
    @Override
    @DatabaseAnnotation
    public List<Diagnose> getDiagnoseByRegNos(List<Long> regNos) {
        return diagnoseMapper.getDiagnose(regNos);
    }

    /**
     * 添加患者历史诊断进诊断表
     *
     * @param diagnoses
     */
    @Override
    @DatabaseAnnotation
    public void addHistoryDiagnose(List<Diagnose> diagnoses) {
        diagnoseMapper.addHistoryDiagnose(diagnoses);
    }

    /**
     * 根据诊断编码获取 诊断名称
     *
     * @param bms
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<DiagnoseTypeDic> getDiagnoseTypeName(List<String> bms) {
        return diagnoseTypeDicMapper.getDiagnoseTypeName(bms);
    }

    /**
     * 根据诊断编码获取 诊断名称 【总院】
     *
     * @param bms
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public List<DiagnoseCodeName> getDiagnoseTypeNameGeneral(List<String> bms) {
        return diagnoseTypeDicMapper.getOldDiagnoseTypeName(bms);
    }

    /**
     * 根据诊断编码获取 诊断名称 【分院】
     *
     * @param bms
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS3)
    public List<DiagnoseCodeName> getDiagnoseTypeNameBranch(List<String> bms) {
        return diagnoseTypeDicMapper.getOldDiagnoseTypeName(bms);
    }

    /**
     * 根据诊断编码获取 诊断名称
     *
     * @param bm
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public DiagnoseTypeDic getDiagnoseTypeName(String bm) {
        return diagnoseTypeDicMapper.getDiagnoseTypeName(bm);
    }


    @Override
    @DatabaseAnnotation
    public Diagnose getDiagnoseByJzlshAndZdbm(String receptionNo, String hospCode, String diagnoseCode) {
        return diagnoseMapper.getDiagnoseByJzlshAndZdbm(receptionNo, hospCode, diagnoseCode);
    }

    /**
     * 根据就诊流水号列表获取所有诊断信息
     *
     * @param regNos
     * @param hospitalId
     */
    @Override
    @DatabaseAnnotation
    public List<Diagnose> getDiagnosesByRegNos(Set<Long> regNos, Integer hospitalId) {
        return diagnoseMapper.getDiagnosesByRegNos(regNos, hospitalId);
    }

    /**
     * 查询主诉信息
     *
     * @param receptionNo
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public List<ChiefComplaint> getChiefComplaint(Integer receptionNo, Integer hospitalCode) {
        return chiefComplaintMapper.getChiefComplaint(receptionNo, hospitalCode);
    }

    /**
     * 根据就诊流水号获取诊断记录
     *
     * @param receptionNo 就诊流水号
     * @param table       表名称
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public List<OldDiagnose> getOldDiagnose(String receptionNo, String table) {
        return oldDiagnoseMapper.getOldDiagnose(receptionNo, table);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public List<OldDiagnoseView> getOldDiagnoseView(String receptionNo, String table) {
        return oldDiagnoseViewMapper.getOldDiagnose(receptionNo, table);
    }

    /**
     * 根据就诊流水号获取诊断记录
     *
     * @param receptionNo 就诊流水号
     * @param table       表名称
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS3)
    public List<OldDiagnose> getOldDiagnoseBranch(String receptionNo, String table) {
        return oldDiagnoseMapper.getOldDiagnose(receptionNo, table);
    }

    @Override
    public List<HistoryDiagnoseResult> getAllFromOldData(List<String> oldDataRegNoList, int hospitalCode) {
        String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.MZYS : DatasourceName.MZYS3;
        DataSourceSwitchAspect.changeDataSource(dataSourceName);
        if (oldDataRegNoList == null || oldDataRegNoList.isEmpty()) {
            return new ArrayList<>(16);
        }
        return oldDiagnoseMapper.getAllFromOldData(oldDataRegNoList);
    }

    /**
     * 根据传染病列表获取传染病
     *
     * @param diagnoseCode
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.ZYEMR)
    public List<Infectious> getInfectiousList(String diagnoseCode) {
        return infectiousMapper.getInfectiousList(diagnoseCode);
    }

    /**
     * 查询医生疾病上报情况
     *
     * @param startTime
     * @param endTime
     * @param certificateNo
     * @param status
     * @param hospitalCode
     * @param doctorId
     * @param uploadTypeId
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<DiseaseReportResponse> reportDisease(String startTime, String endTime, String certificateNo, Integer status, Integer hospitalCode, Integer doctorId, Integer uploadTypeId) {
        return doctorDiseaseReportMapper.getDoctorReportDisease(startTime, endTime, certificateNo, doctorId, status, hospitalCode, uploadTypeId);
    }

    /**
     * 根据就诊流水号和诊断编码获取疾病上报记录
     *
     * @param receptionNo
     * @param diagnoseCode
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public DoctorDiseaseReport getRecord(Integer id, String receptionNo, String diagnoseCode, Integer hospitalCode) {
        return doctorDiseaseReportMapper.getRecord(id, receptionNo, diagnoseCode);
    }

    /**
     * 根据就诊流水号和诊断编码获取疾病上报记录【定稿状态】
     *
     * @param receptionNo
     * @param diagnoseCode
     * @param hospitalCode
     */
    @Override
    public DoctorDiseaseReport getRecordConfirm(String receptionNo, String diagnoseCode, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZYEMR);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZYEMR3);
        }
        return doctorDiseaseReportMapper.getRecordConfirm(receptionNo, diagnoseCode);
    }

    /**
     * 根据就诊流水号，诊断编码获取诊断记录
     *
     * @param receptionNo
     * @param diagnoseCode
     * @param hospitalCode
     */
    @Override
    public List<DiagnoseRecordInfo> getDiagnose(String receptionNo, String diagnoseCode, Integer hospitalCode) {
        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        List<DiagnoseRecordInfo> newDiagnose = diagnoseMapper.getNewDiagnose(receptionNo, diagnoseCode);
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }
        newDiagnose.addAll(diagnoseMapper.getNewDiagnose(receptionNo, diagnoseCode));
        return newDiagnose;
    }

    /**
     * 疾病上报退回
     *
     * @param reception
     * @param diagnoseCode
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public int returnReport(Integer id, String reception, String diagnoseCode, Integer hospitalCode) {
        return doctorDiseaseReportMapper.returnReport(id, reception, diagnoseCode, hospitalCode);
    }

    /**
     * 疾病上报删除
     *
     * @param reception
     * @param diagnoseCode
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public void delReport(Integer id, String reception, String diagnoseCode, Integer hospitalCode) {
        doctorDiseaseReportMapper.delReport(id, reception, diagnoseCode, hospitalCode);

    }

    /**
     * 根据诊断主键id获取诊断信息
     *
     * @param diagnoseNos
     */
    @Override
    @DatabaseAnnotation
    public List<Diagnose> getDiagnoseByIds(List<Integer> diagnoseNos) {
        return diagnoseMapper.getDiagnoseByIds(diagnoseNos);
    }

    /**
     * 根据就诊流水号，诊断编码，医院编码，修改sbbj状态
     *
     * @param receptionNo
     * @param diagnoseCode
     * @param hospitalCode
     * @param reportedStatus
     */
    @Override
    @DatabaseAnnotation
    public void updateDiagnoseReportedStatus(Long receptionNo, String diagnoseCode, Integer hospitalCode, Integer reportedStatus) {
        diagnoseMapper.updateDiagnoseReportedStatus(receptionNo, diagnoseCode, hospitalCode, reportedStatus);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<DiagnoseUploadType> getDiagnoseUploadType(String diagnoseCode, Integer hospitalCode) {
        return diagnoseUploadTypeMapper.getDiagnoseUploadType(diagnoseCode, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<DiagnoseUploadType> getReportUploadType(Integer hospitalCode) {
        return diagnoseUploadTypeMapper.getReportUploadType(hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<DiagnoseUploadType> getDiagnoseList(Integer hospitalCode) {
        return diagnoseUploadTypeMapper.getReportUploadTypeDiagnose(hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<DiagnoseTypeDic> queryDiagnoseListByCodes(List<String> bms) {
        return diagnoseTypeDicMapper.queryDiagnoseListByCodes(bms);
    }

    /**
     * 根据挂号列表获取所有诊断信息
     *
     * @param regNos
     * @param hospitalId
     */
    @Override
    @DatabaseAnnotation
    public List<Diagnose> queryDiagListByRegNos(List<Long> regNos, Integer hospitalId) {
        return diagnoseMapper.queryDiagListByRegNos(regNos, hospitalId);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<InPatientResult> queryIoDiagListByPatId(List<Integer> patientIds, Integer hospitalId) {
        return diagnoseMapper.queryIoDiagListByPatId(patientIds, hospitalId);
    }

}
