<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.RegTbSpecialsourcerelationMapper">

    <select id="getRegTbSpecialsourcerelation" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegTbSpecialsourcerelation">
        select SpecialRegNo,
               SpecialSourceId,
               DispensingRegNo,
               DispensingSourceId,
               HospitalCode,
               CreateTime
        from Reg_Tb_SpecialSourceRelation
        where (
                HospitalCode = #{hospitalCode}
                and SpecialRegNo = #{regNo}
                and DispensingRegNo= (
                                        select r.RegNo
                                        from Reg_Tb_SpecialSourceRelation a,Reg_Tb_RegisterList_Time r
                                        where a.DispensingRegNo=r.<PERSON>o
                                          and a.SpecialRegNo = #{regNo}
                                          and r.Status=0
                                    )
            )
           or (
                HospitalCode = #{hospitalCode}
                and DispensingRegNo = #{regNo}
               )
    </select>

</mapper>