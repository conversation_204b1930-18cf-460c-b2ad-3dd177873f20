package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 患者预约日志表
 *
 * <AUTHOR>
@Data
@Table(name = "Reg_Appointment_Log")
@JsonIgnoreProperties(ignoreUnknown = true)
public class RegAppointmentLog {
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 卡号
     */
    @Column(name = "cardNo")
    private String cardNo;

    /**
     * 病历卡号
     */
    @Column(name = "outPatientNo")
    private String outPatientNo;

    /**
     * 患者姓名
     */
    @Column(name = "patName")
    private String patName;

    /**
     * 患者id
     */
    @Column(name = "patId")
    private Long patId;

    /**
     * 证件号
     */
    @Column(name = "certificate")
    private String certificate;

    /**
     * 医生id
     */
    @Column(name = "doctorId")
    private Integer doctorId;

    /**
     * 医生姓名
     */
    @Column(name = "doctorName")
    private String doctorName;

    /**
     * 科目id
     */
    @Column(name = "subjectId")
    private Integer subjectId;

    /**
     * 科目名称
     */
    @Column(name = "subjectName")
    private String subjectName;

    /**
     * 就诊类型
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 时间段
     */
    @Column(name = "timeSlot")
    private String timeSlot;

    /**
     * 挂号流水号
     */
    @Column(name = "regNo")
    private Long regNo;

    /**
     * 创建人
     */
    @Column(name = "createId")
    private Integer createId;

    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "createTime")
    private Date createTime;

    /**
     * 医院id
     */
    @Column(name = "hospitalId")
    private Integer hospitalId;
}
