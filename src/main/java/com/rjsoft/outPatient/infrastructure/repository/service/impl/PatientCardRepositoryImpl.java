package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.PatientCard;
import com.rjsoft.outPatient.infrastructure.repository.mapper.PatientCardMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.PatientCardRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Date 2022/12/19 17:24
 * @Version: v1.0
 */
@Service
public class PatientCardRepositoryImpl implements PatientCardRepository {

    @Autowired
    private PatientCardMapper patientCardMapper;

    private static final Integer PAT_FLAG_3 = 999;

    private static final Integer OTHER = 9;

    private static final Integer SELF_FEE = 10;

    private static final Integer NOT_STOP = 0;

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<PatientCard> getPatSciCardList(Integer patId, String hisCardNo) {
        return patientCardMapper.getPatSciCardList(patId, hisCardNo, PAT_FLAG_3, NOT_STOP);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public void savePatSciCard(PatientCard patientCard) {
        patientCard.setPatFlag(PAT_FLAG_3);
        patientCard.setCardType(OTHER);
        patientCard.setChargeType(SELF_FEE);
        patientCard.setStatus(NOT_STOP);
        patientCardMapper.insertSelective(patientCard);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public PatientCard getExitSciCard(PatientCard patientCard) {
        return patientCardMapper.selectOne(patientCard);
    }

    /**
     * 查询患者卡表信息
     *
     * @param patId
     * @param cardNo
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public PatientCard getPatientCard(Integer patId, String cardNo) {
        return patientCardMapper.getPatientCard(patId, cardNo);
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<PatientCard> queryPatSciCardListByCardNo(String cardNo) {
        PatientCard patientCard = new PatientCard();
        patientCard.setCardNo(cardNo);
        patientCard.setPatFlag(PAT_FLAG_3);
        return patientCardMapper.select(patientCard);
    }

}
