package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.json.JsonUtils;
import com.rjsoft.outPatient.domain.doctorElemMain.constant.HospitalIdEnum;
import com.rjsoft.outPatient.domain.doctorElemMain.constant.StatusEnum;
import com.rjsoft.outPatient.domain.doctorElemMain.dto.WorkerInfoDTO;
import com.rjsoft.outPatient.domain.doctorElemMain.dto.WorkerInfoQueryDTO;
import com.rjsoft.outPatient.domain.doctorElemMain.dto.WorkerInfoResponse;
import com.rjsoft.outPatient.infrastructure.repository.entity.WorkerContrast;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Date;
import java.util.List;

/**
 * 职工对照
 *
 * <AUTHOR>
public interface WorkerContrastMapper extends Mapper<WorkerContrast> {
    /**
     * 根据ID获取职工信息
     *
     * @param workerId
     * @param hospitalCode
     * @return
     */
    default List<WorkerContrast> getWorkerContrastById(Integer workerId, Integer hospitalCode) {
        WorkerContrast worker = new WorkerContrast();
        worker.setWorkerId(workerId);
        worker.setHospitalId(hospitalCode);
        return select(worker);
    }

    /**
     * 根据总分院入参，查询是否有医生工号的对照记录
     *
     * @param param
     */
     List<WorkerContrast> getWorkerContrast(WorkerInfoDTO param);

    /**
     * 添加职工对照信息
     *
     * @param param
     */
    default int addWorker(WorkerInfoDTO param) {
        WorkerContrast contrast = new WorkerContrast();
        contrast.setWorkerId(param.getWorkerId());
        contrast.setHospitalId(HospitalIdEnum.BRANCH.getCode());
        contrast.setToWorkerId(param.getToWorkerId());
        contrast.setToHospitalId(HospitalIdEnum.GENERAL.getCode());
        contrast.setDelete(StatusEnum.NO_DELETE.getCode());
        contrast.setStatus(0);
        contrast.setCreateTime(new Date());
        return insertSelective(contrast);
    }

    /**
     * 删除职工信息
     */
    default void delWorker(Integer id) {
        WorkerContrast contrast = new WorkerContrast();
        contrast.setId(id);
        contrast.setDelete(StatusEnum.DELETE.getCode());
        updateByPrimaryKeySelective(contrast);
    }

    /**
     * 查询总院医生工号信息
     */
    default int queryGeneralWorker(Integer workerId) {
        WorkerContrast contrast = new WorkerContrast();
        contrast.setToWorkerId(workerId);
        contrast.setDelete(StatusEnum.NO_DELETE.getCode());
        return selectCount(contrast);
    }

    /**
     * 查询分院医生工号信息
     */
    default int queryBranchWorker(Integer workerId) {
        WorkerContrast contrast = new WorkerContrast();
        contrast.setWorkerId(workerId);
        contrast.setDelete(StatusEnum.NO_DELETE.getCode());
        return selectCount(contrast);
    }

    /**
     * 根据主键Id查询实体信息
     */
    default WorkerContrast queryEntityById(Integer id) {
        return selectByPrimaryKey(id);
    }

    /**
     * 修改分院职工对照记录
     */
    default int updateWorkerInfo(Integer id,Integer workerId){
        WorkerContrast contrast = new WorkerContrast();
        contrast.setId(id);
        contrast.setWorkerId(workerId);
        return updateByPrimaryKeySelective(contrast);
    }

    /**
     * 查询总分院职工对照列表
     * @param param
     */
    List<WorkerInfoResponse> queryWorker(WorkerInfoQueryDTO param);



}
