package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.RegNo2Guid;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 挂号流水号对照
 * <AUTHOR>
public interface RegNo2GuidMapper extends BaseMapper<RegNo2Guid>, ExampleMapper<RegNo2Guid> {

    /**
     * 根据整型挂号流水号，加载GUID类型
     * @param regNos
     * @return
     */
    default List<RegNo2Guid> getRegNoGuid(List<Long> regNos){
        if(regNos==null||regNos.size()==0){
            return new ArrayList<>();
        }
        Weekend<RegNo2Guid> weekend=new Weekend(RegNo2Guid.class);
        weekend.weekendCriteria().andIn(RegNo2Guid::getRegNo,regNos);
        List<RegNo2Guid> list = selectByExample(weekend);
        return list;
    }
}
