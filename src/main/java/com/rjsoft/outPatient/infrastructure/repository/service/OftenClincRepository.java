package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.config.dto.DoctorDeptDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.OftenClincDoctor;

import java.util.HashMap;
import java.util.List;

/**
 * 常门诊
 *
 * <AUTHOR>
public interface OftenClincRepository {

    /**
     * 根据医生编码获取常门诊医生科室
     *
     * @param hospitalCode
     * @param doctorId
     * @return
     */
    List<OftenClincDoctor> getDoctorDeptById(Integer doctorId,Integer hospitalCode);

    /**
     * 根据医生id和医院编码获取医生信息
     * @param doctorId
     * @param hospitalCode
     */
    List<OftenClincDoctor> getOftenDoctorById(Integer doctorId,Integer hospitalCode);


    /**
     * 获取医生当天排班科室
     * @param hospitalCode
     * @param doctorId
     * @return
     */
    List<OftenClincDoctor> getDoctorDeptByScheduling(Integer hospitalCode, Integer doctorId);

    /**
     * 保存常门诊医生
     * @param entity
     * @return
     */
    boolean saveOftenClincDoctor(OftenClincDoctor entity);

    /**
     * 删除常门诊医生
     * @param doctorId
     * @param deptId
     * @param hospitalCode
     * @return
     */
    boolean delOftenClincDoctor(Integer doctorId,Integer deptId,Integer hospitalCode);

    /**
     * 获取常门诊医生去重数据
     */
    List<DoctorDeptDto> getDistinctDoctorAndDeptId(Integer hospitalCode);

    /**
     *  根据医生列表获取实体类
     */
    List<OftenClincDoctor> getInfoByDoctors(List<Integer> doctors,Integer hospitalCode);
}
