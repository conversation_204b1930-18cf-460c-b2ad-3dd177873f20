package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.common.enums.DrugRestrictionEnum;
import com.rjsoft.outPatient.domain.recipe.dto.DrugRestrictionDTO;

import java.util.List;
import java.util.Map;

/**
 * 药品限制
 *
 * <AUTHOR>
 * @since 2021/9/27-1:30 下午
 */
public interface DrugRestrictionRepository {


    /**
     * select id from mzys_tb_ypxzmx where xzlxbh=#{} and ypbh=#{}
     *
     * @param restrictionCode 限制类型编码
     * @param drugCode        药品编码
     * @param hospitalCode    医院编码
     * @return 药品限制表唯一标识符
     */
    Integer getDrugRestrictionId(Integer restrictionCode, Integer drugCode, Integer hospitalCode);

    Map<String, Integer> listAllDrugRestrictionIdToMap();

    Integer tyTyMdcGetDrugRestrictionId(Integer restrictionCode, Integer drugCode, Integer hospitalCode);

    /**
     * 根据参数统计数量
     *
     * @param drugCode            项目编码
     * @param hospitalCode        医院编码
     * @param drugRestrictionEnum 药品限制类型
     * @return record count
     */
    Integer getDrugRestrictionCount(Integer drugCode, Integer hospitalCode, DrugRestrictionEnum... drugRestrictionEnum);

    /**
     * 根据参数统计数量
     *
     * @param drugRestrictionEnum 药品限制类型
     * @param drugCode            项目编码
     * @param hospitalCode        医院编码
     * @return record count
     */
    Integer getDrugRestrictionCount(DrugRestrictionEnum drugRestrictionEnum, List<Integer> drugCode, Integer hospitalCode);


    List<DrugRestrictionDTO> countDrugRestriction(List<Integer> itemCodeList, Integer hospId, DrugRestrictionEnum... drugRestrictionEnum);

}
