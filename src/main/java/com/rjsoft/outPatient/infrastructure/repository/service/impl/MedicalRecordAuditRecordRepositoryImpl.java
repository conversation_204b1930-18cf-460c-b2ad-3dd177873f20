package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.MedicalRecordAuditRecord;
import com.rjsoft.outPatient.infrastructure.repository.entity.Recipe;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.MedicalRecordAuditRecordRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

@Service
@AllArgsConstructor
public class MedicalRecordAuditRecordRepositoryImpl implements MedicalRecordAuditRecordRepository {

    MedicalRecordAuditRecordMapper medicalRecordAuditRecordMapper;

    @Override
    @DatabaseAnnotation
    public List<MedicalRecordAuditRecord> getMedicalRecordAuditRecord(Integer checkId, Integer checkSort, Integer hospitalCode) {
        Weekend<MedicalRecordAuditRecord> weekend = new Weekend<>(MedicalRecordAuditRecord.class);
        weekend.weekendCriteria().andEqualTo(MedicalRecordAuditRecord::getCheckId, checkId)
                .andEqualTo(MedicalRecordAuditRecord::getCheckSort, checkSort)
                .andEqualTo(MedicalRecordAuditRecord::getHospitalCode, hospitalCode);
        weekend.orderBy("checkDate").desc();
        return medicalRecordAuditRecordMapper.selectByExample(weekend);
    }
}
