package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.recipe.dto.HlyyDoctor;
import com.rjsoft.outPatient.domain.reserve.dto.DoctorInfoResponse;
import com.rjsoft.outPatient.infrastructure.repository.entity.MdmRecipeWhitelist;
import com.rjsoft.outPatient.infrastructure.repository.entity.Worker;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

/**
 * 职工
 *
 * <AUTHOR>
public interface WorkerMapper extends Mapper<Worker> {

    /**
     * 根据ID获取职工信息
     *
     * @param workerId
     * @param hospitalCode
     * @return
     */
    default Worker getWorkerById(Integer workerId, Integer hospitalCode) {
        Worker worker = new Worker();
        worker.setWorkerId(workerId);
        worker.setHospitalId(hospitalCode);
        worker.setStatus(0);
        return selectOne(worker);
    }

    /**
     * 根据ID获取职工信息
     *
     * @param workerId
     * @param hospitalCode
     * @return
     */
    default Worker getWorkerByIdAll(Integer workerId, Integer hospitalCode) {
        Worker worker = new Worker();
        worker.setWorkerId(workerId);
        worker.setHospitalId(hospitalCode);
        return selectOne(worker);
    }


    /**
     * 根据医生Ids获取医生信息
     *
     * @param ids
     * @param hospId
     */
    List<DoctorInfoResponse> getDoctorInfo(@Param("list") List<Integer> ids, @Param("hospId") Integer hospId);

    /**
     * 根据医生id获取医生相关信息
     */
    HlyyDoctor getHlyyDoctor(@Param("doctorId") Integer doctorId, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 模糊搜索医生信息
     *
     * @param inputCode
     * @param hospitalCode
     */
    List<com.rjsoft.outPatient.domain.config.dto.DoctorInfoResponse> getDoctorInfos(@Param("inputCode") String inputCode,
                                                                                    @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据医生ids,hospitalCode，模糊查找医生信息
     *
     * @param inputCode
     * @param hospitalCode
     * @param list
     */
    List<com.rjsoft.outPatient.domain.config.dto.DoctorInfoResponse> getDoctorInfosByList(@Param("inputCode") String inputCode,
                                                                                          @Param("hospitalCode") Integer hospitalCode,
                                                                                          @Param("list") List<Integer> list);

    /**
     * 根据医生ID，从排班科目中加载医生的最高级别
     *
     * @param workerId
     * @param hospitalCode
     * @return
     */
    List<Integer> getDoctorLevelByWorkerId(@Param("workerId") Integer workerId, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 通过医生工号查询医生信息
     *
     * @param workerNo
     * @param hospitalCode
     */
    default List<Worker> getDoctorInfoByWorkerNo(String workerNo, Integer hospitalCode) {
        Weekend<Worker> weekend = new Weekend<>(Worker.class);
        weekend.selectProperties("workerNo", "name", "inputCode2");
        weekend.weekendCriteria()
                .andEqualTo(Worker::getWorkerNo, workerNo)
                .andEqualTo(Worker::getHospitalId, hospitalCode)
                .andEqualTo(Worker::getStatus, 0);
        return selectByExample(weekend);
    }


    List<Worker> getAllWorkerByInput(@Param("input") String input, @Param("hospitalCode") Integer hospitalCode);

    MdmRecipeWhitelist getIsWhiteDoc(@Param("doctorId") Integer doctorId,@Param("hospitalCode")  Integer hospitalCode);

    /**
     * 根据多学科门诊挂号记录获取副主任级别以上医生人数
     *
     * <AUTHOR>
     * @param regNo 挂号流水号
     * @param hospitalCode 医院编码
     * @return
     */
    Integer getMDTWorkNum(@Param("regNo") Integer regNo,
                         @Param("hospitalCode") Integer hospitalCode);

    /**
     * 获取总分院关联医生信息
     *
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    List<Worker> getRelationWorker(@Param("doctorId") Integer doctorId,@Param("hospitalCode")  Integer hospitalCode);

    /**
     * 获取所有职工
     *
     * @param hospitalCode
     */
    default List<Worker> queryAllWorkerList(Integer hospitalCode) {
        Weekend<Worker> weekend = new Weekend<>(Worker.class);
        weekend.weekendCriteria().andEqualTo(Worker::getHospitalId, hospitalCode);
        return selectByExample(weekend);
    }

}
