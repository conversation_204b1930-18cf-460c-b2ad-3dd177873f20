package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
* 科研患者挂号表
* @TableName Sci_RegisterList
*/
@Data
@Table(name = "Sci_RegisterList")
public class SciRegisterlist implements Serializable {

    /**
    * 
    */
    @Column(name = "RegNo")
    private Long regno;
    /**
    * 
    */
    @Column(name = "PatId")
    private Long patid;
    /**
    * 
    */
    @Column(name = "NewPatId")
    private Long newPatId;
    /**
    * 
    */
    @Column(name = "CardNo")
    private String cardNo;
    /**
    * 
    */
    @Column(name = "OutPatientNo")
    private String outPatientNo;
    /**
    * 
    */
    @Column(name = "HospNo")
    private String hospNo;
    /**
    * 
    */
    @Column(name = "PatName")
    private String patName;
    /**
    * 
    */
    @Column(name = "ProjectId")
    private String projectId;
    /**
    * 
    */
    @Column(name = "ProjectName")
    private String projectName;
    /**
    * 
    */
    @Column(name = "DeptId")
    private Integer deptId;
    /**
    * 
    */
    @Column(name = "DoctorId")
    private Integer doctorId;
    /**
    * 
    */
    @Column(name = "DoctorLevel")
    private Integer doctorLevel;
    /**
    * 
    */
    @Column(name = "status")
    private Integer status;
    /**
    * 
    */
    @Column(name = "RegistTime")
    private Date registTime;
    /**
    * 
    */
    @Column(name = "OpCode")
    private Integer opCode;
    /**
    * 
    */
    @Column(name = "CureCode")
    private String cureCode;
    /**
    * 
    */
    @Column(name = "VisitFlag")
    private Integer visitFlag;
    /**
    * 
    */
    @Column(name = "IsDelete")
    private Boolean isDelete;
    /**
    * 
    */
    @Column(name = "CreatedBy")
    private Integer createdBy;
    /**
    * 
    */
    @Column(name = "CreatedDate")
    private Date createdDate;
    /**
    * 
    */
    @Column(name = "UpdateDate")
    private Date updateDate;
    /**
    * 
    */
    @Column(name = "UpdateBy")
    private Integer updateBy;
    /**
    * 
    */
    @Column(name = "FzFlag")
    private Integer fzFlag;
    /**
    * 
    */
    @Column(name = "HospitalCode")
    private Integer hospitalCode;

}
