package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.infrastructure.repository.entity.TbtLKPushMessage;
import com.rjsoft.outPatient.infrastructure.repository.mapper.TbtLKPushMessageMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.TbtLKPushMessageRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class TbtLKPushMessageRepositoryImpl implements TbtLKPushMessageRepository {
    TbtLKPushMessageMapper tbtLKPushMessageMapper;

    @Override
    public boolean insertWeChatMessage(TbtLKPushMessage tbtLKPushMessage, Integer hospitalCode) {
        String dataName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode)?DatasourceName.ZXHIS:DatasourceName.ZXHIS3;
        DataSourceSwitchAspect.changeDataSource(dataName);
        return tbtLKPushMessageMapper.insertSelective(tbtLKPushMessage)>0;
    }
}
