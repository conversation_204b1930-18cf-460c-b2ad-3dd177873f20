package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.PackagePrimary;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CustomPackageRepository {

    /**
     * 查询自定义套餐
     *
     * @param packageCategory 套餐类型
     * @param hospitalCode    医院编码
     * @param packageRange    使用范围
     * @param doctorId
     * @return List<PackagePrimary>
     */
    List<PackagePrimary> getCustomPackage(Integer packageCategory, Integer hospitalCode, Integer packageRange, Integer doctorId);

    /**
     * 根据套餐 id 查询套餐名称
     *
     * @param id           主键
     * @param hospitalCode 医院编码
     * @return 套餐名称
     */
    String getCustomPackageNameById(Integer id, Integer hospitalCode);

    /**
     * 保存自定义套餐
     *
     * @param packagePrimary 套餐实体
     */
    void saveCustomPackage(PackagePrimary packagePrimary);

    /**
     * 删除自定义套餐
     * {@link Example}可以传null值，如果为null则根据主键按需修改
     *
     * @param packagePrimary 套餐实体
     * @param example
     */
    void updateCustomPackage(PackagePrimary packagePrimary, Example example);

}
