package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.entity.LocalConfig;
import com.rjsoft.common.log.Log;
import com.rjsoft.outPatient.domain.recipe.dto.ChargeItemDto;
import com.rjsoft.outPatient.domain.recipe.dto.CodeResponse;
import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeItem;
import com.rjsoft.outPatient.infrastructure.repository.entity.TbUser;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 收费项目
 *
 * <AUTHOR>
public interface SysLocalConfigMapper extends BaseMapper<LocalConfig>, ExampleMapper<LocalConfig> {
    /**
     * 获取本机配置集合
     *
     * @param systemNo
     * @param key
     * @param hospitalCode
     * @param ip
     * @return
     */
    @DatabaseAnnotation(name = "HISDB")
    default LocalConfig getLocalConfig(Integer systemNo,String key,String hospitalCode, String ip) {
        Weekend<LocalConfig> weekend = new Weekend<>(LocalConfig.class);
        weekend.weekendCriteria().andEqualTo(LocalConfig::getSystemNo, systemNo)
                .andEqualTo(LocalConfig::getHospitalId, hospitalCode)
                .andEqualTo(LocalConfig::getKeyCode, key)
                .andEqualTo(LocalConfig::getMachineAddress, ip);
        List<LocalConfig> configs = selectByExample(weekend);
        Log.info("获取配置条数: " + configs.size());
        return configs.size() == 0 ? null : configs.get(0);
    }




}
