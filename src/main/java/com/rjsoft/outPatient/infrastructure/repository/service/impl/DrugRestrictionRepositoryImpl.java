package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.utils.TyMdc;
import com.rjsoft.outPatient.common.enums.DrugRestrictionEnum;
import com.rjsoft.outPatient.domain.recipe.constant.TyKey;
import com.rjsoft.outPatient.domain.recipe.dto.DrugRestrictionDTO;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugRestriction;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugRestrictionMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DrugRestrictionsTypeMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DrugRestrictionRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/27-1:31 下午
 */
@Service
@AllArgsConstructor
public class DrugRestrictionRepositoryImpl implements DrugRestrictionRepository {

    private final DrugRestrictionMapper drugRestrictionMapper;
    private final DrugRestrictionsTypeMapper drugRestrictionsTypeMapper;
    private final RecipeDetailRepositoryImpl recipeDetailRepositoryImpl;

    @Override
    @DatabaseAnnotation
    public Integer getDrugRestrictionId(Integer restrictionCode, Integer drugCode, Integer hospitalCode) {
        final Weekend<DrugRestriction> weekend = new Weekend<>(DrugRestriction.class);
        final WeekendCriteria<DrugRestriction, Object> criteria = weekend.weekendCriteria();

        weekend.selectProperties("id");

        criteria.andEqualTo(DrugRestriction::getRestrictionCode, restrictionCode)
                .andEqualTo(DrugRestriction::getDrugCode, drugCode)
                .andEqualTo(DrugRestriction::getHospitalCode, hospitalCode);

        final DrugRestriction entity = Optional.ofNullable(drugRestrictionMapper.selectOneByExample(weekend)).orElse(new DrugRestriction());

        return Optional.ofNullable(entity.getId()).orElse(-1);
    }

    /**
     * 列出所有的DrugRestriction，以hospitalCode_drugCode_restrictionCode_为key,id为value,组成map
     * @return
     */
    @Override
    @DatabaseAnnotation
    public Map<String, Integer> listAllDrugRestrictionIdToMap() {
        final Weekend<DrugRestriction> weekend = new Weekend<>(DrugRestriction.class);
        final WeekendCriteria<DrugRestriction, Object> criteria = weekend.weekendCriteria();

        weekend.selectProperties("id","restrictionCode","drugCode","hospitalCode");
        List<DrugRestriction> drugRestrictionList = drugRestrictionMapper.selectByExample(weekend);
        Map<String, Integer> hospDrugCodeResCode4drugRestrictionIdMap = new HashMap<>();
        for (DrugRestriction dr : drugRestrictionList) {
            String key = ""+dr.getHospitalCode()+"_"+dr.getDrugCode()+"_"+dr.getRestrictionCode();
            Integer id = Optional.ofNullable(dr.getId()).orElse(-1);
            hospDrugCodeResCode4drugRestrictionIdMap.put(key, id);
        }

        return hospDrugCodeResCode4drugRestrictionIdMap;
    }



    @Override
    @DatabaseAnnotation
    public Integer tyTyMdcGetDrugRestrictionId(Integer restrictionCode, Integer drugCode, Integer hospitalCode) {
        Map<String,Integer> listAllDrugRestrictionIdMap = TyMdc.get(TyKey.LIST_ALL_DRUG_RESTRICTION_ID_MAP);
        if(listAllDrugRestrictionIdMap!=null){
            return  Optional.ofNullable(listAllDrugRestrictionIdMap
                    .get(""+hospitalCode+"_"+drugCode+"_"+restrictionCode)).orElse(-1);
        }
        return getDrugRestrictionId(restrictionCode, drugCode, hospitalCode);
    }

    /**
     * 查询药品约束数量，先从TyMdc中查并求和，没写过再去库中查
     * @param drugCode            项目编码
     * @param hospitalCode        医院编码
     * @param drugRestrictionEnum 药品限制类型
     * @return
     */
    @Override
    @DatabaseAnnotation
    public Integer getDrugRestrictionCount(Integer drugCode, Integer hospitalCode, DrugRestrictionEnum... drugRestrictionEnum) {
        Map<String, DrugRestrictionDTO> drugRestrictionDTOMap = TyMdc.get(TyKey.DRUG_RESTRICTION_DTOMAP);
        if (drugRestrictionDTOMap == null) {

            final Weekend<DrugRestriction> weekend = new Weekend<>(DrugRestriction.class);
            final WeekendCriteria<DrugRestriction, Object> criteria = weekend.weekendCriteria();

            criteria.andEqualTo(DrugRestriction::getDrugCode, drugCode)
                    .andEqualTo(DrugRestriction::getHospitalCode, hospitalCode);

            if (drugRestrictionEnum != null && drugRestrictionEnum.length > 0) {
                final List<Integer> drugRestrictionCodeList = Arrays.stream(drugRestrictionEnum).map(DrugRestrictionEnum::getCode).collect(Collectors.toList());
                criteria.andIn(DrugRestriction::getRestrictionCode, drugRestrictionCodeList);
            }

            return drugRestrictionMapper.selectCountByExample(weekend);
        } else {
            int count = 0;
            if (drugRestrictionEnum != null && drugRestrictionEnum.length > 0) {
                List<Integer> drugRestrictionCodeList = Arrays.stream(drugRestrictionEnum).map(DrugRestrictionEnum::getCode).collect(Collectors.toList());

                for (Integer restCode : drugRestrictionCodeList) {
                    DrugRestrictionDTO drugRestrictionDTO = drugRestrictionDTOMap.get("" + restCode + "_" + drugCode + "_");
                    if (drugRestrictionDTO != null) {
                        count += drugRestrictionDTO.getNum();
                    }
                }
            }
            return count;
        }
    }

    @Override
    @DatabaseAnnotation
    public Integer getDrugRestrictionCount(DrugRestrictionEnum drugRestrictionEnum, List<Integer> drugCode, Integer hospitalCode) {
        if (drugCode == null || drugCode.isEmpty()) {
            return 0;
        }
        final Weekend<DrugRestriction> weekend = new Weekend<>(DrugRestriction.class);
        final WeekendCriteria<DrugRestriction, Object> criteria = weekend.weekendCriteria();

        criteria.andEqualTo(DrugRestriction::getRestrictionCode, drugRestrictionEnum.getCode())
                .andIn(DrugRestriction::getDrugCode, drugCode)
                .andEqualTo(DrugRestriction::getHospitalCode, hospitalCode);

        return drugRestrictionMapper.selectCountByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<DrugRestrictionDTO> countDrugRestriction(List<Integer> itemCodeList, Integer hospId, DrugRestrictionEnum... drugRestrictionEnum) {
        List<Integer> enumCodeList = Arrays.stream(drugRestrictionEnum).map(DrugRestrictionEnum::getCode).distinct().collect(Collectors.toList());
        List<DrugRestrictionDTO> dtos = drugRestrictionMapper.countDrugRestriction(itemCodeList, enumCodeList, hospId);
        return dtos;
    }
}
