package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.infrastructure.repository.entity.InHospitalInfo;
import com.rjsoft.outPatient.infrastructure.repository.mapper.InHospitalInfoMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.InHospitalInfoRepository;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

/**
 * <AUTHOR>
 * @since 2021/7/12 - 10:22
 */
@Service
@AllArgsConstructor
public class InHospitalInfoRepositoryImpl implements InHospitalInfoRepository {

    InHospitalInfoMapper inHospitalInfoMapper;

    @Override
    public InHospitalInfo getInHospitalInfoByRegNo(String certificateNo, Integer hospitalCode) {
        if (HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
        }
        if (ObjectUtils.isNotEmpty(certificateNo)) {
            return null;
        }
        final Weekend<InHospitalInfo> weekend = new Weekend<>(InHospitalInfo.class);
        weekend.weekendCriteria().andEqualTo(InHospitalInfo::getPatSfz, certificateNo);
        return inHospitalInfoMapper.selectOneByExample(weekend);
    }
}
