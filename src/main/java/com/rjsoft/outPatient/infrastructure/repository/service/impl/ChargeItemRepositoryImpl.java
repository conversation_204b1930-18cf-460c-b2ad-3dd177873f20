package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.alibaba.fastjson.JSON;
import com.rjsoft.common.SystemNo;
import com.rjsoft.common.configuration.SysConfig;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.utils.TyMdc;
import com.rjsoft.outPatient.common.SystemConfigUtils;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.consts.SysConfigKey;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.common.utils.GetHeadInfoDao;
import com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDto;
import com.rjsoft.outPatient.domain.recipe.constant.TyKey;
import com.rjsoft.outPatient.domain.recipe.dto.ChargeItemDto;
import com.rjsoft.outPatient.domain.recipe.dto.CodeResponse;
import com.rjsoft.outPatient.infrastructure.cache.service.CacheConfigService;
import com.rjsoft.outPatient.infrastructure.cache.service.ChargItemCache;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ChargeItemMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DoctorAntimicrobialMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SpecialItemMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.TemplateExtensionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ChargeItemRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.MdmPubFeecategoryexedeptRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.MdmPubItemexedeptRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.TemplateExtensionRepository;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.rjsoft.outPatient.common.enums.RecipeTypeEnum.*;

/**
 * 收费项目
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class ChargeItemRepositoryImpl implements ChargeItemRepository {

    ChargeItemMapper chargeItemMapper;
    SpecialItemMapper specialItemMapper;
    SystemConfigUtils systemConfigUtils;
    DoctorAntimicrobialMapper doctorAntimicrobialMapper;

    // XXX: yutao 2024/4/23 缓存ignite查询
    ChargItemCache chargItemCache;
    CacheConfigService cacheConfigService;
    TemplateExtensionRepository tmplExtensionRepository;

    @Autowired
    private MdmPubItemexedeptRepository exedeptRepository;

    @Autowired
    private MdmPubFeecategoryexedeptRepository feecategoryexedeptRepository;

    @Autowired
    private GetHeadInfoDao getHeadInfoDao;

    private static final Integer DEFAULT_EXEDEPT_FLAG = 1;

    @Autowired
    private SysConfig sysConfig;

    /**
     * 获取收费项目
     *
     * @param itemCode
     * @param hospitalCode
     * @param stopped
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public ChargeItem getChargeItemByIdIsStopped(Integer itemCode, Integer deptId, Integer hospitalCode, Integer stopped) {
        int ydStopped = 0;  //药典停用状态 1停用 0启用
        int ypUsed = 1;  //本部门药品状态 0停用 1启用
        if (stopped == null) {
            stopped = 0;
        }
        if (stopped == 1) {
            ydStopped = 1;
            ypUsed = 0;
        }

        ChargeItem item = chargeItemMapper.getChargeItemByIdIsStopped(itemCode, deptId, hospitalCode, stopped, ydStopped, ypUsed);
        if (item == null) {
            return null;
        }
        return chargeItemTrim(item);
    }

    /**
     * 获取收费项目
     *
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    @Cacheable(cacheNames = "gwChargeItemById1", unless = "#result == null")
    public ChargeItem getChargeItemById(Integer itemCode, Integer hospitalCode) {
        ChargeItem item = chargeItemMapper.getChargeItemById(itemCode, hospitalCode);
        if (item == null) {
            return null;
        }
        return chargeItemTrim(item);
    }

    /**
     * 获取收费项目列表,这个方法不好，因为内部还有
     * specialItemMapper.getSpecialItemById(item.getItemCode(), item.getHospitalId());
     * 循环内sql
     *
     * @param itemCodeList
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<ChargeItem> getChargeItemByIdList(List<Integer> itemCodeList, Integer hospitalCode) {
        final Weekend<ChargeItem> weekend = new Weekend<>(ChargeItem.class);
        weekend.weekendCriteria().andIn(ChargeItem::getItemCode, itemCodeList)
                .andEqualTo(ChargeItem::getHospitalId, hospitalCode);
        List<ChargeItem> itemList = chargeItemMapper.selectByExample(weekend);
        if (itemList == null) {
            itemList = new ArrayList<>();
        }
        for (ChargeItem item : itemList) {
            chargeItemTrim(item);
        }
        return itemList;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public String getChargeItemNameById(Integer itemCode, Integer hospitalCode) {
        final Weekend<ChargeItem> weekend = new Weekend<>(ChargeItem.class);
        weekend.selectProperties("itemName");
        weekend.weekendCriteria()
                .andEqualTo(ChargeItem::getItemCode, itemCode).andEqualTo(ChargeItem::getHospitalId, hospitalCode)
                .andEqualTo(ChargeItem::getStopped, 0).andIn(ChargeItem::getUseRange, Arrays.asList(0, 1));
        final ChargeItem chargeItem = chargeItemMapper.selectOneByExample(weekend);
        if (chargeItem == null) {
            return "";
        }
        return Optional.ofNullable(chargeItemTrim(chargeItem).getItemName()).orElse("");
    }

    /**
     * 字段空格处理，门诊特殊包装单位处理
     *
     * @param item
     * @return
     */
    public ChargeItem chargeItemTrim(ChargeItem item) {
        if (!StringUtils.isEmpty(item.getItemName())) {
            item.setItemName(item.getItemName().trim());
        }
        if (!StringUtils.isEmpty(item.getDosageUnit())) {
            item.setDosageUnit(item.getDosageUnit().trim());
        }
        if (!StringUtils.isEmpty(item.getWardUnit())) {
            item.setWardUnit(item.getWardUnit().trim());
        }

        //根据项目编码获取门诊特殊包装配置
//        SpecialItems specialItem = specialItemMapper.getSpecialItemById(item.getItemCode(), item.getHospitalId());
//        如果前面mdc已经根据itemCode查过了，就直接从Map中取
        Map<Integer, SpecialItems> specialItemByIdMap = TyMdc.get(TyKey.SPECIAL_ITEM_BY_ID_MAP);
        SpecialItems specialItem = null;
        if(specialItemByIdMap == null) {
            specialItem = specialItemMapper.getSpecialItemById(item.getItemCode(), item.getHospitalId());
        }else{
            specialItemByIdMap.get(""+item.getHospitalId()+"_"+item.getItemCode()+"_");
        }
        if (specialItem != null) {
            item.setClinicUnit(specialItem.getClinicUnit());
            item.setClinicQty(specialItem.getClinicQuantity());
        }
        if (!StringUtils.isEmpty(item.getClinicUnit())) {
            item.setClinicUnit(item.getClinicUnit().trim());
        }
        final ChargeItem chargeItem = new ChargeItem();
        BeanUtils.copyProperties(item, chargeItem);
        return chargeItem;
    }

    /**
     * 字段空格处理，门诊特殊包装单位处理
     *
     * @param item
     * @return
     */
    public ChargeItem chargeItemTrim(ChargeItem item, Integer itemCategory) {
        if (!StringUtils.isEmpty(item.getItemName())) {
            item.setItemName(item.getItemName().trim());
        }
        if (!StringUtils.isEmpty(item.getDosageUnit())) {
            item.setDosageUnit(item.getDosageUnit().trim());
        }
        if (!StringUtils.isEmpty(item.getWardUnit())) {
            item.setWardUnit(item.getWardUnit().trim());
        }

        if (ItemCategoryEnum.isDrug(itemCategory)) {
            //根据项目编码获取门诊特殊包装配置
            SpecialItems specialItem = specialItemMapper.getSpecialItemById(item.getItemCode(), item.getHospitalId());
            if (specialItem != null) {
                item.setClinicUnit(specialItem.getClinicUnit());
                item.setClinicQty(specialItem.getClinicQuantity());
            }
            if (!StringUtils.isEmpty(item.getClinicUnit())) {
                item.setClinicUnit(item.getClinicUnit().trim());
            }
        }
        final ChargeItem chargeItem = new ChargeItem();
        BeanUtils.copyProperties(item, chargeItem);
        return chargeItem;
    }


    /**
     * 获取收费项目根据项明细集合
     *
     * @param ids
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<ChargeItem> getChargeItemByIdsAndInitSpec(List<Integer> ids, Integer hospitalCode) {

        if(ids == null || ids.size() == 0) {
            return Collections.emptyList();
        }
        //select in 一次查出，写入Map
        List<ChargeItem> chargeItemByIdList = chargeItemMapper.getChargeItemByIdList(ids, hospitalCode);
        Map<Integer, ChargeItem> chargeItemByIdMap = new HashMap<>();
        for (ChargeItem ci : chargeItemByIdList) {
            chargeItemByIdMap.put(ci.getItemCode(), ci);
        }
        TyMdc.put(TyKey.CHARGE_ITEM_BY_ID_MAP, chargeItemByIdMap);

        List<Integer> hospIdList = new ArrayList<>();
        hospIdList.add(hospitalCode);
        specialItemByIdList2TyMdc(ids, hospIdList);

        List<ChargeItem> chargeItems = new ArrayList<>();
        for (Integer id : ids) {
//            ChargeItem chargeItem = chargeItemMapper.getChargeItemById(id, hospitalCode);
            ChargeItem chargeItem = null;
            chargeItem = chargeItemByIdMap.get(id);
            if (chargeItem != null) {
                chargeItems.add(chargeItemTrim(chargeItem));
            }
        }
        return chargeItems;

//        return chargeItemMapper.getChargeItemByIds(ids, hospitalCode).stream().map(this::chargeItemTrim).collect(Collectors.toList());
    }

    @Override
    public void specialItemByIdList2TyMdc(List<Integer> ids, List<Integer> hospitalCodeList) {
        if (ids == null || ids.size() == 0) {
            return;
        }
        if(hospitalCodeList == null || hospitalCodeList.size() == 0) {
            return;
        }
        List<SpecialItems> specialItems =
                specialItemMapper.querySpecialItemByIdListAndHospitalCodeList(ids, hospitalCodeList);
        Map<String, SpecialItems> specialItemsMap = new HashMap<>();
        for (SpecialItems sci : specialItems) {
            specialItemsMap.put(""+sci.getHospitalCode()+"_"+sci.getItemCode()+"_", sci);
        }
        TyMdc.put(TyKey.SPECIAL_ITEM_BY_ID_MAP, specialItemsMap);
    }

    /**
     * 根据项目类别，查询系统套餐
     *
     * @param category
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<ChargeItem> getPackageItemByCategory(Integer category, Integer hospitalCode, String itemNameOrInputCode) {
        final Weekend<ChargeItem> weekend = new Weekend<>(ChargeItem.class);
        weekend.weekendCriteria()
                .andEqualTo(ChargeItem::getItemCategory, category)
                .andEqualTo(ChargeItem::getHospitalId, hospitalCode)
                .andEqualTo(ChargeItem::getIsSet, 1)
                .andEqualTo(ChargeItem::getStopped, 0)
                .andIn(ChargeItem::getUseRange, Arrays.asList(0, 1));
        if (itemNameOrInputCode != null && !itemNameOrInputCode.trim().equals("")) {
            WeekendCriteria<ChargeItem, Object> criteria = weekend.createCriteriaAddOn();
            criteria.orLike(ChargeItem::getItemName, "%" + itemNameOrInputCode + "%");
            criteria.orLike(ChargeItem::getInputCode1, "%" + itemNameOrInputCode.toUpperCase() + "%");
            weekend.and(criteria);
        }
        return chargeItemMapper.selectByExample(weekend);
    }

    /**
     * 根据输入码查询、执行科室、查询药品
     *
     * @param inputCode    输入码
     * @param itemCategory 收费类型编码
     * @param hospitalCode 医院编码
     * @return ChargeItem
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<ChargeItemDto> getDrugChargeItemByInputCode(String inputCode, Integer itemCategory, Integer hospitalCode, Integer useDept) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("开始");
        /**
         * 新增extendList、extend
         */
        if (ItemCategoryEnum.notDrug(itemCategory)) {
            return new ArrayList<>(0);
        }
        List<TemplateExtension> templateExtensionList = null;
        List<TemplateExtension> drugOriginal = null;
        String curTime = new SimpleDateFormat("HH:mm:ss").format(new Date());
        if (cacheConfigService.enabled()) {
            templateExtensionList = chargItemCache.getDrugDangerLev(0, hospitalCode);
            drugOriginal = chargItemCache.getDrugOriginal(hospitalCode);
        } else {
            templateExtensionList = tmplExtensionRepository.getDrugDangerLev(0, hospitalCode);
            drugOriginal = tmplExtensionRepository.getDrugOriginal(hospitalCode);
        }
        stopWatch.stop();
        stopWatch.start("getSystemConfigByStr");
        String inputType = systemConfigUtils.getSystemConfigByStr(SystemNo.OUTPATIENT, SysConfigKey.INPUT_SEACH_TYPE, hospitalCode);
        String storeTbname = "Drug_Tb_StoreChangeOutPatientVirtual";
        if (itemCategory.equals(14)) {
            storeTbname = "Drug_Tb_StoreChangeInPatient";
        }
        stopWatch.stop();
        stopWatch.start("getChargeItemByInputCodeAndDeptId");
        List<ChargeItemDto> chargeItemDtoList = new ArrayList<>();
        if (cacheConfigService.enabled()) {
//            chargeItemDtoList = chargeItemMapper.getChargeItemByInputCodeAndDeptId(inputCode, hospitalCode, itemCategory, inputType, useDept, curTime, storeTbname);
            chargeItemDtoList = chargItemCache.getChargeItemByInputCodeAndDeptId(inputCode, hospitalCode, itemCategory, inputType, useDept, curTime, storeTbname);
            int x = 1;
        } else {
            chargeItemDtoList = chargeItemMapper.getChargeItemByInputCodeAndDeptId(inputCode, hospitalCode, itemCategory, inputType, useDept, curTime, storeTbname);

        }
        stopWatch.stop();
        stopWatch.start("templateExtensionList");

        if (templateExtensionList.size() > 0) {
            for (ChargeItemDto item : chargeItemDtoList) {
                List<TemplateExtension> list = templateExtensionList.stream().filter(p -> p.getClassId().equals(Converter.toString(item.getItemCode()))).collect(Collectors.toList());
                if (list.size() > 0) {
                    item.setDrugDangerLev(list.get(0).getCode());
                    item.setDrugDangerLevName(list.get(0).getName());
                }
            }
        }
        stopWatch.stop();
        stopWatch.start(" if (drugOriginal != null");
        if (drugOriginal != null && drugOriginal.size() > 0) {
            for (ChargeItemDto item : chargeItemDtoList) {
                List<Integer> extendList = new ArrayList<>();
                List<String> extendName = new ArrayList<>();
                List<TemplateExtension> list = drugOriginal.stream().filter(p -> p.getClassId().equals(Converter.toString(item.getItemCode()))).collect(Collectors.toList());
                if (list.size() > 0) {
                    extendList.add(item.getDrugPropertyId());
                    //fixme:yutao 2024-04-28 移出循环，变成map
                    if (cacheConfigService.enabled()) {
//                        extendName.add(templateExtensionMapper.getDrugPropertyName(item.getDrugPropertyId(), hospitalCode));
                        extendName.add(chargItemCache.getDrugPropertyName(item.getDrugPropertyId(), hospitalCode));
                    } else {
                        extendName.add(tmplExtensionRepository.getDrugPropertyName(item.getDrugPropertyId(), hospitalCode));
                    }
                    extendList.add(-1);
                    extendName.add("原研药物");
                }
                item.setExtendList(extendList);
                item.setExtendName(extendName);
            }
        }
        stopWatch.stop();
        stopWatch.start("itemExeDeptConfig");
        //获取"收费类型"配置
        //List<MdmPubFeecategoryexedept> feeCategoryExeDeptConfig =
        //getFeeCategoryExeDeptConfig(itemCategory, hospitalCode, useDept, curTime);
        //获取符合"收费类型"配置的item
        //List<ChargeItemDto> feeCategoryExeDeptConfigItems =
        //getFeeCategoryExeDeptConfigItem(feeCategoryExeDeptConfig, chargeItemDtoList);

        //获取"收费项目"配置
        List<MdmPubItemexedept> itemExeDeptConfig = getItemExeDeptConfig(hospitalCode, useDept, curTime);
        //获取符合"收费项目"配置的item
        List<ChargeItemDto> rtns =
                getItemExeDeptConfigItem(itemExeDeptConfig, chargeItemDtoList);

//        if(feeCategoryExeDeptConfig.size() > 0 && rtns.size() > 0) {
//            if (itemCategory == ItemCategoryEnum.ChineseHerbMed.getCategoryCode()) {
//                Log.info("草药修改默认执行科室" + feeCategoryExeDeptConfig.get(0).getExecDept());
//                rtns.parallelStream().forEach(p -> p.setExeDept(feeCategoryExeDeptConfig.get(0).getExecDept()));
//            }
//        }
        stopWatch.stop();
        stopWatch.start("mustPositiveStock");
        String mustPositiveStock = sysConfig.getConfig(SystemNo.OUTPATIENT, SysConfigKey.MUST_POSITIVE_STOCK, String.valueOf(hospitalCode));
        if (!StringUtils.isEmpty(mustPositiveStock) && "1".equals(mustPositiveStock)) {
            rtns.parallelStream().filter(p -> p.getStock().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        }
        stopWatch.stop();
        Log.info("stopWatch:" + stopWatch.prettyPrint());
        Log.info("stopWatch:" + stopWatch.shortSummary());

        Log.info("stopWatch:" + "X所有任务总耗时：" + stopWatch.getTotalTimeMillis());
        Log.info("stopWatch:" + "X任务总数：" + stopWatch.getTaskCount());
        Log.info("stopWatch:" + "X所有任务详情：" + stopWatch.getTaskInfo());
        return rtns;
    }

    /**
     * 根据输入码查询、执行科室、查询非药品
     * 如果 itemCategory 为 null 就查询所有非药品
     *
     * @param inputCode    输入码
     * @param itemCategory 收费类型编码
     * @param hospitalCode 医院编码
     * @param useDept      医生开处方科室
     * @return ChargeItem
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<ChargeItemDto> getNonDrugChargeItemByInputCode(String inputCode, List<Integer> itemCategory, Integer hospitalCode, Integer useDept) {
        final List<Integer> nonDrugItemCategoryList;
        // 如果 itemCategory 为 null 就查询所有非药品
        if (itemCategory == null || itemCategory.isEmpty()) {
            nonDrugItemCategoryList = ItemCategoryEnum.getNonDrugTypeCodeList();
        } else {
            nonDrugItemCategoryList = itemCategory.stream().filter(ItemCategoryEnum::notDrug).collect(Collectors.toList());
        }
        if (nonDrugItemCategoryList.isEmpty()) {
            return new ArrayList<>(0);
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("getSystemConfigByStr");
        String inputType = systemConfigUtils.getSystemConfigByStr(SystemNo.OUTPATIENT, SysConfigKey.INPUT_SEACH_TYPE, hospitalCode);
//        if (inputType.equals("0") || inputType.equals("")) {
//            inputCode = "%" + inputCode + "%";
//        } else {
//            inputCode = inputCode + "%";
//        }
        stopWatch.stop();
        stopWatch.start("other1");
        List<ChargeViewItem> chargeItemList = null;
        if (cacheConfigService.enabled()) {
            chargeItemList = chargItemCache.getNonChargeItemByInputCodeAndDeptId(inputCode, hospitalCode, nonDrugItemCategoryList, inputType, useDept);
        } else {
            chargeItemList = chargeItemMapper.getNonChargeItemByInputCodeAndDeptId(inputCode, hospitalCode, nonDrugItemCategoryList, inputType, useDept);
        }
//        stopWatch.start("map.getNonChargeItemByInputCodeAndDeptId");
//        //return chargeItemMapper.selectByExample(weekend).stream().map(ChargeItemDto::createDtoFromEntity).collect(Collectors.toList());
//        List<ChargeViewItem> chargeItemList = chargeItemMapper.getNonChargeItemByInputCodeAndDeptId(inputCode, hospitalCode, nonDrugItemCategoryList, inputType, useDept);
//        stopWatch.stop();

        List<ChargeItemDto> chargeItemDtoList = chargeItemList.stream().map(ChargeItemDto::createDtoFromEntity).collect(Collectors.toList());

        String curTime = new SimpleDateFormat("HH:mm:ss").format(new Date());
        //获取符合"收费类型"配置的item
        List<ChargeItemDto> feeCategoryExeDeptConfigItems = new ArrayList<>();
        List<MdmPubFeecategoryexedept> feeCategoryExeDeptConfigs = new ArrayList<>();

        for (Integer feeCategory : nonDrugItemCategoryList) {
            List<MdmPubFeecategoryexedept> feeCategoryExeDeptConfig = null;
            if (cacheConfigService.enabled()) {
                feeCategoryExeDeptConfig =
                        chargItemCache.getFeeCategoryExeDeptConfig(feeCategory, hospitalCode, useDept, curTime);
            } else {
                //获取"收费类型"配置
                feeCategoryExeDeptConfig =
                        getFeeCategoryExeDeptConfig(feeCategory, hospitalCode, useDept, curTime);
            }


            feeCategoryExeDeptConfigs.addAll(feeCategoryExeDeptConfig);
            //获取符合"收费类型"配置的item
            List<ChargeItemDto> singleFeeExeDeptConfigItem =
                    getFeeCategoryExeDeptConfigItem(feeCategoryExeDeptConfig, chargeItemDtoList);
            feeCategoryExeDeptConfigItems.addAll(singleFeeExeDeptConfigItem);
        }
        stopWatch.stop();
        stopWatch.start("other2");
        //获取"收费项目"配置
        List<MdmPubItemexedept> itemExeDeptConfig = getItemExeDeptConfig(hospitalCode, useDept, curTime);
        //获取符合"收费项目"配置的item
        List<ChargeItemDto> itemExeDeptConfigItems =
                getItemExeDeptConfigItem(itemExeDeptConfig, chargeItemDtoList);

        stopWatch.stop();
        Log.info("stopWatch:" + stopWatch.prettyPrint());
        Log.info("stopWatch:" + stopWatch.shortSummary());

        Log.info("stopWatch:" + "getNonDrugChargeItemByInputCode任务总耗时：" + stopWatch.getTotalTimeMillis());
        Log.info("stopWatch:任务总数：" + stopWatch.getTaskCount());
        //处理得到最终的itemList
        return getFitExeDeptConfigItems(itemExeDeptConfigItems, feeCategoryExeDeptConfigItems, chargeItemDtoList, feeCategoryExeDeptConfigs, itemExeDeptConfig);
    }

    private List<ChargeItemDto> getFitExeDeptConfigItems(List<ChargeItemDto> itemExeDeptConfigItems,
                                                         List<ChargeItemDto> feeCategoryExeDeptConfigItems,
                                                         List<ChargeItemDto> chargeItemDtoList,
                                                         List<MdmPubFeecategoryexedept> feeCategoryExeDeptConfig,
                                                         List<MdmPubItemexedept> itemExeDeptConfig) {
        //根据符合“收费项目”配置的item生成映射表
        HashMap<Integer, ChargeItemDto> map = new HashMap<>();
        for (ChargeItemDto item : itemExeDeptConfigItems) {
            map.put(item.getItemCode(), item);
        }

        //根据符合“收费项目”配置的item初始化结果res
        List<ChargeItemDto> res = new ArrayList<>(itemExeDeptConfigItems);

        //如果映射表中没有“收费类型”的item，则添加到resList，并添加到映射表
        /*for (ChargeItemDto item : feeCategoryExeDeptConfigItems) {
            ChargeItemDto existItem = map.get(item.getItemCode());
            if (ObjectUtils.isEmpty(existItem)) {
                res.add(item);
                map.put(item.getItemCode(), item);
            }
        }*/

        /*HashMap<Integer, MdmPubItemexedept> itemConfigMap = new HashMap<>();
        for (MdmPubItemexedept itemConfig : itemExeDeptConfig) {
            itemConfigMap.put(itemConfig.getItemCode(), itemConfig);
        }*/

        Log.info("chargeItemDtoList" + JSON.toJSONString(chargeItemDtoList));
        //根据处理后的映射表，如果映射表中不存在，说明该项目没有在配置中
        for (ChargeItemDto item : chargeItemDtoList) {
            ChargeItemDto existItem = map.get(item.getItemCode());
            if (ObjectUtils.isEmpty(existItem)) {
                //if (isDrug(item) && noExeDeptConfig(item, itemConfigMap, feeCategoryExeDeptConfig)) {
                if (isDrug(item)) {
                    //药品，没有执行科室配置则添加
                    res.add(item);
                } else if (!isDrug(item)
                        && (DEFAULT_EXEDEPT_FLAG.equals(item.getExeDeptFlag())
                        || ObjectUtils.isEmpty(item.getExeDeptFlag()))) {
                    //非药品，映射表不存在且是默认标识或没有默认标识则添加
                    res.add(item);
                }
            }
        }
        Log.info("getFitExeDeptConfigItems res" + JSON.toJSONString(res));
        return res;
    }

    private boolean isDrug(ChargeItemDto item) {
        return WESTERN_MED.getRecipeType().equals(item.getItemCategory())
                || CHINESE_PAT_MED.getRecipeType().equals(item.getItemCategory())
                || HERBSDRUG.getRecipeType().equals(item.getItemCategory());
    }

    private boolean noExeDeptConfig(ChargeItemDto item, HashMap<Integer, MdmPubItemexedept> itemConfigMap, List<MdmPubFeecategoryexedept> feeCategoryExeDeptConfig) {
        if (itemConfigMap != null && itemConfigMap.size() > 0) {
            return !(itemConfigMap.containsKey(item.getItemCode()) || feeCategoryExeDeptConfig.size() == 1);
        } else {
            return true;
        }
    }

    private void setCurDeptAsNullExeDept(List<ChargeItemDto> res, Integer curDeptId, Integer curHospId) {
        for (ChargeItemDto item : res) {
            if (ObjectUtils.isEmpty(item.getExeDept())) {
                item.setExeDept(curDeptId);
                item.setExeHospitalId(curHospId);
            }
        }
    }

    /**
     * 根据收费类型配置，设置此收费类型执行科室&执行院区
     *
     * @param feeCategoryExeDepts
     * @param chargeItemDtoList
     * @return
     */
    private List<ChargeItemDto> getFeeCategoryExeDeptConfigItem(List<MdmPubFeecategoryexedept> feeCategoryExeDepts, List<ChargeItemDto> chargeItemDtoList) {
        //临时list添加所有收费项目
        List<ChargeItemDto> feeCategoryList = new ArrayList<>();

        //只有一条费用类型执行科室配置
        if (feeCategoryExeDepts.size() == 1) {
            MdmPubFeecategoryexedept feeCategoryExedept = feeCategoryExeDepts.get(0);
            Integer configExecHospitalId = feeCategoryExedept.getExecHospitalId();
            Integer configExecDept = feeCategoryExedept.getExecDept();
            for (ChargeItemDto item : chargeItemDtoList) {
                if (isSameExeDept(configExecHospitalId, configExecDept, item.getExeHospitalId(), item.getExeDept())) {
                    feeCategoryList.add(item);
                }
            }
        }
        return feeCategoryList;
    }

    private List<MdmPubFeecategoryexedept> getFeeCategoryExeDeptConfig(Integer itemCategory, Integer hospitalCode, Integer useDept, String curTime) {
        return feecategoryexedeptRepository.getFeeCategoryExecDeptByTime(itemCategory, curTime, useDept, hospitalCode);
    }

    /**
     * 根据收费项目配置，设置此收费项目执行科室&执行院区
     *
     * @param itemExeDeptConfigs
     * @param chargeItemDtoList
     * @return
     */
    private List<ChargeItemDto> getItemExeDeptConfigItem(List<MdmPubItemexedept> itemExeDeptConfigs, List<ChargeItemDto> chargeItemDtoList) {
        //临时list添加符合的收费项目
        List<ChargeItemDto> itemConfigList = new ArrayList<>();
//        for (MdmPubItemexedept m : itemExeDeptConfigs) {
//            Integer configItemCode = m.getItemCode();
//            Integer configExecHospitalId = m.getExecHospitalId();
//            Integer configExecDept = m.getExecDept();
//            for (ChargeItemDto item : chargeItemDtoList) {
//                //是收费项目配置里有的item，但是执行科室&执行院区不同，从临时list中移除
//                if (configItemCode.equals(item.getItemCode()) &&
//                        isSameExeDept(configExecHospitalId, configExecDept, item.getExeHospitalId(), item.getExeDept())) {
//                    itemConfigList.add(item);
//                }
//            }
//        }

        for (ChargeItemDto item : chargeItemDtoList) {
            for (MdmPubItemexedept m : itemExeDeptConfigs) {
                Integer configItemCode = m.getItemCode();
                Integer configExecHospitalId = m.getHospitalId();
                Integer configExecDept = m.getExecDept();
                if (m.getItemCode().equals(item.getItemCode())) {
                    item.setExeDept(configExecDept);
                    item.setExeHospitalId(m.getExecHospitalId());
                    break;
                }
            }
        }

        return chargeItemDtoList;
    }

    private List<MdmPubItemexedept> getItemExeDeptConfig(Integer hospitalCode, Integer useDept, String curTime) {
        return exedeptRepository.getExecDeptList(curTime, useDept, hospitalCode);
    }

    private boolean isSameExeDept(Integer exeHospitalId1, Integer exeDept1, Integer exeHospitalId2, Integer exeDept2) {
        return exeHospitalId1.equals(exeHospitalId2) && exeDept1.equals(exeDept2);
    }

    /**
     * 根据处方项目编码获取收费表对应的医院处方代码和医保处方代码
     *
     * @param itemCodeList
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public List<CodeResponse> getCode(List<Integer> itemCodeList, Integer hospitalCode) {
        if (itemCodeList == null || itemCodeList.size() == 0) {
            return new ArrayList<>();
        }
        return chargeItemMapper.getCode(itemCodeList, hospitalCode);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<CodeResponse> getCodeByItemCode(List<Integer> itemCode, Integer hospitalCode, Integer execDept) {
        if (itemCode == null || itemCode.size() == 0) {
            return new ArrayList<>();
        }
        return chargeItemMapper.getCodeByItemCode(itemCode, hospitalCode, execDept);
    }

    /**
     * 输入码搜索收费项目
     *
     * @param inputCode
     * @param hospitalCode 医院编码
     * @return {@link ChargeItem}
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<ChargeItem> getSimpleChargeItemList(String inputCode, Integer hospitalCode) {
        Weekend<ChargeItem> weekend = new Weekend<>(ChargeItem.class);
//        weekend.selectProperties("itemCode", "itemName", "inputCode1", "inputCode2", "inputCode3", "itemCategory", "hospitalId", "ClinicExpensePrice", "ClinicNonExpensePrice", "ClinicQty", "ClinicUnit");

        final WeekendCriteria<ChargeItem, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ChargeItem::getHospitalId, hospitalCode)
                .andEqualTo(ChargeItem::getStopped, 0).andIn(ChargeItem::getUseRange, Arrays.asList(0, 1));

        if (org.apache.commons.lang3.StringUtils.isNotBlank(inputCode)) {
            inputCode = "%" + inputCode + "%";
            WeekendCriteria<ChargeItem, Object> keywordCriteria1 = weekend.weekendCriteria();
            keywordCriteria1.andLike(ChargeItem::getItemName, inputCode).orLike(ChargeItem::getInputCode1, inputCode)
                    .orLike(ChargeItem::getInputCode2, inputCode).orLike(ChargeItem::getInputCode3, inputCode);
            weekend.and(keywordCriteria1);
        }
        return chargeItemMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<DoctorAntimicrobial> getDoctorAntimicrobial(Integer doctorId, Integer hospitalCode) {
        return doctorAntimicrobialMapper.getDoctorAntimicrobial(doctorId, hospitalCode);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<ChargeItem> getChargeItemByInputCode(String inputCode, List<Integer> itemCategory, Integer hospitalCode) {
        List<Integer> nonDrugItemCategoryList = itemCategory.stream().filter(ItemCategoryEnum::notDrug).collect(Collectors.toList());
        if (nonDrugItemCategoryList.isEmpty()) {
            return new ArrayList<>(0);
        }
        inputCode = inputCode + "%";

        Weekend<ChargeItem> weekend = new Weekend<>(ChargeItem.class);
        WeekendCriteria<ChargeItem, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andEqualTo(ChargeItem::getHospitalId, hospitalCode)
                .andEqualTo(ChargeItem::getStopped, 0)
                .andIn(ChargeItem::getUseRange, Arrays.asList(0, 1))
                .andIn(ChargeItem::getItemCategory, nonDrugItemCategoryList);


        WeekendCriteria<ChargeItem, Object> keywordCriteria2 = weekend.weekendCriteria();
        keywordCriteria2.andLike(ChargeItem::getItemName, inputCode).
                orLike(ChargeItem::getInputCode1, inputCode)
                .orLike(ChargeItem::getInputCode2, inputCode).
                orLike(ChargeItem::getInputCode3, inputCode);
        weekend.and(keywordCriteria2);

        return chargeItemMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<ChargeItemExecDto> getChargeItemDtos(String inputCode, Integer hospitalId) {
        return chargeItemMapper.getChargeItemDtos(inputCode, hospitalId);
    }
}
