package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
    * 门诊病案首页患者信息
    */
@Data
@Table(name = "MZYS_Tb_MedicalPatient")
public class MzysTbMedicalPatient implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 挂号流水号
     */
    @Column(name = "reg_no")
    private Long regNo;

    /**
     * 医疗机构编码
     */
    @Column(name = "org_code")
    private String orgCode;

    /**
     * 医疗机构名称
     */
    @Column(name = "org_name")
    private String orgName;

    /**
     * 病案号
     */
    @Column(name = "medical_no")
    private String medicalNo;

    /**
     * 患者ID
     */
    @Column(name = "patient_id")
    private String patientId;

    /**
     * 患者姓名
     */
    @Column(name = "patient_name")
    private String patientName;

    /**
     * 性别
     */
    @Column(name = "sex")
    private String sex;

    /**
     * 出生日期
     */
    @Column(name = "birthday")
    private Date birthday;

    /**
     * 婚姻状态
     */
    @Column(name = "marry")
    private String marry;

    /**
     * 国籍
     */
    @Column(name = "nationality")
    private String nationality;

    /**
     * 民族
     */
    @Column(name = "nation")
    private String nation;

    /**
     * 证件类型
     */
    @Column(name = "certifier_type")
    private String certifierType;

    /**
     * 证件号
     */
    @Column(name = "certifier_no")
    private String certifierNo;

    /**
     * 现住址
     */
    @Column(name = "address")
    private String address;

    /**
     * 联系电话
     */
    @Column(name = "relationship_tel")
    private String relationshipTel;

    /**
     * 药物过敏史标识 1有0无
     */
    @Column(name = "allergy_flag")
    private Boolean allergyFlag;

    /**
     * 过敏药物
     */
    @Column(name = "allergy_drug")
    private String allergyDrug;

    /**
     * 其他过敏史标识 1有0无
     */
    @Column(name = "allergy_other_flag")
    private Boolean allergyOtherFlag;

    /**
     * 其他过敏原
     */
    @Column(name = "allergy_other")
    private String allergyOther;

    /**
     * 院区ID
     */
    @Column(name = "hospital_id")
    private Integer hospitalId;

    /**
     * 上传状态 0未上传 1上传成功 2上传失败
     */
    @Column(name = "upload_status")
    private Integer uploadStatus;

    /**
     * 上传结果信息
     */
    @Column(name = "upload_msg")
    private String uploadMsg;

    /**
     * 有效状态 1有效 0无效
     */
    @Column(name = "[status]")
    private Boolean status;

    /**
     * 创建人
     */
    @Column(name = "create_by")
    private Integer createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @Column(name = "update_by")
    private Integer updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

}