package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 接诊记录
 *
 * <AUTHOR>
@Data
@Table(name = "MZYS_TB_KZJL")
public class OldReceptionRecord implements Serializable {

    /**
     * 就诊流水号
     */
    @Id
    @Column(name = "jzlsh")
    private String receptionNo;

    /**
     * 挂号流水号
     */
    @Column(name = "ghlsh")
    private String regNo;

    /**
     * 患者编号
     */
    @Column(name = "hzbh")
    private String patId;

    /**
     * 医生编码
     */
    @Column(name = "ysbm")
    private Integer doctorId;

    /**
     * 科室编码
     */
    @Column(name = "ksbm")
    private Integer deptId;

    /**
     * 首次看诊医生
     */
    @Column(name = "sckzys")
    private Integer firstDoctorId;

    /**
     * 首次看诊时间
     */
    @Column(name = "sckzrq")
    private Date firstDate;

    /**
     * 最后看诊医生
     */
    @Column(name = "zhkzys")
    private Integer lastDoctorId;

    /**
     * 最后看诊日期
     */
    @Column(name = "zhkzrq")
    private Date lastDate;

    /**
     * 备注
     */
    @Column(name = "bz")
    private String remark;

    /**
     * 待配药标记
     */
    @Column(name = "dpybz")
    private Integer prescriptionFlag;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OldReceptionRecord that = (OldReceptionRecord) o;
        return regNo.equals(that.regNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(regNo);
    }
}
