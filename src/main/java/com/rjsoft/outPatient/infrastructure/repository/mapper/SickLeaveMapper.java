package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ReceptionRecord;
import com.rjsoft.outPatient.infrastructure.repository.entity.SickLeave;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/29 - 14:32
 */
public interface SickLeaveMapper extends BaseMapper<SickLeave>, ExampleMapper<SickLeave> {


    /**
     * 获取病假单与疾病证明信息
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    String getSickInfo(@Param("regNo") Integer regNo, @Param("hospitalCode") Integer hospitalCode);


    List<ReceptionRecord> queryReceptionListByRegNos(@Param("regNos") List<Integer> regNos, @Param("hospitalCode") Integer hospitalCode);


}
