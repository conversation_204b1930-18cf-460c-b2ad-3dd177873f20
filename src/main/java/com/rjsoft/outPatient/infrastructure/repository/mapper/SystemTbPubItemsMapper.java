package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.recipe.dto.SystemTvPubItemsDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugInfomation;
import com.rjsoft.outPatient.infrastructure.repository.entity.LocalDeptDrug;
import com.rjsoft.outPatient.infrastructure.repository.entity.SystemTbPubItems;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

public interface SystemTbPubItemsMapper extends BaseMapper<SystemTbPubItems>, ExampleMapper<SystemTbPubItems> {

    SystemTvPubItemsDto getChargeItemTv(@Param("itemCode") Integer itemCode,
                                        @Param("hospitalCode") Integer hospitalCode,
                                        @Param("exeDept") Integer exeDept,
                                        @Param("exeHospitalId") Integer exeHospitalId,
                                        @Param("stopped") Integer stopped);

    SystemTbPubItems getChargeItemByIdIsStopped(@Param("itemCode") Integer itemCode,
                                                @Param("deptId") Integer deptId,
                                                @Param("hospitalCode") Integer hospitalCode,
                                                @Param("stopped") Integer stopped,
                                                @Param("ydStopped") Integer ydStopped,
                                                @Param("ypUsed") Integer ypUsed);

       List<SystemTbPubItems> listChargeItemByIdIsStopped(@Param("itemCodeList") List<Integer> itemCodeList,
                                                          @Param("exeDeptIdList") List<Integer> exeDeptIdList,
                                                          @Param("hospitalCode") Integer hospitalCode,
                                                          @Param("stopped") Integer stopped,
                                                          @Param("ydStopped") Integer ydStopped,
                                                          @Param("ypUsed") Integer ypUsed);


    // XXX: yutao 2023/4/17 add 4 重构
    /**
     * <h1>3表关联sql</h1>
     * @param itemCode
     * @param deptId
     * @param hospitalCode
     * @param stopped
     * @param ydStopped
     * @param ypUsed
     * @return
     */
    SystemTbPubItems getChargeItemById(@Param("itemCode") Integer itemCode,
                                       @Param("deptId") Integer deptId,
                                       @Param("hospitalCode") Integer hospitalCode,
                                       @Param("stopped") Integer stopped,
                                       @Param("ydStopped") Integer ydStopped,
                                       @Param("ypUsed") Integer ypUsed);
    /**
     * 多表关联转为select in
     */
    List<SystemTbPubItems> queryChargeItemByItemCodeList(@Param("itemCodeList") List<Integer> itemCodeList,
                                                         @Param("hospitalCode") Integer hospitalCode,
                                                         @Param("stopped") Integer stopped);
    /**
     * 多表关联转为select in
     * @return
     */
    List<DrugInfomation> queryDrugInfoByItemCodeList(@Param("itemCodeList") List<Integer> itemCodeList,
                                                     @Param("hospitalCode") Integer hospitalCode,
                                                     @Param("ydStopped") Integer ydStopped);
    /**
     * 多表关联转为select in
     * @return
     */
    List<LocalDeptDrug> queryLocalDeptDrugByItemCodeList(@Param("itemCodeList") List<Integer> itemCodeList,
                                                         @Param("deptIdList") List<Integer> deptIdList,
                                                         @Param("hospitalCode") Integer hospitalCode,
                                                         @Param("ypUsed") Integer ypUsed);

    List<SystemTbPubItems> queryChargeItemByIdList(@Param("itemCodeList") List<Integer> itemCodeList, @Param("hospitalCode") Integer hospitalCode);

}
