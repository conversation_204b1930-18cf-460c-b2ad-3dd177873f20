package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.config.dto.AddDoctorParams;
import com.rjsoft.outPatient.domain.config.dto.AddDoctorRightRequest;
import com.rjsoft.outPatient.infrastructure.repository.entity.DoctorRight;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 医生权限
 *
 * <AUTHOR>
public interface DoctorRightRepository {

    /**
     * 根据搜索类型获取医院权限列表
     *
     * @param searchType   1 职工 2 权限
     * @param id           职工ID或权限ID
     * @param hospitalCode
     * @return
     */
    List<DoctorRight> getDoctorRight(Integer searchType, Integer id, Integer hospitalCode);

    /**
     * 保存医生对照权限
     *
     * @param doctorRight
     * @return
     */
    boolean saveDoctorRight(DoctorRight doctorRight);

    /**
     * 删除权限
     *
     * @param id
     * @return
     */
    boolean delDoctorRight(List<Integer> id, Integer typeId, Integer hospitalCode);

    /**
     * 根据权限类型和操作员获取权限
     *
     * @param type
     * @param opCode
     * @return
     */
    List<DoctorRight> getInfoByTypeWorkerId(int type, int opCode, Integer hospCode);

    /**
     * 检查是否存在权限
     *
     * @param type
     * @param workerId
     * @param hospitalCode
     * @return
     */
    boolean checkRight(Integer type, Integer workerId, Integer hospitalCode);

    /**
     * 根据type和当前医生id查询告知单类型
     *
     * @param type
     * @param workerId
     * @param hospitalCode
     * @return
     */
    DoctorRight getDoctorType(Integer type, Integer workerId, Integer hospitalCode);

    /**
     * 根据权限类型和医生Id查询医生
     *
     * @param typeId
     * @param hospitalCode
     */
    List<DoctorRight> queryDoctorInfoByRight(Integer typeId, Integer hospitalCode);

    /**
     * 根据传入添加权限列表获取已经存在列表
     *
     * @param params
     */
    List<DoctorRight> getDoctorRightList(@Param("list") List<AddDoctorParams> params);
}
