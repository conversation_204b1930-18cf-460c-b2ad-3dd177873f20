package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.ReceptionRecordAddInfo;

/**
 * <AUTHOR>
 * @since 2021/8/9 - 15:16
 */
public interface ReceptionRecordAddInfoRepository {

    /**
     * 根据就诊流水号查询
     *
     * @param receptionNo  就诊流水号
     * @param hospitalCode 医院编码
     * @return ReceptionRecordAddInfo
     */
    ReceptionRecordAddInfo getReceptionRecordAddInfoById(Long receptionNo, Integer hospitalCode);

}
