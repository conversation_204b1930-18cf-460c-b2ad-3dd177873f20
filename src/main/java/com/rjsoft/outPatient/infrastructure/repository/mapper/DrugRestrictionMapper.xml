<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DrugRestrictionMapper">

    <select id="isNarcotics" resultType="java.lang.Integer">
        select count(1)
        from MZYS_TB_YPXZMX a
        inner join MZYS_TB_YPXZ b on a.xzlxbh = b.xzlxbh and a.yybm = b.yybm
        where b.xzlxbh = #{restrictCode}
          and a.ypbh = #{itemCode}
          and a.yybm = #{hospitalCode}
    </select>

    <select id="listIsNarcoticCount" resultType="com.rjsoft.outPatient.domain.recipe.dto.ItemCountDto">
        select count(1) itemCount,a.ypbh itemCode
        from MZYS_TB_YPXZMX a
        inner join MZYS_TB_YPXZ b on a.xzlxbh = b.xzlxbh and a.yybm = b.yybm
        where b.xzlxbh = #{restrictCode}
          and a.ypbh in <foreach item="item" index="index" collection="itemCodeList" open="(" separator="," close=")">#{item}</foreach>
          and a.yybm = #{hospitalCode}
        group by a.ypbh
    </select>
    <select id="countDrugRestriction" resultType="com.rjsoft.outPatient.domain.recipe.dto.DrugRestrictionDTO">
        select ypbh as drugCode,
               xzlxbh as restrictionCode,
               count(1) as num
        from MZYS_TB_YPXZMX
        <where>
            yybm = #{hospId}
            <if test="itemCodeList.size > 0">
                and ypbh in
                <foreach collection="itemCodeList" index="index" item="itemCode" open="(" separator="," close=")">
                    #{itemCode}
                </foreach>
            </if>
            <if test="enumCodeList.size > 0">
                and xzlxbh in
                <foreach collection="enumCodeList" index="index" item="enumCode" open="(" separator="," close=")">
                    #{enumCode}
                </foreach>
            </if>
        </where>
        GROUP BY ypbh,xzlxbh;
    </select>


</mapper>