package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * 申请单模板
 */
@Data
@Table(name = "MZYS_TB_JCSQD")
public class ApplicationTemplate  implements Serializable {

    @Id
    @Column(name = "sqdlsh", insertable = false, updatable = false)
    private Integer id;

    @Column(name = "sqdbm")
    private String templateNo;

    @Column(name = "sqdmc")
    private String templateName;

    @Column(name = "type")
    private Integer templateType;

    @Column(name = "jclxbm")
    private Integer examineType;

    @Column(name = "jclxmc")
    private String examineName;

    @Column(name = "bdbm")
    private Integer fromId;

    @Column(name = "sqdbz")
    private String remarks;

    /**
     * 申请单XML内容
     */
    @Column(name = "sqdnr")
    private String templateXml;

    @Column(name = "zt")
    private Integer status;

    @Column(name = "cjr")
    private Integer creDoctor;

    @Column(name = "cjrq")
    private Date creTime;

    @Column(name = "xgr")
    private Integer uptDoctor;

    @Column(name = "xgrq")
    private Date uptTime;

    @Column(name = "yybm")
    private Integer hospitalCode;

    @Transient
    @Column(insertable = false, updatable = false)
    private Integer doctorId;

}
