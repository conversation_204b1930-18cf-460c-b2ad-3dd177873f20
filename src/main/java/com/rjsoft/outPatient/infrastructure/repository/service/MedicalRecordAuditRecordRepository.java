package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.MedicalRecordAuditRecord;

import java.util.List;


/**
 * 病历审核记录
 */
public interface MedicalRecordAuditRecordRepository {

    /**
     * 根据条件获取病历审核记录
     *
     * @param checkId
     * @param checkSort
     * @param hospitalCode
     * @return
     */
    List<MedicalRecordAuditRecord> getMedicalRecordAuditRecord(Integer checkId, Integer checkSort, Integer hospitalCode);

}
