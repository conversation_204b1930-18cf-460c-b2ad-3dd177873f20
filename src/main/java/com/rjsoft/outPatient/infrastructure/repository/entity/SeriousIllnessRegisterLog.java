package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rjsoft.outPatient.domain.seriousIllness.bo.DiseaseRegisterBo;
import com.rjsoft.outPatient.domain.seriousIllness.vo.SeriousIllnessRegisterVo;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 大病登记
 *
 * <AUTHOR>
@Data
@Table(name = "MZYS_TB_DBDJD_LOG")
public class SeriousIllnessRegisterLog implements Serializable {

    /**
     * Id
     */
    @Id
    @Column(name = "Id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 大病登记Id
     */
    @Column(name = "serID")
    private Integer serID;

    /**
     * 操作类型
     */
    @Column(name = "operateType")
    private Integer operateType;

    /**
     * 操作时间
     */
    @Column(name = "opTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opTime;

    /**
     * 操作人ID
     */
    @Column(name = "opDoctorId")
    private Integer opDoctorId;

    /**
     * 医院id
     */
    @Column(name = "hospitalId")
    private Integer hospitalId;

    /**
     * 操作医院id
     */
    @Column(name = "opHospitalId")
    private Integer opHospitalId;

    /**
     * 患者姓名
     */
    @Column(name = "patName")
    private String  patName;

    /**
     * 患者身份证号
     */
    @Column(name = "SFZ")
    private String SFZ;

    /**
     * 疾病诊断
     */
    @Column(name = "diagnosis")
    private String diagnosis;

    /**
     * 治疗项目
     */
    @Column(name = "TreatmentItem")
    private String TreatmentItem;

    /**
     * 操作医生名称
     */
    @Column(name = "opDoctorName")
    private String opDoctorName;

    /**
     * 是否老数据，1：老数据
     */
    @Column(name = "oldFlag")
    private Integer oldFlag;

}
