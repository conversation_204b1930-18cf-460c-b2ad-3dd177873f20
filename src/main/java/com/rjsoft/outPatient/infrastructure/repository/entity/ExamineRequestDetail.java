package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 检查申请明细（MZYS库）
 * <AUTHOR>
@Data
@Table(name = "MZYS_TB_JCSQMX")
public class ExamineRequestDetail  implements Serializable {


    /**
     *检查明细流水号
     */
    @Id
    @Column(name = "jcmxlsh")
    private Integer examineDetailNo;

    /**
     *检查流水号
     */
    @Column(name = "jclsh")
    private Integer examineNo;

    /**
     *risId
     */
    @Column(name = "risid")
    private Integer risId;

    /**
     *检查编号
     */
    @Column(name = "jcbh")
    private String examineCode;

    /**
     *处方明细流水号
     */
    @Column(name = "cfmxlsh")
    private Long recipeDetailNo;

    /**
     *处方明细ID
     */
    @Column(name = "cfmxid")
    private Long recipeDetailId;

    /**
     *处方流水号
     */
    @Column(name = "cflsh")
    private Long recipeNo;

    /**
     *处方id
     */
    @Column(name = "cfid")
    private Long recipeId;

    /**
     *项目编码
     */
    @Column(name = "xmbm")
    private Integer itemCode;

    /**
     *项目名称
     */
    @Column(name = "xmmc")
    private String itemName;

    /**
     *收费标记
     */
    @Column(name = "sfbj")
    private  Integer sfFlag;

    /**
     *检查内容
     */
    @Column(name = "jcnr")
    private String content;

    /**
     * 就诊流水号
     */
    @Column(name = "jzlsh")
    public Long receptionNo;

    /**
     * 病历ID
     */
    @Column(name = "blId")
    public Integer blId;

    /**
     *检查部位
     */
    @Column(name = "jcbwbm")
    private String examinePart;

    /**
     *检查部位名称
     */
    @Column(name = "jcbwmc")
    private String examinePartName;

    /**
     * 数量
     */
    @Column(name = "sl")
    private BigDecimal quantity;

    /**
     *操作员
     */
    @Column(name = "czy")
    private Integer operator;

    /**
     *创建日期
     */
    @Column(name = "cjrq")
    private Date createTime;

    /**
     *修改日期
     */
    @Column(name = "xgrq")
    private  Date updateTime;

    /**
     *旧RIS流水号
     */
    @Column(name = "jjclsh")
    private  Integer oldRisId;

    /**
     *收费日期
     */
    @Column(name = "sfrq")
    private Date sfTime;

    /**
     *住院收费标记
     */
    @Column(name = "zysfbj")
    private Integer wardSfFlag;

    /**
     *退费时间
     */
    @Column(name = "tfsj")
    private  Date refundTime;

    /**
     *
     */
    @Column(name = "bwtcbj")
    private  Integer bwTcFlag;

    /**
     *
     */
    @Column(name = "bwsl")
    private  Integer bwNum;

    /**
     *
     */
    @Column(name = "inspectSerialNo")
    private String inspectSerialNo;

    /**
     * 删除标记
     */
    @Column(name = "scbj")
    private Boolean deleteFlag;

    /**
     * 金额
     */
    @Column(name = "je")
    private BigDecimal amount;

    /**
     * 数据标识
     * -1 删除
     * 0 库中不存在
     * 1 默认表中
     * 2 转移表中
     */
    @Transient
    private Integer dataFlag;

    public ExamineRequestDetail() {
    }

    public ExamineRequestDetail(Integer examineNo, Long recipeDetailId, Long receptionNo) {
        this.examineNo = examineNo;
        this.recipeDetailId = recipeDetailId;
        this.receptionNo = receptionNo;
    }
}
