package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.herbAgreeRecipe.dto.AddHerbAgreeRecipeDTO;
import com.rjsoft.outPatient.domain.herbAgreeRecipe.vo.HerbAgreeRecipeDetailReqVO;
import com.rjsoft.outPatient.domain.herbAgreeRecipe.vo.HerbAgreeRecipeDetailVO;
import com.rjsoft.outPatient.domain.herbAgreeRecipe.vo.HerbAgreeRecipeListReqVO;
import com.rjsoft.outPatient.infrastructure.repository.entity.PackageDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.PackagePrimary;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HerbAgreeRecipeMapper {
    List<PackagePrimary> selectList(@Param("reqVO") HerbAgreeRecipeListReqVO herbAgreeRecipeListReqVO);

    List<PackageDetail> selectDetailList(@Param("reqVO") HerbAgreeRecipeDetailReqVO herbAgreeRecipeDetailReqVO);

    void addHerbAgreeRecipe(@Param("recipeDTO") AddHerbAgreeRecipeDTO recipeDTO);

    void batchInsertAgreeRecipeDetail(@Param("agreeRecipeId") Integer agreeRecipeId, @Param("hospitalCode") Integer hospitalCode, @Param("agreeRecipeList") List<HerbAgreeRecipeDetailVO> agreeRecipeList);

    List<PackagePrimary> selectListByName(@Param("agreeRecipeName") String agreeRecipeName,
                                          @Param("shareFlag") Integer shareFlag,
                                          @Param("hospitalCode") Integer hospitalCode,
                                          @Param("deptId") Integer deptId,
                                          @Param("doctorId") Integer doctorId);
}
