package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.EmrTbRecinfo;
import com.rjsoft.outPatient.infrastructure.repository.mapper.EmrTbRecinfoMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.EmrTbRecinfoRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
@AllArgsConstructor
public class EmrTbRecinfoRepositoryImpl implements EmrTbRecinfoRepository {
    EmrTbRecinfoMapper emrTbRecinfoMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<EmrTbRecinfo> getEmrByRegNo(List<Integer> regNos, List<String> recTempCodes, int hospitalCode) {
        if(regNos==null||regNos.size()==0||recTempCodes==null||recTempCodes.size()==0){
            return new ArrayList<>();
        }
        return emrTbRecinfoMapper.getEmrByRegNo(regNos,recTempCodes, hospitalCode);
    }
}
