package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.MdmPubFeecategoryexedept;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.HashMap;
import java.util.List;

@Mapper
public interface MdmPubFeecategoryexedeptMapper extends BaseMapper<MdmPubFeecategoryexedept>, ExampleMapper<MdmPubFeecategoryexedept> {
    List<HashMap<String,String>> getFeeCategoryExecDept(Integer FeeCategoryCode, Integer useDeptId, String startTime, String endTime,Integer id,Integer curHospitalId);

    List<MdmPubFeecategoryexedept> getFeeCategoryExecDeptByTime(@Param("feeCategory") Integer feeCategory,
                                                                @Param("curTime") String curTime,
                                                                @Param("curDeptId") Integer curDeptId,
                                                                @Param("curHospitalId") Integer curHospitalId);

    List<MdmPubFeecategoryexedept> getExecDeptList(@Param("curTime") String curTime,
                                                   @Param("curDeptId") Integer curDeptId,
                                                   @Param("curHospitalId") Integer curHospitalId);
}
