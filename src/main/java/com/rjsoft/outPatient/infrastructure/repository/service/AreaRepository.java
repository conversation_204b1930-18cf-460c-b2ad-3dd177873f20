package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.Area;

import java.util.List;

/**
 * 地区
 *
 * <AUTHOR>
public interface AreaRepository {
    /**
     * 查询地区根据parentId
     *
     * @param parentId
     * @return
     */
    public List<Area> getAreaByParentId(String parentId);

    /**
     * 查询地区根据code
     *
     * @param code
     * @return
     */
    public List<Area> getAreaByCode(String code);
}
