package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.*;

import java.util.List;

/**
 * 门诊处方扩展
 *
 * <AUTHOR>
public interface RecipeEXRepository {
    /**
     * 根据处方id查询处方扩展信息列表
     *
     * @param recipeNos
     * @return
     */
    List<RecipeEX> getRecipeEXByRecipeNos(List<Long> recipeNos, Integer hospitalCode);

    /**
     * 根据处方id查询处方扩展信息
     *
     * @param recipeNo
     * @return
     */
    RecipeEX getRecipeEXByRecipeNo(Long recipeNo, Integer hospitalCode);

    /**
     * 修改草药处方扩展信息
     *
     * @param recipeNoList
     * @param hospitalCode
     * @return
     */
    Integer updateHerbRecipeExPrintStatus(List<Long> recipeNoList, Integer hospitalCode);
}