<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ReportXdtMapper">

    <select id="getXdtReport" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ReportXdt">
        select *
         from Tbv_GetReportXdt
        where (regNo =#{regNo}  or cardNo = #{cardNo} ) and jcTypeCode = 'XDT'
    </select>
</mapper>