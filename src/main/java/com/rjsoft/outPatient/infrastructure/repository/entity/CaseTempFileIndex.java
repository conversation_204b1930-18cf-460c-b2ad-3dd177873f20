package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2021/8/9 10:30
 * @description 模板文件主键
 **/
@Data
@Table(name = "MZYS_TB_MBXML_INDEX")
public class CaseTempFileIndex  implements Serializable {

    @Id
    @Column(name = "nextwjbm",insertable = false,updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer fileNo;

    @Column(name = "value")
    private Integer value;


}
