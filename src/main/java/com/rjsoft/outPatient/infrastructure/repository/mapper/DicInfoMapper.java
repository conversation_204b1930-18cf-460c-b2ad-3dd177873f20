package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.DicInfo;
import com.ruijing.code.util.StringUtils;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface DicInfoMapper extends BaseMapper<DicInfo>, ExampleMapper<DicInfo> {

    /**
     * 根据大写首拼获取字典信息
     *
     * @param dicTypeSpell 大写首拼
     * @return
     */
    default List<DicInfo> getDicInfo(@Param("spell") String dicTypeSpell) {
        Weekend<DicInfo> weekend = new Weekend<>(DicInfo.class);
        WeekendCriteria<DicInfo, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andLike(DicInfo::getDicTypeSpell, dicTypeSpell);
        weekendCriteria.andEqualTo(DicInfo::getDelStatus, 0);
        weekend.setOrderByClause("dic_code asc");
        return selectByExample(weekend);
    }


    default DicInfo getDicInfoByCode(String dicTypeSpell, String dicName, String dicCode) {
        Weekend<DicInfo> weekend = new Weekend<>(DicInfo.class);
        WeekendCriteria<DicInfo, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andLike(DicInfo::getDicTypeSpell, dicTypeSpell);
        weekendCriteria.andEqualTo(DicInfo::getDelStatus, 0);
        if (!StringUtils.isNullOrEmpty(dicName)) {
            weekendCriteria.andEqualTo(DicInfo::getDicName, dicName);
        }
        if (!StringUtils.isNullOrEmpty(dicCode)) {
            weekendCriteria.andEqualTo(DicInfo::getDicCode, dicCode);
        }
        return selectOneByExample(weekend);
    }
}
