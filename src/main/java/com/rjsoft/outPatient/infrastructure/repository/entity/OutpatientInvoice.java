package com.rjsoft.outPatient.infrastructure.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-10-27
 * 门诊发票
 */

@Data
@Table ( name ="Reg_Tb_OutpatientInvoice_Time" )
public class OutpatientInvoice implements Serializable {

	private static final long serialVersionUID =  6529917861702231354L;

   	@Column(name = "InvoiceID" )
	private Long invoiceId;

   	@Column(name = "ReturnInvoiceID" )
	private Long returnInvoiceId;

   	@Column(name = "RegNo" )
	private Long regNo;

   	@Column(name = "CardType" )
	private String cardType;

   	@Column(name = "CardNo" )
	private String cardNo;

   	@Column(name = "OutpatientNo" )
	private String outpatientNo;

   	@Column(name = "InvoiceNo" )
	private Long invoiceNo;

   	@Column(name = "PatID" )
	private Long patId;

   	@Column(name = "NewPatID" )
	private Long newPatId;

   	@Column(name = "PatName" )
	private String patName;

   	@Column(name = "ChargeType" )
	private Long chargeType;

   	@Column(name = "InvoicePrefix" )
	private String invoicePrefix;

   	@Column(name = "InvoiceLabel" )
	private String invoiceLabel;

   	@Column(name = "InvoiceInfo" )
	private String invoiceInfo;

   	@Column(name = "OpCode" )
	private Long opCode;

   	@Column(name = "OpTime" )
	private Date opTime;

   	@Column(name = "IsPrint" )
	private Long isPrint;

   	@Column(name = "Printer" )
	private Long printer;

   	@Column(name = "PrintTime" )
	private Date printTime;

   	@Column(name = "TotalAmount" )
	private Double totalAmount;

   	@Column(name = "CapitalAmount" )
	private String capitalAmount;

   	@Column(name = "OwnFee" )
	private Double ownFee;

   	@Column(name = "InsuranceTotal" )
	private Double insuranceTotal;

   	@Column(name = "InsuranceCashTotal" )
	private Double insuranceCashTotal;

   	@Column(name = "CostTotal" )
	private Double costTotal;

   	@Column(name = "FLPay" )
	private Double flPay;

   	@Column(name = "PubPay" )
	private Double pubPay;

   	@Column(name = "AppendPay" )
	private Double appendPay;

   	@Column(name = "CurrAccountPay" )
	private Double currAccountPay;

   	@Column(name = "LastAccountPay" )
	private Double lastAccountPay;

   	@Column(name = "CurrAccountBalance" )
	private Double currAccountBalance;

   	@Column(name = "LastAccountBalance" )
	private Double lastAccountBalance;

   	@Column(name = "HisCardFee" )
	private Double hisCardFee;

   	@Column(name = "PayFee" )
	private Double payFee;

   	@Column(name = "CashFee" )
	private Double cashFee;

   	@Column(name = "ReceiveAmount" )
	private Double receiveAmount;

   	@Column(name = "ChangeAmount" )
	private Double changeAmount;

   	@Column(name = "ErrorCents" )
	private Double errorCents;

   	@Column(name = "Status" )
	private Long status;

   	@Column(name = "UseFlag" )
	private Long useFlag;

   	@Column(name = "BussEvent" )
	private Long bussEvent;

   	@Column(name = "BillNo" )
	private Long billNo;

   	@Column(name = "Flag" )
	private Long flag;

   	@Column(name = "TradeSerialNo" )
	private String tradeSerialNo;

   	@Column(name = "PrepaidRemind" )
	private Double prepaidRemind;

   	@Column(name = "PrepaidPay" )
	private Double prepaidPay;

   	@Column(name = "PrepaidId" )
	private Long prepaidId;

   	@Column(name = "DataFrom" )
	private Long dataFrom;

   	@Column(name = "FromFlag" )
	private Long fromFlag;

   	@Column(name = "Rate" )
	private Double rate;

   	@Column(name = "DiscountAmount" )
	private Double discountAmount;

   	@Column(name = "ChargeNo" )
	private Long chargeNo;

   	@Column(name = "OriginalInvoiceId" )
	private Long originalInvoiceId;

   	@Column(name = "ComputerNo" )
	private String computerNo;

   	@Column(name = "TerminalType" )
	private Long terminalType;

   	@Column(name = "PayOrderNo" )
	private String payOrderNo;

   	@Column(name = "ReturnOrderNo" )
	private String returnOrderNo;

   	@Column(name = "DispensingWindow" )
	private String dispensingWindow;

   	@Column(name = "IsDelete" )
	private Boolean isDelete;

   	@Column(name = "CreatedBy" )
	private Long createdBy;

   	@Column(name = "CreatedDate" )
	private Date createdDate;

   	@Column(name = "UpdateBy" )
	private Long updateBy;

   	@Column(name = "UpdateDate" )
	private Date updateDate;

   	@Column(name = "ReturnOpCode" )
	private Long returnOpCode;

   	@Column(name = "ReturnOpTime" )
	private Date returnOpTime;

   	@Column(name = "DrugDept" )
	private Long drugDept;

   	@Column(name = "HospitalCode" )
	private Long hospitalCode;

   	@Column(name = "SerialNo" )
	private Long serialNo;

   	@Column(name = "MzNo" )
	private Long mzNo;

   	@Column(name = "DeptKind" )
	private Integer deptKind;

   	@Column(name = "JzPay" )
	private Double jzPay;

   	@Column(name = "RecipeDoctor" )
	private Long recipeDoctor;

   	@Column(name = "RecipeDept" )
	private Long recipeDept;

   	@Column(name = "AccountFlag" )
	private String accountFlag;

   	@Column(name = "DrugInfo" )
	private String drugInfo;

   	@Column(name = "PayType" )
	private Long payType;

   	@Column(name = "PrintCount" )
	private Long printCount;

	@Column(name = "RemarkAI" )
	private String remarkAI;

}
