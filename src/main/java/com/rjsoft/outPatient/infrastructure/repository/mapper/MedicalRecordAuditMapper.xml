<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.MedicalRecordAuditMapper">


    <select id="getDeptMedicalRecordAuditList"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.MedicalRecordAudit">
        select b.ghlsh regNo,
        a.blkh blCardNo,
        a.id blId,
        a.blHtml blHtml,
        a.ysbm caseHistoryDoctorId,
        c.Id id,
        b.jzlsh receptionNo,
        b.sckzrq firstDate,
        b.ysbm receptionDoctorId,
        b.ksbm receptionDeptId,
        c.CheckSort checkSort,
        isnull(a.dybj,0) printFlag,
        a.hospitalCode
        from MZYS_TB_DZBL a
        inner join MZYS_TB_KZJL b on a.jzlsh = b.jzlsh and a.hospitalCode = b.hospitalCode
        left join MZYS_TB_DZBLSH c on a.id = c.Blid and a.hospitalCode = c.HospitalCode
        where b.sckzrq between #{startTime} and #{endTime}
        and a.HospitalCode = #{hospitalCode}
        and a.tjbj=1
        <if test="doctorId != null and doctorId !='' ">
            and a.ysbm = #{doctorId}
        </if>
        <if test="deptId != null and deptId !=''">
            and a.ksbm = #{deptId}
        </if>
        <if test="auditFlag != null and auditFlag !=''">
            and ((1 = #{auditFlag} and a.csbj = 0) or (2 = #{auditFlag} and a.csbj = 1))
        </if>
        <if test="hisCardNo != null and hisCardNo !=''">
            and a.blkh = #{hisCardNo}
        </if>
        <if test="patName != null and patName !=''">
            and a.hzxm = #{patName}
        </if>
        <if test="visitFlag != null and visitFlag !=''">
            and ((0 = #{visitFlag} and a.czbj = 1) or (1 = #{visitFlag} and a.czbj = 0))
        </if>
    </select>

    <select id="getSpotCheckList"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.MedicalRecordAudit">
        select b.ghlsh regNo,
        a.blkh blCardNo,
        a.id blId,
        a.blHtml blHtml,
        a.ysbm caseHistoryDoctorId,
        c.Id id,
        b.jzlsh receptionNo,
        b.sckzrq firstDate,
        b.ysbm receptionDoctorId,
        b.ksbm receptionDeptId,
        c.CheckSort checkSort,
        isnull(a.dybj,0) printFlag,
        a.hospitalCode
        from MZYS_TB_DZBL a
        inner join MZYS_TB_KZJL b on a.jzlsh = b.jzlsh and a.hospitalCode = b.hospitalCode
        inner join MZYS_TB_DZBLSH c on a.id = c.Blid and a.hospitalCode = c.HospitalCode
        where b.sckzrq between #{startTime} and #{endTime}
        and a.HospitalCode = #{hospitalCode}
        and c.CheckSort=2
        and not exists(select * from MZYS_TB_DZBLSHJL g where c.id=g.CheckId and g.CheckSort=2 )
        <if test="deptId != null and deptId !=''">
            and a.ksbm = #{deptId}
        </if>
    </select>

    <select id="getCheckList"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.MedicalRecordAudit">
        select b.ghlsh regNo,
        a.blkh blCardNo,
        a.id blId,
        a.blHtml blHtml,
        a.ysbm caseHistoryDoctorId,
        c.Id id,
        b.jzlsh receptionNo,
        b.sckzrq firstDate,
        b.ysbm receptionDoctorId,
        b.ksbm receptionDeptId,
        c.CheckSort checkSort,
        isnull(a.dybj,0) printFlag,
        a.hospitalCode
        from MZYS_TB_DZBL a
        inner join MZYS_TB_KZJL b on a.jzlsh = b.jzlsh and a.hospitalCode = b.hospitalCode
        left join MZYS_TB_DZBLSH c on a.id = c.Blid and a.hospitalCode = c.HospitalCode
        where b.sckzrq between #{startTime} and #{endTime}
        and a.HospitalCode = #{hospitalCode}
        and a.tjbj=1
        <if test="doctorId != null and doctorId !='' ">
            and a.ysbm = #{doctorId}
        </if>
        <if test="deptId != null and deptId !=''">
            and a.ksbm = #{deptId}
        </if>
    </select>

</mapper>