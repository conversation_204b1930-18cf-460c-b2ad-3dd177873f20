<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ExecDeptTimeMapper">


    <select id="getExecDeptTime"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ExecDeptTime">
        select DeptType     deptType,
               FromTime     starTime,
               ToTime       endTime,
               DeptXY       westernMed,
               DeptZCHY     chinesePatMed,
               DeptZCY      chineseHerbMed,
               HospitalCode hospitalCode
        from MZYS_TB_DrugDeptToTime
        where DeptType = #{deptType}
          and (Convert(char (10), getdate(), 120) + ' ' + FromTime) &lt;= Convert(char (16), GETDATE(), 120)
          and (Convert(char (10), getdate(), 120) + ' ' + ToTime) &gt;= Convert(char (16), GETDATE(), 120)
          and HospitalCode = #{hospitalCode}
    </select>
</mapper>