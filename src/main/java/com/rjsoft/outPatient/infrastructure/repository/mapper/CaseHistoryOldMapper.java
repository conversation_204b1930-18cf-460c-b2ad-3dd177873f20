package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.outPatient.domain.caseHistory.dto.*;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.CaseHistory;
import com.rjsoft.outPatient.infrastructure.repository.entity.CaseHistoryOld;
import com.rjsoft.outPatient.infrastructure.repository.entity.SFCaseHistory;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.*;

public interface CaseHistoryOldMapper extends Mapper<CaseHistoryOld> {

}
