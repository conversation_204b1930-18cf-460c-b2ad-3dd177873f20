package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.param.SearchParam;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatBlParam;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatientDTO;
import com.rjsoft.outPatient.domain.diagnose.dto.GeneralDiagnoseDto;
import com.rjsoft.outPatient.domain.diagnose.dto.MajorIoDiagnoseDto;
import com.rjsoft.outPatient.domain.diagnose.dto.MajorRegNoDto;
import com.rjsoft.outPatient.domain.reception.dto.ReceptionRegisterDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.IoTbInpatient;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzcfmxGzdEntity;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * 挂号信息
 *
 * <AUTHOR>
public interface RegisterListRepository {

    /**
     * 保存挂号主表，同步老表（门诊医生主要用于科研不挂号收费）
     *
     * @param registerList
     * @return
     */
    boolean saveRegisterList(RegisterList registerList);

    /**
     * 根据患者id查询挂号信息的挂号流水号
     *
     * @param patId 患者id
     * @return
     */
    List<Long> getRegisterListRegNosByPatId(Integer patId);

    /**
     * 获取挂号记录
     *
     * @param param
     * @return
     */
    List<RegisterList> getPageRegisterListByDoctor(SearchParam param);

    List<RegisterList> getRegister(Integer hospitalCode, String workerId, String deptId, Long regNo);


    /**
     * 获取挂号总数
     *
     * @param param
     * @return
     */
    HashMap<String, Integer> getRegisterCountByDoctor(SearchParam param);

    /**
     * 根据病历卡号获取挂号记录
     *
     * @param HisCardNo
     * @param hospitalCode
     * @return
     */
    RegisterList getRegisterByHisCardNo(String HisCardNo, Integer hospitalCode);

    /**
     * 根据挂号流水号获取挂号记录
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    RegisterList getRegisterDtoByRegNo(Long regNo, Integer hospitalCode);

    List<RegisterList> getRegisterDtoByRegNoList(List<Long> regNoList, Integer hospitalCode);

    List<RegisterList> getRegisterByRegNos(List<Long> regNo, Integer hospitalCode);

    /**
     * 根据挂号流水号获取挂号记录
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    RegisterList getRegisterByRegNo(Long regNo, Integer hospitalCode);

    /*
    /**
     * 查询医生职级
     *
     * @param doctorId     医生id
     * @param hospitalCode 医院编码
     * @return {@link RegisterList}
     */
    //RegisterList getRegisterDtoByRegNo(Integer doctorId, Integer hospitalCode);

    /**
     * 根据挂号病历卡号查询挂号指定区间所有挂号
     *
     * @param patIds
     * @param hospitalCode
     * @param startTime
     * @param endDate
     * @return
     */
    List<RegisterList> getRegisterByPatId(List<Integer> patIds, Date startTime, Date endDate, Integer hospitalCode);


    /**
     * 根据patid查询所有住院记录
     *
     * @param patIds
     * @param hospitalCode
     * @param startTime
     * @param endDate
     * @return
     */
    List<IoTbInpatient> getIOInPatientByPatIds(List<Integer> patIds, String startTime, String endDate, Integer hospitalCode);

    /**
     * 根据患者卡号查询挂号指定区间所有状态正常的医保挂号记录
     *
     * @param cardNo       患者卡号
     * @param startTime    开始时间
     * @param endDate      结束时间
     * @param hospitalCode 医院编码
     * @return
     */
    List<RegisterList> getRegisterByCardNo(String cardNo, Date startTime, Date endDate, Integer hospitalCode);

    /**
     * 门诊处方明细告知单保存
     *
     * @param mzcfmxGzdEntity
     * @return
     */
    int saveMzcfmxGzd(MzcfmxGzdEntity mzcfmxGzdEntity);

    /**
     * 修改门诊处方明细告知单
     *
     * @param mzcfmxGzdEntity
     * @return
     */
    int updateMzcfmxGzd(MzcfmxGzdEntity mzcfmxGzdEntity);

    /**
     * 根据处方流水号和项目编码查询是否存在告知单信息
     *
     * @param cflsh
     * @param itemId
     * @param hospitalCode
     * @return
     */
    MzcfmxGzdEntity getMzCfMxGzd(Long cflsh, Integer itemId, Integer hospitalCode);

    @DatabaseAnnotation
    List<MzcfmxGzdEntity> getMzCfMxGzdList(List<Long> cflshList, List<Integer> itemIdList, Integer hospitalCode);

    /**
     * 删除门诊处方明细告知单
     *
     * @param id
     * @return
     */
    int deleteMzCfMxGzd(Long id, Long cflsh);

    @DatabaseAnnotation
    int deleteMzCfMxGzdList(Long id, Long cflsh);

    /**
     * 根据挂号流水号+医生编码+医院编码获取挂号信息
     *
     * @param regNo
     * @param doctorId
     * @param hospCode
     * @return
     */
    RegisterList getRegisterListByRegNoAndDocId(Long regNo, Integer doctorId, Integer hospCode);

    /**
     * 根据医生编码+医院编码获取具体某一天的挂号信息
     *
     * @param registTime
     * @param doctorId
     * @param hospCode
     * @return
     */
    List<RegisterList> getDayRegisterInfo(Date registTime, Integer doctorId, Integer hospCode);

    /**
     * 查询当前挂号同一个医生同一个患者挂号信息
     *
     * @param registTime
     * @param patId
     * @param doctorId
     * @param hospCode
     * @return
     */
    List<RegisterList> getRegisterListByDay(Date registTime, Integer patId, Integer doctorId, Integer hospCode, String tbName);

    /**
     * 根据挂号时间区间，加载出所有挂号记录，用户匹配是否存在当日挂号记录
     *
     * @param minDate
     * @param maxDate
     * @param patId
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    List<RegisterList> getRegisterListByDay(Date minDate, Date maxDate, List<Long> patId, List<Integer> doctorId, Integer hospitalCode);

    /**
     * 根据挂号流水号、收费类型查询
     *
     * @param regNo    挂号流水号
     * @param hospCode 医院编码
     * @return 患者 id
     */
    RegisterList getPatIdByRegNoAndChargeType(Long regNo, Integer hospCode);

    /**
     * 根据患者编号列表获取所有患者信息
     *
     * @param patIds
     * @param hospitalId
     * @return
     */
    List<RegisterList> getRegisterListByPatIds(List<Integer> patIds, Integer hospitalId);

    /**
     * 通过患者ID查询所有挂号信息
     *
     * @param patId 身份证
     * @return regNos
     */
    List<Long> getAllRegNoByPatId(Integer patId);

    /**
     * 通过患者ID查询所有挂号信息
     *
     * @param patIds 患者ID
     * @return regNos
     */
    List<Long> queryAllRegNoByPatIds(List<Integer> patIds);

    /**
     * 通过身份证号查询所有挂号信息
     *
     * @param certificateNo 身份证
     * @return regNos
     */
    List<Long> getAllRegNoByCertificateNo(String certificateNo);

    /**
     * 通过身份证号查询所有挂号信息的挂号时间
     *
     * @param certificateNo 身份证
     * @return regNos
     */
    Date getAllRegistTimeByCertificateNo(String certificateNo, Integer hospitalCode);

    /**
     * 通过patId查询所有挂号信息
     *
     * @param patId
     */
    List<MajorRegNoDto> getMajorAllRegNoByPatId(Integer patId);

    /**
     * 从旧数据中根据身份证查询所有挂号流水号
     *
     * @param patNo
     * @param hospitalCode  医院编码
     * @return regNos
     */
    List<MajorRegNoDto> getMajorAllRegNoFromOldData(Integer patNo, int hospitalCode);

    /**
     * 从旧数据中根据身份证查询所有挂号流水号
     *
     * @param patNos
     * @param hospitalCode  医院编码
     * @return regNos
     */
    List<MajorRegNoDto> queryMajorAllRegNoFromOldData(List<Integer> patNos, int hospitalCode);

    /**
     * 查询住院所有的挂号流水号
     *
     * @param patId
     * @return regNos
     */
    List<Long> getIOAllRegNosByPatId(Integer patId);

    /**
     * 查询住院所有的查房信息
     *
     * @param regNo 身份证
     * @return regNos
     */
    HashMap<String, Integer> getIOCheckInfoByRegNo(Long regNo);

    /**
     * 查询住院的诊断信息
     *
     * @param regNo 身份证
     * @return regNos
     */
    List<MajorIoDiagnoseDto> getIODiagnoseInfoByRegNo(Long regNo);

    /**
     * 从旧数据中根据身份证查询所有挂号流水号
     *
     * @param certificateNo 身份证
     * @param hospitalCode  医院编码
     * @return regNos
     */
    List<String> getAllRegNoFromOldData(String certificateNo, int hospitalCode);


    /**
     * 从旧数据中根据patId查询所有挂号流水号
     *
     * @param patId 身份证
     * @param hospitalCode  医院编码
     * @return regNos
     */
    List<String> getAllRegNoFromOldDataByPatId(Integer patId, int hospitalCode);

    /**
     * 从旧数据中根据patId查询所有挂号流水号
     *
     * @param patIds
     * @param hospitalCode  医院编码
     * @return regNos
     */
    List<String> getAllRegNoFromOldDataByPatIds(List<Integer> patIds, int hospitalCode,Date startTime, Date endTime);

    /**
     * 根据挂号流水号获取挂号信息
     *
     * @param regNo
     * @return
     */
    ReceptionRegisterDto getRegTvRegisterList(Long regNo);

    /**
     * 根据挂号流水号列表，查询是否有已删除挂号信息
     *
     * @param regNos
     */
    List<Long> selectInvalidRegNos(@Param("list") Set<Long> regNos);

    /**
     * 根据身份证号获取患者诊断记录[总院 106 ]
     *
     * @param certificateNo
     */
    List<GeneralDiagnoseDto> queryDiagnoses(String certificateNo);

    /**
     * 根据身份证号获取患者诊断记录[分院 107 ]
     *
     * @param certificateNo
     */
    List<GeneralDiagnoseDto> queryDiagnosesBranch(String certificateNo);

    /**
     * 根据挂号流水号获取相关信息
     * 【病历补写】
     *
     * @param regNos
     */
    List<PatBlDto> getPatBlInfoByRegNos(List<String> regNos, String hisCardNo, String patName);

    /**
     * 获取
     * 【病历补写】
     *
     * @param param
     */
    List<PatBlDto> getNoFilledPatBlInfo(PatBlParam param);

    /**
     * 获取未提交患者病历列表
     * 【病历补写】
     *
     * @param param
     */
    List<PatBlDto> getNoCommittedPatBlInfo(PatBlParam param);

    /**
     * 获取未提交患者病历列表
     * 【病历补写】
     *
     * @param param
     */
    List<PatBlDto> getNoSignedPatBlInfo(PatBlParam param);

    /**
     * 根据挂号流水号和医院编码查询挂号信息
     *
     * @param regNo
     * @param hospitalCode
     */
    RegisterList getRegisterInfoByRegNo(Long regNo, Integer hospitalCode);

    /**
     * 根据挂号流水号和医院编码查询挂号信息
     *
     * @param regNo
     * @param hospitalCode
     */
    RegisterList getAllRegisterInfoByRegNo(Long regNo, Integer hospitalCode);

    /**
     * 根据挂号流水号查询患者信息
     *
     * @param regNo
     */
    PatientDTO getRegisterInfoByRegNo(String regNo);

    /**
     * 根据patid查询患者挂号列表
     *
     * @param param
     */
    List<RegisterList> getPageRegisterListByPatid(SearchParam param);

    /**
     * 根据处方明细id查询告知单列表
     *
     * @param recipeDetailNo
     */
    MzcfmxGzdEntity getMzCfMxGzdByMxlsh(Long recipeDetailNo, Integer hospitalCode);

    /**
     * 查询老系统所有挂号信息
     *
     * @param certificateNo
     * @param hospitalCode
     * @return
     */
    List<RegisterList> getRegisterListFromOldData(String certificateNo, Integer hospitalCode, Date startTime, Date endTime);


    /**
     * 查询老系统所有挂号信息
     *
     * @param patNo
     * @param hospitalCode
     * @param startTime
     * @param endTime
     * @return
     */
    List<RegisterList> getRegisterListOldDataByPatNo(Integer patNo, Integer hospitalCode, Date startTime, Date endTime);


    /**
     * 更新系统中挂号信息中的号源
     *
     * @param regNo
     * @param hospitalCode
     * @param registOrder
     * @return
     */
    Boolean updateRegistOrderByRegNo(Long regNo, Integer hospitalCode, Integer registOrder);

    /**
     * 更新系统中挂号信息中vip标准
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    Boolean updateRegistVipFlag(Long regNo, Integer hospitalCode);

    /**
     * 根据患者编号列表获取指定时间段内所有患者信息
     *
     * @param patIds
     * @param hospitalId
     * @param startTime
     * @param endTime
     * @return
     */
    List<RegisterList> getRegisterListByPatIds(List<Integer> patIds, Integer hospitalId, Date startTime, Date endTime);

    /**
     * 根据患者编号列表获取指定时间段内所有患者信息
     *
     * <AUTHOR>
     * @param regNo 挂号流水号
     * @param hospitalCode 医院编码
     * @param insuType 险种类型
     * @return
     */
    Integer verifyIsJMYB(Integer regNo, Integer hospitalCode, String insuType);

    /**
     * 根据病历卡号查询患者
     *
     * @param hospitalCode 医院编码
     * @param doctorId     医生编码
     * @param deptId    科室编码
     * @param outPatientNo    病历卡号
     */
    List<RegisterList> getByOutPatientNo(Integer hospitalCode, Integer doctorId, Integer deptId, String outPatientNo);

    List<RegisterList> listRegisterByRegNo(Integer hospitalCode, List<Long> regNoList);

}
