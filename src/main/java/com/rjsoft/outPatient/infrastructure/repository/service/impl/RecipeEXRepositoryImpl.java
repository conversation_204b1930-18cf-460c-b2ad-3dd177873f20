package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeEX;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeEXMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeEXRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

/**
 * 门诊处方扩展
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class RecipeEXRepositoryImpl implements RecipeEXRepository {
    RecipeEXMapper recipeEXMapper;

    @Override
    @DatabaseAnnotation
    public List<RecipeEX> getRecipeEXByRecipeNos(List<Long> recipeNos,Integer hospitalCode) {
        Weekend<RecipeEX> weekend = new Weekend<>(RecipeEX.class);
        weekend.weekendCriteria().andIn(RecipeEX::getRecipeNo,recipeNos).andEqualTo(RecipeEX::getHospitalCode,hospitalCode);
        return recipeEXMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public RecipeEX getRecipeEXByRecipeNo(Long recipeNo, Integer hospitalCode) {
        Weekend<RecipeEX> weekend = new Weekend<>(RecipeEX.class);
        weekend.weekendCriteria().andEqualTo(RecipeEX::getRecipeNo,recipeNo).andEqualTo(RecipeEX::getHospitalCode,hospitalCode);
        return recipeEXMapper.selectOneByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public Integer updateHerbRecipeExPrintStatus(List<Long> recipeNoList, Integer hospitalCode) {
        RecipeEX recipeEX = new RecipeEX();
        recipeEX.setPrintStatus(1);
        Weekend<RecipeEX> weekend = new Weekend<>(RecipeEX.class);
        weekend.weekendCriteria().andIn(RecipeEX::getRecipeNo,recipeNoList).andEqualTo(RecipeEX::getHospitalCode,hospitalCode);
        return recipeEXMapper.updateByExampleSelective(recipeEX,weekend);
    }
}
