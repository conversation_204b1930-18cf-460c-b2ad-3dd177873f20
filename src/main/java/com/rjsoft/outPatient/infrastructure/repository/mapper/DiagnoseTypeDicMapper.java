package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.diagnose.dto.DiagnoseCodeName;
import com.rjsoft.outPatient.infrastructure.repository.entity.DiagnoseTypeDic;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface DiagnoseTypeDicMapper extends BaseMapper<DiagnoseTypeDic>, ExampleMapper<DiagnoseTypeDic> {

    /**
     * 查询诊断字字典
     *
     * @param inputCode
     * @return
     */
    List<DiagnoseTypeDic> getDiagnoseListByType(@Param("inputCode") String inputCode, @Param("inputType") String inputType);


    /**
     * 根据诊断编码获取 诊断名称
     *
     * @param bms
     */
    default List<DiagnoseTypeDic> getDiagnoseTypeName(List<String> bms) {
        Weekend<DiagnoseTypeDic> weekend = new Weekend<DiagnoseTypeDic>(DiagnoseTypeDic.class);
        WeekendCriteria<DiagnoseTypeDic, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(DiagnoseTypeDic::getDiagnoseCode, bms);
        return selectByExample(weekend);
    }

    /**
     * 根据诊断编码获取 诊断名称
     *
     * @param bm
     */
    default DiagnoseTypeDic getDiagnoseTypeName(String bm) {
        DiagnoseTypeDic dic = new DiagnoseTypeDic();
        dic.setDiagnoseCode(bm);
        dic.setIsStop(false);
        return selectOne(dic);
    }


    /**
     * 根据诊断编码获取诊断名称
     *
     * @param bms
     */
    List<DiagnoseCodeName> getOldDiagnoseTypeName(@Param("list") List<String> bms);

    List<DiagnoseTypeDic> queryDiagnoseListByCodes(@Param("diagCodeList") List<String> diagCodeList);


}
