<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.HerbAgreeRecipeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="MainResultMap" type="com.rjsoft.outPatient.infrastructure.repository.entity.PackagePrimary">
        <id column="tcid" property="packageId"/>
        <result column="sjjd" property="parentNode"/>
        <result column="tcmc" property="packageName"/>
        <result column="tclx" property="packageCategory"/>
        <result column="ksbm" property="deptId"/>
        <result column="type" property="type"/>
        <result column="cjr" property="createId"/>
        <result column="cjsj" property="createTime"/>
        <result column="xgr" property="updateId"/>
        <result column="xgsj" property="updateTime"/>
        <result column="zt" property="status"/>
        <result column="sjc" property="timestamp"/>
        <result column="sfgx" property="packageRange"/>
        <result column="yybm" property="hospitalCode" />
        <result column="agreeRecipeClass" property="agreeRecipeClass" />
        <result column="isAgreeRecipe" property="isAgreeRecipe" />
        <result column="isPrivate" property="isPrivate" />
    </resultMap>

    <resultMap id="DetailResultMap" type="com.rjsoft.outPatient.infrastructure.repository.entity.PackageDetail">
        <id column="tcmxid" property="packageDetailId"/>
        <result column="tcid" property="packageId"/>
        <result column="xmbm" property="itemCode"/>
        <result column="xmsl" property="quantity"/>
        <result column="sftc" property="isPackage"/>
        <result column="sffl" property="itemChargeCategory"/>
        <result column="cjr" property="createId"/>
        <result column="cjsj" property="createTime"/>
        <result column="xgr" property="updateId"/>
        <result column="xgsj" property="updateTime"/>
        <result column="zt" property="status"/>
        <result column="sjc" property="timestamp"/>
        <result column="dosage" property="dosage"/>
        <result column="dosageUnit" property="dosageUnit" />
        <result column="unit" property="unit" />
        <result column="guage" property="specification" />
        <result column="yf" property="frequency" />
        <result column="gytj" property="usage" />
        <result column="ts" property="days" />
        <result column="yszt" property="doctorOrder" />
        <result column="bz" property="remark" />
        <result column="zxks" property="execDept" />
        <result column="yybm" property="hospitalCode" />
        <result column="xmmc" property="itemName" />
        <result column="xmlx" property="itemClass" />
        <result column="xmdw" property="itemUnit" />
    </resultMap>

    <select id="selectList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.PackagePrimary"
            parameterType="com.rjsoft.outPatient.domain.herbAgreeRecipe.vo.HerbAgreeRecipeListReqVO"
            resultMap="MainResultMap">
        select  tcid,
        sjjd,
        tcmc,
        tclx,
        ksbm,
        cjr,
        cjsj,
        xgr,
        xgsj,
        zt,
        sjc,
        sfgx,
        yybm,
        agreeRecipeClass,
        isAgreeRecipe,
        isPrivate from MZYS_TB_MZTCZB
        where isAgreeRecipe = 1
        and yybm = #{reqVO.hospitalCode}
        and sfgx = #{reqVO.shareFlag}
        <if test="null != reqVO.agreeRecipeName and reqVO.agreeRecipeName != ''">
            and tcmc = #{reqVO.agreeRecipeName}
        </if>
        <if test="null != reqVO.agreeRecipeClass and reqVO.agreeRecipeClass != ''">
            and agreeRecipeClass = #{reqVO.agreeRecipeClass}
        </if>
        <if test="reqVO.shareFlag == 1">
            and cjr = #{reqVO.doctorId}
        </if>
        <if test="reqVO.shareFlag == 2">
            and ksbm = #{reqVO.deptId}
        </if>
    </select>

    <select id="selectDetailList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.PackageDetail"
            parameterType="com.rjsoft.outPatient.domain.herbAgreeRecipe.vo.HerbAgreeRecipeDetailReqVO"
            resultMap="DetailResultMap">
        select tcmxid,
        tcid,
        xmbm,
        xmsl,
        sftc,
        sffl,
        cjr,
        cjsj,
        xgr,
        xgsj,
        zt,
        sjc,
        dosage,
        dosageunit,
        guage,
        unit,
        yf,
        gytj,
        ts,
        yszt,
        bz,
        zxks,
        xmmc,
        xmlx,
        xmdw,
        yybm  from MZYS_TB_MZTCMX
        where yybm = #{reqVO.hospitalCode}
        and tcid = #{reqVO.agreeRecipeId}
    </select>

    <insert id="addHerbAgreeRecipe" useGeneratedKeys="true" keyProperty="recipeDTO.id" parameterType="com.rjsoft.outPatient.domain.herbAgreeRecipe.dto.AddHerbAgreeRecipeDTO">
        insert into MZYS_TB_MZTCZB ( agreeRecipeClass,
        tcmc,isPrivate,sfgx,yybm,ksbm,cjr,cjsj,xgr,xgsj,isAgreeRecipe)
        VALUES
            (
            #{recipeDTO.agreeRecipeClass},
            #{recipeDTO.agreeRecipeName},
            #{recipeDTO.isPrivate},
            #{recipeDTO.shareFlag},
            #{recipeDTO.hospitalCode},
            #{recipeDTO.deptId},
            #{recipeDTO.doctorId},
            #{recipeDTO.createTime},
            #{recipeDTO.doctorId},
            #{recipeDTO.updateTime},
            #{recipeDTO.isAgreeRecipe}
            )
    </insert>
    <insert id="batchInsertAgreeRecipeDetail" useGeneratedKeys="false">
        insert into MZYS_TB_MZTCMX (
        tcid,xmbm,xmmc,dosage,dosageUnit,bz,xmdw,yybm)
        VALUES
        <foreach collection="agreeRecipeList" item="item" index="index" separator=",">
            (
             #{agreeRecipeId},
            #{item.itemCode},
            #{item.itemName},
            #{item.dosage},
            #{item.dosageUnit},
            #{item.remark},
            #{item.herbComatibleType},
            #{hospitalCode}
            )
        </foreach>
    </insert>

    <select id="selectListByName" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.PackagePrimary"
            resultMap="MainResultMap">
        select  tcid,
        sjjd,
        tcmc,
        tclx,
        ksbm,
        cjr,
        cjsj,
        xgr,
        xgsj,
        zt,
        sjc,
        sfgx,
        yybm,
        agreeRecipeClass,
        isAgreeRecipe,
        isPrivate
        from MZYS_TB_MZTCZB
        where isAgreeRecipe = 1
        <if test="shareFlag == 1">
          and sfgx = 1
          and cjr = #{doctorId}
        </if>
        <if test="shareFlag == 2">
            and sfgx = 2
            and ksbm = #{deptId}
        </if>
        and yybm = #{hospitalCode}
        and tcmc = #{agreeRecipeName}
    </select>
</mapper>