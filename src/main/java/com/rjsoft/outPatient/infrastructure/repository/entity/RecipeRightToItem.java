package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 药品开立权限
 *
 * <AUTHOR>
 * @since 2021/7/23 - 17:02
 */
@Data
@Table(name = "Tbt_MzRecipeRightToItem")
public class RecipeRightToItem implements Serializable {
    
    /**
     * 药品编码
     */
    @Column(name = "DrugCode")
    private Integer drugCode;

    /**
     * 权限代码
     */
    @Column(name = "RightCode")
    private Integer rightCode;
    
}
