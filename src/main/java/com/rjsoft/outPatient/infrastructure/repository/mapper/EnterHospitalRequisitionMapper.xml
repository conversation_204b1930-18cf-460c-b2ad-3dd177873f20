<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.EnterHospitalRequisitionMapper">
    <update id="updateByRecipeNo">
        UPDATE MZYS_TB_MZZYSQD  SET RisSignStatus = #{status} WHERE        id=#{id}

    </update>

    <update id="updateRiskReportStatusByRecipeNo">
        UPDATE MZYS_TB_MZZYSQD  SET RisSignStatus = #{riskSignStatus},RiskStatus = #{riskStatus} WHERE        id=#{id}
    </update>

    <select id="importReport" resultType="java.lang.String">
        exec usp_Get_BGJG
             @patname= #{patName},
             @hiscardno= #{hisCardNo},
             @jzlsh = #{receptionNo}
    </select>

    <select id="getInspect"
            resultType="com.rjsoft.outPatient.domain.enterHospitalRequisition.dto.ProcessScheduleResult">
        SELECT case when status in (2) then 1 else 0 end    isReceive,
               case when status in (2, 4) then 1 else 0 end isStatus,
               case
                   when
                           isnull(SQDXML.query('XTextDocument/XElements/Element/XElements/Element[ID="报告结果"]').value(
                                          '(/Element/TextValue)[1]', 'nvarchar(max)'), '') = '' then 0
                   else 1 end                               isInspect
        FROM MZYS_TB_MZZYSQD
        where jzlsh = #{receptionNo}
          and hospitalCode = #{hospitalCode}
    </select>
    <select id="getFxpgStatus" resultType="java.lang.Integer">
        select top 1 FinishStatu
        from N_GzdDetail
        where RgNo= #{receptionNo} and HospitalId=#{hospitalCode}
        and IsDelete =0 and FinishStatu!=99 and GZDName='精神障碍患者风险评估表'
    </select>
    <select id="getNotificationDetailsByRgNosAndGzdName" resultType="java.lang.String">
        select rgNo from N_GzdDetail a
        where IsDelete =0
        <if test="rgNos != null and rgNos.size > 0">
            and a.RgNo in
            <foreach collection="rgNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and isnull(a.Signature3,0)=1
        and a.FinishStatu!=99 and a.GZDName=#{gzdName}
    </select>
    <select id="getSq"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.EnterHospitalRequisition">
        select * from MZYS_TB_MZZYSQD a where
        <if test="rgNos != null and rgNos.size > 0">
            where a.JZLSH in
            <foreach collection="rgNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="getSqhInfo"
            resultType="java.util.HashMap">
        select
            isnull(w.name,w1.name) name, LTRIM(RTRIM(ce.HisDictionaryName)) JZLX
            from  MZYS_TB_MZZYSQD  a
            inner join  MZYS_TB_MZZYSQDContent b  on a.id = b.sqdId and a.HospitalCode = b.HospitalCode
            left JOIN System_Tb_Worker w on b.DoctorId = w.WorkerId and b.HospitalCode = w.HospitalId
            left JOIN System_Tb_Worker w1 on a.czr = w1.WorkerId and b.HospitalCode = w1.HospitalId
            LEFT JOIN TB_Dic_HisDictionary  ce on a.ZJLX = ce.HisDictionaryCode and a.HospitalCode = ce.HospitalId and ce.DictionaryTypeID=27
            where a.id=#{id}

    </select>
    <select id="getpatPhone"
            resultType="java.util.HashMap">
        select
            a.PatPhone patPhone
        from  Reg_Tb_PatientDetl  a(nolock)
        where a.PatID=#{patID}

    </select>
</mapper>
