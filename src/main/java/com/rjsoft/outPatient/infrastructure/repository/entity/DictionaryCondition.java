package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 状态类诊断信息
 */
@Data
@Table(name = "MZYS_TB_ZYSQDZD")
public class DictionaryCondition implements Serializable {

    /**
     * 字典类型ID
     */
    @Column(name = "zdbm")
    private String dictionaryCode;

    /**
     * 字典类型名称
     */
    @Column(name = "zdmc")
    private String dictionaryTypeName;

    /**
     * 字典类型名称
     */
    @Column(name = "idcbm")
    private String dictionaryIDC;

    public void setDictionaryCode(String dictionaryCode) {
        this.dictionaryCode = dictionaryCode == null ? "" : dictionaryCode.trim();
    }
}