package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.OftenUseItemUsage;
import tk.mybatis.mapper.common.BaseMapper;

/**
 * 常用项目用法
 * 常用项目子表，配合常用项目使用
 * <AUTHOR>
public interface OftenUseItemUsageMapper extends BaseMapper<OftenUseItemUsage> {

    /**
     * 保存常用项目用法
     * @param oftenUseItemUsage
     * @return
     */
    default boolean saveOftenUseItemUsage(OftenUseItemUsage oftenUseItemUsage) {
        if (0 == oftenUseItemUsage.getDataFlag()) {
            return insert(oftenUseItemUsage) > 0;
        } else if (1 == oftenUseItemUsage.getDataFlag()) {
            return updateByPrimaryKeySelective(oftenUseItemUsage) > 0;
        } else {
            return false;
        }
    }

    /**
     * 根据项目编码获取常用项目用法
     * @param id
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    default OftenUseItemUsage getOftenUseItemUsageByItemCode(Integer id, Integer doctorId, Integer hospitalCode) {
        OftenUseItemUsage entity = new OftenUseItemUsage();
        entity.setId(id);
        entity.setDoctorId(doctorId);
        entity.setHospitalCode(hospitalCode);
        return selectOne(entity);
    }
}
