package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.PsychotherapyApplyDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.PsychotherapyApplyList;
import com.rjsoft.outPatient.infrastructure.repository.entity.PsychotherapyItem;

import java.util.List;

/**
 * 门诊处方
 *
 * <AUTHOR>
public interface PsychotherapyApplyDetailRepository {

    /**
     * 保存心理治疗申请明细表
     *
     * @param psychotherapyApplyDetail
     * @return
     */
    boolean savePsychotherapyApplyDetail(com.rjsoft.outPatient.infrastructure.repository.entity.PsychotherapyApplyDetail psychotherapyApplyDetail, Integer hospitalCode);

    /**
     * 保存心理治疗申请明细表到MZYS_NEW库中
     *
     * @param psychotherapyApplyDetail
     * @return
     */
    boolean savePsychotherapyApplyDetailToMZYSNew(com.rjsoft.outPatient.infrastructure.repository.entity.PsychotherapyApplyDetail psychotherapyApplyDetail);

    /**
     * 更新心理治疗申请明细表为已删除
     *
     * @param recipeDetailId
     * @return
     */
    boolean updatePsychotherapyApplyDetailToMZYSNew(Long recipeDetailId, Integer hospitalId, Integer isDel);

    @DatabaseAnnotation
    boolean updatePsychotherapyApplyDetailListToMZYSNew(List<Long> recipeDetailIdList, Integer hospitalId, Integer isDel);

    /**
     * 删除心理治疗申请明细表
     *
     * @param applyDetailId
     * @return
     */
    boolean delPsychotherapyApplyDetail(Integer applyDetailId);

    /**
     * 删除心理治疗申请明细表从MZYS_NEW
     *
     * @param applyDetailId
     * @return
     */
    boolean delPsychotherapyApplyDetailMZYSNew(Integer applyDetailId);

    /**
     * 删除心理治疗申请明细表从MZYS_NEW
     *
     * @param applyId
     * @return
     */
    boolean delPsychotherapyApplyDetailByApplyId(Integer applyId);



    /**
     * 查询心理治疗项目
     *
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    PsychotherapyItem getPsychotherapyItem(Integer itemCode, Integer hospitalCode);

    /**
     * 查询心理治疗项目通过项目编码列表
     *
     * @param itemCodeList
     * @param hospitalCode
     * @return
     */
    List<PsychotherapyItem> getPsychotherapyItemList(List<Integer> itemCodeList, Integer hospitalCode);

    /**
     * 获取心理治疗申请主表
     *
     * @param receptionNo
     * @param doctorId
     * @return
     */
    PsychotherapyApplyList getPsychotherapyApplyList(Integer receptionNo, Integer doctorId, Integer hospitalCode);

    /**
     * 获取心理治疗申请主表从MZYS_new
     *
     * @param receptionNo
     * @param doctorId
     * @return
     */
    PsychotherapyApplyList getPsychotherapyApplyListFromMZYSNew(Integer receptionNo, Integer doctorId);


    /**
     * 获取心理治疗申请主表从MZYS_new
     *
     * @param RecipeNo
     * @param doctorId
     * @return
     */
    PsychotherapyApplyList getPsyApplyListByRecipeNo(Integer RecipeNo, Integer doctorId);


    /**
     * 保存心理治疗申请主表
     *
     * @param psychotherapyApplyList
     * @return
     */
    boolean savePsychotherapyApplyList(PsychotherapyApplyList psychotherapyApplyList, Integer hospitalCode);

    /**
     * 保存心理治疗申请主表到MZYS_NEW中
     *
     * @param psychotherapyApplyList
     * @return
     */
    boolean savePsychotherapyApplyListToMZYSNew(PsychotherapyApplyList psychotherapyApplyList);

    /**
     * 删除心理治疗申请主表
     *
     * @param applyId
     * @return
     */
    boolean delPsychotherapyApplyList(Integer applyId, Integer hospitalCode);

    /**
     * 删除心理治疗申请主表从MZYS_NEW
     *
     * @param applyId
     * @return
     */
    boolean delPsychotherapyApplyListMZYSNew(Integer applyId);

    /**
     * 获取心理治疗申请明细表
     *
     * @param recipeDetailId
     * @return
     */
    PsychotherapyApplyDetail getPsychotherapyApplyDetail(Integer recipeDetailId, Integer applyId, Integer hospitalCode);

    /**
     * 获取心理治疗申请明细表从MZYS_NEW
     *
     * @param recipeDetailId
     * @return
     */
    PsychotherapyApplyDetail getPsychotherapyApplyDetailFromMZYSNew(Integer recipeDetailId, Integer applyId);

    /**
     * 获取心理治疗申请明细表
     *
     * @param recipeDetailId
     * @return
     */
    List<PsychotherapyApplyDetail> queryPsychotherapyApplyDetailListByRecipeDetailIds(List<Long> recipeDetailId, Integer hospitalCode);

}
