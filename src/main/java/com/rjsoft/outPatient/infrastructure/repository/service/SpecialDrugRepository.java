package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.InjectionFeeAssociative;
import com.rjsoft.outPatient.infrastructure.repository.entity.SpecialDrugItem;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021/11/24-2:57 下午
 */
public interface SpecialDrugRepository {

    /**
     * 根据参数查询 categoryId
     *
     * @param itemCode      收费项目编码
     * @param drugUsageCode 给药途径编码
     * @param hospitalCode  医院编码
     * @return categoryId
     */
    Integer getCategoryIdByItemCodeAndDrugUsageCode(Integer itemCode, Integer drugUsageCode, Integer hospitalCode);


    /**
     * 根据特殊药品分类 id 查询 特殊收费项目编码
     *
     * @param categoryId   特殊药品分类 id
     * @param hospitalCode 医院编码
     * @return 收费项目编码
     */
    Integer getProjectNumByCategoryId(Integer categoryId, Integer hospitalCode);

    /**
     * 是否存在注射费用对照
     *
     * @param receptionNo       就诊流水号
     * @param recipeDetailNo    处方明细流水号
     * @param injectionItemCode 注射项目编码
     * @return boolean
     */
    boolean existInjectionFeeAssociative(Long receptionNo, Long recipeDetailNo, Integer injectionItemCode);

    /**
     * 是否存在注射费用对照
     *
     * @param receptionNo       就诊流水号
     * @param recipeDetailNo    处方明细流水号
     * @param xmbms 注射项目编码
     * @return boolean
     */
    boolean existInjectionFeeAssociativeItems(Long receptionNo, Long recipeDetailNo, Set<Integer> xmbms);

    /**
     * 查询注射费用对照
     *
     * @param receptionNo       就诊流水号
     * @param recipeDetailNo    处方明细流水号
     * @param injectionItemCode 注射项目编码
     * @return boolean
     */
    InjectionFeeAssociative getInjectionFeeAssociative(Long receptionNo, Long recipeDetailNo, Integer injectionItemCode);

    /**
     * 保存注射费用对照
     *
     * @param record {@link InjectionFeeAssociative}
     */
    void saveInjectionFeeAssociative(InjectionFeeAssociative record);

    /**
     * 删除注射费用对照
     *
     * @param id 主键
     */
    void deleteInjectionFeeAssociative(Integer id);


    @DatabaseAnnotation
    List<SpecialDrugItem> getProjectItemsByUsageId(Integer usageId, Integer hospitalCode);

    /**
     * 查询附加关联项目
     *
     * @param hospitalCode 医院
     * @param type 类型
     */
    List<SpecialDrugItem> getAllProjectItemsByType(Integer hospitalCode, Integer type);

    /**
     * 查询注射费用对照集合
     *
     * @param receptionNo       就诊流水号
     * @param recipeDetailNo    处方明细流水号
     * @return boolean
     */
    List<InjectionFeeAssociative> getInjectionFeeAssociativeList(Long receptionNo, Long recipeDetailNo);
}
