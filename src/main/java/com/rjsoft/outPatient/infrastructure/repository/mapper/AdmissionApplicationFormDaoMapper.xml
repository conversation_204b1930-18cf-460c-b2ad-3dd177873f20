<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.AdmissionApplicationFormDaoMapper">

    <sql id="Base_Column_List">
        jzlsh
        , lczd, ys, bscw, glffcb, glffhx, yszl, hszysx, mzcl, ysqz, qzsj, ysxm, hzjg,
                hzmz, ksbm, CreateUserId, CreateOn, HospitalId, UpdateUserId, UpdateOn
    </sql>
    <update id="changeIssueInspectStatus">
        update MZYS_TB_MZZYSQD
        set issueInspectStatus=#{issueInspectStatus}
        where JZLSH = #{jzlsh}
          and HospitalCode = #{hospitalCode}
    </update>

    <select id="getPatient" resultType="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.RetPatientResult">
        select top 1
               b.hospitalCode, a.patName,
               a.sex                                               patSex,
               FLOOR(datediff(DY, a.Birthday, getdate()) / 365.25) age,
               a.hospNo,
               b.CardNo                                            cardId,
               b.visitTime,
               b.regNo,
               a.CertificateNo                                     idCard,
               b.DoctorID                                          workerId,
               b.deptId,
               w.WorkerNo                                          operateNo
        from Reg_Tb_PatientList a (nolock),
             Reg_Tv_RegisterList b (nolock),
             System_Tb_Worker w
        where a.patId = b.patId
          and w.workerId = b.DoctorID
          and b.regNo = #{regNo}
          and w.HospitalId = #{hospitalCode}
        order by VisitTime desc
    </select>

    <select id="listWards" resultType="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.DeptsResult">
        select DeptId wardId,RTRIM(DeptName) wardName from System_Tb_Department where DeptClassCode=6 and HospitalId=#{hospitalCode} and Status=1 order by SID
    </select>

    <select id="listDeptWards" resultType="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.DeptsResult">
        SELECT
            distinct
            b.DeptId wardId,
            RTRIM( b.DeptName ) wardName
        FROM
            System_Tb_DeptToDept a
                INNER JOIN System_Tb_Department b ON a.WardId = b.DeptId
                AND a.HospitalId = b.HospitalId
        WHERE
            a.isUse = 1
          AND a.HospitalId = #{hospitalCode}
          <if test="deptId!=null and deptId != 0">
          AND a.DeptId = #{deptId}
          </if>
    </select>

    <select id="listWardDepts" resultType="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.DeptsResult">
        SELECT
            distinct
            b.DeptId wardId,
            RTRIM( b.DeptName ) wardName
        FROM
            System_Tb_DeptToDept a
                INNER JOIN System_Tb_Department b ON a.DeptId = b.DeptId
                AND a.HospitalId = b.HospitalId
        WHERE
            a.isUse = 1
          AND a.HospitalId = #{hospitalCode}
            <if test="wardId!=null and wardId != 0">
                AND a.WardId = #{wardId}
            </if>
    </select>

    <select id="getWardCount" resultType="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.WardCountResult">
        select *
        from [MZYS_TV_GetBedInfo] (nolock)
        where HospitalId = #{hospitalCode}
          and wardId = #{wardId}
    </select>

    <select id="getWardDeptCount" resultType="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.WardCountResult">
        select DeptId wardId,b.DeptName wardName,b.hospitalId,
               (select count(1) from System_Tb_Bed a where  a.wardId=b.deptId and a.HospitalId = b.HospitalId and isDelete = 0 and a.depeId = #{deptId}) as BedTotal,--病床数
               (select count(1) from System_Tb_Bed a where  a.wardId=b.deptId and a.HospitalId = b.HospitalId and Status=0 and isDelete = 0 and a.depeId = #{deptId}) as EmptyBedTotal,--空床总数
               (select count(1) from System_Tb_Bed a where  a.wardId=b.deptId and a.HospitalId = b.HospitalId and Status=0 and BedSex=1 and isDelete = 0 and a.depeId = #{deptId}) as EmptyBedMale,--空床男
               (select count(1) from System_Tb_Bed a where  a.wardId=b.deptId and a.HospitalId = b.HospitalId and Status=0 and BedSex=2 and isDelete = 0 and a.depeId = #{deptId}) as EmptyBedFemale,--空床女
               (select count(1) from System_Tb_Bed a where  a.wardId=b.deptId and a.HospitalId = b.HospitalId and Status=0 and BedSex=3 and isDelete = 0 and a.depeId = #{deptId}) as EmptyBedCom,--公用床数
               (select count(1) from System_Tb_Bed a where  a.wardId=b.deptId and a.HospitalId = b.HospitalId and Status=0 and IsAdd!=1 and isDelete = 0 and a.depeId = #{deptId}) as EmptyBedAdd,--空闲加床数
               (select count(1) from System_Tb_Bed a where  a.wardId=b.deptId and a.HospitalId = b.HospitalId and BedSex=1 and isDelete = 0 and a.depeId = #{deptId}) as BedMale,--男床数
               (select count(1) from System_Tb_Bed a where  a.wardId=b.deptId and a.HospitalId = b.HospitalId and BedSex=2 and isDelete = 0 and a.depeId = #{deptId}) as BedFemale,--女床数
               (select count(1) from System_Tb_Bed a where  a.wardId=b.deptId and a.HospitalId = b.HospitalId and BedSex=3 and isDelete = 0 and a.depeId = #{deptId}) as BedCom,--公用床数
               (select count(1) from System_Tb_Bed a where  a.wardId=b.deptId and a.HospitalId = b.HospitalId and IsAdd!=1 and isDelete = 0 and a.depeId = #{deptId}) as BedAdd--加床数
        from System_Tb_Department b
        where DeptClassCode = 6 and Status = 1
          and HospitalId = #{hospitalCode}
          and wardId = #{wardId}
        group by b.DeptId,b.DeptName,b.HospitalId
    </select>

    <select id="listBeds" resultType="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.BedsResult">
        select BedNo,
               case BedSex when 1 then '男' when 2 then '女' else '混床' end sexName,
               case IsAdd when 1 then '非加床' else '加床' end                isAdd
        from HISDB.dbo.System_Tb_Bed
        where Status = 0
          and IsAdd = 1
          and wardId = #{wardId}
          and HospitalId = #{hospitalCode}
    </select>

    <select id="getTypeAndID" resultType="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.InspectionResult">
        {call Usp_MZYS_Report_RIS_GetTypeAndID(
                #{cfmxId,mode=IN}
            )}
    </select>


    <!--  查询化验结果  -->
    <resultMap id="Assay" type="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.AssayResult">
        <result column="报告唯一标识" property="reportId"/>
        <result column="项目编码" property="itemCode"/>
        <result column="项目名称" property="itemName"/>
        <result column="检验结果" property="result"/>
        <result column="单位" property="unit"/>
        <result column="标志标志" property="sign"/>
        <result column="参考值" property="reference"/>
        <result column="排列次序" property="orderId"/>
        <result column="jdate" property="jDate"/>
        <result column="ybh" property="ybh"/>
        <result column="hisid" property="hisId"/>
        <result column="itemid" property="itemId"/>
    </resultMap>

    <!--    查询化验结果-->
    <select id="getAssay" resultMap="Assay">
        {call Usp_MZYS_Report_Lis(#{recipeDetailId,mode=IN})}
    </select>


    <!--  查询化验状态  -->
    <resultMap id="AssayState" type="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.AssayStateResult">
        <result column="报告种类" property="reportType"/>
        <result column="报告状态" property="reportState"/>
        <result column="采样时间" property="takeDate"/>
        <result column="送检时间" property="checkingDate"/>
        <result column="检测时间" property="detectDate"/>
        <result column="检测花费" property="amount"/>
        <result column="报告日期" property="reportDate"/>
        <result column="报告时间" property="reportTime"/>
        <result column="审核时间" property="auditDate"/>
        <result column="打印时间" property="printDate"/>
        <result column="设置时间" property="setDate"/>
        <result column="仪器" property="instrument"/>
        <result column="样本号" property="sampleNum"/>
        <result column="类别" property="category"/>
        <result column="科别" property="department"/>
        <result column="月份" property="month"/>

    </resultMap>

    <!--    查询化验状态-->
    <select id="getAssayState" resultMap="AssayState">
        {call Usp_MZYS_Report_Lis_GetCFZTByCfmxid(#{recipeDetailId,mode=IN})}
    </select>


    <select id="listHistoryDiagnose"
            resultType="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.HistoryDiagnoseResult">
        select distinct a.jzlsh  receptionNo,
                        0        compositeSign,
                        ''       compositeSignName,
                        a.sckzrq visitTime,
                        a.zhkzys doctorId,
                        hospitalCode
        from MZYS_TB_KZJL a (nolock)
                 left join MZYS_TB_MZYSZD b (nolock) ON a.ghlsh = b.regNo
        where a.ghlsh = #{regNo}
    </select>
    <select id="listOutPatientNo"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.RegisterListResult">
        select regNo
        from Reg_TV_RegisterList (nolock)
        where OutPatientNo = #{outPatientNo}
    </select>
    <select id="getOutPatient"
            resultType="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.OutPatientInvoiceTimeResult">
        select top 1 AccountFlag
        from Reg_TV_OutpatientInvoice (nolock)
        where regNo = #{regNo}
          and hospitalCode = #{hospitalCode}
    </select>
    <select id="getTopMzCfMx" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbMzCfMx">
        select top 1 cfmxlsh recipeDetailNo, cfmxid recipeDetailId,
               jzlsh receptionNo,
               cflsh recipeNo,
               cfId  recipeId,
               zxks  dept,
               hospitalCode
        from MZYS_TB_MZCFMX (nolock)
        where jzlsh = #{receptionNo}
          and hospitalCode = #{hospitalCode}
        order by sckfsj desc
    </select>
    <select id="listMzCfMx" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbMzCfMx">
        select top 1 *
        from MZYS_TB_MZCFMX (nolock)
        where jzlsh = #{jzlsh}
          and zhkfsj
            between DATEADD(MONTH, -1 * cast(#{months} as int), GETDATE()) and GETDATE()
          and zt = 1
          and hospitalCode = #{hospitalCode}
    </select>
    <select id="listInPatients" resultType="com.rjsoft.outPatient.domain.admissionApplicationForm.dto.InPatientResult">
        select
        regNo,
        outTime,
        iop.wardId,
        LTrim(RTrim(OutDiagnosisCode)) diagnoseCode,
        LTrim(RTrim(OutDiagnosisName)) diagnoseName,
        LTrim(RTrim(ward.DeptName)) wardName,
        iop.hospitalId hospitalCode,
        1 isReview,
        iop.Professor reviewDoctor
        from
        Io_Tb_InPatient iop (nolock)
        LEFT JOIN System_Tb_Department ward ON iop.wardId = ward.DeptId AND iop.hospitalId = ward.hospitalId
        <where>
            AND iop.status =9 and OutDiagnosisCode is not null
            and iop.patId in
            <foreach collection="patientIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="getRecipeByRecipeDetailId"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbMzCfMx">
        select cfmxlsh recipeDetailNo,
               cfmxid  recipeDetailId,
               jzlsh   receptionNo,
               cflsh   recipeNo,
               cfId    recipeId,
               zxks    dept,
               hospitalCode
        from MZYS_TB_MZCFMX (nolock)
        where cfmxlsh = #{cfmxlsh}
          and hospitalCode = #{hospitalCode}
    </select>
    <select id="getOldRecipeDetail" resultType="java.lang.Integer">
        select count(1)
        from MZYS_TB_MZCFMX (nolock)
        where jzlsh = #{jzlsh}
          and sqd = #{tcmc}
          and hospitalCode = #{hospitalCode}
    </select>
</mapper>
