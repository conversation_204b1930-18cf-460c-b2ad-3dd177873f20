package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QuerySonResult;
import com.rjsoft.outPatient.domain.doctorElemMain.dto.ElemParams;
import com.rjsoft.outPatient.infrastructure.repository.entity.ElemMain;
import com.rjsoft.outPatient.infrastructure.repository.entity.ElemTemp;
import com.rjsoft.outPatient.infrastructure.repository.entity.ElemTempFile;
import com.rjsoft.outPatient.infrastructure.repository.mapper.CurrencyMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ElemMainMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ElemTempFileMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ElemTempMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DoctorElemMainRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Date;
import java.util.List;

@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class DoctorElemMainRepositoryImpl implements DoctorElemMainRepository {
    ElemMainMapper elemMainMapper;
    ElemTempMapper elemTempMapper;
    CurrencyMapper currencyMapper;
    ElemTempFileMapper elemTempFileMapper;

    @Override
    @DatabaseAnnotation
    public int getMbwjMaxId() {
        return currencyMapper.getMbwjMaxId();
    }

    /**
     * 根节点,子节点(保存)
     *
     * @param elemMain
     * @return
     */
    @Override
    @DatabaseAnnotation
    public int saveElemMain(ElemMain elemMain) {
        return elemMainMapper.insertSelective(elemMain);
    }


    /**
     * 根节点,子节点(修改)
     *
     * @param elemMain
     * @return
     */
    @Override
    @DatabaseAnnotation
    public int updateElemMain(ElemMain elemMain) {
        return elemMainMapper.updateByPrimaryKey(elemMain);
    }

    /**
     * 元素主表删除
     *
     * @param elemCode
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public int delElemMain(Integer elemCode, Integer hospitalCode, Integer createUser) {
        Date time = currencyMapper.getDate();
        ElemMain elemMain = new ElemMain();
        elemMain.setElemCode(elemCode);
        elemMain.setHospitalCode(hospitalCode);
        elemMain.setCreateUser(createUser);
        elemMain.setStatus(0);//修改zt类型为0
        elemMain.setUpdateDate(time);
        return elemMainMapper.updateByPrimaryKeySelective(elemMain);
    }


    /**
     * 根据元素编码和医院编码查询是否存在子节点
     *
     * @param elemCode
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<ElemMain> listYSZBs(Integer elemCode, Integer hospitalCode, Integer createUser) {
        ElemMain elemMain = new ElemMain();
        elemMain.setParentElemCode(elemCode);
        elemMain.setHospitalCode(hospitalCode);
        elemMain.setCreateUser(createUser);
        elemMain.setStatus(1);
        return elemMainMapper.select(elemMain);
    }

    /**
     * 根据元素编码，医院编码，创建人查询是否存在数据
     *
     * @param elemCode
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public ElemMain getElemMains(Integer elemCode, Integer hospitalCode) {
        ElemMain elemMain = new ElemMain();
        elemMain.setElemCode(elemCode);
        elemMain.setHospitalCode(hospitalCode);
        elemMain.setStatus(1);
        return elemMainMapper.selectOne(elemMain);
    }

    /**
     * 段落模板(保存)
     *
     * @param elemTemp
     * @return
     */
    @Override
    @DatabaseAnnotation
    public int saveElemTemp(ElemTemp elemTemp) {
        return elemTempMapper.insertSelective(elemTemp);
    }

    /**
     * 段落模板(修改)
     *
     * @param elemTemp
     * @return
     */
    @Override
    @DatabaseAnnotation
    public int updateElemTemp(ElemTemp elemTemp) {
        return elemTempMapper.updateByPrimaryKey(elemTemp);
    }

    /**
     * 查询段落模板信息
     *
     * @param elemDetailCode
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public ElemTemp getElemTemp(Integer elemDetailCode, Integer hospitalCode) {
        ElemTemp elemTemp = new ElemTemp();
        elemTemp.setElemDetailCode(elemDetailCode);
        elemTemp.setHospitalCode(hospitalCode);
        elemTemp.setStatus(1);
        return elemTempMapper.selectOne(elemTemp);
    }

    /**
     * 查询段落模板信息
     *
     * @param elemDetailCode
     * @param hospitalCode
     * @param createUser
     * @return
     */
    @Override
    @DatabaseAnnotation
    public ElemTemp queryElemTemp(Integer elemDetailCode, Integer hospitalCode, Integer createUser) {
        ElemTemp elemTemp = new ElemTemp();
        elemTemp.setElemDetailCode(elemDetailCode);
        elemTemp.setHospitalCode(hospitalCode);
        elemTemp.setCreateUser(createUser);
        elemTemp.setStatus(1);
        return elemTempMapper.selectOne(elemTemp);
    }

    /**
     * 删除段落模板
     *
     * @param elemDetailCode
     * @param hospitalCode
     * @param createUser
     * @return
     */
    @Override
    @DatabaseAnnotation
    public int deleteElemTemp(Integer elemDetailCode, Integer hospitalCode, Integer createUser) {
        Date time = currencyMapper.getDate();
        ElemTemp elemTemp = new ElemTemp();
        elemTemp.setElemDetailCode(elemDetailCode);
        elemTemp.setHospitalCode(hospitalCode);
        elemTemp.setCreateUser(createUser);
        elemTemp.setStatus(0);
        elemTemp.setUpdateDate(time);
        return elemTempMapper.updateByPrimaryKeySelective(elemTemp);
    }

    /**
     * 查询模板内容
     *
     * @param tempId
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public ElemTempFile getElemTempFile(Integer tempId, Integer hospitalCode) {
        ElemTempFile elemTempFile = new ElemTempFile();
        elemTempFile.setFileCode(tempId);
        elemTempFile.setHospitalCode(hospitalCode);
        return elemTempFileMapper.selectOne(elemTempFile);
    }

    /**
     * 保存模板内容
     *
     * @param elemTempFile
     * @return
     */
    @Override
    @DatabaseAnnotation
    public int saveElemTempFile(ElemTempFile elemTempFile) {
        return elemTempFileMapper.insertSelective(elemTempFile);
    }

    /**
     * 修改模板内容
     *
     * @param elemTempFile
     * @return
     */
    @Override
    @DatabaseAnnotation
    public int updateElemTempFile(ElemTempFile elemTempFile) {
        return elemTempFileMapper.updateByPrimaryKey(elemTempFile);
    }

    @Override
    @DatabaseAnnotation
    public List<ElemMain> queryRoot(Integer createUser, String searchVal, Integer hospCode) {
        Weekend<ElemMain> weekend = new Weekend<>(ElemMain.class);
        WeekendCriteria<ElemMain, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ElemMain::getCreateUser, createUser);
        weekendCriteria.andEqualTo(ElemMain::getHospitalCode, hospCode);
        weekendCriteria.andEqualTo(ElemMain::getStatus, 1);
        weekendCriteria.andEqualTo(ElemMain::getParentElemCode, 0);
        weekendCriteria.andLike(ElemMain::getElemName, "%" + searchVal + "%");
        weekend.setOrderByClause("create_date asc");
        return elemMainMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<ElemMain> queryRootNext(List<Integer> elemCodeList, Integer createUser, Integer hospCode) {
        Weekend<ElemMain> weekend = new Weekend<>(ElemMain.class);
        WeekendCriteria<ElemMain, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ElemMain::getHospitalCode, hospCode);
        weekendCriteria.andEqualTo(ElemMain::getCreateUser, createUser);
        weekendCriteria.andEqualTo(ElemMain::getStatus, 1);
        weekendCriteria.andIn(ElemMain::getParentElemCode, elemCodeList);
        return elemMainMapper.selectByExample(weekend);
    }


    @Override
    @DatabaseAnnotation
    public List<ElemTemp> queryElemNext(List<Integer> elemCodeList, Integer createUser, Integer hospCode) {
        Weekend<ElemTemp> weekend = new Weekend<>(ElemTemp.class);
        WeekendCriteria<ElemTemp, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ElemTemp::getCreateUser, createUser);
        weekendCriteria.andEqualTo(ElemTemp::getHospitalCode, hospCode);
        weekendCriteria.andEqualTo(ElemTemp::getStatus, 1);
        weekendCriteria.andIn(ElemTemp::getElemCode, elemCodeList);
        return elemTempMapper.selectByExample(weekend);
    }


    @Override
    @DatabaseAnnotation
    public QuerySonResult queryNextRoot(ElemParams elemParams) {
        QuerySonResult querySonResult = new QuerySonResult();
        //查询元素子级信息
        List<ElemMain> elemMainList = elemMainMapper.querySonDirectory(elemParams);
        querySonResult.setElemMainList(elemMainList);

        //查询元素内容信息
        List<ElemTemp> elemTempList = elemTempMapper.queryElemTemp(elemParams);
        querySonResult.setElemTempList(elemTempList);
        return querySonResult;
    }


}
