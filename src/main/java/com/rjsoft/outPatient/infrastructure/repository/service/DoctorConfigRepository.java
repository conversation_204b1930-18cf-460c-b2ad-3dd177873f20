package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.DoctorConfig;
import com.rjsoft.outPatient.infrastructure.repository.entity.DoctorRight;
import com.rjsoft.outPatient.infrastructure.repository.entity.RightType;

import java.util.List;

/**
 * 医生配置
 * <AUTHOR>
public interface DoctorConfigRepository {

    /**
     * 保存医生配置
     *
     * @param entity
     * @return
     */
    boolean saveDoctorConfig(DoctorConfig entity);

    /**
     * 获取医生配置
     *
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    List<DoctorConfig> getConfigByDoctor(Integer doctorId, Integer hospitalCode);

    /**
     * 删除医生配置
     *
     * @param configKey
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    boolean deleteDoctorConfig(String configKey, Integer doctorId, Integer hospitalCode);

    /**
     * 根据医院编号获取全部权限类型
     * @param hospitalCode
     */
    List<RightType> getRightList(Integer hospitalCode);

    /**
     *  添加门诊医生对应权限
     * @param doctorRight
     */
    void addDoctorRight(List<DoctorRight> doctorRight);
}
