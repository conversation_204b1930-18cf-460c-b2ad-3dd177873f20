package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.rjsoft.outPatient.domain.dictionary.dto.CommonDicEntity;
import com.rjsoft.outPatient.domain.dictionary.vo.CommonDicVo;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 申请单对照条件
 */
@Data
@Table(name = "MZYS_TB_JCSQDZTJ")
public class SelectedCondition extends CommonDicEntity implements Serializable {

    @Column(name = "tjlx")
    private Integer selCondition;

    @Column(name = "tjfh")
    private String selConditionName;

    @Column(name = "tjsm")
    private String selConditionCaption;

    @Override
    public CommonDicVo conversionFill() {
        super.setCode(this.selCondition);
        super.setName(this.selConditionName);
        return new CommonDicVo(this);
    }
}
