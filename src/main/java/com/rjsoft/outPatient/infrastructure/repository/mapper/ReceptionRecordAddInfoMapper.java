package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ReceptionRecordAddInfo;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

/**
 * <AUTHOR>
 * @since 2021/8/9 - 15:15
 */
public interface ReceptionRecordAddInfoMapper extends BaseMapper<ReceptionRecordAddInfo>, ExampleMapper<ReceptionRecordAddInfo> {
}
