package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ExecDeptTime;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;


public interface ExecDeptTimeMapper extends Mapper<ExecDeptTime> {

    /**
     * 查询当前时间配置科室信息
     *
     * @param deptType
     * @param hospitalCode
     * @return
     */
    ExecDeptTime getExecDeptTime(@Param("deptType") String deptType, @Param("hospitalCode") Integer hospitalCode);


}
