package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.utils.TyMdc;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.ChargeStatusEnum;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DrugInfo;
import com.rjsoft.outPatient.domain.diseaseReport.dto.ReceptionDrugDTO;
import com.rjsoft.outPatient.domain.mq.MessageSender;
import com.rjsoft.outPatient.domain.preload.PreLoadCache;
import com.rjsoft.outPatient.domain.prescriptionAudit.vo.AuditMedicine;
import com.rjsoft.outPatient.domain.reception.dto.ReceptionListDto;
import com.rjsoft.outPatient.domain.recipe.constant.TyKey;
import com.rjsoft.outPatient.domain.recipe.dto.HistoryRecipeDetailDto;
import com.rjsoft.outPatient.domain.recipe.dto.TreatDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList;
import com.rjsoft.outPatient.infrastructure.repository.mapper.PreRecipeDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RegisterListMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RecipeDetailRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class RecipeDetailRepositoryImpl implements RecipeDetailRepository {
    RecipeDetailMapper recipeDetailMapper;
    PreRecipeDetailMapper preRecipeDetailMapper;
    RegisterListMapper registerListMapper;
    PreLoadCache preLoadCache;
    MessageSender messageSender;

    @Override
    @DatabaseAnnotation
    public Integer deleteByRecipeDetailNos(List<Long> recipeDetailNos, Integer feeCategory, Integer hospitalCode) {
        if(recipeDetailNos==null||recipeDetailNos.size()==0){
            return 0;
        }
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria().andIn(RecipeDetail::getRecipeDetailNo,recipeDetailNos).andEqualTo(RecipeDetail::getFeeCategory,feeCategory).andEqualTo(RecipeDetail::getHospitalCode,hospitalCode);
        return recipeDetailMapper.deleteByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public Integer deleteByRecipeNos(Long recipeNo, Integer feeCategory, Integer hospitalCode) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria().andEqualTo(RecipeDetail::getRecipeNo,recipeNo).andEqualTo(RecipeDetail::getFeeCategory,feeCategory).andEqualTo(RecipeDetail::getHospitalCode,hospitalCode);
        return recipeDetailMapper.deleteByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public RecipeDetail getByReceptionNoAndItemCode(Long receptionNo, Integer itemCode, Integer hospitalCode) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria().andEqualTo(RecipeDetail::getReceptionNo, receptionNo).andEqualTo(RecipeDetail::getItemCode, itemCode).andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        List<RecipeDetail> recipeDetailList = recipeDetailMapper.selectByExample(weekend);
        if (CollUtil.isNotEmpty(recipeDetailList)) {
            return recipeDetailList.get(0);
        }
        return null;
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getByReceptionNoAndUsage(Long receptionNo, Integer usage, Integer hospitalCode) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria().andEqualTo(RecipeDetail::getReceptionNo,receptionNo).andEqualTo(RecipeDetail::getUsage,usage).andEqualTo(RecipeDetail::getHospitalCode,hospitalCode);
        return recipeDetailMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public boolean updateRecipeDetailRegno(Long regNo, Long receptionNo, List<Long> recipeDetailNos, Integer hospitalCode, Integer doctorId) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        WeekendCriteria<RecipeDetail, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andIn(RecipeDetail::getRecipeDetailNo,recipeDetailNos).andEqualTo(RecipeDetail::getHospitalCode,hospitalCode);
        RecipeDetail recipeDetail = new RecipeDetail();
        recipeDetail.setRegNo(regNo);
        recipeDetail.setReceptionNo(receptionNo);
        return recipeDetailMapper.updateByExampleSelective(recipeDetail,weekend)>0;
    }
    @Override
    @DatabaseAnnotation
    public Integer deleteRecipeDetailByDetailIds(List<Long> detailIds, Integer hospitalCode) {
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria()
                .andIn(RecipeDetail::getRecipeDetailId, detailIds)
                .andNotIn(RecipeDetail::getStatus, Arrays.asList(ChargeStatusEnum.CHARGED.getCode(),
                        ChargeStatusEnum.BEING_CHARGED.getCode(),
                        ChargeStatusEnum.REGISTERED.getCode(),
                        ChargeStatusEnum.DEREGISTER.getCode()))
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        return recipeDetailMapper.deleteByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<RecipeDetail> getRecipeDetailByRegNoAndStatus(Long regNo, Integer hospitalCode, List<Integer> statusList, String tableName) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        if(StrUtil.isNotBlank(tableName)){
            weekend.setTableName(tableName);
        }
        WeekendCriteria<RecipeDetail, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andEqualTo(RecipeDetail::getRegNo, regNo)
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                .andIn(RecipeDetail::getStatus,statusList);
        weekend.orderBy("groupNo").orderBy("itemNo");
        return recipeDetailMapper.selectByExample(weekend);
    }


    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailHerbsNotSpecial(Long ReceptionNo, Integer hospitalCode) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria()
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                .andEqualTo(RecipeDetail::getReceptionNo, ReceptionNo)
                .andEqualTo(RecipeDetail::getRecipeCategory, ItemCategoryEnum.ChineseHerbMed.getCategoryCode())
                .andEqualTo(RecipeDetail::getFeeCategory, ItemCategoryEnum.ChineseHerbMed.getCategoryCode())
                .andNotIn(RecipeDetail::getStatus, Arrays.asList(ChargeStatusEnum.CHARGED.getCode(),
                        ChargeStatusEnum.BEING_CHARGED.getCode(),
                        ChargeStatusEnum.REGISTERED.getCode(),
                        ChargeStatusEnum.DEREGISTER.getCode()));
        return recipeDetailMapper.selectByExample(weekend);
    }


    @Override
    @DatabaseAnnotation
    public Integer deleteRecipeDetailByDetailId(Long detailId, Integer hospitalCode) {
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria()
                .andEqualTo(RecipeDetail::getRecipeDetailId, detailId)
                .andNotIn(RecipeDetail::getStatus, Arrays.asList(ChargeStatusEnum.CHARGED.getCode(),
                        ChargeStatusEnum.BEING_CHARGED.getCode(),
                        ChargeStatusEnum.REGISTERED.getCode(),
                        ChargeStatusEnum.DEREGISTER.getCode()))
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        return recipeDetailMapper.deleteByExample(weekend);
    }

    /**
     * 保存处方明细
     *
     * @param recipeDetail
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean saveRecipeDetail(RecipeDetail recipeDetail) {
        return recipeDetailMapper.saveRecipeDetail(recipeDetail);
    }

    @Override
    @DatabaseAnnotation
    public int updateRecipeDetail(RecipeDetail recipeDetail) {
        return recipeDetailMapper.updateByPrimaryKeySelective(recipeDetail);
    }

    @Override
    @DatabaseAnnotation
    public int updateRecipeDetail(List<Long> recipeDetailIds, String urgent) {

        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria().andIn(RecipeDetail::getRecipeDetailId, recipeDetailIds);

        final RecipeDetail record = new RecipeDetail();
        record.setUrgent(urgent);

        return recipeDetailMapper.updateByExampleSelective(record, weekend);
    }

    /**
     * 保存处方明细
     *
     * @param recipeDetail
     * @return
     */
    @Override
    @DatabaseAnnotation
    @Transactional(rollbackFor = {Exception.class})
    public boolean saveRecipeDetails(List<RecipeDetail> recipeDetail) {
        boolean success = false;
        for (RecipeDetail entity : recipeDetail) {
            success = recipeDetailMapper.saveRecipeDetail(entity);
            if (!success) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return false;
            }
        }
        return success;
    }


    /**
     * 根据就诊流水号查询处方明细
     *
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailByReceptionNo(Long receptionNo, Integer itemCategory) {
        RecipeDetail entity = new RecipeDetail();
        entity.setReceptionNo(receptionNo);
        if (itemCategory != null && itemCategory > 0) {
            entity.setFeeCategory(itemCategory);
        }
        return recipeDetailMapper.select(entity);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailByExamineNo(Integer examineNo, Integer hospitalCode) {
        RecipeDetail entity = new RecipeDetail();
        entity.setExamineNo(Converter.toString(examineNo));
        entity.setHospitalCode(hospitalCode);
        return recipeDetailMapper.select(entity);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailByExamineNos(List<Integer> examineNo, Integer hospitalCode) {
        if (examineNo == null || examineNo.isEmpty()) {
            return new ArrayList<>();
        }
        Weekend weekend = new Weekend(RecipeDetail.class);
        WeekendCriteria<RecipeDetail, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(RecipeDetail::getExamineNo, examineNo);
        weekendCriteria.andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        return recipeDetailMapper.selectByExample(weekend);
    }


    @Override
    @DatabaseAnnotation
    public Integer deleteRecipeDetailByDetailIdList(List<Long> detailIdList, Integer hospitalCode) {
        if(detailIdList == null|| detailIdList.size()==0){
            return 0;
        }
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria()
                .andIn(RecipeDetail::getRecipeDetailId, detailIdList)
                .andNotIn(RecipeDetail::getStatus, Arrays.asList(ChargeStatusEnum.CHARGED.getCode(),
                        ChargeStatusEnum.BEING_CHARGED.getCode(),
                        ChargeStatusEnum.REGISTERED.getCode(),
                        ChargeStatusEnum.DEREGISTER.getCode()))
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        return recipeDetailMapper.deleteByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public Boolean deleteRecipeDetailByRecipeId(Long id, Integer hospitalCode) {
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria()
                .andEqualTo(RecipeDetail::getRecipeDetailId, id)
                .andNotIn(RecipeDetail::getStatus, Arrays.asList(ChargeStatusEnum.CHARGED.getCode(),
                        ChargeStatusEnum.BEING_CHARGED.getCode(),
                        ChargeStatusEnum.REGISTERED.getCode(),
                        ChargeStatusEnum.DEREGISTER.getCode()))
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        return recipeDetailMapper.deleteByExample(weekend) > 0;
    }

    @Override
    @DatabaseAnnotation
    public Boolean deleteRecipeDetailByApplyId(List<Integer> applyIds, Integer hospitalCode) {
        if (applyIds == null || applyIds.size() == 0) {
            return true;
        }
        List<String> list = new ArrayList<>();
        for(Integer a:applyIds){
            list.add(a.toString());
        }
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria()
                .andIn(RecipeDetail::getExamineNo, list)
                .andNotIn(RecipeDetail::getStatus, Arrays.asList(ChargeStatusEnum.CHARGED.getCode(),
                        ChargeStatusEnum.BEING_CHARGED.getCode(),
                        ChargeStatusEnum.REGISTERED.getCode(),
                        ChargeStatusEnum.DEREGISTER.getCode()))
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        return recipeDetailMapper.deleteByExample(weekend) > 0;
    }

    @Override
    @DatabaseAnnotation
    public boolean deleteRecipeDetailPackageMainItem(List<Long> packageIdList, Integer hospitalCode) {
        if (CollUtil.isEmpty(packageIdList)) {
            return false;
        }
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria()
                .andIn(RecipeDetail::getPackageId, packageIdList)
                .andEqualTo(RecipeDetail::getPackageFlag, 1)
                .andNotIn(RecipeDetail::getStatus, Arrays.asList(ChargeStatusEnum.CHARGED.getCode(),
                        ChargeStatusEnum.BEING_CHARGED.getCode(),
                        ChargeStatusEnum.REGISTERED.getCode(),
                        ChargeStatusEnum.DEREGISTER.getCode()))
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        return recipeDetailMapper.deleteByExample(weekend) > 0;
    }


    /**
     * 根据ID获取处方明细
     *
     * @param detailNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public RecipeDetail getRecipeDetailById(Long detailNo, Integer hospitalCode) {
        RecipeDetail entity = new RecipeDetail();
        entity.setRecipeDetailNo(detailNo);
        entity.setHospitalCode(hospitalCode);
        //hospitalCode传了没意义 yutao noted
        return recipeDetailMapper.selectByPrimaryKey(entity);
    }

    /**
     * 根据ID获取处方明细
     *
     * @param detailNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public RecipeDetail tyMdcGetRecipeDetailById(Long detailNo, Integer hospitalCode) {
        Map<String,RecipeDetail> recipeDetailMap = TyMdc.get(TyKey.RECIPE_DETAIL_MAP);
        if(recipeDetailMap!=null){
            return recipeDetailMap.get("" + detailNo + "_" + hospitalCode);
        }
        RecipeDetail entity = new RecipeDetail();
        entity.setRecipeDetailNo(detailNo);
        entity.setHospitalCode(hospitalCode);
        //hospitalCode传了没意义 yutao noted
        return recipeDetailMapper.selectByPrimaryKey(entity);
    }

    /**
     * 根据ID获取处方明细
     *
     * @param detailNoList
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public List<RecipeDetail> getRecipeDetailByIdList(List<Long> detailNoList, Integer hospitalCode) {
        if(detailNoList == null||detailNoList.size()==0){
            return new ArrayList<RecipeDetail>();
        }
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);

        weekend.weekendCriteria().andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                .andIn(RecipeDetail::getRecipeDetailNo, detailNoList);
        return recipeDetailMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailByRecipeNosAndStatus(List<Long> recipeNos, Integer hospitalCode) {
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);

        weekend.weekendCriteria().andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                .andIn(RecipeDetail::getRecipeNo, recipeNos);

        final WeekendCriteria<RecipeDetail, Object> criteria = weekend.weekendCriteria()
                .orEqualTo(RecipeDetail::getStatus, "0")
                .orEqualTo(RecipeDetail::getStatus, "3");

        weekend.and(criteria);
        return recipeDetailMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailByRecipeNos(List<Long> recipeNos, Integer hospitalCode) {
        if (recipeNos == null || recipeNos.stream().count() == 0) {
            return new ArrayList<>();
        }
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);

        weekend.weekendCriteria().andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                .andIn(RecipeDetail::getRecipeNo, recipeNos);

        return recipeDetailMapper.selectByExample(weekend);
    }

    /**
     * 根据处方明细ID集合获取处方明细
     *
     * @param ids
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailByIds(List<Long> ids, Integer hospitalCode) {
        List<RecipeDetail> res = recipeDetailMapper.getRecipeDetailByIds(ids, hospitalCode, "");
        for (RecipeDetail recipeDetail : res) {
            recipeDetail.setDataFlag(1);
        }
        return res;
    }

    @Override
    @DatabaseAnnotation
    public Integer getDrugRecipeCountById(Long receptionNo, Integer hospitalCode) {
        Integer recipeCount = recipeDetailMapper.getDrugRecipeCountById(receptionNo, hospitalCode, "");
        return recipeCount;
    }

    /**
     * 根据就诊流水号判断是否处方
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean hasRecipeDetailByReceptionNo(Long receptionNo, Integer hospitalCode) {
        //RecipeDetail entity = new RecipeDetail();
        //entity.setReceptionNo(receptionNo);
        //return recipeDetailMapper.selectCount(entity) > 0;

        List<RecipeDetail> recipeDetailList = recipeDetailMapper.hasRecipeDetailByReceptionNo(receptionNo, hospitalCode);
        return recipeDetailList.size() > 0;

    }


    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeByReceptionNoAndFeeCategory(Long receptionNo, Integer hospitalCode, List<Integer> feeCategory, Integer dispensing) {
        return recipeDetailMapper.getRecipeByReceptionNoAndFeeCategory(receptionNo, hospitalCode, feeCategory, dispensing);
    }

    @Override
    public List<HistoryRecipeDetailDto> getInternetDrugRecipeByRegNo(String regNo, Integer hospitalCode) {
        if (hospitalCode == 1) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
        }
        return recipeDetailMapper.getInternetDrugRecipeByRegNo(regNo);
    }

    @Override
    @DatabaseAnnotation
    //@Cacheable(cacheNames = "getDrugRecipeByRegNo1", unless = "#result == null", key = "'SimpleKey ['+#regNo+','+#hospitalCode+']'")
    public List<RecipeDetail> getDrugRecipeByRegNo(Long regNo, Integer hospitalCode) {
        return recipeDetailMapper.getDrugRecipeByRegNo(regNo, hospitalCode, "");
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailsByRecipeId(Long recipeId) {
        final RecipeDetail entity = new RecipeDetail();
        entity.setRecipeId(recipeId);
        return recipeDetailMapper.select(entity);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailsByRecipeId(Long receptionNo, List<Integer> itemCategory) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        WeekendCriteria<RecipeDetail, Object> keywordCriteria = weekend.weekendCriteria();

        keywordCriteria.andEqualTo(RecipeDetail::getReceptionNo, receptionNo);
        if (itemCategory != null) {
            keywordCriteria.andIn(RecipeDetail::getFeeCategory, itemCategory);
        }
        WeekendCriteria<RecipeDetail, Object> keywordCriteria1 = weekend.weekendCriteria()
                .andEqualTo(RecipeDetail::getPackageFlag, 0)
                .orIsNull(RecipeDetail::getPackageFlag);
        weekend.orderBy("groupNo").orderBy("itemNo");
        weekend.and(keywordCriteria1);

        return recipeDetailMapper.selectByExample(weekend);
    }

    /**
     * 查询处方明细
     *
     * @param receptionNo  就诊流水号
     * @param itemCategory 收费类型
     * @return List<RecipeDetail>
     */
    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailsByReceptionNo(Long receptionNo, List<Integer> itemCategory) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.orderBy("groupNo").orderBy("itemNo");

        WeekendCriteria<RecipeDetail, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andEqualTo(RecipeDetail::getReceptionNo, receptionNo);
        if (itemCategory != null) {
            keywordCriteria.andIn(RecipeDetail::getFeeCategory, itemCategory);
        }

        return recipeDetailMapper.selectByExample(weekend);
    }


    /**
     * 根据挂号流水号查询处方明细
     *
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailByRegNo(Long regNo, Integer itemCategory) {
        RecipeDetail entity = new RecipeDetail();
        entity.setRegNo(regNo);
        if (itemCategory != null && itemCategory > 0) {
            entity.setFeeCategory(itemCategory);
        }
        return recipeDetailMapper.select(entity);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailsByReceptionNoAndNoCategory(Long receptionNo) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.orderBy("groupNo").orderBy("itemNo");

        WeekendCriteria<RecipeDetail, Object> keywordCriteria = weekend.weekendCriteria();
        keywordCriteria.andEqualTo(RecipeDetail::getReceptionNo, receptionNo);

        return recipeDetailMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getDrugRecipeDetailByRecipeId(Long receptionNo, Integer hospitalCode) {
        final Example example = new Example.Builder(RecipeDetail.class)
                .select("itemCode", "recipeDetailNo")
                .where(WeekendSqls.<RecipeDetail>custom().andEqualTo(RecipeDetail::getReceptionNo, receptionNo).andEqualTo(RecipeDetail::getHospitalCode, hospitalCode))
                .andWhere(WeekendSqls.<RecipeDetail>custom().andEqualTo(RecipeDetail::getFeeCategory, ItemCategoryEnum.WesternMed.getCategoryCode())
                        .orEqualTo(RecipeDetail::getFeeCategory, ItemCategoryEnum.ChinesePatMed.getCategoryCode().toString())).build();
        return recipeDetailMapper.selectByExample(example);
    }



    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailByRegNo(Long regNo) {
        return recipeDetailMapper.getDrugInfoByRegNo(regNo);
    }


    /**
     * 根据就诊流水号、项目编码，查询处方明细
     *
     * @param receptionNo
     * @param itemCode
     * @param hospitalCode
     * @param columns
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailsByItemCode(Long receptionNo, List<Integer> itemCode, Integer hospitalCode, String... columns) {
        if (itemCode == null || itemCode.size() == 0) {
            return new ArrayList<>();
        }
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        if (columns != null || columns.length > 0) {
            weekend.selectProperties(columns);
        }
        WeekendCriteria<RecipeDetail, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(RecipeDetail::getReceptionNo, receptionNo)
                .andIn(RecipeDetail::getItemCode, itemCode)
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        return recipeDetailMapper.selectByExample(weekend);
    }

    /**
     * 获取处方预存明细(根据预存流水号)
     *
     * @param preSaveNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailsBySaveNo(Long preSaveNo, Long recipeDetailId, Integer doctorId, Integer hospitalCode) {
        if (preSaveNo.equals(0L) && recipeDetailId.equals(0L)) {
            return new ArrayList<>();
        }
        RecipeDetail entity = new RecipeDetail();
        if (!preSaveNo.equals(0L)) {
            entity.setPreSaveNo(preSaveNo);
        }
        if (!recipeDetailId.equals(0L)) {
            entity.setRecipeDetailId(recipeDetailId);
        }
        entity.setHospitalCode(hospitalCode);
        return recipeDetailMapper.select(entity);
    }



    @Override
    @DatabaseAnnotation(name = DatasourceName.ZYEMR)
    public List<DrugInfo> getDrugInfo(Integer itemCode, String dose, Integer frequency) {
        return recipeDetailMapper.getDrugInfo(itemCode, dose, frequency);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZYEMR3)
    public List<DrugInfo> getDrugInfoTwo(Integer itemCode, String dose, Integer frequency) {
        return recipeDetailMapper.getDrugInfo(itemCode, dose, frequency);
    }

    /**
     * 根据就诊流水号，项目编码，医院id查询处方明细信息
     *
     * @param jzlsh
     * @param mxbm
     * @param hospitalCode
     * @return
     */
    @Override
    public RecipeDetail getMzCfMx(Long jzlsh, Integer mxbm, Integer hospitalCode) {
        RecipeDetail recipeDetail = new RecipeDetail();
        recipeDetail.setReceptionNo(jzlsh);
        recipeDetail.setItemCode(mxbm);
        recipeDetail.setHospitalCode(hospitalCode);
        //xxx: yutao 不知为什么原代码返回了一个空
//        return null;
        return recipeDetail;
    }

    @Override
    @DatabaseAnnotation
    public List<AuditMedicine> getRecipeDetailByRecipeNo(String recipeNo, String hospitalCode) {
        return recipeDetailMapper.getRecipeDetailByRecipeNo(recipeNo, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public Integer getRecipeDetailCount(Long receptionNo, List<Integer> itemCategory, Integer dispensing, Integer hospitalCode, Integer itemCode) {
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.setDistinct(true);
        weekend.selectProperties("itemCode");
        weekend.weekendCriteria().andEqualTo(RecipeDetail::getReceptionNo, receptionNo).andEqualTo(RecipeDetail::getDispensing, dispensing)
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode).andIn(RecipeDetail::getFeeCategory, itemCategory)
                .andNotEqualTo(RecipeDetail::getItemCode, itemCode);
        weekend.and(
                weekend.weekendCriteria().andNotEqualTo(RecipeDetail::getApplicationForm, "MECT").orIsNull(RecipeDetail::getApplicationForm));
        return recipeDetailMapper.selectByExample(weekend).size();
    }


    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailByLastDoctorId(Integer lastDoctorId, Long receptionNo, Integer itemCode, Integer hospitalCode, Long recipeDetailNo) {
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        final WeekendCriteria<RecipeDetail, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(RecipeDetail::getLastDoctorId, lastDoctorId)
                .andEqualTo(RecipeDetail::getReceptionNo, receptionNo)
                .andEqualTo(RecipeDetail::getItemCode, itemCode)
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        if (recipeDetailNo != null) {
            criteria.andNotEqualTo(RecipeDetail::getRecipeDetailNo, recipeDetailNo);
        }
        final WeekendCriteria<RecipeDetail, Object> criteria1 = weekend.weekendCriteria();
        criteria1.andNotEqualTo(RecipeDetail::getApplicationForm, "MECT")
                .orIsNull(RecipeDetail::getApplicationForm);
        weekend.and(criteria1);
        return recipeDetailMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailsByLastDoctorId(Integer lastDoctorId, Long receptionNo, Integer hospitalCode) {
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        final WeekendCriteria<RecipeDetail, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(RecipeDetail::getLastDoctorId, lastDoctorId)
                .andEqualTo(RecipeDetail::getReceptionNo, receptionNo)
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        final WeekendCriteria<RecipeDetail, Object> criteria1 = weekend.weekendCriteria();
        criteria1.andNotEqualTo(RecipeDetail::getApplicationForm, "MECT")
                .orIsNull(RecipeDetail::getApplicationForm);
        weekend.and(criteria1);
        return recipeDetailMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailByLastDoctorIdAndReceptionNos(Integer lastDoctorId, List<Long> receptionNos, Integer itemCode, Integer hospitalCode, Long recipeDetailNo) {
        if (receptionNos == null || receptionNos.isEmpty()) {
            return new ArrayList<>();
        }
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        final WeekendCriteria<RecipeDetail, Object> criteria = weekend.weekendCriteria();
        criteria.andNotEqualTo(RecipeDetail::getLastDoctorId, lastDoctorId)
                .andIn(RecipeDetail::getReceptionNo, receptionNos)
                .andEqualTo(RecipeDetail::getItemCode, itemCode)
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        if (recipeDetailNo != null) {
            criteria.andNotEqualTo(RecipeDetail::getRecipeDetailNo, recipeDetailNo);
        }
        final WeekendCriteria<RecipeDetail, Object> criteria1 = weekend.weekendCriteria();
        criteria1.andNotEqualTo(RecipeDetail::getApplicationForm, "MECT")
                .orIsNull(RecipeDetail::getApplicationForm);
        weekend.and(criteria1);
        return recipeDetailMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailsByLastDoctorIdAndReceptionNos(Integer lastDoctorId, List<Long> receptionNos, Integer hospitalCode) {
        if (receptionNos == null || receptionNos.isEmpty()) {
            return new ArrayList<>();
        }
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        final WeekendCriteria<RecipeDetail, Object> criteria = weekend.weekendCriteria();
        criteria.andNotEqualTo(RecipeDetail::getLastDoctorId, lastDoctorId)
                .andIn(RecipeDetail::getReceptionNo, receptionNos)
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);

        final WeekendCriteria<RecipeDetail, Object> criteria1 = weekend.weekendCriteria();
        criteria1.andNotEqualTo(RecipeDetail::getApplicationForm, "MECT")
                .orIsNull(RecipeDetail::getApplicationForm);
        weekend.and(criteria1);
        return recipeDetailMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> drugsSpeciesCount(Weekend<RecipeDetail> weekend) {
        return recipeDetailMapper.selectByExample(weekend);
    }


    @Override
    @DatabaseAnnotation
    public Integer drugsSpeciesCount(List<Integer> itemCategory, Integer dispensing, List<Long> receptionNo, Integer hospitalCode) {
        if (receptionNo.isEmpty()) {
            return 0;
        }
        final Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        final WeekendCriteria<RecipeDetail, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(RecipeDetail::getFeeCategory, itemCategory)
                .andIn(RecipeDetail::getReceptionNo, receptionNo).andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                .andEqualTo(RecipeDetail::getDispensing, dispensing);
        //return recipeDetailMapper.selectCountByExample(weekend);
        List<RecipeDetail> result = recipeDetailMapper.selectByExample(weekend);
        int count = result.stream().collect(Collectors.groupingBy(RecipeDetail::getItemCode)).size();
        return count;
    }


    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailByGroupNo(Integer groupNo, Integer hospitalCode, Long receptionNo) {
        final RecipeDetail record = new RecipeDetail();
        record.setGroupNo(groupNo);
        record.setHospitalCode(hospitalCode);
        record.setReceptionNo(receptionNo);
        return recipeDetailMapper.select(record);
    }

    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailByPackageId(Long receptionNo, Long packageId, Integer hospitalCode) {
        final RecipeDetail record = new RecipeDetail();
        record.setPackageId(packageId);
        record.setHospitalCode(hospitalCode);
        record.setReceptionNo(receptionNo);
        return recipeDetailMapper.select(record);
    }
    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailByPackageIdList(Long receptionNo, List<Long> packageIdList, Integer hospitalCode) {
        if (packageIdList == null || packageIdList.size() == 0) {
            return new ArrayList<>();
        }
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        WeekendCriteria<RecipeDetail, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                .andEqualTo(RecipeDetail::getReceptionNo, receptionNo)
                .andIn(RecipeDetail::getPackageId, packageIdList);
        return recipeDetailMapper.selectByExample(weekend);
    }


    @Override
    @DatabaseAnnotation
    public boolean updateRecipeStatus(Long receptionNo, Integer recipeNo, Integer recipeStatus, Integer hospitalCode) {
        Example example = null;
        if (!recipeNo.equals(0)) {
            example = new Example.Builder(RecipeDetail.class)
                    .where(WeekendSqls.<RecipeDetail>custom().andEqualTo(RecipeDetail::getRecipeNo, recipeNo)
                            .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                            .andNotIn(RecipeDetail::getStatus, Arrays.asList(1))
                            .andIn(RecipeDetail::getFeeCategory, Arrays.asList(12, 13, 14))).build();
        } else {
            example = new Example.Builder(RecipeDetail.class)
                    .where(WeekendSqls.<RecipeDetail>custom().andEqualTo(RecipeDetail::getReceptionNo, receptionNo).
                            andEqualTo(RecipeDetail::getHospitalCode, hospitalCode))
                    .andWhere(WeekendSqls.<RecipeDetail>custom().andEqualTo(RecipeDetail::getStatus, 0).orEqualTo(RecipeDetail::getStatus, "3")).build();
        }
        RecipeDetail record = new RecipeDetail();
        record.setRecipeStatus(recipeStatus);

//发送消息，清除缓存
        sendMq4ClearPreLoad(receptionNo, recipeNo, hospitalCode);

        return recipeDetailMapper.updateByExampleSelective(record, example) > 0;
    }

    /**
     * 向消息系统发送信息，通知清除preLoad中对应patId的数据
     * @param receptionNo
     * @param recipeNo
     * @param hospitalCode
     */
    private void sendMq4ClearPreLoad(Long receptionNo, Integer recipeNo, Integer hospitalCode) {
        try {
            Example example4PatId = null;
            if (!recipeNo.equals(0)) {
                example4PatId = new Example.Builder(RecipeDetail.class)
                        .where(WeekendSqls.<RecipeDetail>custom().andEqualTo(RecipeDetail::getRecipeNo, recipeNo)
                        ).build();
            } else {
                example4PatId = new Example.Builder(RecipeDetail.class)
                        .where(WeekendSqls.<RecipeDetail>custom().andEqualTo(RecipeDetail::getReceptionNo, receptionNo)
                        ).build();
            }
            List<RecipeDetail> recipeDetails = recipeDetailMapper.selectByExample(example4PatId);
            if (recipeDetails.size() > 0) {
                RecipeDetail recipeDetail = recipeDetails.get(0);
                Long regNo = recipeDetail.getRegNo();
                RegisterList regTbRegisterListTime = registerListMapper.getRegisterByRegNo(regNo, hospitalCode, "Reg_Tb_RegisterList_Time");
                Long patID = regTbRegisterListTime.getPatID();
                //XXX: yutao 2024/9/4 想办法快速得到patId,然后清除
                ReceptionListDto receptionListDto = new ReceptionListDto();
                receptionListDto.setPatID(patID);
                receptionListDto.setHospitalCode(hospitalCode);
                messageSender.sendClearBl(receptionListDto);
            }
        }catch (Exception e){
            Log.error("通知消除PreLoad失败 receptionNo: "+receptionNo+" recipeNo:"+recipeNo,e);
        }
    }


    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> checkRecipeStatus(Long receptionNo, Integer recipeNo, Integer hospitalCode) {
        Example example = new Example.Builder(RecipeDetail.class)
                .where(WeekendSqls.<RecipeDetail>custom().andEqualTo(RecipeDetail::getReceptionNo, receptionNo)
                        .andEqualTo(RecipeDetail::getRecipeNo, recipeNo)
                        .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                        .andNotIn(RecipeDetail::getStatus, Arrays.asList(-1, 1))
                        .andIn(RecipeDetail::getFeeCategory, Arrays.asList(12, 13, 14))
                        .andIn(RecipeDetail::getRecipeStatus, Arrays.asList(1, -1, 5))
                ).build();
        return recipeDetailMapper.selectByExample(example);
    }

    /**
     * 根据就诊流水号和处方明细状态获取处方明细
     *
     * @param recipeNo
     */
    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailByStatus(Long recipeNo) {
        return recipeDetailMapper.getRecipeDetailByStatus(recipeNo);
    }


    @Override
    @DatabaseAnnotation
    public List<ReceptionDrugDTO> getRecipeByReceptionFromMzysnew(Long receptionNo) {
        return recipeDetailMapper.getRecipeByReceptionFromMzysnew(receptionNo);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public List<ReceptionDrugDTO> getRecipeFromZy(String receptionNo) {
        return recipeDetailMapper.getRecipeFromOld(receptionNo);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS3)
    public List<ReceptionDrugDTO> getRecipeFromFy(String receptionNo) {
        return recipeDetailMapper.getRecipeFromOld(receptionNo);
    }


    /**
     * 查询治疗单列表
     *
     * @param regNo
     * @param type
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public List<TreatDto> getTreatDto(Long regNo, Integer type, Integer hospitalCode) {
        return recipeDetailMapper.getTreatDto(regNo, type, hospitalCode);
    }

    /**
     * 更新处方状态
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean updateRecipeTollStatus(Long receptionNo, Integer hospitalCode) {
        Example example = new Example.Builder(RecipeDetail.class)
                .where(WeekendSqls.<RecipeDetail>custom()
                        .andEqualTo(RecipeDetail::getReceptionNo, receptionNo)
                        .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                        .andNotIn(RecipeDetail::getStatus, Arrays.asList(1))
                ).build();
        RecipeDetail record = new RecipeDetail();
        record.setStatus(0);
        return recipeDetailMapper.updateByExampleSelective(record, example) > 0;

    }

    /**
     * 根据就诊流水号获取处方明细信息[只获取申请单数据]
     *
     * @param receptionNo
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getRecipeDetailsByReceptionNo(Long receptionNo, Integer hospitalCode) {
        return recipeDetailMapper.getRecipeDetailsByReceptionNo(receptionNo, hospitalCode);
    }

    /**
     * 根据就诊流水号和医院编码获取未审核通过的门诊处方明细记录
     *
     * @param receptionNo
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public List<RecipeDetail> getNoCheckedRecipeStatus(Integer operateType, Long receptionNo, Integer firstDoctorId, Integer hospitalCode) {
        return recipeDetailMapper.getNoCheckedRecipeStatus(operateType, receptionNo, firstDoctorId, hospitalCode);
    }


    @Override
    @DatabaseAnnotation
    public boolean deleteHerbsMakeInfo(Long recipeNo, Integer hospitalCode, Integer itemCode) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria().andEqualTo(RecipeDetail::getRecipeNo, recipeNo)
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                .andEqualTo(RecipeDetail::getRecipeCategory, ItemCategoryEnum.ChineseHerbMed.getCategoryCode())
                .andEqualTo(RecipeDetail::getItemCode, itemCode);
        return recipeDetailMapper.deleteByExample(weekend) > 0;
    }

    @Override
    public List<RecipeDetail> getPsychotherapyDrugDeptRecipeDetail(Integer hospitalCode, Long receptionNo, Integer drugDeptId) {
        Weekend<RecipeDetail> weekend = new Weekend<>(RecipeDetail.class);
        weekend.weekendCriteria().andEqualTo(RecipeDetail::getReceptionNo, receptionNo)
                .andEqualTo(RecipeDetail::getHospitalCode, hospitalCode)
                .andIn(RecipeDetail::getFeeCategory, Arrays.asList(12, 13))
                .andEqualTo(RecipeDetail::getStatus, ChargeStatusEnum.NO_CHARGE.getCode())
                .andNotEqualTo(RecipeDetail::getExecDept, drugDeptId);
        WeekendCriteria<RecipeDetail, Object> packageCriteria = weekend.weekendCriteria();
        packageCriteria.andEqualTo(RecipeDetail::getPackageId, 0).orIsNull(RecipeDetail::getPackageId);
        weekend.and(packageCriteria);
        return recipeDetailMapper.selectByExample(weekend);
    }


}
