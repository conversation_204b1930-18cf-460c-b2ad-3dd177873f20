package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
* 
* @TableName Sci_RegisterList_Project
*/
@Data
@Table(name = "Sci_RegisterList_Project")
public class SciRegisterlistProject implements Serializable {

    @Id
    @Column(name = "Id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    @Column(name = "projectid")
    private String projectid;

    @Column(name = "projectname")
    private String projectname;

    @Column(name = "regno")
    private Long regno;

    @Column(name = "hospitalcode")
    private Integer hospitalcode;

    @Column(name = "createtime")
    private Date createtime;

    @Column(name = "creator")
    private Integer creator;

    @Column(name = "updater")
    private Integer updater;

    @Column(name = "updatetime")
    private Date updatetime;

    @Column(name = "followup")
    private Date followup;

    @Column(name = "followupTitle")
    private Date followupTitle;

}
