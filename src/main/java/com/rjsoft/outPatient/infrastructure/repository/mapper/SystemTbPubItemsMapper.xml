<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.SystemTbPubItemsMapper">

    <select id="getChargeItemTv" resultType="com.rjsoft.outPatient.domain.recipe.dto.SystemTvPubItemsDto">
        select itemCode,itemName,inputCode1,inputCode2,inputCode3,itemCategory,childItemCategory,clinicExpensePrice,clinicNonExpensePrice,
               clinicQty,clinicUnit,wardUnit,dosage,dosageUnit,drugGuage,exeDept,stopped,abClass,productPlace,tradePrice,retailPrice,
               itemTypeToAdv,checkCode,mzItemId,wardItemId,useRange,hospitalId,globalId,isSet,exeHospitalId
        from System_Tv_PubItems
        where itemCode = #{itemCode} and hospitalId = #{hospitalCode} and (exeDept = #{exeDept} or exeDept = 0)
          and exeHospitalId = #{exeHospitalId} and stopped = #{stopped} and useRange = 1
    </select>

    <select id="getChargeItemByIdIsStopped" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.SystemTbPubItems">
        select distinct a.itemCode,a.itemName,a.inputCode1,a.inputCode2,a.inputCode3,a.itemCategory,a.childItemCategory,a.clinicExpensePrice,a.clinicNonExpensePrice,
        a.clinicQty,a.clinicUnit,a.wardUnit,a.dosage,a.dosageUnit,a.drugGuage,a.exeDept,a.stopped,a.abClass,a.productPlace,tradePrice,a.retailPrice,
        a.itemTypeToAdv,a.checkCode,a.mzItemId,a.wardItemId,a.useRange,a.hospitalId,a.globalId,a.isSet
        from System_Tb_PubItems a
        INNER JOIN Drug_Tb_DrugInfomation d ON a.ItemCode = d.DrugId AND a.HospitalId = d.HospitalId
        INNER JOIN Drug_Tb_LocalDeptDrug l ON d.DrugId = l.DrugId AND d.HospitalId = l.HospitalId and l.DeptId=#{deptId}
        where a.itemCode = #{itemCode} and a.hospitalId = #{hospitalCode}  and a.stopped = #{stopped} and a.useRange in (0,1)
        and d.IsStop = #{ydStopped}
        and l.IsUsed = #{ypUsed}
    </select>
    <select id="listChargeItemByIdIsStopped" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.SystemTbPubItems">
        select distinct a.itemCode,a.itemName,a.inputCode1,a.inputCode2,a.inputCode3,a.itemCategory,a.childItemCategory,a.clinicExpensePrice,a.clinicNonExpensePrice,
        a.clinicQty,a.clinicUnit,a.wardUnit,a.dosage,a.dosageUnit,a.drugGuage,a.exeDept,a.stopped,a.abClass,a.productPlace,tradePrice,a.retailPrice,
        a.itemTypeToAdv,a.checkCode,a.mzItemId,a.wardItemId,a.useRange,a.hospitalId,a.globalId,a.isSet
        from System_Tb_PubItems a
        INNER JOIN Drug_Tb_DrugInfomation d ON a.ItemCode = d.DrugId AND a.HospitalId = d.HospitalId
        INNER JOIN Drug_Tb_LocalDeptDrug l ON d.DrugId = l.DrugId AND d.HospitalId = l.HospitalId
        and l.DeptId in <foreach item="deptId" index="index" collection="exeDeptIdList" open="(" separator="," close=")">#{deptId}</foreach>
        <where>
            <if test="itemCodeList != null and itemCodeList.size() > 0">
                a.itemCode IN <foreach item="code" index="index" collection="itemCodeList" open="(" separator="," close=")">
                #{code}
            </foreach>
            </if>
            AND a.hospitalId = #{hospitalCode}
            AND a.stopped = #{stopped}
            AND a.useRange IN (0,1)
            AND d.IsStop = #{ydStopped}
            AND l.IsUsed = #{ypUsed}
        </where>
    </select>

    <select id="getChargeItemById" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.SystemTbPubItems">
        select a.itemCode,a.itemName,a.inputCode1,a.inputCode2,a.inputCode3,a.itemCategory,a.childItemCategory,a.clinicExpensePrice,a.clinicNonExpensePrice,
               a.clinicQty,a.clinicUnit,a.wardUnit,a.dosage,a.dosageUnit,a.drugGuage,a.exeDept,a.stopped,a.abClass,a.productPlace,tradePrice,a.retailPrice,
               a.itemTypeToAdv,a.checkCode,a.mzItemId,a.wardItemId,a.useRange,a.hospitalId,a.globalId,a.isSet
        from System_Tb_PubItems a

        where a.itemCode = #{itemCode} and a.hospitalId = #{hospitalCode}  and a.stopped = #{stopped} and a.useRange in (0,1)
    </select>

    <select id="queryChargeItemByItemCodeList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.SystemTbPubItems">
        select a.itemCode,a.itemName,a.inputCode1,a.inputCode2,a.inputCode3,a.itemCategory,a.childItemCategory,a.clinicExpensePrice,a.clinicNonExpensePrice,
        a.clinicQty,a.clinicUnit,a.wardUnit,a.dosage,a.dosageUnit,a.drugGuage,a.exeDept,a.stopped,a.abClass,a.productPlace,tradePrice,a.retailPrice,
        a.itemTypeToAdv,a.checkCode,a.mzItemId,a.wardItemId,a.useRange,a.hospitalId,a.globalId,a.isSet
        from System_Tb_PubItems a
        where a.itemCode in <foreach item='item' index='index' collection='itemCodeList' open='(' separator=',' close=')'>#{item}</foreach>
        and a.hospitalId = #{hospitalCode}  and a.stopped = #{stopped} and a.useRange in (0,1)
    </select>

    <select id="queryDrugInfoByItemCodeList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.DrugInfomation">
        select *
        from Drug_Tb_DrugInfomation d
        where DrugId in <foreach item='item' index='index' collection='itemCodeList' open='(' separator=',' close=')'>#{item}</foreach>
        and hospitalId = #{hospitalCode}  and IsStop = #{ydStopped}
    </select>
    <select id="queryLocalDeptDrugByItemCodeList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.LocalDeptDrug">
        select *
        from Drug_Tb_LocalDeptDrug
        where DrugId in <foreach item='item' index='index' collection='itemCodeList' open='(' separator=',' close=')'>#{item}</foreach>
        and hospitalId = #{hospitalCode}
        and DeptId in <foreach item='item' index='index' collection='deptIdList' open='(' separator=',' close=')'>#{item}</foreach>
        and IsUsed = #{ypUsed}
    </select>
    <select id="queryChargeItemByIdList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.SystemTbPubItems">
        SELECT
            ItemCode AS itemCode,
            ItemName AS itemName,
            InputCode1 AS inputCode1,
            InputCode2 AS inputCode2,
            InputCode3 AS inputCode3,
            ItemCategory AS itemCategory,
            ChildItemCategory AS childItemCategory,
            ClinicExpensePrice AS clinicExpensePrice,
            ClinicNonExpensePrice AS clinicNonExpensePrice,
            ClinicQty AS clinicQty,
            ClinicUnit AS clinicUnit,
            WardUnit AS wardUnit,
            Dosage AS dosage,
            DosageUnit AS dosageUnit,
            DrugGuage AS drugGuage,
            ExeDept AS exeDept,
            Stopped AS stopped,
            ABClass AS abClass,
            ProductPlace AS productPlace,
            TradePrice AS tradePrice,
            RetailPrice AS retailPrice,
            ItemTypeToAdv AS itemTypeToAdv,
            CheckCode AS checkCode,
            MzItemId AS mzItemId,
            WardItemId AS wardItemId,
            UseRange AS useRange,
            HospitalId AS hospitalId,
            GlobalId AS globalId,
            IsSet AS isSet,
            isFixed AS isFixed,
            MedicalType1 AS medicalType1,
            MedicalType2 AS medicalType2,
            MedicalType3 AS medicalType3
        FROM System_Tb_PubItems (nolock)
        where hospitalId = #{hospitalCode}
        and ItemCode in
        <foreach item='item' index='index' collection='itemCodeList' open='(' separator=',' close=')'>
            #{item}
        </foreach>
    </select>
</mapper>