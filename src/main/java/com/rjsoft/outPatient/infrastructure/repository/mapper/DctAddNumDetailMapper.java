package com.rjsoft.outPatient.infrastructure.repository.mapper;


import com.rjsoft.outPatient.domain.reserve.dto.*;
import com.rjsoft.outPatient.infrastructure.repository.entity.DctAddNumDetail;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

public interface DctAddNumDetailMapper extends BaseMapper<DctAddNumDetail> {

    /**
     * 查询加号列表
     *
     * @param type
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    List<DctAddNumDetail> queryDctAddNumDetail(@Param("type") Integer type,
                                               @Param("doctorId") Integer doctorId,
                                               @Param("hospitalCode") Integer hospitalCode,
                                               @Param("visitFlag") Integer visitFlag,
                                               @Param("cardNo") String cardNo,
                                               @Param("patName") String patName,
                                               @Param("certificateNo") String certificateNo);


    /**
     * 获取医生排班信息
     *
     * @param doctorId
     * @param hospitalCode
     * @param deptCode
     * @return
     */
    List<DoctorAddNum> getDoctorSchedulingInfo(@Param("doctorId") Integer doctorId,
                                               @Param("hospitalCode") Integer hospitalCode,
                                               @Param("deptCode") Integer deptCode);

    /**
     * 获取医生当日加号的数量
     *
     * @param subjectId
     * @param hospitalCode
     * @param deptCode
     * @param doctorId
     * @return
     */
    int queryDctAddNumDetailNum(@Param("subjectId") Integer subjectId,
                                @Param("hospitalCode") Integer hospitalCode,
                                @Param("deptCode") Integer deptCode,
                                @Param("doctorId") Integer doctorId);


    //----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    //usp_DoctAddNumber

    /**
     * 加号按钮功能-获取医生排班信息
     *
     * @param doctorId
     * @param hospitalCode
     * @param deptCode
     * @return
     */
    List<DoctorAddNum> getDoctorScheduling(@Param("doctorId") Integer doctorId,
                                           @Param("deptCode") Integer deptCode,
                                           @Param("hospitalCode") Integer hospitalCode,
                                           @Param("timeMs") String timeMs);


    /**
     * 查询当天是否有该患者加号信息
     *
     * @param certificateNo
     * @param hospitalCode
     * @return
     */
    int getAddNumDetailCount(@Param("certificateNo") String certificateNo,
                             @Param("hospitalCode") Integer hospitalCode);


    /**
     * 获取总号源
     *
     * @param hospitalCode
     * @param mainId
     * @return
     */
    ReserveNum getVisitNum(@Param("hospitalCode") Integer hospitalCode, @Param("mainId") Integer mainId);


    /**
     * 获取加号表中加号序号
     *
     * @param subjectId
     * @param hospitalCode
     * @return
     */
    Integer getSeqNum(@Param("subjectId") Integer subjectId, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 获取科目详情表中中加号序号
     *
     * @param subjectId
     * @param hospitalCode
     * @return
     */
    Integer getSubjectSeqNum(@Param("subjectId") Integer subjectId, @Param("hospitalCode") Integer hospitalCode);


    //------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

}
