package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.admissionApplicationForm.dto.HistoryDiagnoseResult;
import com.rjsoft.outPatient.infrastructure.repository.entity.OldDiagnose;
import com.ruijing.code.util.StringUtils;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;
import java.util.Set;

public interface OldDiagnoseMapper extends Mapper<OldDiagnose> {

    /**
     * 根据挂号流水号获取老门诊诊断信息
     *
     * @param patIds
     * @param tableName
     * @param tableNameRecord
     */
    OldDiagnose getOldDiagnoseInfo(@Param("list") List<String> patIds, @Param("tableName") String tableName,
                                   @Param("tableNameRecord") String tableNameRecord);

//    /**
//     * 根据就诊流水号获取挂号流水号
//     * @param reception
//     */
//    List<String> getOldRegNoString(@Param("list") Set<String> reception);
//
//    /**
//     * 根据挂号流水号唯一标识获取挂号流水号
//     * @param regNoString
//     */
//    List<Integer> getOldRegNo(@Param("list") List<String> regNoString);


    /**
     * 根据就诊流水号获取诊断记录
     *
     * @param receptionNo 就诊流水号
     * @param table       表名称
     */
    default List<OldDiagnose> getOldDiagnose(String receptionNo, String table) {
        Weekend<OldDiagnose> weekend = new Weekend<>(OldDiagnose.class);
        WeekendCriteria<OldDiagnose, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(OldDiagnose::getJzlsh, receptionNo);
        if (!StringUtils.isEmpty(table)) {
            weekend.setTableName(table);
        }
        return selectByExample(weekend);
    }


    /**
     * 查询患者诊断信息
     *
     * @param oldDataRegNoList
     * @return
     */
    List<HistoryDiagnoseResult> getAllFromOldData(@Param("list") List<String> oldDataRegNoList);
}
