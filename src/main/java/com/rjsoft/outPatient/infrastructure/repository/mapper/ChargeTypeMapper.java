package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeType;
import tk.mybatis.mapper.common.Mapper;

/**
 * <AUTHOR>
 * @since 2021/9/15-9:53 上午
 */
public interface ChargeTypeMapper extends Mapper<ChargeType> {
    /**
     * 获取收费类型
     * @param chargeTypeCode
     * @param hospitalId
     */
    default ChargeType getChargeType(Integer chargeTypeCode,Integer hospitalId){
        ChargeType chargeType = new ChargeType();
        chargeType.setChargeTypeCode(chargeTypeCode);
        chargeType.setHospitalCode(hospitalId);
        chargeType.setIsDeleted(0);
        return selectOne(chargeType);
    }

}
