package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.utils.ObjectUtils;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.common.enums.RecipeTypeEnum;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.recipe.dto.CheckRecipeDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeItem;
import com.rjsoft.outPatient.infrastructure.repository.entity.ItemInfoDept;
import com.rjsoft.outPatient.infrastructure.repository.entity.ItemSetDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.PreRecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.mapper.PreRecipeDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 门诊处方
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class PreRecipeDetailRepositoryImpl implements PreRecipeDetailRepository {

    PreRecipeDetailMapper preRecipeDetailMapper;

    ItemInfoDeptRepository itemInfoDeptRepository;

    ItemSetDetailRepository itemSetDetailRepository;

    SysFunctionRepository sysFunctionRepository;

    ChargeItemRepository chargeItemRepository;

    MectItemRepository mectItemRepository;

    RecipeDetailRepository recipeDetailRepository;

    @Override
    @DatabaseAnnotation
    public List<PreRecipeDetail> getPreRecipeDetailsByApplyId(Long applyId, Integer hospitalCode) {
        final PreRecipeDetail entity = new PreRecipeDetail();
        entity.setExamineNo(Converter.toString(applyId));
        entity.setHospitalCode(hospitalCode);
        return preRecipeDetailMapper.select(entity);
    }

    /**
     * 获取处方预存明细(根据套餐ID)
     *
     * @param packageId
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<PreRecipeDetail> getPreRecipeDetailsByPackageId(Long packageId, Integer doctorId, Integer hospitalCode, Long preSaveNo, CheckRecipeDto inputData, Map errInfo) {
        List<PreRecipeDetail> result = new ArrayList<>();
        final List<ItemSetDetail> setDetailList = itemSetDetailRepository.getItemSetDetailBySetId(Math.toIntExact(packageId), hospitalCode);
        // 不是系统套餐
        if (setDetailList.isEmpty()) {
            return result;
        }
        Long recipeDetailNo = 0L;
        Integer updateFlag = 0;
        //如果预存流水号不为空，检查一下预存表数据，如果预存表数据中不存在此收费项目，则从集合中移除(化验套餐中可能有项目已经通过申请单开立了，因此要移除掉，避免重复开设)
        if (preSaveNo != null) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
            List<PreRecipeDetail> preRecipeDetailList = getPreRecipeDetailsBySaveNo(preSaveNo, doctorId, hospitalCode);
            final long dataFrom = preRecipeDetailList.stream().filter(p -> ObjectUtils.compare(p.getPreSaveType(), 1)).count();
            if (dataFrom > 0) {
                //保证至少走过一次前置校验，不然通过输入码录入的信息，套餐明细无法保存
                List<Integer> hasItems = preRecipeDetailList.stream().map(PreRecipeDetail::getItemCode).collect(Collectors.toList());
                setDetailList.removeIf(p -> !hasItems.contains(p.getItemId()));
            }
            DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
            if (preSaveNo > 0) {
                recipeDetailNo = preRecipeDetailList.get(0).getRecipeDetailNo();
                updateFlag = 1;
            }
        }

        if (recipeDetailNo.equals(0L)) {
            recipeDetailNo = sysFunctionRepository.getGetSequences(SequenceEnum.RECIPE_DETAIL_NO);
        }
        final Map<Integer, ItemSetDetail> setDetailMap = setDetailList.stream().collect(Collectors.toMap(ItemSetDetail::getItemId, Function.identity()));
        // 套餐主项目
        final ChargeItem packageMainItem = chargeItemRepository.getChargeItemById(Math.toIntExact(packageId), hospitalCode);
        // 查询套餐中所有的收费项目
        List<ChargeItem> chargeItemList = setDetailList.parallelStream().map(ItemSetDetail::getItemId).map(i -> chargeItemRepository.getChargeItemById(i, hospitalCode)).collect(Collectors.toList());
        if (preSaveNo == null) {
            preSaveNo = sysFunctionRepository.getGetSequences(SequenceEnum.PRE_SAVE_NO);  //优化后第一次校验不会删除预存，无需重新生成
        }
        final boolean isMectItem = mectItemRepository.isMectItem(Math.toIntExact(packageId), hospitalCode);
        if(isMectItem && inputData.getReceiveDeptId()!=null){
            //判断麻醉科权限
            Log.info("进入mect判断麻醉科权限流程");
            int receiveDeptid = inputData.getReceiveDeptId();
            List<Integer> preItemcodeList = new ArrayList<Integer>();
            preItemcodeList.add(Math.toIntExact(packageId));
            List<ItemInfoDept> itemInfoDeptList = itemInfoDeptRepository.getItemInfoListByItemCodes(preItemcodeList, hospitalCode, receiveDeptid, 1, null);
            if(itemInfoDeptList.size() < 1)
            {
                Log.info(receiveDeptid + "无mect可使用权限" + preItemcodeList);
                errInfo.put("errInfo", "当前科室无MECT可用权限或未配置");
                return new ArrayList<>();
            }
        }

        // 套餐主项目
        final PreRecipeDetail packageMainPreRecipeDetail = new PreRecipeDetail();
        packageMainPreRecipeDetail.ChangeChargeItem(packageMainItem);
        packageMainPreRecipeDetail.setPackageFlag(1);
        packageMainPreRecipeDetail.setPackageId(recipeDetailNo);
        packageMainPreRecipeDetail.setRecipeDetailNo(recipeDetailNo);
        packageMainPreRecipeDetail.setPreSaveNo(preSaveNo);
        packageMainPreRecipeDetail.setQuantity(BigDecimal.ONE);
        packageMainPreRecipeDetail.setAmount(BigDecimal.ZERO);
        if (updateFlag.equals(1)) {
            packageMainPreRecipeDetail.setOpFlag(1);
        }
        result.add(packageMainPreRecipeDetail);

        chargeItemList.stream().filter(Objects::nonNull).forEach(chargeItem -> {
            final PreRecipeDetail preRecipeDetail = new PreRecipeDetail();
            preRecipeDetail.ChangeChargeItem(chargeItem);
            preRecipeDetail.setQuantity(BigDecimal.ONE);
            preRecipeDetail.setRecipeDetailNo(sysFunctionRepository.getGetSequences(SequenceEnum.RECIPE_DETAIL_NO));
            preRecipeDetail.setPreSaveNo(packageMainPreRecipeDetail.getPreSaveNo());
            preRecipeDetail.setPackageId(packageMainPreRecipeDetail.getRecipeDetailNo());
            final ItemSetDetail itemSetDetail = setDetailMap.get(chargeItem.getItemCode());
            preRecipeDetail.setApplicationForm(packageMainPreRecipeDetail.getApplicationForm());
            preRecipeDetail.setQuantity(itemSetDetail.getQty() == null ? BigDecimal.ONE : itemSetDetail.getQty());
            preRecipeDetail.setOpFlag(packageMainPreRecipeDetail.getOpFlag());
            if (isMectItem && ItemCategoryEnum.isDrug(chargeItem.getItemCategory())) {
                preRecipeDetail.setDoseUnit(chargeItem.getDosageUnit().trim());
                preRecipeDetail.setDoseUnit(chargeItem.getClinicUnit());
                preRecipeDetail.setFrequency(12);
                //需求9193 门诊医生：mect套餐注射液默认用法改为“静脉注射”
                preRecipeDetail.setUsage(3);
                preRecipeDetail.setUnit(chargeItem.getClinicUnit());
                preRecipeDetail.setRecipeCategory(RecipeTypeEnum.NARCOSIS.getRecipeType());
                preRecipeDetail.setApplicationForm("MECT");
            }
            //packageMainPreRecipeDetail.setAmount(packageMainPreRecipeDetail.getAmount().add(preRecipeDetail.getAmount()));
            result.add(preRecipeDetail);
        });

        result.forEach(preRecipeDetail -> {
            preRecipeDetail.setDays(1);
            preRecipeDetail.setOpFlag(0);
            preRecipeDetail.setOpCode(doctorId);
            preRecipeDetail.setOpTime(sysFunctionRepository.getDate());
        });
        packageMainPreRecipeDetail.setPrice(packageMainPreRecipeDetail.getAmount());
        return result;
    }

    @Override
    @DatabaseAnnotation
    public PreRecipeDetail getPreRecipeDetailsByRecipeDetailNo(Integer receptionNo, Integer hospitalCode) {
        PreRecipeDetail entity = new PreRecipeDetail();
        entity.setRecipeDetailNo(Converter.toInt64(receptionNo));
        entity.setHospitalCode(hospitalCode);
        return preRecipeDetailMapper.selectOne(entity);


    }
    /**
     * 获取处方预存明细(根据预存流水号)
     *
     * @param preSaveNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<PreRecipeDetail> getPreRecipeDetailsBySaveNo(Long preSaveNo, Integer doctorId, Integer hospitalCode) {
        PreRecipeDetail entity = new PreRecipeDetail();
        entity.setPreSaveNo(preSaveNo);
        entity.setHospitalCode(hospitalCode);

        List<PreRecipeDetail> preRecipeDetails = preRecipeDetailMapper.select(entity);
        for (PreRecipeDetail preRecipeDetail : preRecipeDetails) {
            preRecipeDetail.setOpFlag(1);
            preRecipeDetail.setOpCode(doctorId);
        }

        // 如果是 MECT 项目，修改了MECT根项目的数量，要将子项目的数量统一修改
        PreRecipeDetail packageMainPreRecipeDetail = new PreRecipeDetail();
        boolean isMectItem = false;
        for (PreRecipeDetail preRecipeDetail : preRecipeDetails) {
            if (preRecipeDetail.getRecipeDetailNo().equals(preRecipeDetail.getPackageId())) {
                isMectItem = mectItemRepository.isMectItem(preRecipeDetail.getItemCode(), hospitalCode);
                if (isMectItem) {
                    packageMainPreRecipeDetail = preRecipeDetail;
                    break;
                }
            }
        }

        if (isMectItem) {
            for (PreRecipeDetail preRecipeDetail : preRecipeDetails) {
                preRecipeDetail.setQuantity(packageMainPreRecipeDetail.getQuantity());
                preRecipeDetail.calculateAmount();
            }
        }

        return preRecipeDetails;
    }

    /**
     * 根据预存流水号删除预存记录
     *
     * @param preSaveNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public void deletePreRecipeDetailBySaveNo(Long preSaveNo, Integer hospitalCode) {
        preRecipeDetailMapper.deleteByExample(new Example.Builder(PreRecipeDetail.class)
                .where(WeekendSqls.<PreRecipeDetail>custom()
                        .andEqualTo(PreRecipeDetail::getPreSaveNo, preSaveNo)
                        .andEqualTo(PreRecipeDetail::getHospitalCode, hospitalCode)).build());
    }


    @Override
    @DatabaseAnnotation
    public Integer deletePreRecipeDetailByDetailId(Long detailId, Integer hospitalCode) {
        return preRecipeDetailMapper.deleteByExample(new Example.Builder(PreRecipeDetail.class)
                .where(WeekendSqls.<PreRecipeDetail>custom()
                        .andEqualTo(PreRecipeDetail::getRecipeDetailNo, detailId)
                        .andEqualTo(PreRecipeDetail::getHospitalCode, hospitalCode)).build());
    }

    @Override
    @DatabaseAnnotation
    public Integer deletePreRecipeDetailByDetailIdList(List<Long> detailIdList, Integer hospitalCode) {
        if(detailIdList == null|| detailIdList.size()==0){
            return 0;
        }
        return preRecipeDetailMapper.deleteByExample(new Example.Builder(PreRecipeDetail.class)
                .where(WeekendSqls.<PreRecipeDetail>custom()
                        .andIn(PreRecipeDetail::getRecipeDetailNo, detailIdList)
                        .andEqualTo(PreRecipeDetail::getHospitalCode, hospitalCode)).build());
    }



    @Override
    @DatabaseAnnotation
    public PreRecipeDetail getPreRecipeById(Long preSaveNo, Long detailNo, Integer hospitalCode) {
        PreRecipeDetail entity = new PreRecipeDetail();
        entity.setPreSaveNo(preSaveNo);
        entity.setRecipeDetailNo(detailNo);
        entity.setHospitalCode(hospitalCode);
        return preRecipeDetailMapper.selectOne(entity);
    }

    /**
     * 根据预存流水号删除预存明细
     *
     * @param preSaveNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean delPreRecipeDetailByPreNo(Long preSaveNo, Integer hospitalCode) {
        PreRecipeDetail entity = new PreRecipeDetail();
        entity.setPreSaveNo(preSaveNo);
        entity.setHospitalCode(hospitalCode);
        preRecipeDetailMapper.delete(entity);
        return true;
    }



    /**
     * 检查是否存在数据并保存
     *
     * @param preRecipeDetail
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean savePreRecipeDetail(PreRecipeDetail preRecipeDetail) {
        boolean success = false;
        switch (preRecipeDetail.getOpFlag()) {
            case -1:
                success = preRecipeDetailMapper.deleteByPrimaryKey(preRecipeDetail) >= 0;
                recipeDetailRepository.deleteRecipeDetailByDetailId(preRecipeDetail.getRecipeDetailNo(), preRecipeDetail.getHospitalCode());
                break;
            case 0:
                success = preRecipeDetailMapper.insert(preRecipeDetail) > 0;
                break;
            case 1:
                //避免高并发下因 savePreRecipe 赋值后，canSaveRecipe 获取旧数据引发的更新问题
                if (ItemCategoryEnum.isDrug(preRecipeDetail.getFeeCategory())) {
                    success = preRecipeDetailMapper.updateByPrimaryKeySelective(preRecipeDetail) >= 0;
                } else {
                    success = preRecipeDetailMapper.updateByPrimaryKey(preRecipeDetail) >= 0;
                }
                break;
            default:
                break;
        }
        return success;
    }
    /**
     * 检查是否存在数据并保存
     *
     * @param preRecipeDetailList
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean savePreRecipeDetailList(List<PreRecipeDetail> preRecipeDetailList) {
        boolean success = true;
        //处理小数问题,否则会数据精度丢失
        preRecipeDetailList.forEach(obj -> {
            obj.setQuantity(obj.getQuantity().setScale(6, RoundingMode.HALF_UP));
            obj.setPrice(obj.getPrice().setScale(6, RoundingMode.HALF_UP));
            obj.setAmount(obj.getAmount().setScale(2, RoundingMode.HALF_UP));
        });
        int listCount = preRecipeDetailMapper.batchInsert(preRecipeDetailList);
        if(listCount!= preRecipeDetailList.size()){
            return false;
        }
        return success;
    }
    @Override
    @DatabaseAnnotation
    public boolean delPreRecipeDetailList(List<PreRecipeDetail> insertList) {
        //fixme: 还没实现
        return true;
    }

    @Override
    @DatabaseAnnotation
    @Transactional(rollbackFor = {Exception.class})
    public boolean savePreRecipeDetails(List<PreRecipeDetail> preRecipeDetails) {
        boolean success = true;
        for (PreRecipeDetail preRecipeDetail : preRecipeDetails) {
            success = savePreRecipeDetail(preRecipeDetail);
            if (!success) {
                return false;
            }
        }
        return true;
    }


}
