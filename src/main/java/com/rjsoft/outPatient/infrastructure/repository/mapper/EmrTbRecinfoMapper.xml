<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.EmrTbRecinfoMapper">
    <resultMap id="HistoryDiagnoseResult" type="com.rjsoft.outPatient.infrastructure.repository.entity.EmrTbRecinfo">
        <result property="recId" column="RecId"/>
        <result property="hospNo" column="HospNo"/>
        <result property="patId" column="PatId"/>
        <result property="recTypeId" column="RecTypeId"/>
        <result property="recDate" column="RecDate"/>
        <result property="recTitle" column="RecTitle"/>
        <result property="recTempId" column="RecTempId"/>
        <result property="recTempVer" column="RecTempVer"/>
        <result property="doctorId" column="doctorId"/>
        <result property="doctorName" column="DoctorName"/>
        <result property="recStatus" column="RecStatus"/>
        <result property="creatorId" column="CreatorId"/>
        <result property="createDate" column="CreateDate"/>
        <result property="updatorId" column="UpdatorId"/>
        <result property="updateDate" column="UpdateDate"/>
        <result property="delFlag" column="DelFlag"/>
        <result property="printFlag" column="PrintFlag"/>
        <result property="hospitalId" column="HospitalId"/>
        <result property="regNo" column="RegNo"/>
        <result property="sealStatus" column="SealStatus"/>
        <result property="recTempCode" column="RecTempCode"/>
        <association property="emrTbRecinfoContent" javaType="com.rjsoft.outPatient.infrastructure.repository.entity.EmrTbRecinfoContent">
            <result property="contentId" column="ContentId"/>
            <result property="recId" column="RecId"/>
            <result property="content" column="Content"/>
            <result property="creatorId" column="CreatorId"/>
            <result property="createDate" column="CreateDate"/>
            <result property="hospitalId" column="HospitalId"/>
            <result property="recencryText" column="RecencryText"/>
            <result property="contentHtml" column="ContentHtml"/>
        </association>
    </resultMap>

    <select id="getEmrByRegNo" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.EmrTbRecinfo">
        SELECT
            *
        FROM
            EMR_Tb_RecInfo a (nolock)
        <where>
            a.DelFlag = 0
            <if test="regNos.size > 0">
                and a.RegNo in
                <foreach collection="regNos" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and a.RecTempCode in
            <foreach collection="recTempCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="hospitalCode != null">
                and a.HospitalId = #{hospitalCode}
            </if>
        </where>
    </select>
</mapper>
