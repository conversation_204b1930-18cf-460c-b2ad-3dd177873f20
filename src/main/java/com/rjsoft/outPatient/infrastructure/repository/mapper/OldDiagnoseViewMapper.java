package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.OldDiagnoseView;
import com.ruijing.code.util.StringUtils;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface OldDiagnoseViewMapper extends Mapper<OldDiagnoseView> {


    /**
     * 根据就诊流水号获取诊断记录
     *
     * @param receptionNo 就诊流水号
     * @param table       表名称
     */
    default List<OldDiagnoseView> getOldDiagnose(String receptionNo, String table) {
        Weekend<OldDiagnoseView> weekend = new Weekend<>(OldDiagnoseView.class);
        WeekendCriteria<OldDiagnoseView, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(OldDiagnoseView::getJzlsh, receptionNo);
        if (!StringUtils.isEmpty(table)) {
            weekend.setTableName(table);
        }
        return selectByExample(weekend);
    }


}
