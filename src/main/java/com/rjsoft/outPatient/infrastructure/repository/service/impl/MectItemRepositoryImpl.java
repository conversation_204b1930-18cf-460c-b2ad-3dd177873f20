package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.MectItem;
import com.rjsoft.outPatient.infrastructure.repository.mapper.MectItemMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.MectItemRepository;
import lombok.AllArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Mect项目配置
 * <AUTHOR>
@Service
@AllArgsConstructor
public class MectItemRepositoryImpl implements MectItemRepository {

    MectItemMapper mectItemMapper;

    /**
     * 根据类型获取Mect项目
     * @param type
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<MectItem> getMectItemsByType(String type, Integer hospitalCode) {
        MectItem entity=new MectItem();
        entity.setType(type);
        entity.setHospitalCode(hospitalCode);
        return mectItemMapper.select(entity);
    }

    /**
     * 保存Mect项目
     * @param entity
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean saveMectItems(MectItem entity) {
        boolean success;
        boolean hasData;
        hasData = mectItemMapper.existsWithPrimaryKey(entity);
        if (hasData) {
            success = mectItemMapper.updateByPrimaryKey(entity) > 0;
        } else {
            success = mectItemMapper.insert(entity) > 0;
        }
        return success;
    }

    @Override
    @DatabaseAnnotation
    @Cacheable(cacheNames = "isMectItem", unless = "#result == null")
    public boolean isMectItem(Integer itemCode, Integer hospitalCode) {
        if (itemCode == null || hospitalCode == null) {
            return false;
        }
        final Weekend<MectItem> weekend = new Weekend<>(MectItem.class);
        weekend.setCountProperty("itemCode");
        weekend.weekendCriteria().andEqualTo(MectItem::getItemCode, itemCode).andEqualTo(MectItem::getHospitalCode, hospitalCode);
        final int count = mectItemMapper.selectCountByExample(weekend);
        return count > 0;
    }

    @Override
    @DatabaseAnnotation
    @Cacheable(cacheNames = "mectItemMulCount", unless = "#result == null")
    public Map<Integer, Integer> mectItemMulCount(List<Integer> itemCodeList, Integer hospitalCode) {
        if (itemCodeList == null || hospitalCode == null) {
            return new HashMap<>();
        }
        if(itemCodeList.size()== 0){
            return new HashMap<>();
        }
        final Weekend<MectItem> weekend = new Weekend<>(MectItem.class);
//        weekend.setCountProperty("itemCode");
        weekend.weekendCriteria().andIn(MectItem::getItemCode, itemCodeList)
                .andEqualTo(MectItem::getHospitalCode, hospitalCode);
//        weekend.selectProperties("ItemCode");
        //表中字段很少，直接查了。
        List<MectItem> mectItems = mectItemMapper.selectByExample(weekend);
        Map<Integer, Integer> codeCountMap = new HashMap<>();
        for (MectItem mi : mectItems) {
            Integer count = codeCountMap.get(mi.getItemCode());
            if(count == null){
                count = 0;
            }
            count+=1;
            codeCountMap.put(mi.getItemCode(),count);
        }
        return codeCountMap;
    }

    /**
     * 删除Mect项目
     *
     * @param itemCode
     * @param hospitalCode
     * @param type
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean delMectItems(Integer itemCode, Integer hospitalCode, String type) {
        MectItem entity = new MectItem();
        entity.setItemCode(itemCode);
        entity.setHospitalCode(hospitalCode);
        entity.setType(type);
        mectItemMapper.delete(entity);
        return true;
    }
}
