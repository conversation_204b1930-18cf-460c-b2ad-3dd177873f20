package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.common.enums.RegistrationFormStatusEnum;
import com.rjsoft.outPatient.common.enums.SeriousIllnessSourceEnum;
import com.rjsoft.outPatient.common.enums.YesOrNoEnum;
import com.rjsoft.outPatient.config.HisConfig;
import com.rjsoft.outPatient.domain.seriousIllness.vo.SeriousIllnessRegisterVo;
import com.rjsoft.outPatient.infrastructure.repository.entity.Hospital;
import com.rjsoft.outPatient.infrastructure.repository.entity.OldSeriousIllnessRegister;
import com.rjsoft.outPatient.infrastructure.repository.entity.SeriousIllnessRegister;
import com.rjsoft.outPatient.infrastructure.repository.mapper.HospitalMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.OldSeriousIllnessRegisterMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SeriousIllnessRegisterMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SeriousIllnessRegisterOldMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.SeriousIllnessRegisterRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.TreatmentItemRepository;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.text.ParseException;
import java.util.*;

/**
 * 大病登记
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class SeriousIllnessRegisterRepositoryImpl implements SeriousIllnessRegisterRepository {

    SeriousIllnessRegisterMapper seriousIllnessRegisterMapper;
    SeriousIllnessRegisterOldMapper seriousIllnessRegisterOldMapper;
    OldSeriousIllnessRegisterMapper oldSeriousIllnessRegisterMapper;
    HospitalMapper hospitalMapper;
    HisConfig hisConfig;
    TreatmentItemRepository treatmentItemRepository;

    @Override
    @DatabaseAnnotation
    public SeriousIllnessRegister getSeriousIllnessRegisterById(Integer id, Integer hospitalCode) {
        SeriousIllnessRegister entity = new SeriousIllnessRegister();
        entity.setId(id);
        entity.setHospitalCode(hospitalCode);
        return seriousIllnessRegisterMapper.selectByPrimaryKey(entity);
    }

    @Override
    public OldSeriousIllnessRegister getOldSeriousIllnessRegisterById(Integer id,Integer hospitalCode) {
        String datasourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode)?DatasourceName.MZYS:DatasourceName.MZYS3;
        DataSourceSwitchAspect.changeDataSource(datasourceName);
        OldSeriousIllnessRegister oldSeriousIllnessRegister = new OldSeriousIllnessRegister();
        oldSeriousIllnessRegister.setId(id);
        return oldSeriousIllnessRegisterMapper.selectByPrimaryKey(oldSeriousIllnessRegister);
    }

    @Override
    @DatabaseAnnotation
    public List<SeriousIllnessRegister> getSeriousIllnessRegister(Date startTime, Date endTime, String cardNo, String patName, Integer status, Integer hospitalCode, String hisCardNo) {
        Weekend<SeriousIllnessRegister> weekend = new Weekend<>(SeriousIllnessRegister.class);
        WeekendCriteria<SeriousIllnessRegister, Object> weekendCriteria = weekend.weekendCriteria();
        if (!StringUtils.isEmpty(patName)) {
            weekendCriteria.andLike(SeriousIllnessRegister::getPatName, "%" + patName + "%");
        }
        if (!StringUtils.isEmpty(cardNo)) {
            weekendCriteria.andEqualTo(SeriousIllnessRegister::getCardNo, cardNo);
        }
        if (!StringUtils.isEmpty(hisCardNo)) {
            weekendCriteria.andEqualTo(SeriousIllnessRegister::getHisCardNo, hisCardNo);
        }
        if(startTime!=null&&endTime!=null) {
            weekendCriteria.andBetween(SeriousIllnessRegister::getCreTime,startTime,endTime);
        }
        Optional.ofNullable(status).ifPresent(s -> weekendCriteria.andEqualTo(SeriousIllnessRegister::getState, s));
        if(hospitalCode!=null){
            weekendCriteria.andEqualTo(SeriousIllnessRegister::getHospitalCode, hospitalCode);
        }
        weekendCriteria.andEqualTo(SeriousIllnessRegister::getDelFlag, YesOrNoEnum.NO.getCode());
        List<SeriousIllnessRegister> seriousIllnessRegisterList = seriousIllnessRegisterMapper.selectByExample(weekend);
        return seriousIllnessRegisterList;
    }

    @Override
    @DatabaseAnnotation
    public boolean saveSeriousIllnessRegister(SeriousIllnessRegister entity) {
        return seriousIllnessRegisterMapper.insertSelective(entity) > 0;
    }

    @Override
    @DatabaseAnnotation
    public boolean updateSeriousIllness(SeriousIllnessRegister entity) {
        return seriousIllnessRegisterMapper.updateByPrimaryKeySelective(entity) > 0;
    }

    @Override
    @DatabaseAnnotation
    public boolean updateSeriousIllnessRegisterCancel(SeriousIllnessRegister entity) {
        if(StringUtils.isEmpty(entity.getTrtDclaDetlSn())){
            return false;
        }else{
            Weekend<SeriousIllnessRegister> weekend = new Weekend<>(SeriousIllnessRegister.class);
            WeekendCriteria<SeriousIllnessRegister, Object> weekendCriteria = weekend.weekendCriteria();
            weekendCriteria.andEqualTo(SeriousIllnessRegister::getTrtDclaDetlSn,entity.getTrtDclaDetlSn())
                    .andIsNotNull(SeriousIllnessRegister::getTrtDclaDetlSn);
            WeekendCriteria<SeriousIllnessRegister, Object> weekendCriteria1 = weekend.weekendCriteria();
            weekendCriteria1.andEqualTo(SeriousIllnessRegister::getYbSerialNo,entity.getTrtDclaDetlSn())
                    .andIsNotNull(SeriousIllnessRegister::getYbSerialNo);
            weekend.or(weekendCriteria1);
            entity.setTrtDclaDetlSn(null);
            return seriousIllnessRegisterMapper.updateByExampleSelective(entity,weekend) > 0;
        }
    }

    @Override
    public boolean updateOldSeriousIllness(OldSeriousIllnessRegister entity) {
        String datasourceName = HospitalClassify.GENERAL.getHospitalCode().equals(entity.getHospitalCode())?DatasourceName.MZYS:DatasourceName.MZYS3;
        DataSourceSwitchAspect.changeDataSource(datasourceName);
        return oldSeriousIllnessRegisterMapper.updateByPrimaryKeySelective(entity)>0;
    }

    @Override
    public boolean updateOldSeriousIllnessRegisterCancel(OldSeriousIllnessRegister entity) {
        if(StringUtils.isEmpty(entity.getYbSerialNo())){
            return false;
        }else{
            String datasourceName = HospitalClassify.GENERAL.getHospitalCode().equals(entity.getHospitalCode())?DatasourceName.MZYS:DatasourceName.MZYS3;
            DataSourceSwitchAspect.changeDataSource(datasourceName);
            Weekend<OldSeriousIllnessRegister> weekend = new Weekend<>(OldSeriousIllnessRegister.class);
            weekend.weekendCriteria().andEqualTo(OldSeriousIllnessRegister::getYbSerialNo,entity.getYbSerialNo())
                    .andIsNotNull(OldSeriousIllnessRegister::getYbSerialNo);
            entity.setYbSerialNo(null);
            return oldSeriousIllnessRegisterMapper.updateByExampleSelective(entity,weekend)>0;
        }
    }

    @Override
    public boolean updateOldRegister(OldSeriousIllnessRegister entity) {
        String datasourceName = HospitalClassify.GENERAL.getHospitalCode().equals(entity.getHospitalCode())?DatasourceName.MZYS:DatasourceName.MZYS3;
        DataSourceSwitchAspect.changeDataSource(datasourceName);
        return oldSeriousIllnessRegisterMapper.updateByPrimaryKeySelective(entity) > 0;
    }

    @Override
    @DatabaseAnnotation
    public boolean delSeriousIllnessRegisterById(Integer id, Integer hospitalCode, String opName, Integer operator) {
        SeriousIllnessRegister entity = new SeriousIllnessRegister(id, hospitalCode);
        entity = seriousIllnessRegisterMapper.selectOne(entity);
        if (ObjectUtils.isEmpty(entity)) {
            throw new RuntimeException("未根据id查询到数据");
        }
        final Integer state = entity.getState();
        if (RegistrationFormStatusEnum.unalterable(state)) {
            throw new RuntimeException("只允许删除「未提交、已打回」的大病登记，当前操作的登记单状态为：「 " + RegistrationFormStatusEnum.getValue(state) + " 」");
        }
        entity.setUpdName(opName);
        entity.setUpdTime(new Date());
        entity.setUpdDoctorId(operator);
        entity.setDelFlag(YesOrNoEnum.YES.getCode());
        return seriousIllnessRegisterMapper.updateByPrimaryKeySelective(entity) > 0;
    }

    /**
     * 根据医院编号获取hospital
     *
     * @param hospitalCode
     * @return com.rjsoft.outPatient.infrastructure.repository.entity.Hospital
     * <AUTHOR>
     **/
    @Override
    @DatabaseAnnotation(name = "HISDB")
    public Hospital queryHospitalByHospitalCode(String hospitalCode) {
        return hospitalMapper.queryHospitalByHospitalCode(hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public boolean cancelSeriousIllnessApply(Integer id,Integer operator,String opName) {
        SeriousIllnessRegister sr = new SeriousIllnessRegister();
        sr.setId(id);
        sr.setState(RegistrationFormStatusEnum.CANCEL.getCode());
        sr.setUpdDoctorId(operator);
        sr.setUpdName(opName);
        sr.setUpdTime(new Date());
        return seriousIllnessRegisterMapper.updateByPrimaryKeySelective(sr)>0?true:false;
    }

    @Override
    @DatabaseAnnotation(name = "HISDB")
    public String getSFZ(String cardNo, String hisCardNo, Integer hospitalId) {
        String sfz = oldSeriousIllnessRegisterMapper.getSFZ(cardNo,hisCardNo,hospitalId);
        return sfz;
    }

    @Override
    @DatabaseAnnotation
    public List<SeriousIllnessRegisterVo> getNewSeriousIllnessList(Date startTime, Date endTime, String cardNo, String patName, Integer hospitalCode, String hisCardNo,String patSfz,List<Integer> statusList) {
        return seriousIllnessRegisterMapper.getNewList(startTime,endTime,cardNo,patName,hospitalCode,hisCardNo,patSfz,statusList);
    }

    @Override
    public List<SeriousIllnessRegisterVo> getOldSeriousIllnessList(Date startTime, Date endTime, String cardNo, String patName, Integer hospitalCode, String hisCardNo, List<Integer> statusList, Integer doctorNo,String  patSFZ) {
        List<SeriousIllnessRegisterVo> allList = new ArrayList<>();
        if(HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode)&&hospitalCode!=null){
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
            List<SeriousIllnessRegisterVo> mzsyList = seriousIllnessRegisterOldMapper.getOldList(startTime,endTime,cardNo,patName,hospitalCode,hisCardNo,hisConfig.getOldMZYSIP(),patSFZ,statusList);
            mzsyList.stream().forEach(e->{e.setHospitalCode(HospitalClassify.GENERAL.getHospitalCode());e.setOldFlag(true);});
            allList.addAll(mzsyList);
        } else if (HospitalClassify.BRANCH.getHospitalCode().equals(hospitalCode)&&hospitalCode!=null){
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
            List<SeriousIllnessRegisterVo> mzsy3List = seriousIllnessRegisterOldMapper.getOldList(startTime,endTime,cardNo,patName,hospitalCode,hisCardNo,hisConfig.getOldMZYSIP(),patSFZ,statusList);
            mzsy3List.stream().forEach(e->{e.setHospitalCode(HospitalClassify.BRANCH.getHospitalCode());e.setOldFlag(true);});
            allList.addAll(mzsy3List);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
            List<SeriousIllnessRegisterVo> mzsyList = seriousIllnessRegisterOldMapper.getOldList(startTime,endTime,cardNo,patName,hospitalCode,hisCardNo,hisConfig.getOldMZYSIP(),patSFZ,statusList);
            mzsyList.stream().forEach(e->{e.setHospitalCode(HospitalClassify.GENERAL.getHospitalCode());e.setOldFlag(true);});
            allList.addAll(mzsyList);
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
            List<SeriousIllnessRegisterVo> mzsy3List = seriousIllnessRegisterOldMapper.getOldList(startTime,endTime,cardNo,patName,hospitalCode,hisCardNo,hisConfig.getOldMZYSIP(),patSFZ,statusList);
            mzsy3List.stream().forEach(e->{e.setHospitalCode(HospitalClassify.BRANCH.getHospitalCode());e.setOldFlag(true);});
            allList.addAll(mzsy3List);
        }
        return allList;
    }

    @Override
    @DatabaseAnnotation
    public SeriousIllnessRegister getNewSeriousIllnessSurplusDays(Integer patId, String idCard , Integer hospitalCode) {
        Weekend<SeriousIllnessRegister> weekend = new Weekend<>(SeriousIllnessRegister.class);
        WeekendCriteria<SeriousIllnessRegister, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(SeriousIllnessRegister::getDelFlag,0);
        weekendCriteria.andEqualTo(SeriousIllnessRegister::getHospitalCode,hospitalCode);
        weekendCriteria.andEqualTo(SeriousIllnessRegister::getPatId,patId);
        weekendCriteria.andEqualTo(SeriousIllnessRegister::getPatSfz,idCard);
        weekendCriteria.andEqualTo(SeriousIllnessRegister::getState,RegistrationFormStatusEnum.REGISTER_TO_THE_CENTER.getCode());
        weekend.setOrderByClause("SealTime desc");
        List<SeriousIllnessRegister> seriousIllnessRegister = seriousIllnessRegisterMapper.selectByExample(weekend);
        return seriousIllnessRegister != null ? seriousIllnessRegister.get(0) : null;
    }

    @Override
    public Date getOldSeriousIllnessSurplusDays(String patId, String idCard, Integer hospitalCode) {
        String datasourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode)?DatasourceName.MZYS:DatasourceName.MZYS3;
        DataSourceSwitchAspect.changeDataSource(datasourceName);
        Date registerTime = seriousIllnessRegisterOldMapper.getRegisterTimeByPatIdAndPatSFZ(patId,idCard);
        return registerTime;
    }

}
