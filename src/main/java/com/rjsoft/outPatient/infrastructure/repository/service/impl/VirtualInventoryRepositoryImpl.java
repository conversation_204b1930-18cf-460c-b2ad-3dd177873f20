package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.rjsoft.common.configuration.SysConfigImpl;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.dictionary.AutoTranslateAspect;
import com.rjsoft.common.utils.ObjectUtils;
import com.rjsoft.common.utils.TyMdc;
import com.rjsoft.outPatient.common.DicTypeConst;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.common.enums.RecipeTypeEnum;
import com.rjsoft.outPatient.domain.recipe.constant.TyKey;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SysFunctionMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.VirtualInventoryMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.VirtualInventoryRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/9/8-11:15 上午
 */
@Service
@AllArgsConstructor
public class VirtualInventoryRepositoryImpl implements VirtualInventoryRepository {

    private final VirtualInventoryMapper virtualInventoryMapper;
    private final ChargeItemRepositoryImpl chargeItemRepository;
    private final SysFunctionMapper sysFunctionMapper;
    private final RecipeDetailMapper recipeDetailMapper;
    private final SysConfigImpl sysConfig;
    private final AutoTranslateAspect autoTranslateAspect;
    DepartmentRepositoryImpl departmentRepository;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<VirtualInventory> getAllByDrugIdAndDeptId(Integer drugId, Integer deptId, Integer hospitalId) {
        if (drugId == null || deptId == null || hospitalId == null) {
            return null;
        }
        final VirtualInventory entity = new VirtualInventory(drugId, deptId, hospitalId);
        return virtualInventoryMapper.select(entity);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public BigDecimal getStorePackFactorInventoryByDrugIdAndDeptId(Integer drugId, Integer deptId, Integer hospitalId) {
        final BigDecimal storePackFactorInventory = virtualInventoryMapper.getStorePackFactorInventoryByDrugIdAndDeptId(drugId, deptId, hospitalId);
        return Optional.ofNullable(storePackFactorInventory).orElse(BigDecimal.ZERO);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public BigDecimal getClinicPackFactorInventoryByDrugIdAndDeptId(Integer drugId, Integer deptId, Integer hospitalId) {
        Map<String, BigDecimal> stockMap = TyMdc.get(TyKey.STOCK_MAP);

        if (CollUtil.isEmpty(stockMap) || !stockMap.containsKey(deptId+"_"+drugId)) {
            final BigDecimal clinicPackFactorInventory = virtualInventoryMapper.getClinicPackFactorInventoryByDrugIdAndDeptId(drugId, deptId, hospitalId);
            return Optional.ofNullable(clinicPackFactorInventory).orElse(BigDecimal.ZERO);
        }
        return Optional.ofNullable(stockMap.get(deptId+"_"+drugId)).orElse(BigDecimal.ZERO);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<DeptItempStock> getClinicPackFactorInventoryByDrugIdAndDeptId(List<Integer> drugIdList, List<Integer> deptIdList) {
        if (drugIdList == null || drugIdList.size()==0){
            return new ArrayList<>();
        }
        if (deptIdList == null || deptIdList.size()==0){
            return new ArrayList<>();
        }
        List<DeptItempStock> list = virtualInventoryMapper.getClinicPackFactorInventoryByDrugIdAndDeptIdList(drugIdList, deptIdList);
        for (DeptItempStock dis : list) {
            BigDecimal stock = Optional.ofNullable(dis.getStock()).orElse(BigDecimal.ZERO);
            dis.setStock(stock);
        }
        return list;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public BigDecimal getInHospPackFactorInventoryByDrugIdAndDeptId(Integer drugId, Integer deptId, Integer hospitalId) {
        final BigDecimal clinicPackFactorInventory = virtualInventoryMapper.getInHospPackFactorInventoryByDrugIdAndDeptId(drugId, deptId, hospitalId);
        return Optional.ofNullable(clinicPackFactorInventory).orElse(BigDecimal.ZERO);
    }

    public String getDictKeyByValue(String hospital, String dicTypeConst, String value) {
        Map<String, String> map = sysConfig.getAutoTranslateDicInCache(hospital, dicTypeConst);
        // value 查找 key
        for (String key : map.keySet()) {
            if (value.equals(map.get(key))) {
                return key;
            }
        }
        return null;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Integer inOrOutOfWarehouse(List<Recipe> recipeList, Integer changeType) {
        int result = 0;
        for (Recipe recipe : recipeList) {
            for (RecipeDetail recipeDetail : recipe.getRecipeDetails()) {
                //非药品和外配药品不扣库存
                if (ItemCategoryEnum.notDrug(recipeDetail.getFeeCategory())
                || recipeDetail.getRecipeCategory().equals(RecipeTypeEnum.TAKE_OUT.getRecipeType())) {
                    continue;
                }
                final ChargeItem chargeItem = chargeItemRepository.getChargeItemById(recipeDetail.getItemCode(), recipeDetail.getHospitalCode());
                if(chargeItem==null) {
                    throw new RuntimeException("收费项目【" + recipeDetail.getItemName() + "(" + recipeDetail.getItemCode() + ")】已失效");
                }

                final VirtualInventory record = new VirtualInventory(recipeDetail, chargeItem, virtualInventoryMapper.getNextId());
                record.setCreateOn(sysFunctionMapper.getDate());
                Department department = departmentRepository.getDepartmentByDeptId(recipeDetail.getExecDept(), recipeDetail.getHospitalCode());
                if(department!=null&&3==department.getDrugType()) {
                    record.setTableName("Drug_Tb_StoreChangeInPatientVirtual");
                }
                // 查询包装单位字典 ，通过 value 查询 key
                String packUnit = getDictKeyByValue(recipeDetail.getHospitalCode().toString(), DicTypeConst.PACKING_UNIT, recipeDetail.getUnit());
                record.setPackUnit(packUnit == null ? null : Integer.valueOf(packUnit));

                if (changeType == null) {
                    // 新增或者修改
                    DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
                    final RecipeDetail oldRecipeDetail = recipeDetailMapper.getRecipeDetailByDetailNo(recipeDetail.getRecipeDetailNo(), recipeDetail.getHospitalCode());
                    if (oldRecipeDetail == null) {
                        // 新增收费项目-去库存
                        record.setQuantity(BigDecimal.ZERO.subtract(recipeDetail.getQuantity()));
                    } else if (!oldRecipeDetail.getItemCode().equals(recipeDetail.getItemCode())) {
                        DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
                        // 修改收费项目，原收费项目退回库存、修改后收费项目去库存
                        record.setDrugId(recipeDetail.getItemCode());
                        record.setQuantity(BigDecimal.ZERO.subtract(recipeDetail.getQuantity()));
                        result += virtualInventoryMapper.insert(record);

                        packUnit = getDictKeyByValue(recipeDetail.getHospitalCode().toString(), DicTypeConst.PACKING_UNIT, recipeDetail.getUnit());
                        record.setPackUnit(packUnit == null ? null : Integer.valueOf(packUnit));
                        record.setDrugId(oldRecipeDetail.getItemCode());
                        record.setId(Math.toIntExact(virtualInventoryMapper.getNextId()));
                        result += virtualInventoryMapper.insert(record);
                        continue;
                    } else if (ObjectUtils.compare(oldRecipeDetail.getQuantity(), recipeDetail.getQuantity())) {
                        // 是修改操作，但收费项目没有改变，项目数量也没有改变，不进行任何操作
                        continue;
                    } else {
                        // 是修改操作，收费项目没有改变，项目数量改变了
                        record.setQuantity(oldRecipeDetail.getQuantity().subtract(recipeDetail.getQuantity()));
                    }
                } else if (changeType == 15) {
                    record.setQuantity(BigDecimal.ZERO.subtract(recipeDetail.getQuantity()));
                }
                DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
                result += virtualInventoryMapper.insert(record);
            }
        }
        return result;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Integer inOrOutOfWarehouseByRecipeDetail(RecipeDetail recipeDetail, Integer changeType) {
        int result = 0;
        //非药品和外配药品不扣库存
        if (ItemCategoryEnum.notDrug(recipeDetail.getFeeCategory()) || recipeDetail.getRecipeCategory().equals(RecipeTypeEnum.TAKE_OUT.getRecipeType())) {
            return result;
        }
        final ChargeItem chargeItem = chargeItemRepository.getChargeItemById(recipeDetail.getItemCode(), recipeDetail.getHospitalCode());
        if (chargeItem == null) {
            throw new RuntimeException("收费项目【" + recipeDetail.getItemName() + "(" + recipeDetail.getItemCode() + ")】已失效");
        }
        final VirtualInventory record = new VirtualInventory(recipeDetail, chargeItem, virtualInventoryMapper.getNextId());
        record.setCreateOn(sysFunctionMapper.getDate());
        Department department = departmentRepository.getDepartmentByDeptId(recipeDetail.getExecDept(), recipeDetail.getHospitalCode());
        if (department != null && 3 == department.getDrugType()) {
            record.setTableName("Drug_Tb_StoreChangeInPatientVirtual");
        }
        // 查询包装单位字典 ，通过 value 查询 key
        String packUnit = getDictKeyByValue(recipeDetail.getHospitalCode().toString(), DicTypeConst.PACKING_UNIT, recipeDetail.getUnit());
        record.setPackUnit(packUnit == null ? null : Integer.valueOf(packUnit));
        if (changeType == 15) {
            record.setQuantity(BigDecimal.ZERO.subtract(recipeDetail.getQuantity()));
        } else if (changeType == 16) {
            record.setQuantity(recipeDetail.getQuantity());
        }
        DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
        result += virtualInventoryMapper.insert(record);
        return result;
    }

    @Override
    public Integer inOfWarehouse(List<Recipe> recipeList) {
        return this.inOrOutOfWarehouse(recipeList, 16);
    }

    @Override
    public Integer outOfWarehouse(List<Recipe> recipeList) {
        return this.inOrOutOfWarehouse(recipeList, 15);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public void deleteRecord(Integer drugId, Integer deptId, Integer hospitalId, Integer sourceDetailId) {
        virtualInventoryMapper.deleteRecord(drugId, deptId, hospitalId, sourceDetailId);
    }
}
