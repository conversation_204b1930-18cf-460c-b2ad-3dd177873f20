package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.rjsoft.common.SystemNo;
import com.rjsoft.common.configuration.SysConfig;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.param.RequestHead;
import com.rjsoft.common.param.SearchParam;
import com.rjsoft.outPatient.common.DicTypeConst;
import com.rjsoft.outPatient.common.consts.SysLocalConfigKey;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.common.enums.ItemCategoryEnum;
import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeItem;
import com.rjsoft.outPatient.infrastructure.repository.entity.ExecDeptConfig;
import com.rjsoft.outPatient.infrastructure.repository.entity.ExecDeptTime;
import com.rjsoft.outPatient.infrastructure.repository.entity.SystemTbPubItems;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ExecDeptConfigMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ExecDeptTimeMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.*;
import com.ruijing.code.api.HeadInfo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import tk.mybatis.mapper.weekend.Weekend;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 执行科室配置
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class ExecDeptConfigRepositoryImpl implements ExecDeptConfigRepository {

    ExecDeptConfigMapper execDeptConfigMapper;
    ChargeItemRepository chargeItemRepository;
    ExecDeptTimeRepository execDeptTimeRepository;
    SysConfig sysConfig;
    SysConfigRepository sysConfigRepository;
    SystemTbPubItemsRepository systemTbPubItemsRepository;

    /**
     * 保存执行科室配置
     *
     * @param config
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean saveExecDeptConfig(ExecDeptConfig config) {
        boolean hasData = execDeptConfigMapper.existsWithPrimaryKey(config);
        if (hasData) {
            return execDeptConfigMapper.updateByPrimaryKey(config) > 0;
        } else {
            return execDeptConfigMapper.insert(config) > 0;
        }
    }

    /**
     * 查询执行科室内容信息，判断 是否允许保存
     *
     * @param configId
     * @param configType
     * @param feeCategory
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean getExecDept(Integer configId, String configType, Integer feeCategory, Integer hospitalCode) {
        ExecDeptConfig entity = new ExecDeptConfig();
        entity.setConfigId(configId);
        entity.setConfigType(configType);
        entity.setFeeCategory(feeCategory);
        entity.setHospitalCode(hospitalCode);
        List<ExecDeptConfig> execDeptConfigList = execDeptConfigMapper.select(entity);
        if (execDeptConfigList.size() > 0) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 删除执行科室配置
     *
     * @param id
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean delExecDeptConfig(Integer id, Integer hospitalCode) {
        ExecDeptConfig entity = new ExecDeptConfig();
        entity.setId(id);
        entity.setHospitalCode(hospitalCode);
        return execDeptConfigMapper.deleteByPrimaryKey(entity) > 0;
    }

    /**
     * 查询执行科室列表
     *
     * @param searchParam
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<ExecDeptConfig> getExecDeptConfigByInputCode(SearchParam searchParam) {
        String configType = Converter.toString(searchParam.getKeys().get("configType"));
        final String feeCategoryStr = searchParam.getKeys().get("feeCategory");

        final Integer feeCategory = StringUtils.isBlank(feeCategoryStr) ? null : Converter.toInt32(feeCategoryStr);
        Integer hospitalCode = searchParam.getHospitalCode();

        PageHelper.startPage(searchParam.getPageNum(), searchParam.getPageSize(), "configName");
        List<ExecDeptConfig> list = execDeptConfigMapper.getExecDeptByInputCode(configType, feeCategory, hospitalCode);
        PageInfo pageInfo = new PageInfo<>(list);
        searchParam.setTotalCount(Converter.toInt32(pageInfo.getTotal()));
        return list;
    }


    /**
     * 根据医院编码查询执行科室列表
     *
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<ExecDeptConfig> getExecDeptConfigFeeCategory(Integer hospitalCode) {
        Weekend<ExecDeptConfig> weekend = new Weekend<>(ExecDeptConfig.class);
        weekend.weekendCriteria().andEqualTo(ExecDeptConfig::getHospitalCode, hospitalCode)
                .andNotEqualTo(ExecDeptConfig::getFeeCategory, "0");
        return execDeptConfigMapper.selectByExample(weekend);
    }


    /**
     * 获取默认执行科室
     *
     * @param regDept
     * @param itemCode
     * @param itemCategory
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public Integer getDefaultExecDept(Integer regDept, Integer itemCode, Integer itemCategory, Integer hospitalCode, String loginIp) {
        List<ExecDeptConfig> execDeptConfigList = getExecDeptConfigs(itemCode, itemCategory, regDept, hospitalCode);
        if (execDeptConfigList.size() <= 0) {
            return null;
        }
        Integer execDept = execDeptConfigList.get(0).getExecDept();
        Log.info("患者对应科室信息： " + execDept);

        if (hospitalCode.equals(HospitalClassify.BRANCH.getHospitalCode())) {
            return execDept;
        }

        // 当前主机所属科室  0 : 普通精神；1: 心理咨询；2：全部
        String localConfigDeptCode = sysConfigRepository.getLocalConfig(SystemNo.OUTPATIENT, SysLocalConfigKey.OUTPATIENT_DEPT_CODE, hospitalCode == null ? null : hospitalCode + "", loginIp);
        if (localConfigDeptCode == null) {
            localConfigDeptCode = "";
        }
        localConfigDeptCode = localConfigDeptCode.trim();
        Log.info("loginIp: " + loginIp + " localConfigDeptCode: " + localConfigDeptCode);

        //"全部"默认为精神科
        if (localConfigDeptCode.equals("2")) {
            localConfigDeptCode = "0";
        }

        //设置本机心理质询,精神科对应的默认药房
        Integer localDeptId = 0;
        if (localConfigDeptCode.equals("0")) {
            localDeptId = 1073;
        } else if (localConfigDeptCode.equals("1")) {
            localDeptId = 1076;
        }

        List<ExecDeptConfig> localExecDeptConfigList = getExecDeptByItemCode(null, itemCategory, localDeptId, hospitalCode);
        if (localExecDeptConfigList.size() <= 0) {
            return null;
        }
        Integer localExecDept = localExecDeptConfigList.get(0).getExecDept();
        Log.info("本机对应科室信息： " + localExecDept);


        Integer drugTimeDept = 0;
        if ("1".equals(localConfigDeptCode)) {
            //根据时间匹配对应的收费项目
            ExecDeptTime execDeptTime = execDeptTimeRepository.getExecDeptTime(localConfigDeptCode, hospitalCode);
            if (ItemCategoryEnum.WesternMed.getCategoryCode().equals(itemCategory) && (!drugTimeDept.equals(execDeptTime.getWesternMed()))) {
                drugTimeDept = execDeptTime.getWesternMed();
            } else if (ItemCategoryEnum.ChinesePatMed.getCategoryCode().equals(itemCategory) && (!drugTimeDept.equals(execDeptTime.getChinesePatMed()))) {
                drugTimeDept = execDeptTime.getChinesePatMed();
            } else if (ItemCategoryEnum.ChineseHerbMed.getCategoryCode().equals(itemCategory) && (!drugTimeDept.equals(execDeptTime.getChineseHerbMed()))) {
                drugTimeDept = execDeptTime.getChineseHerbMed();
            }
            //到心理咨询看诊
            if ((!execDept.equals(drugTimeDept)) && drugTimeDept.equals(1065)) {
                Log.info("精神科到心理咨询看诊,且心理咨询未关门");
                execDept = 1064;
            } else if (drugTimeDept.equals(1064)) {
                Log.info("到心理咨询看诊,且心理咨询关门了");
                execDept = drugTimeDept;
            } else {
                Log.info("心理咨询到心理咨询看诊,且心理咨询未关门" + drugTimeDept);
                execDept = 1065;
            }
        } else {
            //根据时间匹配对应的收费项目
            ExecDeptTime execDeptTime = execDeptTimeRepository.getExecDeptTime("0", hospitalCode);
            if (execDeptTime != null) {
                if (ItemCategoryEnum.WesternMed.getCategoryCode().equals(itemCategory) && (!drugTimeDept.equals(execDeptTime.getWesternMed()))) {
                    drugTimeDept = execDeptTime.getWesternMed();
                } else if (ItemCategoryEnum.ChinesePatMed.getCategoryCode().equals(itemCategory) && (!drugTimeDept.equals(execDeptTime.getChinesePatMed()))) {
                    drugTimeDept = execDeptTime.getChinesePatMed();
                } else if (ItemCategoryEnum.ChineseHerbMed.getCategoryCode().equals(itemCategory) && (!drugTimeDept.equals(execDeptTime.getChineseHerbMed()))) {
                    drugTimeDept = execDeptTime.getChineseHerbMed();
                }
                //到精神科看诊
                if ((!execDept.equals(drugTimeDept))) {
                    Log.info("心理咨询到精神科看诊,且心理咨询关门了");
                    execDept = 1064;
                } else {
                    Log.info("精神科到精神科看诊");
                    execDept = 1064;
                }
            } else {
                if ((execDept.equals(1064))) { //drugTimeDept为0
                    Log.info("精神科到精神科看诊,且心理咨询未关门");
                    execDept = 1064;
                } else {
                    Log.info("心理咨询到精神科看诊,且心理咨询未关门");
                    execDept = 1065;
                }
            }

        }

        return execDept;
    }

    /**
     * 获取默认执行科室(自动获取ip，非多线程可调)
     *
     * @param regDept
     * @param itemCode
     * @param itemCategory
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public Integer getDefaultExecDept(Integer regDept, Integer itemCode, Integer itemCategory, Integer hospitalCode) {
        List<ExecDeptConfig> execDeptConfigList = getExecDeptConfigs(itemCode, itemCategory, regDept, hospitalCode);
        if (execDeptConfigList.size() <= 0) {
            return null;
        }
        Integer execDept = execDeptConfigList.get(0).getExecDept();

        if(ItemCategoryEnum.isDrug(itemCategory)) {
            // 当前主机所属科室  0 : 普通精神；1: 心理咨询
            //String loginIp = RequestHead.get().getLoginIp(); //common包有坑
            String loginIp = HeadInfo.getIp();
            String localConfigDeptCode = sysConfigRepository.getLocalConfig(SystemNo.OUTPATIENT, SysLocalConfigKey.OUTPATIENT_DEPT_CODE, hospitalCode == null ? null : hospitalCode + "", loginIp);
            if (localConfigDeptCode == null) {
                localConfigDeptCode = "";
            }
            localConfigDeptCode = localConfigDeptCode.trim();
            Log.info("loginIp: " + loginIp + " localConfigDeptCode: " + localConfigDeptCode);

            if ("1".equals(localConfigDeptCode)) {
                //根据时间匹配对应的收费项目
                ExecDeptTime execDeptTime = execDeptTimeRepository.getExecDeptTime(localConfigDeptCode, hospitalCode);
                if (execDeptTime == null) {
                    return null;
                }
                if (ItemCategoryEnum.WesternMed.getCategoryCode().equals(itemCategory) && (!execDept.equals(execDeptTime.getWesternMed()))) {
                    execDept = execDeptTime.getWesternMed();
                } else if (ItemCategoryEnum.ChinesePatMed.getCategoryCode().equals(itemCategory) && (!execDept.equals(execDeptTime.getChinesePatMed()))) {
                    execDept = execDeptTime.getChinesePatMed();
                } else if (ItemCategoryEnum.ChineseHerbMed.getCategoryCode().equals(itemCategory) && (!execDept.equals(execDeptTime.getChineseHerbMed()))) {
                    execDept = execDeptTime.getChineseHerbMed();
                }
            }
        }

        return execDept;
    }


    /**
     * 根据[药品项目编码]和[医院编码] 获取默认执行科室
     *
     * @param itemCode
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public List<ExecDeptConfig> getDept(Integer itemCode, Integer hospitalCode, Integer feeCategory, String configType) {
        return execDeptConfigMapper.getDept(itemCode, hospitalCode, feeCategory, configType);
    }

    /**
     * 根据项目编码查询执行科室
     *
     * @param regDept
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<ExecDeptConfig> getExecDeptByItemCode(Integer itemCode, Integer itemCategory, Integer regDept, Integer hospitalCode) {
        List<ExecDeptConfig> execDeptConfigList = getExecDeptConfigs(itemCode, itemCategory, regDept, hospitalCode);
        return execDeptConfigList;
    }


    /**
     * 获取收费项目的执行科室
     *
     * @param itemCode
     * @param itemCategory
     * @param regDept
     * @param hospitalCode
     * @return
     */
    private List<ExecDeptConfig> getExecDeptConfigs(Integer itemCode, Integer itemCategory, Integer regDept, Integer hospitalCode) {
        List<ExecDeptConfig> execDeptConfigList = new ArrayList<>();

        //非药品获取收费项目表内执行科室
        if (itemCategory != null && ItemCategoryEnum.notDrug(itemCategory)) {

            //查询收费项目信息
            SystemTbPubItems chargeItem = systemTbPubItemsRepository.getChargeItemById(itemCode,hospitalCode,0);
            if (chargeItem == null) {
                return execDeptConfigList;
            }
            ExecDeptConfig execDeptConfig = new ExecDeptConfig();
            execDeptConfig.setExecDept(chargeItem.getExeDept());

            //匹配科室名称
            Map<String, String> map = sysConfig.getAutoTranslateDicInCache(Converter.toString(hospitalCode), DicTypeConst.DEPT);
            String deptName = map.getOrDefault(Converter.toString(chargeItem.getExeDept()), "");
            execDeptConfig.setExecDeptName(deptName.trim());
            execDeptConfig.setHospitalCode(hospitalCode);
            execDeptConfigList.add(execDeptConfig);
        }

        //药品查询配置表内执行科室信息
        else {
            ExecDeptConfig config = null;
            if (itemCode != null && itemCode != 0) {
                //默认根据收费项目ID加载执行科室
                config = execDeptConfigMapper.getExecDeptById(itemCode, "项目", itemCategory, hospitalCode);
                if (config != null) {
                    execDeptConfigList.add(config);
                    return execDeptConfigList;
                }
            }
            //如果没有配置取则根据挂号科室获取项目
            config = execDeptConfigMapper.getExecDeptById(regDept, "科室", itemCategory, hospitalCode);
            if (config != null) {
                execDeptConfigList.add(config);
            }
        }
        return execDeptConfigList;
    }

}