package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.OldReceptionRecord;
import com.rjsoft.outPatient.infrastructure.repository.entity.ReceptionRecord;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegNo2Guid;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/4 - 10:31
 */
public interface OldReceptionRecordRepository {

    /**
     * 根据挂号流水号获取接诊记录
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    List<OldReceptionRecord> getReceptionByRegNo(List<String> regNo, Integer hospitalCode);

    /**
     * 根据整型挂号流水号，加载GUID类型挂号流水号
     * @param regNos
     * @param hospitalCode
     * @return
     */
    List<RegNo2Guid> getRegNoGuid(List<Long> regNos, Integer hospitalCode);

}
