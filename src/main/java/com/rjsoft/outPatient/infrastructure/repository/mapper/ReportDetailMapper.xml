<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ReportDetailMapper">

    <select id="getReportDetail" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ReportDetail">
      select Id,TableId, TableNewId, ReportId, KeyCode, KeyName, KeyValue, DictionaryTypeId, DictionaryLevel, HospitalId, DrugType
      from Tb_ReportDetail
      where ReportId=#{formId}
      and HospitalId=#{hospitalCode}
      and  isnumeric(substring(KeyCode,1,1))=1
    </select>

</mapper>