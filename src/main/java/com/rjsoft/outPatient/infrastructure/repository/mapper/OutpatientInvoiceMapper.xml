<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.OutpatientInvoiceMapper">
    <select id="queryOutpatientInvoiceByRegNoList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.OutpatientInvoiceView">
        SELECT
            InvoiceID AS invoiceid,
            ReturnInvoiceID AS returninvoiceid,
            RegNo AS regno,
            CardType AS cardtype,
            CardNo AS cardno,
            OutpatientNo AS outpatientno,
            InvoiceNo AS invoiceno,
            PatID AS patid,
            NewPatID AS newpatid,
            PatName AS patname,
            ChargeType AS chargetype,
            InvoicePrefix AS invoiceprefix,
            InvoiceLabel AS invoicelabel,
            InvoiceInfo AS invoiceinfo,
            OpCode AS opcode,
            OpTime AS optime,
            IsPrint AS isprint,
            Printer AS printer,
            PrintTime AS printtime,
            TotalAmount AS totalamount,
            CapitalAmount AS capitalamount,
            OwnFee AS ownfee,
            InsuranceTotal AS insurancetotal,
            InsuranceCashTotal AS insurancecashtotal,
            CostTotal AS costtotal,
            FLPay AS flpay,
            PubPay AS pubpay,
            AppendPay AS appendpay,
            CurrAccountPay AS curraccountpay,
            LastAccountPay AS lastaccountpay,
            CurrAccountBalance AS curraccountbalance,
            LastAccountBalance AS lastaccountbalance,
            HisCardFee AS hiscardfee,
            PayFee AS payfee,
            CashFee AS cashfee,
            ReceiveAmount AS receiveamount,
            ChangeAmount AS changeamount,
            ErrorCents AS errorcents,
            Status AS status,
            UseFlag AS useflag,
            BussEvent AS bussevent,
            BillNo AS billno,
            Flag AS flag,
            TradeSerialNo AS tradeserialno,
            PrepaidRemind AS prepaidremind,
            PrepaidPay AS prepaidpay,
            PrepaidId AS prepaidid,
            DataFrom AS datafrom,
            FromFlag AS fromflag,
            Rate AS rate,
            DiscountAmount AS discountamount,
            ChargeNo AS chargeno,
            OriginalInvoiceId AS originalinvoiceid,
            ComputerNo AS computerno,
            TerminalType AS terminaltype,
            PayOrderNo AS payorderno,
            ReturnOrderNo AS returnorderno,
            DispensingWindow AS dispensingwindow,
            IsDelete AS isdelete,
            CreatedBy AS createdby,
            CreatedDate AS createddate,
            UpdateBy AS updateby,
            UpdateDate AS updatedate,
            ReturnOpCode AS returnopcode,
            ReturnOpTime AS returnoptime,
            DrugDept AS drugdept,
            HospitalCode AS hospitalcode,
            SerialNo AS serialno,
            MzNo AS mzno,
            DeptKind AS deptkind,
            JzPay AS jzpay,
            RecipeDoctor AS recipedoctor,
            RecipeDept AS recipedept,
            AccountFlag AS accountflag,
            DrugInfo AS druginfo,
            PayType AS paytype,
            PrintCount AS printcount,
            MoveFlag AS moveflag
        FROM Reg_TV_OutpatientInvoice (nolock)
        where HospitalCode=#{hospitalCode} and status = 0
        and RegNo in
        <foreach item="item" index="index" collection="regNoList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryChargeDetailByChargeNoList" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.ChargeDetailView">
        SELECT
            ChargeDetl AS chargedetl,
            ChargeNo AS chargeno,
            RecipeNum AS recipenum,
            RecipeID AS recipeid,
            RecipeDetlID AS recipedetlid,
            ItemID AS itemid,
            ItemName AS itemname,
            Quantiy AS quantiy,
            ItemCategory AS itemcategory,
            PackageNo AS packageno,
            GroupNo AS groupno,
            ClinicUnit AS clinicunit,
            BasicUnit AS basicunit,
            ClinicQty AS clinicqty,
            Usage AS usage,
            Dosage AS dosage,
            DosageUnit AS dosageunit,
            DrugGauge AS druggauge,
            CheckCode AS checkcode,
            ExecuteDept AS executedept,
            Times AS times,
            Price AS price,
            ExpensePrice AS expenseprice,
            NonExpensePrice AS nonexpenseprice,
            [Status] AS status,
            TotalAmount AS totalamount,
            DoctorId AS doctorid,
            DeptId AS deptid,
            DoctorLevel AS doctorlevel,
            FeeType AS feetype,
            IsDrug AS isdrug,
            SpecialFlag AS specialflag,
            DataFrom AS datafrom,
            FromFlag AS fromflag,
            DiscountAmount AS discountamount,
            IsDelete AS isdelete,
            CreatedBy AS createdby,
            CreatedDate AS createddate,
            UpdateBy AS updateby,
            UpdateDate AS updatedate,
            InsuranceTradeAmount AS insurancetradeamount,
            SelfAmount AS selfamount,
            ClassifyTotal AS classifytotal,
            InsuranceCashAmount AS insurancecashamount,
            HospitalCode AS hospitalcode,
            CreditAmt AS creditamt,
            RealAmt AS realamt,
            OtherAmt AS otheramt,
            JzAmount AS jzamount,
            OldChargeDetl AS oldchargedetl
        FROM Reg_Tv_ChargeDetail (nolock)
        where HospitalCode=#{hospitalCode}
        and ChargeNo in
        <foreach item="item" index="index" collection="chargeNoList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>