package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.CheckType;
import com.rjsoft.outPatient.infrastructure.repository.entity.SelectedType;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

public interface SelectedTypeMapper extends BaseMapper<SelectedType> {


    /**
     * 查询申请单对照类型
     *
     * @return
     */
    default List<SelectedType> getSelectedType() {
        SelectedType selectedType = new SelectedType();
        return select(selectedType);
    }

}
