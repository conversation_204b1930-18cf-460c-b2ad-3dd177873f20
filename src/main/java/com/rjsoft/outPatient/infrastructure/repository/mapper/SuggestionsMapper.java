package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.outPatient.domain.caseHistory.dto.CheckBlDetail;
import com.rjsoft.outPatient.domain.caseHistory.dto.UpdateCheckedBlParam;
import com.rjsoft.outPatient.infrastructure.repository.entity.Agent;
import com.rjsoft.outPatient.infrastructure.repository.entity.Suggestions;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface SuggestionsMapper extends Mapper<Suggestions> {

    /**
     * 根据病历id获取病历审核信息
     * @param blId
     */
    List<CheckBlDetail> getCheckBls(@Param("list") Set<String> blId);

    /**
     * 已审核病历提交
     * @param suggestionId
     */
    default void commitBl(Integer suggestionId){
        Suggestions suggestions = new Suggestions();
        suggestions.setLsh(suggestionId);
        suggestions.setCommitDate(new Date());
        suggestions.setCommitState(1);
        updateByPrimaryKeySelective(suggestions);
    }

    /**
     * 已审核病历修改保存
     * @param param
     */
    default void updateBl(UpdateCheckedBlParam param){
        Suggestions suggestions = new Suggestions();
        suggestions.setLsh(param.getSuggestionId());
        suggestions.setDoctorId(Converter.toString(param.getDoctorId()));
        suggestions.setDoctorName(param.getDoctorName());
        suggestions.setHandleDate(new Date());
        suggestions.setHandleState(1);
        suggestions.setHandleResult(param.getHandleResult());
        suggestions.setHandleEmr(param.getHandleEMR());
        suggestions.setState(param.getState());
        updateByPrimaryKeySelective(suggestions);
    }


}
