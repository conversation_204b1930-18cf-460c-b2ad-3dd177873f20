<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DictionaryTbFrequencyMapper" >
  <select id="selFrequency" resultType="com.rjsoft.outPatient.infrastructure.repository.entity.FrequencyResult">
    select rtrim(FreqId) FreqId
    ,rtrim(FreqCode) FreqCode
    ,rtrim(FreqName) FreqName
    ,rtrim(FreqType) FreqType
    ,rtrim(FreqParam) FreqParam
    ,rtrim(DefaultDay) DefaultDay
    ,rtrim(DefaultTime) DefaultTime
    ,rtrim(IsLong) IsLong
    ,rtrim(SID) SID
    ,rtrim(CreateUserId) CreateUserId
    ,rtrim(CreateOn) CreateOn
    ,rtrim(UpdateUserId) UpdateUserId
    ,rtrim(UpdateOn) UpdateOn
    ,rtrim(IsUse) IsUse
    ,rtrim(HospitalId) HospitalId from  Dictionary_Tb_Frequency
    <where>
      isUse  = 1
      <if test="hospId !=null">
        and hospitalId = #{hospId}
      </if>
      <if test="isLong !=null">
        and isLong = #{isLong}
      </if>
      <if test="id !=null">
        and FreqId = #{id}
      </if>
      order by SID
    </where>
  </select>
</mapper>