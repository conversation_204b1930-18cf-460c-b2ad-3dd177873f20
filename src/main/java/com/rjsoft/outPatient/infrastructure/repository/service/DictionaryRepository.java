package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.config.dto.HisDictionaryDto;
import com.rjsoft.outPatient.domain.dictionary.dto.CommonDicEntity;
import com.rjsoft.outPatient.domain.dictionary.dto.GeneralDicDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.AreaInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.DicInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.MenuByHisRight;
import com.rjsoft.outPatient.infrastructure.repository.entity.MenuByPrescription;

import java.util.List;

/**
 * 查询门诊医生字典信息
 */
public interface DictionaryRepository {

    /**
     * 查询门诊医生字典信息
     *
     * @param type
     * @param hospitalCode
     * @return
     */
    List<CommonDicEntity> getDictionaryConfig(Integer type, Integer hospitalCode);

    /**
     * 根据大写首拼获取字典信息
     *
     * @param dicTypeSpell 大写首拼
     * @return
     */
    List<DicInfo> getDicInfo(String dicTypeSpell);

    /**
     * 根据大写首拼+名称+码值获取字典信息
     *
     * @param dicTypeSpell
     * @return
     */
    DicInfo getDicInfoByCode(String dicTypeSpell, String dicName, String dicCode);

    /**
     * 根据父代码和区域级别获取区域信息
     *
     * @param parentCode 父代码
     * @param level      区域级别
     * @return
     */
    List<AreaInfo> getAreaInfo(String parentCode, Integer level);

    /**
     * 根据父代码和级别、代码获取区域
     *
     * @param parentCode 父代码
     * @param level      级别
     * @param code       代码值
     * @return
     */
    AreaInfo getAreaInfoByCode(String parentCode, Integer level, String code);

    /**
     * 根据民族码值,获取民族字典
     *
     * @param nation
     * @return
     */
    GeneralDicDto getNationDic(String nation);

    /**
     * 根据教育码值获取教育程度信息
     *
     * @param education
     * @return
     */
    GeneralDicDto getEducationDic(String education);

    /**
     * 根据码值获取婚姻状况信息
     *
     * @param maritalStatus
     * @return
     */
    GeneralDicDto getMaritalDic(String maritalStatus);

    /**
     * 根据DictionaryTypeID+hisDictionaryCode获取字典信息
     *
     * @param typeId
     * @param nation
     * @return
     */
    HisDictionaryDto getGenaralDic(Integer typeId, String nation, Integer hospId);

    /**
     * 查询门诊处方菜单
     *
     * @param hospitalCode
     * @return
     */
    List<MenuByPrescription> getPrescriptionMenu(Integer hospitalCode);

    /**
     * 查询门诊菜单
     *
     * @param hospitalCode
     * @return
     */
    List<MenuByHisRight> getHisRightMenu(Integer hospitalCode);


}
