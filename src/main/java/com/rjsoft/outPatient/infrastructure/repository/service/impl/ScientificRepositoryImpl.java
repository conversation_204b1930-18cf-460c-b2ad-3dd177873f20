package com.rjsoft.outPatient.infrastructure.repository.service.impl;


import com.github.pagehelper.PageInfo;
import com.rjsoft.common.SystemNo;
import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.json.JsonUtils;
import com.rjsoft.common.log.Log;
import com.rjsoft.common.request.RequestApi;
import com.rjsoft.outPatient.common.SystemConfigUtils;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.consts.SysConfigKey;
import com.rjsoft.outPatient.common.utils.GetHeadInfoDao;
import com.rjsoft.outPatient.domain.scientific.vo.PatJoinProjectReqVO;
import com.rjsoft.outPatient.infrastructure.repository.entity.RegisterList;
import com.rjsoft.outPatient.infrastructure.repository.entity.SciPatientProject;
import com.rjsoft.outPatient.infrastructure.repository.entity.Worker;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SciPatientProjectMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ScientificMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ScientificRepository;
import com.rjsoft.outPatient.infrastructure.repository.service.WorkerRepository;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 科研
 */
@AllArgsConstructor
@Service
public class ScientificRepositoryImpl implements ScientificRepository {

    ScientificMapper scientificMapper;
    GetHeadInfoDao getHeadInfoDao;
    SciPatientProjectMapper sciPatientProjectMapper;
    WorkerRepository workerRepository;
    SystemConfigUtils systemConfigUtils;

    private static final String JOIN_SUCCESS = "入组成功";
    private static final String EXECUTE_SUCCESS = "执行成功";

    @Override
    @DatabaseAnnotation(name = "RDR_ResHisExchange")
    public List<HashMap> getProjectsList(String doctorId, Integer hospitalCode) {
        return scientificMapper.getProjectsList(doctorId, hospitalCode);
    }

    @Override
    public void patJoinProject(PatJoinProjectReqVO patJoinProjectReqVO) {
        Integer patId = patJoinProjectReqVO.getPatId();
        String projectName = patJoinProjectReqVO.getProjectName();
        String groupNo = patJoinProjectReqVO.getGroupNo();
        String projectId = patJoinProjectReqVO.getProjectId();
        String hisCardNo = patJoinProjectReqVO.getHisCardNo();
        String patName = patJoinProjectReqVO.getPatName();
        Integer sex = patJoinProjectReqVO.getSex();
        String patSfz = patJoinProjectReqVO.getPatSfz();
        Integer workerId = getHeadInfoDao.getWorkerId();
        Integer hospId = getHeadInfoDao.getHospId();
        Worker operator = workerRepository.getWorkerByIdAll(workerId, hospId);
        String workerNo = operator.getWorkerNo();
        String cardNo = patJoinProjectReqVO.getCardNo();

        SciPatientProject sciPatientProject = new SciPatientProject();
        initSciPatProject(sciPatientProject, patId, projectId, hospId);
//        List<SciPatientProject> existSciPatProjects = getExistSciPatProject(sciPatientProject);
//        if (ObjectUtils.isNotEmpty(existSciPatProjects)) {
//            throw new RuntimeException("不能重复入组");
//        }
        //调用柯林布瑞科研入组接口 Start
        String clinbrainDataCenterUrl = systemConfigUtils
                .getSystemConfigByStr(SystemNo.OUTPATIENT, SysConfigKey.Clinbrain_DataCenterUrl, 1);
        if(clinbrainDataCenterUrl == ""){
            throw new RuntimeException("调用柯林布瑞科研平台接口参数(clinbrainDataCenterUrl)未配置，请联系信息中心！");
        }
        String returnStr = "";
        clinbrainDataCenterUrl += "/hisDataSource/patientAddProject";
        HashMap req = new HashMap();
        req.put("idCard",patSfz);
        req.put("projectId",projectId);
        req.put("researchNo",cardNo);
        req.put("joinNo",groupNo);
        String json = JsonUtils.serialize(req);
        SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Log.info("科研患者入组调用科研平台开始时间：" + sdf.format(new Date()));
        HashMap res = RequestApi.post(clinbrainDataCenterUrl, json, HashMap.class);
        Log.info("科研患者入组调用科研平台返回时间：" + sdf.format(new Date()));
        if (res != null && res.get("code") != null) {
            Boolean flag = String.valueOf(res.get("code")).equals("0");
            if(flag){
                saveNewSciPatProject(groupNo, projectName, workerId, sciPatientProject);
            }else{
                throw new RuntimeException(String.valueOf(res.get("msg")));
            }
        }else{
            throw new RuntimeException("调用柯林布瑞科研平台（受试者入组）接口异常，请联系信息中心！");
        }
        //调用柯林布瑞科研入组接口 End
    }

    private void saveNewSciPatProject(String groupNo,String projectName, Integer workerId, SciPatientProject sciPatientProject) {
        if (ObjectUtils.isNotEmpty(groupNo)) {
            sciPatientProject.setGroupNo(groupNo);
        }
        sciPatientProject.setProjectName(projectName);
        sciPatientProject.setCreator(workerId);
        sciPatientProject.setCreateTime(new Date());
        sciPatientProject.setUpdater(workerId);
        sciPatientProject.setUpdateTime(new Date());
        DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
        sciPatientProjectMapper.insertSelective(sciPatientProject);
    }

    private void initSciPatProject(SciPatientProject sciPatientProject, Integer patId, String projectId, Integer hospId) {
        sciPatientProject.setPatId(patId);
        sciPatientProject.setProjectId(projectId);
        sciPatientProject.setHospitalCode(hospId);
    }

    private List<SciPatientProject> getExistSciPatProject(SciPatientProject sciPatientProject) {
        DataSourceSwitchAspect.changeDataSource(DatasourceName.HISDB);
        return sciPatientProjectMapper.select(sciPatientProject);
    }
}
