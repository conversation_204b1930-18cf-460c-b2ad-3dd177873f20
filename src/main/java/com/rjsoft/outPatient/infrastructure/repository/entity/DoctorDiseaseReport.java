package com.rjsoft.outPatient.infrastructure.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021-12-04
 */

@Setter
@Getter
@ToString
@Entity
@Table(name = "Tbt_ZyEMR_Qpmzxbr")
public class DoctorDiseaseReport implements Serializable {

    private static final long serialVersionUID = 7315088405010628526L;

    @Id
    @Column(name = "Id", updatable = false)
    private Integer id;

    @Column(name = "diagnoseNo")
    private Integer diagnoseNo;

    /**
     * 就诊流水号
     */
    @Column(name = "jzlsh")
    private String jzlsh;

    /**
     * 诊断编码
     */
    @Column(name = "diagnoseCode")
    private String diagnoseCode;

    /**
     * 精神症状幻觉
     */
    @Column(name = "Illusion")
    private String illusion;

    /**
     * 精神症状情感
     */
    @Column(name = "Emotion")
    private String emotion;

    /**
     * 精神症状思维
     */
    @Column(name = "Thought")
    private String thought;

    /**
     * 精神症状行为
     */
    @Column(name = "Action")
    private String action;

    /**
     * 精神症状意志
     */
    @Column(name = "Volition")
    private String volition;

    /**
     * 精神症状其他
     */
    @Column(name = "Other")
    private String other;

    /**
     * 诊断医生姓名
     */
    @Column(name = "DoctorName")
    private String doctorName;

    /**
     * 诊断日期
     */
    @Column(name = "DiagnoseDate")
    private Date diagnoseDate;

    /**
     * 诊断复核编码
     */
    @Column(name = "DiagnoseCode2")
    private String diagnoseCode2;

    /**
     * 诊断复核医生姓名
     */
    @Column(name = "DoctorName2")
    private String doctorName2;

    /**
     * 诊断复核日期
     */
    @Column(name = "DiagnoseDate2")
    private Date diagnoseDate2;

    /**
     * 初发病日期
     */
    @Column(name = "FirstAppearDate")
    private Date firstAppearDate;

    /**
     * 初诊日期
     */
    @Column(name = "FirstConfirmDate")
    private Date firstConfirmDate;

    /**
     * 诊断机构编码
     */
    @Column(name = "DiagnoseOrgId")
    private String diagnoseOrgId;

    /**
     * 诊断机构名称
     */
    @Column(name = "DiagnoseOrgName")
    private String diagnoseOrgName;

    /**
     * 患者姓名
     */
    @Column(name = "Name")
    private String name;

    /**
     * 患者性别
     */
    @Column(name = "Gender")
    private Long gender;

    /**
     * 出生日期
     */
    @Column(name = "Birthday")
    private Date birthday;

    /**
     * 身份证号
     */
    @Column(name = "IdNo")
    private String idNo;

    /**
     * 手机号
     */
    @Column(name = "Phone")
    private String phone;

    /**
     * 工作地点
     */
    @Column(name = "WorkPlace")
    private String workPlace;

    /**
     * 户籍地  省（自治区、直辖市）
     */
    @Column(name = "hj1")
    private String hj1;

    /**
     * 户籍地  市（帝、州、盟）
     */
    @Column(name = "hj2")
    private String hj2;

    /**
     * 户籍地  县(市区、旗)
     */
    @Column(name = "hj3")
    private String hj3;

    /**
     * 户籍地  乡
     */
    @Column(name = "hj4")
    private String hj4;

    /**
     * 户籍地  村（居委会）
     */
    @Column(name = "hj5")
    private String hj5;

    /**
     * 户籍地  详细至门牌号
     */
    @Column(name = "hj6")
    private String hj6;

    /**
     * 现居地 省（自治区、直辖市）
     */
    @Column(name = "xj1")
    private String xj1;

    /**
     * 现居地 市（地、州、盟）
     */
    @Column(name = "xj2")
    private String xj2;

    /**
     * 现居地 县（市区、旗）
     */
    @Column(name = "xj3")
    private String xj3;

    /**
     * 现居地 乡（镇，街道）
     */
    @Column(name = "xj4")
    private String xj4;

    /**
     * 现居地 村（居委会）
     */
    @Column(name = "xj5")
    private String xj5;

    /**
     * 现居地 门牌号
     */
    @Column(name = "xj6")
    private String xj6;

    /**
     * 户籍地乡 行政区划
     */
    @Column(name = "hj4code")
    private String hj4Code;

    /**
     * 现居地 乡行政区划
     */
    @Column(name = "xj4code")
    private String xj4Code;

    /**
     * 职业
     */
    @Column(name = "Occupaton")
    private String occupaton;

    /**
     * 联系人姓名
     */
    @Column(name = "ContractName")
    private String contractName;

    /**
     * 联系人手机号
     */
    @Column(name = "ContractPhone")
    private String contractPhone;

    /**
     * 民族
     */
    @Column(name = "Nation")
    private String nation;

    /**
     * 户别
     */
    @Column(name = "RegType")
    private Long regType;

    /**
     * 联系人与患者关系
     */
    @Column(name = "ContractRelation")
    private String contractRelation;

    /**
     * 文化程度
     */
    @Column(name = "Education")
    private String education;

    /**
     * 婚姻状态
     */
    @Column(name = "MaritalStatus")
    private String maritalStatus;

    /**
     * 送检主体（多选，以英文逗号,隔开）
     */
    @Column(name = "SendDiagnosis")
    private String sendDiagnosis;

    /**
     * 送检主体其他
     */
    @Column(name = "SendDiagnosisOther")
    private String sendDiagnosisOther;

    /**
     * 两系三代精神疾病家族史
     */
    @Column(name = "FamilyHistory")
    private String familyHistory;

    /**
     * 是否已经行抗精神病药物治疗
     */
    @Column(name = "IfCure")
    private String ifCure;

    /**
     * 首次抗精神病药物治疗时间
     */
    @Column(name = "FirstCureTime")
    private Date firstCureTime;

    /**
     * 既往住院情况
     */
    @Column(name = "HospitalStateHistory")
    private String hospitalStateHistory;

    /**
     * 既往关锁情况
     */
    @Column(name = "ShutStatusHistory")
    private String shutStatusHistory;

    /**
     * 既往危险行为
     */
    @Column(name = "PastRiskhave")
    private String pastRiskhave;

    /**
     * 目前危险性评估
     */
    @Column(name = "RiskPast")
    private String riskPast;

    /**
     * 知情同意
     */
    @Column(name = "AgreeType")
    private String agreeType;

    /**
     * 药品信息
     */
    @Column(name = "DrugInfos")
    private String drugInfos;

    /**
     * 删除状态 0否1是
     */
    @Column(name = "Status")
    private String status;

    /**
     * 创建人
     */
    @Column(name = "Creator")
    private String creator;

    /**
     * 创建日期
     */
    @Column(name = "CreateTime")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "Updator")
    private String updator;

    /**
     * 修改时间
     */
    @Column(name = "UpdateTime")
    private Date updateTime;

    @Column(name = "ComeFlag")
    private String comeFlag;

    @Column(name = "HospitalCode")
    private Integer hospitalCode;

}
