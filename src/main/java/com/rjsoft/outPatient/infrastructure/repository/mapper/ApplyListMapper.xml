<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ApplyListMapper">

    <select id="getApplyItemCount" resultType="java.lang.Integer">
        select count(0) from hisdb..Apply_Tb_Form a (nolock)
        inner join hisdb..Apply_Tb_Item b on a.id=b.FormId and a.HopsitalId=b.HopsitalId
        where a.Code=#{formCode} and a.HopsitalId=#{hospitalCode} and a.EnableFlag=1 and b.EnableFlag=1
    </select>
</mapper>