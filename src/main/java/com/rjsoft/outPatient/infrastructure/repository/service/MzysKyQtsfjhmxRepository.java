package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.research.dto.ExtraVisitPlanSaveInputDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysKyQtsfjhmx;

import java.util.List;

public interface MzysKyQtsfjhmxRepository {
    boolean saveExtraVisitPlan(ExtraVisitPlanSaveInputDto **********************);

    List<MzysKyQtsfjhmx> getExtraVisitPlanDetail(Integer id);

    boolean insertMzysKyQtsfjhmxList(List<MzysKyQtsfjhmx> mzysKyQtsfjhmxList);
}
