package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Agent;
import com.rjsoft.outPatient.infrastructure.repository.entity.MainPackage;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

public interface MainPackageMapper extends Mapper<MainPackage> {

    /**
     * 根据套餐Id获取套餐记录
     * @param itemCodes
     */
    default List<MainPackage> getMainPackageByPackageId(List<Integer> itemCodes){
        Weekend<MainPackage> weekend = new Weekend<>(MainPackage.class);
        WeekendCriteria<MainPackage, Object> criteria = weekend.weekendCriteria();
        criteria.andIn(MainPackage::getTcid,itemCodes);
        criteria.andEqualTo(MainPackage::getZt,0);
        return selectByExample(weekend);
    }

}
