package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.entity.LocalConfig;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SysLocalConfigMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.SysConfigRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *  本机配置
 * <AUTHOR>
 * @create 2022/08/16 11:26
 */
@Service
@AllArgsConstructor
public class SysConfigRepositoryImpl implements SysConfigRepository {

    SysLocalConfigMapper localConfigMapper;

    /**
     * 获取本机配置
     *
     * @param systemNo
     * @param key
     * @param hospitalCode
     * @param ip
     * @return
     */
    @Override
    public String getLocalConfig(int systemNo, String key, String hospitalCode, String ip) {
        LocalConfig config = localConfigMapper.getLocalConfig(systemNo, key, hospitalCode, ip);
        return config == null ? null : config.getKeyValue();
    }
}
