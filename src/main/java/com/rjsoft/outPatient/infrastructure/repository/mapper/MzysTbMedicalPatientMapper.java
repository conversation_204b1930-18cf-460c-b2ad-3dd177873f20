package com.rjsoft.outPatient.infrastructure.repository.mapper;

import cn.hutool.core.collection.CollUtil;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.domain.medicalInsurance.dto.QueryMedicalRegisterDTO;
import com.rjsoft.outPatient.domain.medicalInsurance.vo.QueryMedicalRegisterVO;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Collections;
import java.util.List;

public interface MzysTbMedicalPatientMapper extends Mapper<MzysTbMedicalPatient> {

    @DatabaseAnnotation
    default MzysTbMedicalPatient getMedicalPatientByRegNo(Long regNo, Integer hospitalCode) {
        Weekend<MzysTbMedicalPatient> weekend = Weekend.of(MzysTbMedicalPatient.class);
        WeekendCriteria<MzysTbMedicalPatient, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andEqualTo(MzysTbMedicalPatient::getRegNo, regNo).andEqualTo(MzysTbMedicalPatient::getHospitalId, hospitalCode).andEqualTo(MzysTbMedicalPatient::getStatus, true);
        return this.selectOneByExample(weekend);
    }

    @DatabaseAnnotation
    default List<MzysTbMedicalPatient> queryMedicalPatientByRegNoList(List<Long> regNoList, Integer hospitalCode) {
        if (CollUtil.isEmpty(regNoList)) {
            return Collections.emptyList();
        }
        Weekend<MzysTbMedicalPatient> weekend = Weekend.of(MzysTbMedicalPatient.class);
        WeekendCriteria<MzysTbMedicalPatient, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andIn(MzysTbMedicalPatient::getRegNo, regNoList).andEqualTo(MzysTbMedicalPatient::getHospitalId, hospitalCode).andEqualTo(MzysTbMedicalPatient::getStatus, true);
        return this.selectByExample(weekend);
    }

    @DatabaseAnnotation
    default boolean deleteByRegNo(Long regNo, Integer hospitalCode) {
        Weekend<MzysTbMedicalPatient> weekend = Weekend.of(MzysTbMedicalPatient.class);
        WeekendCriteria<MzysTbMedicalPatient, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andEqualTo(MzysTbMedicalPatient::getRegNo, regNo).andEqualTo(MzysTbMedicalPatient::getHospitalId, hospitalCode);
        return this.deleteByExample(weekend) > 0;
    }

    @DatabaseAnnotation
    default boolean deleteByRegNoList(List<Long> regNoList, Integer hospitalCode) {
        Weekend<MzysTbMedicalPatient> weekend = Weekend.of(MzysTbMedicalPatient.class);
        WeekendCriteria<MzysTbMedicalPatient, Object> weekendedCriteria = weekend.weekendCriteria();
        weekendedCriteria.andIn(MzysTbMedicalPatient::getRegNo, regNoList).andEqualTo(MzysTbMedicalPatient::getHospitalId, hospitalCode);
        return this.deleteByExample(weekend) > 0;
    }

    /**
     * 筛选多学科门诊的挂号
     *
     * @param regNoList    挂号流水号
     * @param hospitalCode 医院编码
     * @return
     */
    @DatabaseAnnotation
    List<Long> queryMDTWorkRegNoList(@Param("regNoList") List<Long> regNoList, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 筛选慢特病信息挂号
     */
    @DatabaseAnnotation
    List<Long> queryChronicRegNoList(@Param("regNoList") List<Long> regNoList, @Param("hospitalCode") Integer hospitalCode);

    @DatabaseAnnotation
    List<EnterHospitalRequisition> queryEnterHospitalRequisitionList(@Param("regNoList") List<Long> regNoList, @Param("hospitalCode") Integer hospitalCode);

    @DatabaseAnnotation
    List<ReceptionRecord> queryReceptionByRegNoList(@Param("regNoList") List<Long> regNoList, @Param("hospitalCode") Integer hospitalCode);

    @DatabaseAnnotation
    List<QueryMedicalRegisterVO> queryMedicalRegisterList(@Param("param") QueryMedicalRegisterDTO param);

    @DatabaseAnnotation
    List<RegisterList> queryExportMedicalRegisterList(@Param("param") QueryMedicalRegisterDTO param);

    @DatabaseAnnotation
    int insertMedicalPatientBatch(@Param("list") List<MzysTbMedicalPatient> list);

    @DatabaseAnnotation
    int insertMedicalReceptionBatch(@Param("list") List<MzysTbMedicalReception> list);

    @DatabaseAnnotation
    int insertMedicalDiagBatch(@Param("list") List<MzysTbMedicalDiag> list);

    @DatabaseAnnotation
    int insertMedicalFeeBatch(@Param("list") List<MzysTbMedicalFee> list);

}