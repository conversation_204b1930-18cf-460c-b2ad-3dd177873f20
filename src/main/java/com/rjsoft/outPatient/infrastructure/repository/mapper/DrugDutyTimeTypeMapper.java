package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugDutyTimeType;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Date;

/**
 * 药品值班时间类型
 * <AUTHOR>
public interface DrugDutyTimeTypeMapper extends BaseMapper<DrugDutyTimeType>, ExampleMapper<DrugDutyTimeType> {

    /**
     * 根据当前时间获取值班时间类型
     * @param date
     * @return
     */
    default Integer getTimeTypeByDate(Date date) {
        Weekend weekend = new Weekend(DrugDutyTimeType.class);
        WeekendCriteria<DrugDutyTimeType, Object> weekendCriteria = weekend.weekendCriteria();
        String time = Converter.toDateStr(date, "HH:mm");
        weekendCriteria.andGreaterThanOrEqualTo(DrugDutyTimeType::getBeginTime, time);
        weekendCriteria.andLessThan(DrugDutyTimeType::getEndTime, time);
        DrugDutyTimeType entity = selectOneByExample(weekend);
        if (entity == null) {
            return -2;
        }
        return entity.getTimeType();
    }
}
