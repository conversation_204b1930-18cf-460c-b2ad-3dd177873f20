package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDeptDto;
import com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDeptDtoWithItemCode;
import com.rjsoft.outPatient.infrastructure.repository.entity.SystemTvIteminfodept;

import java.util.List;

public interface SystemTvIteminfodeptRepository {
    List<SystemTvIteminfodept> getSystemTbItemexecdept(Integer itemCode, Integer hospitalId);


    /**
     *
     * @param itemCode
     * @param feeCategory 字串类型（与调用者的入参兼容），为null时直接调用原始未分类似的方法
     * @return
     */
    List<ChargeItemExecDeptDto> getItemsExecDept(Integer itemCode, String feeCategory);

    List<ChargeItemExecDeptDtoWithItemCode> getItemsExecDeptByItemCodeList(List<Integer> itemCodeList);
}
