<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.DctAddNumDetailMapper">

    <select id="queryDctAddNumDetail"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.DctAddNumDetail">
        select a.*,
        rtrim(b.DeptName) deptName,
        c.SubjectName subjectName
        from TB_Config_DctAddNumDetail a
        LEFT join TB_Dic_DeptExt b on a.DeptID = b.DEPTID
        LEFT join TB_Cinfiger_SubjectItem c on a.SubjectID = c.SubjectID
        where a.IsUse = 1
        and a.DoctID = #{doctorId}
        and a.HospitalCode = #{hospitalCode}
        and Convert(varchar (10), a.CreatedDate, 120) = Convert(varchar (10), getdate(), 120)
        <if test="cardNo != null and cardNo!= ''">
            and a.<PERSON>No = #{cardNo}
        </if>
        <if test="patName != null and patName!= ''">
            <bind name="pattern" value="'%'+patName+'%'"/>
            and a.PatName like #{pattern}
        </if>
        <if test="certificateNo != null and certificateNo!= ''">
            and a.CertificateNo = #{certificateNo}
        </if>
        <if test="visitFlag != null and visitFlag != 0">
            and a.VisitFlag = #{visitFlag}
        </if>
        <if test="type ==0">
            and a.Status in (2)
            order by ApproveDate desc
        </if>
        <if test="type ==1">
            and a.Status in (1)
            and c.IncreaseType != 1
            order by ApplyDate
        </if>
    </select>

    <select id="getDoctorSchedulingInfo" resultType="com.rjsoft.outPatient.domain.reserve.dto.DoctorAddNum">
        select
        a.ID mainId,
        a.ExtraMaxNum extraMaxNum,
        a.Subjectid subjectId
        from TB_Cinfiger_Scheduling a
        inner join TB_Cinfiger_SubjectItem b on a.Subjectid = b.SubjectID and a.HOSPITALCODE = b.HOSPITALCODE
        where b.DctCode = #{doctorId}
        and b.TimeType = (CASE WHEN CONVERT(varchar (12), getdate(), 108) > '12:00:00' THEN 'B0001284' ELSE 'B0001282'
        END)
        and DATEDIFF(DAY, a.DutyDate, getdate()) = 0
        and a.HOSPITALCODE = #{hospitalCode}
        <if test="deptCode != null">
            and b.DeptCode = #{deptCode}
        </if>
    </select>

    <select id="queryDctAddNumDetailNum" resultType="java.lang.Integer">
        select COUNT(1)
        from TB_Config_DctAddNumDetail
        where isdelete = 0
        and isUse = 1
        and HospitalCode = #{hospitalCode}
        and DATEDIFF(DAY,CreatedDate, GETDATE()) = 0
        <if test="subjectId != null">
            and SubjectID = #{subjectId}
        </if>
        <if test="deptCode != null">
            and DeptID = #{deptCode}
        </if>
        <if test="doctorId != null">
            and DoctID = #{doctorId}
        </if>
    </select>


    <select id="getDoctorScheduling" resultType="com.rjsoft.outPatient.domain.reserve.dto.DoctorAddNum">
        select a.ID                   mainId,
               a.ExtraMaxNum          extraMaxNum,
               isnull(a.Subjectid, 0) subjectId
        from TB_Cinfiger_Scheduling a
                 inner join TB_Cinfiger_SubjectItem b on a.Subjectid = b.SubjectID and a.HOSPITALCODE = b.HOSPITALCODE
                 inner join dbo.TB_Dic_HisDictionaryExt c
                            on b.TimeType = c.HisDictionaryID and lang = 1 and a.HOSPITALCODE = c.HospitalCode
        where DATEDIFF(DAY, a.DutyDate, getdate()) = 0
          and b.DeptCode = #{deptCode}
          and b.DctCode = #{doctorId}
          and a.HOSPITALCODE = #{hospitalCode}
        <if test="timeMs != null">
            and c.HisDictionaryName = #{timeMs}
        </if>
    </select>

    <select id="getAddNumDetailCount" resultType="java.lang.Integer">
        select count(1)
        from TB_Config_DctAddNumDetail
            where CertificateNo = #{certificateNo}
            and DATEDIFF(DAY, CreatedDate, GETDATE()) = 0
            and IsDelete = 0
            and IsUse = 1
            and HospitalCode = #{hospitalCode}
    </select>

    <select id="getVisitNum" resultType="com.rjsoft.outPatient.domain.reserve.dto.ReserveNum">
        select sum(quatity) firstVisit, sum(maxNum) secondVisit
        from TB_Cinfiger_SchedulingTimespanDetail
        where mainID = #{mainId}
          and IsDelete = 0
          and IsUse = 1
          and HOSPITALCODE = #{hospitalCode}
    </select>

    <select id="getSeqNum" resultType="java.lang.Integer">
        select isnull(MAX(SeqNum), 0) + 1
        from TB_Config_DctAddNumDetail
        where SubjectId = #{subjectId}
          and DATEDIFF(DAY,CreatedDate, getdate()) = 0
          and HospitalCode = #{hospitalCode}
    </select>

    <select id="getSubjectSeqNum" resultType="java.lang.Integer">
        select isnull(MAX(SeqNum), 0) + 1
        from TB_Config_SubjectNumDetail
        where SubjectID = #{subjectId}
          and DATEDIFF(DAY,DutyDate, getdate()) = 0
          and HospitalCode = #{hospitalCode}
    </select>



</mapper>