package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
    * 门诊病案首页就诊信息
    */
@Data
@Table(name = "MZYS_Tb_MedicalReception")
public class MzysTbMedicalReception implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 挂号流水号
     */
    @Column(name = "reg_no")
    private Long regNo;

    /**
     * 挂号时间
     */
    @Column(name = "register_time")
    private Date registerTime;

    /**
     * 报到时间
     */
    @Column(name = "arrival_time")
    private Date arrivalTime;

    /**
     * 就诊时间
     */
    @Column(name = "reception_time")
    private Date receptionTime;

    /**
     * 就诊科室
     */
    @Column(name = "reception_dept")
    private Integer receptionDept;

    /**
     * 接诊医师
     */
    @Column(name = "reception_doctor")
    private Integer receptionDoctor;

    /**
     * 接诊医师职称
     */
    @Column(name = "reception_doctor_title")
    private String receptionDoctorTitle;

    /**
     * 就诊类型 1-急诊 2-普通门诊 3-特需门诊 4-互联网诊疗 5-MDT门诊 9-其他
     */
    @Column(name = "reception_type")
    private String receptionType;

    /**
     * 是否复诊 1是0否
     */
    @Column(name = "visit_flag")
    private Boolean visitFlag;

    /**
     * 是否输液 1是0否
     */
    @Column(name = "infusion_flag")
    private Boolean infusionFlag;

    /**
     * 是否为门诊慢特病患者  1是0否
     */
    @Column(name = "chronic_flag")
    private Boolean chronicFlag;

    /**
     * 急诊患者分级
     */
    @Column(name = "emergency_level")
    private String emergencyLevel;

    /**
     * 急诊患者去向
     */
    @Column(name = "emergency_destination")
    private String emergencyDestination;

    /**
     * 住院证开具时间
     */
    @Column(name = "admission_register_time")
    private Date admissionRegisterTime;

    /**
     * 患者主诉
     */
    @Column(name = "chief_complaint")
    private String chiefComplaint;

    /**
     * 院区ID
     */
    @Column(name = "hospital_id")
    private Integer hospitalId;

    private static final long serialVersionUID = 1L;
}