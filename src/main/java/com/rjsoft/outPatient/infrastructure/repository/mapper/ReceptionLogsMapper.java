package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ReceptionLogs;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;

/**
 * 接诊日志
 * <AUTHOR>
public interface ReceptionLogsMapper extends BaseMapper<ReceptionLogs> , ExampleMapper<ReceptionLogs> {

    /**
     * 根据挂号流水号获取接诊记录
     * @param regNo
     * @param hospitalCode
     * @return
     */
    default List<ReceptionLogs> getReceptionLogByRegNo(Long regNo, Integer hospitalCode) {
        Weekend weekend = new Weekend(ReceptionLogs.class);
        WeekendCriteria<ReceptionLogs, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ReceptionLogs::getRegNo, regNo);
        weekendCriteria.andEqualTo(ReceptionLogs::getHospitalCode, hospitalCode);
        weekend.setOrderByClause("createTime");
        return selectByExample(weekend);
    }
}
