<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.SuggestionsMapper">
    <select id="getCheckBls" resultType="com.rjsoft.outPatient.domain.caseHistory.dto.CheckBlDetail">
        select lsh suggestionId,
        CONVERT(varchar (50), Blid) id,
        CheckDoctorId checkDoctorId,
        CheckDoctorName checkDoctorName,
        CheckDate checkDate,
        CheckResult checkResult,
        CheckEMR checkEMR,
        DoctorId handleDoctorId,
        DoctorName handleDoctorName,
        HandleDate handleDate,
        isnull(HandleState,0) handleState,
        HandleResult handleResult,
        HandleEMR handleEMR,
        isnull([State],0) [state],
        isnull(CommitState,0) commitState
        from tbt_Emr_Suggestions
        where Convert(varchar (50), Blid) in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
</mapper>