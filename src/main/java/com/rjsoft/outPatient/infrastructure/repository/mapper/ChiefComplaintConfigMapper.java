package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ChiefComplaint;
import com.rjsoft.outPatient.infrastructure.repository.entity.ChiefComplaintConfig;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

public interface ChiefComplaintConfigMapper extends BaseMapper<ChiefComplaintConfig> {

    /**
     * 查询主诉配置信息
     *
     * @param type
     * @param hospitalCode
     * @return
     */
    default List<ChiefComplaintConfig> getPatChiefComplaintConfig(Integer type, Integer hospitalCode) {
        ChiefComplaintConfig chiefComplaintConfig = new ChiefComplaintConfig();
        chiefComplaintConfig.setType(type);
        chiefComplaintConfig.setHospitalCode(hospitalCode);
        chiefComplaintConfig.setStatus(1);
        return select(chiefComplaintConfig);
    }


}
