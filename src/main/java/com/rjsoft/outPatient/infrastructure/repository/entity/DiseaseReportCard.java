package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2021/7/29 17:22
 * @description
 **/
@Data
@Table(name = "Tbt_ZyEMR_Qpyzjszabgk")
public class DiseaseReportCard  implements Serializable {

    @Column(name = "icd10")
    private String icd10;
    @Column(name = "sfz")
    private String sfz;

}
