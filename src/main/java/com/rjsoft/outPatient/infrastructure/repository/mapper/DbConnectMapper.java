package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.DbConnect;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * 数据库链接
 * <AUTHOR>
public interface DbConnectMapper extends BaseMapper<DbConnect> {

    /**
     * 获取数据库链接
     * @param connName
     * @param hospitalCode
     * @return
     */
    default String getDbConnectStr(String connName,Integer hospitalCode) {
        if (StringUtils.isEmpty(connName) || hospitalCode == null) {
            return "";
        }
        DbConnect entity = new DbConnect();
        entity.setDbName(connName);
        entity.setHospitalCode(hospitalCode);
        List<DbConnect> connects = select(entity);
        if (connects.size() == 0) {
            return "";
        }
        return connects.get(0).getDbString();
    }
}
