<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.ReportMapper">

    <resultMap id="getRadiationReportMap" type="com.rjsoft.outPatient.domain.report.dto.RadiationReportDto">
        <result column="ReportDate" property="reportDate" jdbcType="TIMESTAMP"/>
        <result column="CodeName" property="codeName" jdbcType="VARCHAR"/>
        <result column="ExamBodyPart" property="examBodyPart" jdbcType="VARCHAR"/>
        <result column="EaxmMethod" property="eaxmMethod" jdbcType="VARCHAR"/>
        <result column="ClinicDiagnose" property="clinicDiagnose" jdbcType="VARCHAR"/>
        <result column="ImageFinding" property="imageFinding" jdbcType="VARCHAR"/>
        <result column="Impression" property="impression" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="getCrisisValueMap" type="com.rjsoft.outPatient.domain.report.dto.CrisisValueDto">
        <result column="RecDate" property="recDate" jdbcType="TIMESTAMP"/>
        <result column="Itemcode" property="itemcode" jdbcType="VARCHAR"/>
        <result column="Itemname" property="itemname" jdbcType="VARCHAR"/>
        <result column="Testvalue" property="testvalue" jdbcType="VARCHAR"/>
        <result column="Textrange" property="textrange" jdbcType="VARCHAR"/>
        <result column="Textdanwei" property="textdanwei" jdbcType="VARCHAR"/>
        <result column="Content" property="content" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="getHyMaterListGeneralMap" type="com.rjsoft.outPatient.domain.report.dto.TestReportDto">
        <result column="病历号" property="caseCard"/>
        <result column="科别" property="category"/>
        <result column="床号" property="bedNo"/>
        <result column="病人姓名" property="patName"/>
        <result column="性别" property="patSex"/>
        <result column="年龄" property="patAge"/>
        <result column="检验种类" property="examineType"/>
        <result column="备注" property="remark"/>
        <result column="采样日期" property="samplingDate"/>
        <result column="送检日期" property="inspectionDate"/>
        <result column="报告日期" property="reportDate"/>
        <result column="标本种类" property="specimenType"/>
        <result column="申请单号" property="applyFormNo"/>
        <result column="审核时间" property="auditDate"/>
        <result column="mid" property="mid"/>
        <result column="样本号" property="sampleNo"/>
        <result column="hospitalName" property="hospitalName"/>
    </resultMap>

    <resultMap id="getTestReportDetailMap" type="com.rjsoft.outPatient.domain.report.dto.TestReportDetailDto">
        <result column="编码" property="projectCode"/>
        <result column="项目名称" property="projectName"/>
        <result column="结果" property="result"/>
        <result column="提示" property="prompt"/>
        <result column="WJZ" property="crisisValue"/>
        <result column="单位" property="unit"/>
        <result column="参考值" property="reference"/>
        <result column="hisid" property="hisId"/>
    </resultMap>

    <resultMap id="getCheckReportMap" type="com.rjsoft.outPatient.domain.report.dto.CheckReportDto">
        <result column="ReportDate" property="reportDate"/>
        <result column="CodeName" property="codeName"/>
        <result column="ReportResult" property="reportResult"/>
    </resultMap>


    <select id="getCrisisValue" resultMap="getCrisisValueMap">
        {call Usp_MZYS_GetWJZ(#{hisCardNo})}
    </select>

    <select id="getRadiationReport" resultMap="getRadiationReportMap">
        {call Usp_Report_GetFSList(#{hisCardNo})}
    </select>

    <select id="getTestReportDetail" resultMap="getTestReportDetailMap">
        exec uspOptionGetLisInfoNew @m_Flag=12,
        @NewSqdh=  #{newSqdh},
        @Ybh= #{mid},
        @End_Time=#{endTime}
    </select>

    <select id="getHyMaterListGeneral" resultType="com.rjsoft.outPatient.domain.report.dto.TestReportDto">
        exec getHyMaterListNewByPatsfz_New @name=#{patName}, @patsfz=#{patSfz}, @startTime=#{startTime}, @endTime=#{endTime}
    </select>

    <select id="getHyMaterList" resultType="com.rjsoft.outPatient.domain.report.dto.TestReportDto">
        exec usp_Get_Hy_Mater_New @name=#{patName}, @patSfz=#{patSfz}, @startTime=#{startTime}, @endTime=#{endTime}
    </select>


    <select id="getTestType" resultType="com.rjsoft.outPatient.domain.report.dto.TestTypeResponse">
        SELECT
        b.detailid id,
        a.id typeId,
        a.name typeName,
        b.ItemId itemId,
        b.ItemName itemName,
        [dbo].[fn_GetPy](a.name) pym
        FROM
        MZYS_TV_HYFLMX b
        INNER JOIN MZYS_TV_HYFLZB a ON b.mainid= a.id
        AND a.hospitalCode= b.hospitalCode
        AND a.Status= b.Status
        WHERE a.hospitalCode= #{hospId}
        AND a.Status= 1
        <if test="code != null">
            AND ( b.ItemName LIKE #{code}
                      OR a.Name LIKE #{code}
                      OR a.InputCode LIKE #{code}
                      OR b.InputCode LIKE #{code})
        </if>
        order by a.Name desc
    </select>

    <select id="getTestItemList" resultType="com.rjsoft.outPatient.domain.report.dto.TestItemResponse">
            SELECT
            ItemCode itemId,
            CONVERT(FLOAT,ClinicExpensePrice) price,
            ClinicUnit unit,
            rtrim(InputCode1) inputCode
            FROM
            System_Tb_PubItems
            WHERE
            HospitalId = #{hospId}
            AND ItemCode IN
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item.itemId}
        </foreach>
    </select>

    <select id="getCheckReportList" resultMap="getCheckReportMap">
        {call Usp_Report_GetReportList(#{hisCardNo},#{startTime})}
    </select>


</mapper>