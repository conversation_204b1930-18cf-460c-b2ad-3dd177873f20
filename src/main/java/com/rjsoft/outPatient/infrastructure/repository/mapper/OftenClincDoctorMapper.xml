<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.OftenClincDoctorMapper">

    <select id="getDoctorDeptByScheduling"
            resultType="com.rjsoft.outPatient.infrastructure.repository.entity.OftenClincDoctor">
      exec Usp_QueryDoctorPreInfo
      @doctorid=#{doctorId},
      @begindate=#{begindate},
      @enddate=#{enddate}
    </select>

<select id="getDistinctDoctorAndDeptId" resultType="com.rjsoft.outPatient.domain.config.dto.DoctorDeptDto">
    select distinct ysbm doctorId from MZYS_TB_CMZYS WHERE yybm=#{hospitalCode}
</select>

</mapper>