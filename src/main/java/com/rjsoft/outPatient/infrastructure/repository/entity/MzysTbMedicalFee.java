package com.rjsoft.outPatient.infrastructure.repository.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import javax.persistence.*;
import lombok.Data;

/**
    * 门诊病案首页费用信息
    */
@Data
@Table(name = "MZYS_Tb_MedicalFee")
public class MzysTbMedicalFee implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 挂号流水号
     */
    @Column(name = "reg_no")
    private Long regNo;

    /**
     * 门（急）诊总费用
     */
    @Column(name = "total_fee")
    private BigDecimal totalFee;

    /**
     * 自付金额
     */
    @Column(name = "out_pocket_fee")
    private BigDecimal outPocketFee;

    /**
     * 一般医疗服务费
     */
    @Column(name = "commonly_serve_fee")
    private BigDecimal commonlyServeFee;

    /**
     * 一般治疗操作费
     */
    @Column(name = "commonly_treat_fee")
    private BigDecimal commonlyTreatFee;

    /**
     * 护理费
     */
    @Column(name = "nursing_fee")
    private BigDecimal nursingFee;

    /**
     * 综合医疗服务类其他费用
     */
    @Column(name = "other_serve_fee")
    private BigDecimal otherServeFee;

    /**
     * 病理诊断费
     */
    @Column(name = "pathology_diag_fee")
    private BigDecimal pathologyDiagFee;

    /**
     * 实验室诊断费
     */
    @Column(name = "laboratory_diag_fee")
    private BigDecimal laboratoryDiagFee;

    /**
     * 影像学诊断费
     */
    @Column(name = "imaging_diag_fee")
    private BigDecimal imagingDiagFee;

    /**
     * 临床诊断项目费
     */
    @Column(name = "clinical_diag_fee")
    private BigDecimal clinicalDiagFee;

    /**
     * 非手术治疗项目费
     */
    @Column(name = "no_operation_fee")
    private BigDecimal noOperationFee;

    /**
     * 临床物理治疗费
     */
    @Column(name = "clinical_treat_fee")
    private BigDecimal clinicalTreatFee;

    /**
     * 手术治疗费
     */
    @Column(name = "operation_treat_fee")
    private BigDecimal operationTreatFee;

    /**
     * 麻醉费
     */
    @Column(name = "anaesthesia_fee")
    private BigDecimal anaesthesiaFee;

    /**
     * 手术费
     */
    @Column(name = "operation_fee")
    private BigDecimal operationFee;

    /**
     * 康复费
     */
    @Column(name = "recovery_fee")
    private BigDecimal recoveryFee;

    /**
     * 中医治疗费
     */
    @Column(name = "chinese_treat_fee")
    private BigDecimal chineseTreatFee;

    /**
     * 西药费
     */
    @Column(name = "western_drug_fee")
    private BigDecimal westernDrugFee;

    /**
     * 抗菌药物费用
     */
    @Column(name = "antibiosis_drug_fee")
    private BigDecimal antibiosisDrugFee;

    /**
     * 中成药费
     */
    @Column(name = "chinese_drug_fee")
    private BigDecimal chineseDrugFee;

    /**
     * 中草药费
     */
    @Column(name = "chinese_herbal_fee")
    private BigDecimal chineseHerbalFee;

    /**
     * 血费
     */
    @Column(name = "blood_fee")
    private BigDecimal bloodFee;

    /**
     * 白蛋白类制品费
     */
    @Column(name = "albumin_fee")
    private BigDecimal albuminFee;

    /**
     * 球蛋白类制品费
     */
    @Column(name = "globins_Fee")
    private BigDecimal globinsFee;

    /**
     * 凝血因子类制品费
     */
    @Column(name = "coagula_factors_fee")
    private BigDecimal coagulaFactorsFee;

    /**
     * 细胞因子类制品费
     */
    @Column(name = "cell_factor_fee")
    private BigDecimal cellFactorFee;

    /**
     * 检查用一次性医用材料费
     */
    @Column(name = "inspect_disposable_fee")
    private BigDecimal inspectDisposableFee;

    /**
     * 治疗用一次性医用材料费
     */
    @Column(name = "treat_disposable_fee")
    private BigDecimal treatDisposableFee;

    /**
     * 手术用一次性医用材料费
     */
    @Column(name = "operation_disposable_fee")
    private BigDecimal operationDisposableFee;

    /**
     * 其他费
     */
    @Column(name = "other_fee")
    private BigDecimal otherFee;

    /**
     * 院区ID
     */
    @Column(name = "hospital_id")
    private Integer hospitalId;

    private static final long serialVersionUID = 1L;
}