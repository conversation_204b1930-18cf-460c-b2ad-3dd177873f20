package com.rjsoft.outPatient.infrastructure.repository.mapper;

import org.apache.ibatis.annotations.Param;
import com.rjsoft.outPatient.infrastructure.repository.entity.SciRegisterlistProject;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;


public interface SciRegisterlistProjectMapper extends BaseMapper<SciRegisterlistProject>, ExampleMapper<SciRegisterlistProject> {

    int updateSciRegisterlistProject(@Param("projectId") String projectId
            , @Param("regNo") Long regNo
            , @Param("followup") String followup
            , @Param("followupTitle") String followupTitle
            , @Param("hospitalCode") Integer hospitalCode);
}
