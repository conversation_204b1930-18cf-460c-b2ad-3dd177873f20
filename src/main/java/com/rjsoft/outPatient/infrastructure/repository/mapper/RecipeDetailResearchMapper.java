package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetailResearch;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/18 - 15:24
 */
public interface RecipeDetailResearchMapper extends BaseMapper<RecipeDetailResearch>, ExampleMapper<RecipeDetailResearch> {
    /**
     * 通过就诊流水号、项目编号查询
     *
     * @param receptionNo 就诊流水号
     * @param hospitalCode   医院编码
     * @return {@link RecipeDetailResearch}
     */
    List<RecipeDetailResearch> getByReceptionNoAndItemCodes(@Param("receptionNo") Long receptionNo, List<String> itemCodeList , @Param("hospitalCode") Integer hospitalCode);

    int updateRecipeDetailResearch(@Param("receptionNo") Long receptionNo, List<String> recipeDetailIds, @Param("hospitalCode") Integer hospitalCode);
}
