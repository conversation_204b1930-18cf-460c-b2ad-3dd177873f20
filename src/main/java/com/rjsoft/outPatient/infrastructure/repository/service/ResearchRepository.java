package com.rjsoft.outPatient.infrastructure.repository.service;

import com.github.pagehelper.PageInfo;
import com.rjsoft.outPatient.domain.research.dto.PatientListDto;
import com.rjsoft.outPatient.domain.research.dto.PatientListSearchDto;
import com.rjsoft.outPatient.domain.research.dto.RecipePatientListDto;

import java.util.HashMap;
import java.util.List;

public interface ResearchRepository {
    /**
     * 查询科研患者列表
     * @param patientListSearchDto
     * @return
     */
    List<PatientListDto> patientList(PatientListSearchDto patientListSearchDto);

    /**
     * 查询科研已就诊患者列表
     * @param patientListSearchDto
     * @return
     */
    PageInfo<RecipePatientListDto> recipePatientList(PatientListSearchDto patientListSearchDto);

    /**
     * 获取病历卡号
     * @param type
     * @return
     */
    HashMap getHisCardNo(Integer type);

    Long getRegNo(String tabName,String columnName);
}
