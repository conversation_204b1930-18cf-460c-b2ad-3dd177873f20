package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.SciPatientProject;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SciPatientProjectMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.SciPatientProjectRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

@Service
@AllArgsConstructor
public class SciPatientProjectRepositoryImpl implements SciPatientProjectRepository {
    private SciPatientProjectMapper sciPatientProjectMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<SciPatientProject> getSciPatientProject(Integer patId, Integer creator, Integer hospitalCode, String projectId) {
        Weekend<SciPatientProject> weekend = new Weekend<>(SciPatientProject.class);
        weekend.weekendCriteria().andEqualTo(SciPatientProject::getPatId,patId)
                .andEqualTo(SciPatientProject::getProjectId,projectId)
                .andEqualTo(SciPatientProject::getCreator,creator)
                .andEqualTo(SciPatientProject::getHospitalCode,hospitalCode);
        return sciPatientProjectMapper.selectByExample(weekend);
    }

    public List<SciPatientProject> querySciPatientProjectByPatIdAndProjectId(Integer patId, String projectId, Integer hospitalCode) {
        Weekend<SciPatientProject> weekend = new Weekend<>(SciPatientProject.class);
        weekend.weekendCriteria().andEqualTo(SciPatientProject::getPatId, patId)
                .andEqualTo(SciPatientProject::getProjectId, projectId)
                .andEqualTo(SciPatientProject::getHospitalCode, hospitalCode);
        return sciPatientProjectMapper.selectByExample(weekend);
    }

}
