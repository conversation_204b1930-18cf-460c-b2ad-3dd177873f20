package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.DrugInfoHospital;
import org.apache.ibatis.annotations.Mapper;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

@Mapper
public interface DrugInfoHospitalMapper extends BaseMapper<DrugInfoHospital>, ExampleMapper<DrugInfoHospital> {
    List<DrugInfoHospital> getDrugInfoHospital(Integer drugId, Integer hospitalId, Integer useDeptRange);
}
