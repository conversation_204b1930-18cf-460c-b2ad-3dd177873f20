package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.item.vo.ItemExeDeptRespVO;
import com.rjsoft.outPatient.domain.prescriptionAudit.dto.AuditPrescriptionDto;
import com.rjsoft.outPatient.domain.recipe.dto.*;
import com.rjsoft.outPatient.domain.recipe.vo.DoctorFeeQuotaVo;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * 门诊处方
 *
 * <AUTHOR>
public interface RecipeRepository {


    /**
     * 获取系统时间
     *
     * @return
     */
    Date getDate();

    long getGzdMaxId();


    /**
     * 根据处方id集合删除处方
     *
     * @param ids          处方id
     * @param hospitalCode 医院编码
     * @return 受影响的行数
     */
    Integer deleteRecipeByIds(List<Long> ids, Integer hospitalCode);

    /**
     * 根据处方id删除处方
     *
     * @param recipeId
     * @param hospitalCode
     * @return
     */
    Integer deleteRecipeById(Long recipeId, Integer hospitalCode);

    /**
     * 根据处方类型、就诊流水号 删除处方
     *
     * @param receptionNo    就诊流水号
     * @param hospitalCode   医院编码
     * @param recipeCategory 处方类型
     * @return Boolean
     */
    Boolean deleteRecipeByReceptionNoAndRecipeCategory(Long receptionNo, Integer hospitalCode, Integer recipeCategory);

    /**
     * 根据处方类型、就诊流水号 查询
     *
     * @param receptionNo    就诊流水号
     * @param hospitalCode   医院编码
     * @param recipeCategory 处方类型
     * @return Boolean
     */
    Recipe getRecipeByReceptionNoAndRecipeCategory(Long receptionNo, Integer hospitalCode, Integer recipeCategory);


    /**
     * 根据处方流水号加载处方
     *
     * @param recipeNo
     * @param hospitalCode
     * @return
     */
    Recipe getRecipeById(Long recipeNo, Integer hospitalCode);


    /**
     * 根据处方ids加载处方
     *
     * @param recipeIds    处方ids
     * @param hospitalCode 医院编码
     * @return
     */
    List<Recipe> getRecipeByIds(List<Long> recipeIds, Integer hospitalCode);

    /**
     * 根据就诊流水号、首次看诊医生查询处方
     *
     * @param operateType   操作类型 1：普通处方，2：精二类处方
     * @param receptionNo   就诊流水号
     * @param firstDoctorId 首次开方医生
     * @param hospitalCode  医院编码
     * @return
     */
    List<Recipe> getRecipeByReceptionNoAndFirstDoctorId(Integer operateType, Long receptionNo, Integer firstDoctorId, Integer hospitalCode);

    /**
     * 根据处方类型加载处方信息
     *
     * @param receptionNo
     * @param feeCategory
     * @param recipeCategory
     * @param hospitalCode
     * @return
     */
    List<Recipe> getRecipeByType(Long receptionNo, Integer feeCategory, Integer recipeCategory, Integer hospitalCode);

    /**
     * 保存处方
     *
     * @param recipe
     * @return
     */
    boolean saveRecipe(Recipe recipe);

    /**
     * 保存处方
     *
     * @param recipe
     * @return
     */
    boolean saveRecipes(List<Recipe> recipe);

    /**
     * 查询医生日费用指标
     *
     * @param doctorId     医生id
     * @param hospitalCode 医院编码
     * @return
     */
    DoctorFeeQuotaVo getDoctorDayFeeQuota(Integer doctorId, Integer hospitalCode);

    /**
     * 查询医生月费用指标
     *
     * @param doctorId     医生id
     * @param hospitalCode 医院编码
     * @return
     */
    DoctorFeeQuotaVo getDoctorMonthFeeQuota(Integer doctorId, Integer hospitalCode);

    /**
     * 根据就诊流水号查询处方
     *
     * @param receptionNo  就诊流水号
     * @param itemCategory 项目大类
     * @param hospitalCode 医院编码
     * @return List<Recipe>
     */
    List<Recipe> getRecipeByReceptionNo(Long receptionNo, Integer itemCategory, Integer hospitalCode);

    List<Recipe> getRecipeByReceptionNoTwo(Set<Long> receptionNo, List<Integer> itemCategoryList, Integer hospitalCode);

    /**
     * 根据住院流水号查询住院带药(历史处方接口使用)
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    List<WardTbAdv> getHistoryWardAdv(Long regNo, Integer hospitalCode);

    /**
     * 根据advid查询住院带药(历史处方接口使用)
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    WardTbAdv getHistoryWardAdvById(Integer regNo, Integer hospitalCode);

    /**
     * 获取序列
     *
     * @param sequenceEnum
     * @return
     */
    Long getSequence(SequenceEnum sequenceEnum);

    List<Long> getSequenceList(Integer num ,SequenceEnum sequenceEnum);

    /**
     * 获取最大组号
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    Integer getRecipeMaxGroupNo(Long receptionNo, Integer hospitalCode);

    /**
     * 获取最大换方标记
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    Integer getRecipeMaxChangeRecipeFlag(Long receptionNo, Integer hospitalCode);


    /**
     * 计算药品处方（剂量、单位、数量、天数、门诊单位、金额）
     *
     * @param recipe
     * @param preRecipe
     * @param dic
     * @param updateProperty
     */
    void calcDrugRecipe(PreRecipeDetail recipe, PreRecipeDto preRecipe, SystemTbPubItems dic, String updateProperty);

    //<editor-fold desc="处方前置校验，整理处方">
    Map<Integer, List<Integer>> getItemCodeTailByItemCodeList(List<Integer> itemCodeList);

    /**
     * 根据申请单生成明细
     *
     * @param record
     * @param applyList
     * @return
     */
    List<RecipeDetail> getRecipeDetailsByApply(ReceptionRecord record, ApplyList applyList);


    /**
     * 根据就诊流水号+医院编码获取处方信息
     *
     * @param receptionNo
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    List<AuditPrescriptionDto> getRecipeInfoByReceptionNo(String receptionNo, Integer doctorId, String hospitalCode);

    Integer getRecipeDetailCountOutOfSql(Long receptionNo, List<Integer> itemCategory, Integer dispensing, Integer hospitalCode, Integer itemCode);


    /**
     * 获取处方的最大序号并增
     *
     * @param feeCategory  处方 id
     * @param receptionNo  就诊流水号
     * @param hospitalCode 医院编码
     * @return
     */
    Integer getRecipeMaxSerialNumberNext(Integer feeCategory, Long receptionNo, Integer hospitalCode);

    /**
     * 查询岱嘉收费项目
     *
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    RisItemDj getRisItemByCode(Integer itemCode, Integer hospitalCode);

    Map<Integer,RisItemDj> getRisItemByCodeList(List<Integer> itemCodeList, Integer hospitalCode);

    /**
     * 取所有的RisItem,很少，用于缓存。必要时可以在调用方加接口清除，或定时刷新。
     * @return
     */
    Map<Integer,RisItemDj> getAllRisItem();

    /**
     * 获取未审方药品处方数量
     *
     * @param receptionNo
     */
    int getRecipeCount(Long receptionNo);

    /**
     * 获取就诊流水号对应的病历数据数量
     *
     * @param receptionNo
     * @param hospId
     */
    int getBlCount(Long receptionNo, Integer hospId);

    /**
     * 微信推送数据
     *
     * @param content
     * @param telephone
     * @param doctorId
     */
    int insertTemplate(String content, String telephone, Integer doctorId);

    /**
     * 根据处方流水号获取处方类型和处方名称
     *
     * @param recipeNo
     * @param hospitalCode
     */
    RecipeTypeResponse getRecipeType(Long recipeNo, Integer hospitalCode);

    /**
     * 根据套餐项目编码获取套餐下详情编码
     *
     * @param itemCode
     */
    List<Integer> getItemCodeTail(Integer itemCode);

    /**
     * 根据获取套餐下所有详情编码，以套餐项目编码分组
     *
     */
    Map<Integer, List<Integer>> getAllItemCodeTail();

    /**
     * 根据套餐项目编码获取套餐下详情编码,从预读缓存中取
     *
     * @param itemCodeList
     */
    Map<Integer, List<Integer>> getItemCodeTailByItemCodeListInPreLoad(List<Integer> itemCodeList);

    /**
     * 保存约束保护
     *
     * @param protection
     * @return
     */
    Boolean saveProtection(Protection protection);

    /**
     * 查询约束保护
     *
     * @param protection
     * @return
     */
    Protection getProtection(Protection protection);

    /**
     * 根据套餐Id获取套餐记录
     *
     * @param itemCodes
     */
    List<MainPackage> getMainPackageByPackageId(List<Integer> itemCodes);


    /**
     * 添加CA验签数据
     *
     * @param checkList
     * @return
     */
    boolean AddCaCheckInfo(List<CaCheckList> checkList);

    /**
     * 根据开药项目编码获取是否是麻醉类药品
     *
     * @param itemCode     项目编码
     * @param hospitalCode 医院编码
     */
    boolean isNarcotics(Integer itemCode, Integer hospitalCode, Integer restrictCode);

    List<ItemCountDto> listIsNarcoticCount(List<Integer> itemCodeList, Integer hospitalCode, Integer restrictCode);

    @DatabaseAnnotation
    List<RecipeDetail> listDoctorRecipePartDetail(Integer hospitalCode, Integer doctorId, Long receptionNo);

    List<RecipeDetail> listRecipePartDetail(Long receptionNo);

    /**
     * 添加输液分组明细
     *
     * @param params
     */
    void insertInfuseGroup(List<InfuseGroupDetail> params);

    /**
     * 根据处方明细id获取输液分组信息
     *
     * @param recipeDetailNos
     * @param hospitalCode
     */
    List<InfuseGroupDetail> getInfuseGroup(Set<Long> recipeDetailNos, Integer hospitalCode);

    /**
     * 根据挂号流水号和医院编码修改打印标记为【已打印】
     *
     * @param recipeDetailNo
     * @param hospitalCode
     */
    void updatePrintStatus(List<Long> recipeDetailNo, Integer hospitalCode);

    /**
     * 获取收费项目对应标本信息
     *
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    List<AssayReportItem> getAssayReportItemByItemCode(Integer itemCode, Integer hospitalCode);

    /**
     * 获取院区下对应标本信息
     *
     * @param hospitalCode
     * @return
     */
    List<AssayReportItem> getAssayReportItemByHospitalCode(Integer hospitalCode);
    /**
     * 保存CA处方信息
     *
     * @param receptionNo
     */
    void savePrescription(String receptionNo, Long caId);

    /**
     * 查询CA保存处方信息
     *
     * @param caId
     * @return
     */
    List<Integer> getCaPrescriptionId(Long caId);

    /**
     * 根据就就诊号查询处方
     *
     * @param receptionNos
     * @return
     */
    List<Recipe> getRecipeInfobyReceptionNos(List<Long> receptionNos, Integer feeCategory, Integer hospitalCode);

    /**
     * 根据处方流水号查询处方
     *
     * @param recipeNo
     * @return
     */
    Recipe getRecipeInfobyRecipeNo(Long recipeNo, Integer feeCategory, Integer hospitalCode);

    /**
     * 删除联动的草药处方
     *
     * @param recipeNo
     * @param feeCategory
     * @param hospitalCode
     * @return
     */
    boolean deleteLinkagebyRrecipeNo(Long recipeNo, Integer feeCategory, Integer hospitalCode, Long receptionNo);


    void updateRecipeDetailExedept(Long recipeDetailNo, Integer exeDept, Integer exeHospitalId, Integer hospitalCode);




    void setDefaultExeDept(PreRecipeDetail preRecipeDetail, Integer hospId, Integer itemCode, Integer feeCategory);

    List<ItemExeDeptRespVO> queryDefaultExeDept(String itemCode, String itemCategory, Integer applyFlag);

    /**
     * 查询处方点评结果
     *
     * @param doctorId
     * @param startDate
     * @param endDate
     * @return
     */
    List<PrescriptionCommentDto> getPrescriptionCommentByDoctorId(Integer doctorId, String startDate, String String);



    /**
     * 通过regno查询处方
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    List<Recipe> getRecipeByRegNo(Long regNo, Integer hospitalCode, String tableName);


    boolean updateRecipeRegno(Long regNo, Long receptionNo, List<Long> recipeDetailNos, Integer hospitalCode, Integer doctorId);


    void fillItemExecDeptByTimeItemCodeMap2TyMdc(List<Integer> itemCodeList, String keyName);


    void calcRecipeQty(PreRecipeDetail recipe, ChargeItem dic);

    /**
     * 删除处方外配往中间表数据
     */
    int deleteInsuranceOutRecipeByRecipeNo(String RecipeNo,String ReceptionNo,Integer HospitalCode);

    /**
     * 根据条件查询外配中间表记录是否可删除
     *
     * @param ReceptionNo
     * @param RecipeNo
     * @param HospitalCode
     */
    int getInsuranceOutRecipeByStatus(String ReceptionNo,String RecipeNo,Integer HospitalCode);
}
