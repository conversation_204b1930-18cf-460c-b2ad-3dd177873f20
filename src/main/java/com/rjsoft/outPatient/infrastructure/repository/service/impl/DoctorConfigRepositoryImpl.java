package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.infrastructure.repository.entity.DoctorConfig;
import com.rjsoft.outPatient.infrastructure.repository.entity.DoctorRight;
import com.rjsoft.outPatient.infrastructure.repository.entity.RightType;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DoctorConfigMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.DoctorRightMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RightTypeMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.DoctorConfigRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class DoctorConfigRepositoryImpl implements DoctorConfigRepository {
    DoctorConfigMapper doctorConfigMapper;
    RightTypeMapper rightTypeMapper;
    Doctor<PERSON><PERSON><PERSON><PERSON><PERSON> doctorRightMapper;

    /**
     * 保存医生配置
     * @param entity
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean saveDoctorConfig(DoctorConfig entity) {
        return doctorConfigMapper.save(entity);
    }

    /**
     * 获取医生配置
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<DoctorConfig> getConfigByDoctor(Integer doctorId, Integer hospitalCode){
        return doctorConfigMapper.getConfigByDoctor(doctorId,hospitalCode);
    }

    /**
     * 删除医生配置
     * @param configKey
     * @param doctorId
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public  boolean deleteDoctorConfig(String configKey,Integer doctorId,Integer hospitalCode) {
        return doctorConfigMapper.deleteDoctorConfig(configKey, doctorId, hospitalCode);

    }

    /**
     * 根据医院编号获取全部权限类型
     *
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public List<RightType> getRightList(Integer hospitalCode) {
        return rightTypeMapper.getRightList(hospitalCode);
    }

    /**
     * 添加门诊医生对应权限
     *
     * @param doctorRight
     */
    @Override
    @DatabaseAnnotation
    public void addDoctorRight(List<DoctorRight> doctorRight) {
        doctorRightMapper.addDoctorRight(doctorRight);
    }

}
