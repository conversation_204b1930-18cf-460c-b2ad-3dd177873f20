package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
* 
* @TableName System_Tv_ItemInfoDept
*/
@Data
@Table(name = "System_Tv_ItemInfoDept")
public class SystemTbItemexecdept implements Serializable {

    /**
    * 数据主键
    */
    @Id
    @Column(name = "ID", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;
    /**
    * 收费项目主信息ID
    */
    @Column(name = "globalId")
    private Integer globalId;
    /**
    * 收费项目编码
    */
    @Column(name = "itemCode")
    private Integer itemCode;
    /**
    * 执行科室ID
    */
    @Column(name = "deptId")
    private Integer deptId;
    /**
    * 停启用
    */
    @Column(name = "isStop")
    private Integer isStop;
    /**
    * 医院编码
    */
    @Column(name = "hospitalId")
    private Integer hospitalId;
    /**
    * 创建时间
    */
    @Column(name = "createdate")
    private Date createDate;
    /**
    * 创建人ID
    */
    @Column(name = "createuserid")
    private Integer createUserId;
    /**
    * 修改时间
    */
    @Column(name = "updatedate")
    private Date updateDate;
    /**
    * 修改人ID
    */
    @Column(name = "updateuserid")
    private Integer updateUserId;

}
