package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 处方限制
 * MZYS_NEW..MZYS_TB_CFXZMX
 *
 * <AUTHOR>
 * @since 2021/9/27-1:55 下午
 */
@Data
@Table(name = "MZYS_TB_CFXZMX")
public class RecipeRestriction implements Serializable {

    /**
     * 唯一标识符
     */
    @Id
    @Column(name = "id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;
    /**
     * 处方限制类型
     */
    @Column(name = "cfxzlx")
    private Integer recipeRestriction;
    /**
     * 科室编码
     */
    @Column(name = "ksbm")
    private Integer deptId;
    /**
     * 医生编码
     */
    @Column(name = "ysbm")
    private Integer doctorId;
    /**
     * 项目编码
     */
    @Column(name = "xmbm")
    private Integer itemCode;
    /**
     * 创建日期
     */
    @Column(name = "cjrq")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @Column(name = "xgrq")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    /**
     * 状态 0 正常 1 删除
     */
    @Column(name = "zt")
    private Integer status;
    /**
     * 医院编码
     */
    @Column(name = "yybm")
    private Integer hospitalCode;

    public RecipeRestriction(Integer itemCode, Integer hospitalCode) {
        this.itemCode = itemCode;
        this.hospitalCode = hospitalCode;
    }
    public RecipeRestriction(Integer doctorId, Integer itemCode, Integer hospitalCode) {
        this.doctorId = doctorId;
        this.itemCode = itemCode;
        this.hospitalCode = hospitalCode;
    }
    public RecipeRestriction() {
    }

}
