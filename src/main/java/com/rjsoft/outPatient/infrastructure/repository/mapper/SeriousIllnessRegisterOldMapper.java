package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.seriousIllness.vo.SeriousIllnessRegisterVo;
import com.rjsoft.outPatient.infrastructure.repository.entity.SeriousIllnessRegister;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.Date;
import java.util.List;

/**
 * 大病登记查询
 *
 * <AUTHOR>
public interface SeriousIllnessRegisterOldMapper {
    /**
     * 新库大病登记查询
     *  @param cardNo
     *  @param patName
     *  @param hospitalCode
     *  @param hisCardNo
     *  @param OldMZYSIP
     *  @param stateList
     *  @return
     */
    List<SeriousIllnessRegisterVo> getOldList(Date startTime, Date endTime, String cardNo, String patName, Integer hospitalCode, String hisCardNo,String OldMZYSIP,String patSFZ,List<Integer> stateList);

    /**
     * 新库大病登记查询
     *  @param idCard
     *  @return
     */
    Date getRegisterTimeByPatIdAndPatSFZ(String patId,String idCard);

}
