package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.*;
import com.rjsoft.outPatient.infrastructure.repository.service.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.*;

/**
 * 门诊处方
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class PsychotherapyApplyDetailRepositoryImpl implements PsychotherapyApplyDetailRepository {
    PsychotherapyApplyDetailMapper psychotherapyApplyDetailMapper;
    PsychotherapyItemMapper psychotherapyItemMapper;
    PsychotherapyApplyListMapper psychotherapyApplyListMapper;
    ApplicationTemplateMapper applicationTemplateMapper;

    @Override
    public boolean savePsychotherapyApplyDetail(PsychotherapyApplyDetail psychotherapyApplyDetail, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }
        boolean success;
        boolean hasData = psychotherapyApplyDetailMapper.existsWithPrimaryKey(psychotherapyApplyDetail);
        if (hasData) {
            success = psychotherapyApplyDetailMapper.updateByPrimaryKeySelective(psychotherapyApplyDetail) > 0;
        } else {
            success = psychotherapyApplyDetailMapper.insert(psychotherapyApplyDetail) > 0;
        }
        return success;
    }

    @Override
    @DatabaseAnnotation
    public boolean savePsychotherapyApplyDetailToMZYSNew(PsychotherapyApplyDetail psychotherapyApplyDetail) {
        boolean success;
        boolean hasData = psychotherapyApplyDetailMapper.existsWithPrimaryKey(psychotherapyApplyDetail);
        if (hasData) {
            success = psychotherapyApplyDetailMapper.updateByPrimaryKeySelective(psychotherapyApplyDetail) > 0;
        } else {
            success = psychotherapyApplyDetailMapper.insert(psychotherapyApplyDetail) > 0;
        }
        return success;
    }

    @Override
    @DatabaseAnnotation
    public boolean updatePsychotherapyApplyDetailToMZYSNew(Long recipeDetailId,Integer hospitalId, Integer isDel) {
        Weekend<PsychotherapyApplyDetail>weekend = new Weekend(PsychotherapyApplyDetail.class);
        weekend.weekendCriteria().andEqualTo(PsychotherapyApplyDetail::getRecipeDetailId,recipeDetailId).andEqualTo(PsychotherapyApplyDetail::getHospitalId,hospitalId);
        PsychotherapyApplyDetail psychotherapyApplyDetail = new PsychotherapyApplyDetail();
        psychotherapyApplyDetail.setIsDelete(isDel);
        return psychotherapyApplyDetailMapper.updateByExampleSelective(psychotherapyApplyDetail,weekend)>0;
    }

    @Override
    @DatabaseAnnotation
    public boolean updatePsychotherapyApplyDetailListToMZYSNew(List<Long> recipeDetailIdList, Integer hospitalId, Integer isDel) {
        if (recipeDetailIdList == null || recipeDetailIdList.size() == 0) {
            return false;
        }
        Weekend<PsychotherapyApplyDetail>weekend = new Weekend(PsychotherapyApplyDetail.class);
        weekend.weekendCriteria().andIn(PsychotherapyApplyDetail::getRecipeDetailId,recipeDetailIdList).andEqualTo(PsychotherapyApplyDetail::getHospitalId,hospitalId);
        PsychotherapyApplyDetail psychotherapyApplyDetail = new PsychotherapyApplyDetail();
        psychotherapyApplyDetail.setIsDelete(isDel);
        return psychotherapyApplyDetailMapper.updateByExampleSelective(psychotherapyApplyDetail,weekend)>0;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.MZYS)
    public boolean delPsychotherapyApplyDetail(Integer applyDetailId) {
        PsychotherapyApplyDetail psychotherapyApplyDetail = new PsychotherapyApplyDetail();
        psychotherapyApplyDetail.setId(applyDetailId);
        psychotherapyApplyDetailMapper.delete(psychotherapyApplyDetail);
        return true;
    }

    @Override
    @DatabaseAnnotation
    public boolean delPsychotherapyApplyDetailMZYSNew(Integer applyDetailId) {
        PsychotherapyApplyDetail psychotherapyApplyDetail = new PsychotherapyApplyDetail();
        psychotherapyApplyDetail.setId(applyDetailId);
        psychotherapyApplyDetailMapper.delete(psychotherapyApplyDetail);
        return true;
    }

    @Override
    @DatabaseAnnotation
    public boolean delPsychotherapyApplyDetailByApplyId(Integer applyId) {
        PsychotherapyApplyDetail psychotherapyApplyDetail = new PsychotherapyApplyDetail();
        psychotherapyApplyDetail.setApplyId(applyId);
        return psychotherapyApplyDetailMapper.delete(psychotherapyApplyDetail) > 0;
    }


    @Override
    @DatabaseAnnotation
    public PsychotherapyItem getPsychotherapyItem(Integer itemCode, Integer hospitalCode) {
        PsychotherapyItem psychotherapyItem = new PsychotherapyItem();
        psychotherapyItem.setItemCode(itemCode);
        psychotherapyItem.setHospitalCode(hospitalCode);
        List<PsychotherapyItem> list = psychotherapyItemMapper.select(psychotherapyItem);
        if (list.size() <= 0) {
            return null;
        } else {
            return list.get(0);
        }
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.PRIMARY)
    public List<PsychotherapyItem> getPsychotherapyItemList(List<Integer> itemCodeList, Integer hospitalCode) {
        if (itemCodeList == null || itemCodeList.size() == 0) {
            return new ArrayList<>();
        }
        Weekend<PsychotherapyItem> weekend = new Weekend<>(PsychotherapyItem.class);
        weekend.weekendCriteria().andIn(PsychotherapyItem::getItemCode, itemCodeList).andEqualTo(PsychotherapyItem::getHospitalCode, hospitalCode);
        return psychotherapyItemMapper.selectByExample(weekend);
    }

    @Override
    public PsychotherapyApplyList getPsychotherapyApplyList(Integer receptionNo, Integer doctorId, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }
        PsychotherapyApplyList psychotherapyItem = new PsychotherapyApplyList();
        psychotherapyItem.setVisitNo(Converter.toString(receptionNo));
        psychotherapyItem.setApplyDoctor(doctorId);
        List<PsychotherapyApplyList> list = psychotherapyApplyListMapper.select(psychotherapyItem);
        if (list.size() <= 0) {
            return null;
        } else {
            return list.get(0);
        }
    }

    @Override
    @DatabaseAnnotation
    public PsychotherapyApplyList getPsychotherapyApplyListFromMZYSNew(Integer receptionNo, Integer doctorId) {
        PsychotherapyApplyList psychotherapyItem = new PsychotherapyApplyList();
        psychotherapyItem.setVisitNo(Converter.toString(receptionNo));
        psychotherapyItem.setApplyDoctor(doctorId);
        List<PsychotherapyApplyList> list = psychotherapyApplyListMapper.select(psychotherapyItem);
        if (list.size() <= 0) {
            return null;
        } else {
            return list.get(0);
        }
    }

    @Override
    @DatabaseAnnotation
    public PsychotherapyApplyList getPsyApplyListByRecipeNo(Integer RecipeNo, Integer doctorId) {
        PsychotherapyApplyList psychotherapyItem = new PsychotherapyApplyList();
        psychotherapyItem.setRecipeNo(RecipeNo);
        psychotherapyItem.setApplyDoctor(doctorId);
        List<PsychotherapyApplyList> list = psychotherapyApplyListMapper.select(psychotherapyItem);
        if (list.size() <= 0) {
            return null;
        } else {
            return list.get(0);
        }
    }

    @Override
    public boolean savePsychotherapyApplyList(PsychotherapyApplyList psychotherapyApplyList, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }
        boolean success;
        boolean hasData = applicationTemplateMapper.existsWithPrimaryKey(psychotherapyApplyList);
        if (hasData) {
            success = psychotherapyApplyListMapper.updateByPrimaryKeySelective(psychotherapyApplyList) > 0;
        } else {
            psychotherapyApplyList.setStatus(-1);
            success = psychotherapyApplyListMapper.insert(psychotherapyApplyList) > 0;
        }
        return success;
    }

    @Override
    @DatabaseAnnotation
    public boolean savePsychotherapyApplyListToMZYSNew(PsychotherapyApplyList psychotherapyApplyList) {
        boolean success;
        boolean hasData = applicationTemplateMapper.existsWithPrimaryKey(psychotherapyApplyList);
        if (hasData) {
            success = psychotherapyApplyListMapper.updateByPrimaryKeySelective(psychotherapyApplyList) > 0;
        } else {
            psychotherapyApplyList.setStatus(-1);
            success = psychotherapyApplyListMapper.insert(psychotherapyApplyList) > 0;
        }
        return success;
    }

    @Override
    public boolean delPsychotherapyApplyList(Integer applyId, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }
        PsychotherapyApplyList psychotherapyApplyList = new PsychotherapyApplyList();
        psychotherapyApplyList.setId(applyId);
        psychotherapyApplyListMapper.delete(psychotherapyApplyList);
        return true;
    }

    @Override
    @DatabaseAnnotation
    public boolean delPsychotherapyApplyListMZYSNew(Integer applyId) {
        PsychotherapyApplyList psychotherapyApplyList = new PsychotherapyApplyList();
        psychotherapyApplyList.setId(applyId);
        psychotherapyApplyListMapper.delete(psychotherapyApplyList);
        return true;
    }

    @Override
    public PsychotherapyApplyDetail getPsychotherapyApplyDetail(Integer recipeDetailId, Integer applyId, Integer hospitalCode) {
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }
        PsychotherapyApplyDetail psychotherapyItem = new PsychotherapyApplyDetail();
        if (recipeDetailId != null) {
            psychotherapyItem.setRecipeDetailId(Converter.toInt64(recipeDetailId));
        }
        if (applyId != null) {
            psychotherapyItem.setApplyId(applyId);
        }
        List<PsychotherapyApplyDetail> list = psychotherapyApplyDetailMapper.select(psychotherapyItem);
        if (list.size() <= 0) {
            return null;
        } else {
            return list.get(0);
        }
    }

    @Override
    @DatabaseAnnotation
    public PsychotherapyApplyDetail getPsychotherapyApplyDetailFromMZYSNew(Integer recipeDetailId, Integer applyId) {
        PsychotherapyApplyDetail psychotherapyItem = new PsychotherapyApplyDetail();
        if (recipeDetailId != null) {
            psychotherapyItem.setRecipeDetailId(Converter.toInt64(recipeDetailId));
        }
        if (applyId != null) {
            psychotherapyItem.setApplyId(applyId);
        }
        List<PsychotherapyApplyDetail> list = psychotherapyApplyDetailMapper.select(psychotherapyItem);
        if (list.size() <= 0) {
            return null;
        } else {
            return list.get(0);
        }
    }

    @Override
    @DatabaseAnnotation
    public List<PsychotherapyApplyDetail> queryPsychotherapyApplyDetailListByRecipeDetailIds(List<Long> recipeDetailIdList, Integer hospitalCode) {
        Weekend<PsychotherapyApplyDetail> weekend = new Weekend<>(PsychotherapyApplyDetail.class);
        weekend.weekendCriteria().andIn(PsychotherapyApplyDetail::getRecipeDetailId, recipeDetailIdList)
                .andEqualTo(PsychotherapyApplyDetail::getHospitalId, hospitalCode);
        return psychotherapyApplyDetailMapper.selectByExample(weekend);
    }

}
