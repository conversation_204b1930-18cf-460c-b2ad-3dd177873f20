<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.CurrencyMapper">

    <select id="getDate" resultType="java.util.Date">
        select GETDATE()
    </select>
    <select id="getGzdMaxId" statementType="CALLABLE" resultType="java.lang.Long">
        {call dbo.USP_MZYS_GzdMaxId}
    </select>
    <select id="getMbwjMaxId" statementType="CALLABLE" resultType="java.lang.Integer">
        {call dbo.USP_MZYS_MbwjMaxId}
    </select>
    <select id="getSqdMaxId" statementType="CALLABLE" resultType="java.lang.Integer">
        {call dbo.USP_MZYS_SqdMaxId}
    </select>

</mapper>
