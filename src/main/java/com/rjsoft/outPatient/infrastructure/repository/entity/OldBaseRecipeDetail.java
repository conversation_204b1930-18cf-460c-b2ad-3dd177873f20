package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 处方明细基础类
 *
 * <AUTHOR>
 */
@Data
public class OldBaseRecipeDetail implements Serializable {

    /**
     * 就诊流水号
     */
    @Column(name = "jzlsh")
    private String receptionNo;

    /**
     * 挂号流水号
     */
    @Column(name = "ghlsh")
    private String regNo;

    /**
     * 处方流水号
     */
    @Column(name = "cflsh")
    private String recipeNo;

    /**
     * 项目编码
     */
    @Column(name = "xmbm")
    private Integer itemCode;

    /**
     * 项目名称
     */
    @Column(name = "xmmc")
    private String itemName;

    /**
     * 费用类别
     */
    @Column(name = "sflx")
    private Integer feeCategory;

    /**
     * 处方类型
     */
    @Transient
    private Integer recipeCategory;

    /**
     * 剂量
     */
    @Column(name = "jl")
    private String dose;

    /**
     * 剂量单位
     */
    @Column(name = "jldw")
    private String doseUnit;

    /**
     * 频率
     */
    @Column(name = "yf")
    private Integer frequency;

    /**
     * 规格
     */
    @Column(name = "gg")
    private String specification;

    /**
     * 给药途径
     */
    @Column(name = "gytj")
    private Integer usage;

    /**
     * 特殊用法
     */
    @Column(name = "tsyf")
    private String specialUsage;

    /**
     * 天数
     */
    @Column(name = "ts")
    private Integer days;

    /**
     * 项目数量
     */
    @Column(name = "xmsl")
    private BigDecimal quantity;

    /**
     * 单价
     */
    @Column(name = "dj")
    private BigDecimal price;

    /**
     * 单位
     */
    @Column(name = "dw")
    private String unit;

    /**
     * 门诊单位数量
     */
    @Column(name = "mzdwsl")
    private Integer clincUnitNum;

    /**
     * 处方金额
     */
    @Column(name = "cfje")
    private BigDecimal amount;

    /**
     * 组号
     */
    @Column(name = "zh")
    private Integer groupNo;

    /**
     * 配药途径编码
     */
    @Column(name = "pytjbm")
    private Integer dispensing;

    /**
     * 医生嘱托
     */
    @Column(name = "yszt")
    private String doctorEntrust;

    /**
     * 状态
     * 0 新增  1 修改  2 可以保存
     */
    @Column(name = "zt")
    private Integer status;

    /**
     * 执行科室
     */
    @Column(name = "zxks")
    private Integer execDept;

    /**
     * 换方标记
     */
    @Column(name = "hfbj")
    private Integer changeRecipeFlag;

    /**
     * 套餐标记
     */
    @Column(name = "tcbj")
    private Integer packageFlag;

    /**
     * 套餐明细流水号
     */
    @Column(name = "tcmxlsh")
    private String packageDetailNo;

    /**
     * 申请单
     */
    @Column(name = "sqd")
    private String applicationForm;

    /**
     * 检查流水号
     */
    @Column(name = "jclsh")
    private String examineNo;

    /**
     * 套餐ID
     */
    @Column(name = "tcid")
    private String packageId;

    /**
     * 处方状态
     */
    @Column(name = "cfzt")
    private Integer recipeStatus;

    /**
     * 审方标识
     */
    @Transient
    private Integer reviewStatus;

    public BigDecimal getHerbsAmount() {
        if (amount == null) {
            if (price != null && StringUtils.isNoneBlank(dose)) {
                amount = price.multiply(new BigDecimal(dose));
            }
        }
        return amount;
    }
}
