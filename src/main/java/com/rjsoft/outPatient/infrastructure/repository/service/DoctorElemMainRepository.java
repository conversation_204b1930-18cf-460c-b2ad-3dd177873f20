package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.QuerySonResult;
import com.rjsoft.outPatient.domain.doctorElemMain.dto.ElemParams;
import com.rjsoft.outPatient.infrastructure.repository.entity.ElemMain;
import com.rjsoft.outPatient.infrastructure.repository.entity.ElemTemp;
import com.rjsoft.outPatient.infrastructure.repository.entity.ElemTempFile;

import java.util.List;

public interface DoctorElemMainRepository {

    int getMbwjMaxId();

    /**
     * 根节点,子节点(保存)
     *
     * @param elemMain
     * @return
     */
    int saveElemMain(ElemMain elemMain);

    /**
     * 根节点,子节点(修改)
     *
     * @param elemMain
     * @return
     */
    int updateElemMain(ElemMain elemMain);

    /**
     * 元素主表删除
     *
     * @param elemCode
     * @param hospitalCode
     * @return
     */
    int delElemMain(Integer elemCode, Integer hospitalCode, Integer createUser);

    /**
     * 根据元素编码和医院编码查询是否存在子节点
     *
     * @param elemCode
     * @param hospitalCode
     * @param createUser
     * @return
     */
    List<ElemMain> listYSZBs(Integer elemCode, Integer hospitalCode, Integer createUser);

    /**
     * 根据元素编码，医院编码，创建人查询是否存在数据
     *
     * @param elemCode
     * @param hospitalCode
     * @return
     */
    ElemMain getElemMains(Integer elemCode, Integer hospitalCode);

    /**
     * 段落模板(保存)
     *
     * @param elemTemp
     * @return
     */
    int saveElemTemp(ElemTemp elemTemp);

    /**
     * 段落模板(修改)
     *
     * @param elemTemp
     * @return
     */
    int updateElemTemp(ElemTemp elemTemp);

    /**
     * 查询段落模板信息
     *
     * @param elemDetailCode
     * @param hospitalCode
     * @return
     */
    ElemTemp getElemTemp(Integer elemDetailCode, Integer hospitalCode);

    /**
     * 查询段落模板信息
     *
     * @param elemDetailCode
     * @param hospitalCode
     * @param createUser
     * @return
     */
    ElemTemp queryElemTemp(Integer elemDetailCode, Integer hospitalCode, Integer createUser);

    /**
     * 删除段落模板
     *
     * @param elemDetailCode
     * @param hospitalCode
     * @param createUser
     * @return
     */
    int deleteElemTemp(Integer elemDetailCode, Integer hospitalCode, Integer createUser);

    /**
     * 查询模板内容
     *
     * @param tempId
     * @param hospitalCode
     * @return
     */
    ElemTempFile getElemTempFile(Integer tempId, Integer hospitalCode);

    /**
     * 保存模板内容
     *
     * @param elemTempFile
     * @return
     */
    int saveElemTempFile(ElemTempFile elemTempFile);

    /**
     * 修改模板内容
     *
     * @param elemTempFile
     * @return
     */
    int updateElemTempFile(ElemTempFile elemTempFile);

    /**
     * 查询顶层文件元素列表
     *
     * @param createUser
     * @param searchVal
     * @param hospCode
     * @return
     */
    List<ElemMain> queryRoot(Integer createUser, String searchVal, Integer hospCode);

    /**
     * 查询顶级元素节点一级子信息
     *
     * @param elemCodeList
     * @param createUser
     * @param hospCode
     * @return
     */
    List<ElemMain> queryRootNext(List<Integer> elemCodeList, Integer createUser, Integer hospCode);

    /**
     * 查询顶级元素节点一级子元素明细信息
     *
     * @param elemCodeList
     * @param createUser
     * @param hospCode
     * @return
     */
    List<ElemTemp> queryElemNext(List<Integer> elemCodeList, Integer createUser, Integer hospCode);

    /**
     * 查询顶层文件元素子集列表
     *
     * @param elemParams
     * @return
     */
    QuerySonResult queryNextRoot(ElemParams elemParams);


}
