package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.ItemSetDetail;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

import java.util.List;

/**
 * 套餐明细
 * <AUTHOR>
public interface ItemSetDetailMapper extends BaseMapper<ItemSetDetail>, ExampleMapper<ItemSetDetail> {
    List<ItemSetDetail> getItemSetDetailBySetId(@Param("hospitalCode") Integer hospitalCode);

    List<ItemSetDetail> getItemSetDetailByDeptId(@Param("setIdList")List<Integer> setIdList,@Param("deptId") Integer deptId, @Param("hospitalCode") Integer hospitalCode);
}
