package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.Appointment;
import com.rjsoft.outPatient.infrastructure.repository.entity.AppointmentPatient;

import java.util.List;

/**
 * 预约表
 *
 * <AUTHOR>
public interface AppointmentRepository {

    /**
     * 患者当天是否已存在预约信息
     *
     * @param workDate
     * @param certificateNo
     * @param subjectId
     * @param hospitalCode
     */
    List<Appointment> isReserved(String workDate, String certificateNo, Integer subjectId, Integer hospitalCode);


    /**
     * 根据身份证号和科目Id判断患者当天是否已经预约占号
     *
     * @param certificateNo
     * @param subjectId
     * @param hospitalCode
     * @return
     */
    int getAppointmentCount(String certificateNo, Integer subjectId, Integer hospitalCode);

    /**
     * 患者当天是否已存在预约信息，加号管理功能
     *
     * @param subjectId
     * @param certificateNo
     * @param hospitalCode
     * @return
     */
    List<Appointment> isAppointment(List<Integer> subjectId, String certificateNo, Integer hospitalCode);

    /**
     * 根据患者初复诊状态查询预约表中已经预约的数量
     *
     * @param isReview
     * @param subjectId
     * @param deptId
     * @param doctorId
     * @param timeMs
     * @param hospitalCode
     * @return
     */
    Integer isReviewCount(Integer isReview, Integer subjectId, Integer deptId, Integer doctorId, String timeMs, Integer hospitalCode);

    /**
     * 根据主键列表查询预约信息
     *
     * @param appointmentIDs
     * @param hospitalCode
     * @returnreception/getRegisterListByDoctor
     */
    List<Appointment> getAppointmentBySQHs(List<Long> appointmentIDs, String hospitalCode);

    /**
     * 查询当天预约未到患者列表
     *
     * @param doctorId
     * @param deptId
     * @param hospitalCode
     * @return
     */
    List<Appointment> getTodayAppointment(Long doctorId,Long deptId, String hospitalCode);

    /**
     * 查询当天预约患者
     *
     * @param doctorId
     * @param deptId
     * @param hospitalCode
     * @return
     */
    List<Appointment> getTodayAppointmentList(Long doctorId,Long deptId, String hospitalCode);

    /**
     * 查询当天医生号源数量
     *
     * @param doctorId
     * @param deptId
     * @param hospitalCode
     * @return
     */
    int getTodayTotalNum(Long doctorId,Long deptId, String hospitalCode);

    /**
     * 根据科目Id判断患者当天是否已经预约占号
     *
     * @param subjectId
     * @param hospitalCode
     * @param vipNum
     * @return
     */
    int countBySubjectIdAndVipNum(Integer subjectId, Integer hospitalCode, String vipNum);

    /**
     * 根据sqh获取预约信息
     *
     * @param appointmentNum
     * @param hospitalCode
     * @return
     */
    Appointment getBySqh(Long appointmentNum, Integer hospitalCode);

    /**
     * 更新预约表中的vip号
     *
     * @param hospitalCode
     * @param vipNum
     * @param appointmentNum
     * @return
     */
    Boolean updateVipNumBySqh(Integer hospitalCode, String vipNum, Long appointmentNum);


    /**
     * 根据电话和身份证号获取患者预约信息
     *
     * @param phone
     * @param certificate
     */
    AppointmentPatient getPatientAppoint(String phone, String certificate);

    /**
     * 根据电话和身份证号获取患者预约信息，判断患者有没有认证
     *
     * @param phone
     * @param certificate
     */
    AppointmentPatient getPatientAppointment(String phone, String certificate);


}
