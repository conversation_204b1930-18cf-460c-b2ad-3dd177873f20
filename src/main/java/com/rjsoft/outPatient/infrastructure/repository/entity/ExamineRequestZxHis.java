package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.rjsoft.common.dictionary.AutoTranslateDict;
import com.rjsoft.outPatient.common.DicTypeConst;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 检查申请单主表（ZxHIS库）
 *
 * <AUTHOR>
@Data
@Table(name = "Tbt_RisRequest")
public class ExamineRequestZxHis implements Serializable {

    /**
     * risId
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @Column(name = "RisId", insertable = false)
    private Integer risId;

    /**
     * 检查编号
     */
    @Column(name = "RisNo")
    private String examineCode;

    /**
     * 检查预约号
     */
    @Column(name = "RisYyNo")
    private String examineAppointmentNo;

    /**
     * 处方明细ID
     */
    @Column(name = "cfMxId")
    private Long recipeDetailId;

    /**
     * 处方id
     */
    @Column(name = "CfId")
    private Long recipeId;

    /**
     * 收费类型
     */
    @Column(name = "CfType")
    private Integer feeCategory;

    /**
     * 检查类型名称
     */
    @Column(name = "JcTypeName")
    private String examineTypeName;

    /**
     * 检查类型编码
     */
    @Column(name = "JcTypeCode")
    private Integer examineType;

    /**
     * 患者编号
     */
    @Column(name = "patId")
    private Integer patId;

    /**
     * 卡号
     */
    @Column(name = "PatCard")
    private String cardNo;

    /**
     * 病历卡号
     */
    @Column(name = "PatHisCard")
    private String hisCardNo;

    /**
     * 患者姓名
     */
    @Column(name = "PatName")
    private String patName;

    /**
     * 患者性别
     */
    @Column(name = "PatSex")
    private Integer sex;

    /**
     * 患者年龄
     */
    @Column(name = "PatAge")
    private Integer age;

    /**
     * 患者地址
     */
    @Column(name = "PatAddress")
    private String patAddress;

    /**
     * 住院号
     */
    @Column(name = "HospNo")
    private String hospNo;

    /**
     * 住院流水号
     */
    @Column(name = "RegNo")
    private String inRegNo;

    /**
     * 住院病区
     */
    @Column(name = "HospZone")
    private Integer hospZone;

    /**
     * 住院床号
     */
    @Column(name = "HospBedNo")
    private String hospBedNo;

    /**
     * 诊断时间
     */
    //@Column(name = "ZdSj")
    //private String diagnoseTime;

    /**
     * 临床诊断
     */
    //@Column(name = "LcZd")
    //private String clinicalDiagnose;

    /**
     * 检查部位
     */
    @Column(name = "JcBw")
    private String examinePart;

    /**
     * 检查部位名称
     */
    @Column(name = "JcBwName")
    private String examinePartName;

    /**
     * 检查项目编码
     */
    @Column(name = "JcItemCode")
    private Integer examineItemCode;

    /**
     * 检查项目名称
     */
    @Column(name = "JcItemName")
    private String examineItemName;

    /**
     * 申请科室编码
     */
    @Column(name = "SqDeptId")
    @AutoTranslateDict(target = "applyDeptName", dictType = DicTypeConst.DEPT)
    private Integer applyDeptId;

    /**
     * 申请科室名称
     */
    @Column(name = "SqDeptName")
    private String applyDeptName;

    /**
     * 申请医生
     */
    @Column(name = "SqDoct")
    @AutoTranslateDict(target = "applyDoctorName", dictType = DicTypeConst.WORKER)
    private Integer applyDoctor;

    /**
     * 申请医生姓名
     */
    @Column(name = "SqDoctName")
    private String applyDoctorName;

    /**
     * 申请时间
     */
    @Column(name = "SqDate")
    private Date applyDate;

    /**
     * 检查医生
     */
    @Column(name = "JcDoct")
    private Integer examineDoctor;

    /**
     * 检查医生姓名
     */
    @Column(name = "JcDoctName")
    private String examineDoctorName;

    /**
     * 设备编码
     */
    @Column(name = "SbCode")
    private Integer deviceCode;

    /**
     * 设备名称
     */
    @Column(name = "SbName")
    private String deviceName;

    /**
     * 图片日期
     */
    @Column(name = "ImagDate")
    private Date imageDate;

    /**
     * 检查图片
     */
    @Column(name = "ImgeSee")
    private String imageSee;

    /**
     * 图片诊断
     */
    @Column(name = "ImgeZd")
    private String imageDiagnose;

    /**
     * 报告医生
     */
    @Column(name = "RepDoct")
    private Integer repDoctor;

    /**
     * 报告医生姓名
     */
    @Column(name = "RepDoctName")
    private String repDoctorName;

    /**
     * 报告日期
     */
    @Column(name = "RepDate")
    private Date repDate;

    /**
     * 报告日期
     */
    @Column(name = "ShDate")
    private Date reviewDate;

    /**
     * 审核医生
     */
    @Column(name = "ShDoct")
    private Integer reviewDoctor;

    /**
     * 审核医生姓名
     */
    @Column(name = "ShDoctName")
    private String reviewDoctorName;

    /**
     * 收费标记
     */
    @Column(name = "SfFlag")
    private Integer sfFlag;

    /**
     * 审核级别
     */
    @Column(name = "ShLevel")
    private Integer reviewLevel;

    /**
     * 状态
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 检查明细
     */
    @Transient
    private List<ExamineRequestDetailZxHis> requestDetails;
}
