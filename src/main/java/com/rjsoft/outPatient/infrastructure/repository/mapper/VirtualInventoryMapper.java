package com.rjsoft.outPatient.infrastructure.repository.mapper;


import com.rjsoft.outPatient.infrastructure.repository.entity.DeptItempStock;
import com.rjsoft.outPatient.infrastructure.repository.entity.VirtualInventory;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/8-11:14 上午
 */
public interface VirtualInventoryMapper extends BaseMapper<VirtualInventory>, ExampleMapper<VirtualInventory> {



    /**
     * 查询药库包装库存
     *
     * @param drugId     药品编码
     * @param deptId     药房编码
     * @param hospitalId 医院编码
     * @return 药品库存
     */
    BigDecimal getStorePackFactorInventoryByDrugIdAndDeptId(@Param("drugId") Integer drugId, @Param("deptId") Integer deptId, @Param("hospitalId") Integer hospitalId);

    /**
     * 查询门诊包装库存
     *
     * @param drugId     药品编码
     * @param deptId     药房编码
     * @param hospitalId 医院编码
     * @return 药品库存
     */
    BigDecimal getClinicPackFactorInventoryByDrugIdAndDeptId(@Param("drugId") Integer drugId, @Param("deptId") Integer deptId, @Param("hospitalId") Integer hospitalId);

    /**
     * 批量查询门诊包装库存
     *
     * @param drugIdList     药品编码list
     * @param deptIdList     药房编码list
     * @param hospitalId 医院编码
     * @return 药品库存
     */
    List<DeptItempStock> getClinicPackFactorInventoryByDrugIdAndDeptIdList(@Param("drugIdList") List<Integer> drugIdList, @Param("deptIdList") List<Integer> deptIdList);

    /**
     * 查询住院药房库存
     * @param drugId
     * @param deptId
     * @param hospitalId
     * @return
     */
    BigDecimal getInHospPackFactorInventoryByDrugIdAndDeptId(@Param("drugId") Integer drugId, @Param("deptId") Integer deptId, @Param("hospitalId") Integer hospitalId);

    @Select("exec UspSystemGetMaxId")
    Long getNextId();

    void deleteRecord(@Param("drugId") Integer drugId,
                      @Param("deptId") Integer deptId,
                      @Param("hospitalId") Integer hospitalId,
                      @Param("sourceDetailId") Integer sourceDetailId);

}
