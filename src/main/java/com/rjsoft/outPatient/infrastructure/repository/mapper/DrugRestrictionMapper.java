package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.recipe.dto.DrugRestrictionDTO;
import com.rjsoft.outPatient.domain.recipe.dto.ItemCountDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.DrugRestriction;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 药品限制 mapper
 *
 * <AUTHOR>
 * @since 2021/9/27-1:29 下午
 */
public interface DrugRestrictionMapper extends Mapper<DrugRestriction> {

    /**
     * 根据开药项目编码获取是否是麻醉类药品
     * @param itemCode     项目编码
     * @param hospitalCode 医院编码
     * @param restrictCode 药品限制编码
     */
    int isNarcotics(@Param("itemCode") Integer itemCode, @Param("hospitalCode")Integer hospitalCode,@Param("restrictCode")Integer restrictCode);

    /**
     * 根据开药项目编码获取是否是麻醉类药品
     * @param itemCodeList     项目编码
     * @param hospitalCode 医院编码
     * @param restrictCode 药品限制编码
     */
    List<ItemCountDto> listIsNarcoticCount(@Param("itemCodeList") List<Integer> itemCodeList, @Param("hospitalCode")Integer hospitalCode, @Param("restrictCode")Integer restrictCode);

    List<DrugRestrictionDTO> countDrugRestriction(@Param("itemCodeList") List<Integer> itemCodeList,
                                                  @Param("enumCodeList") List<Integer> enumCodeList,
                                                  @Param("hospId") Integer hospId);
}
