package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.DoctorAntimicrobial;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

public interface DoctorAntimicrobialMapper extends BaseMapper<DoctorAntimicrobial> {


    /**
     * 查询医生抗菌药权限信息
     *
     * @param doctorId
     * @return
     */
    default List<DoctorAntimicrobial> getDoctorAntimicrobial(Integer doctorId, Integer hospitalCode) {
        DoctorAntimicrobial doctorAntimicrobial = new DoctorAntimicrobial();
        doctorAntimicrobial.setWorkerId(doctorId);
        doctorAntimicrobial.setHospitalId(hospitalCode);
        doctorAntimicrobial.setStatus(1);
        return select(doctorAntimicrobial);
    }
}
