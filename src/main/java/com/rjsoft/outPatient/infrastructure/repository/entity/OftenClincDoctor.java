package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 常门诊医生
 *
 * <AUTHOR>
@Data
@Table(name = "MZYS_TB_CMZYS")
public class OftenClincDoctor implements Serializable {

    @Id
    @Column(name = "id", insertable = false)
    private Integer id;

    /**
     * 医生编码
     */
    @Column(name = "ysbm")
    private Integer doctorId;

    /**
     * 科室
     */
    @Column(name = "ksbm")
    private Integer deptId;

    /**
     * 医院编码
     */
    @Column(name = "yybm")
    private Integer hospitalCode;
}
