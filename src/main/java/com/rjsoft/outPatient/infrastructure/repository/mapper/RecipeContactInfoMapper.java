package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.Recipe;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeContactInfo;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeEX;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;

public interface RecipeContactInfoMapper extends BaseMapper<RecipeContactInfo>, ExampleMapper<RecipeContactInfo> {
    /**
     * 保存代煎信息
     *
     * @param recipe
     * @return
     */
    default boolean saveRecipeContactInfo(Recipe recipe) {
        RecipeContactInfo recipeContactInfo = recipe.getRecipeContactInfo();
        if (recipe.getDataFlag() == 0) {
            return insertSelective(recipeContactInfo) > 0;
        } else if (recipe.getDataFlag() == 1) {
            return updateByPrimaryKeySelective(recipeContactInfo) > 0;
        } else {
            return false;
        }
    }
}
