package com.rjsoft.outPatient.infrastructure.repository.service.impl;


import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.infrastructure.repository.entity.OldPatientInfo;
import com.rjsoft.outPatient.infrastructure.repository.mapper.OldPatientInfoMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.OldPatientInfoRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

@Service
@AllArgsConstructor
public class OldPatientInfoRepositoryImpl implements OldPatientInfoRepository {
    OldPatientInfoMapper oldPatientInfoMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.ZXHIS)
    public OldPatientInfo getOldPatientInfoByPatId(Integer patNo) {
        Weekend<OldPatientInfo> weekend = new Weekend<>(OldPatientInfo.class);
        weekend.weekendCriteria().andEqualTo(OldPatientInfo::getPatNo,patNo);
        return oldPatientInfoMapper.selectOneByExample(weekend);
    }

    @Override
    public String getHzbhByPatId(Integer patNo, Integer hospitalId) {
        String dataName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalId)?DatasourceName.MZYS:DatasourceName.MZYS3;
        DataSourceSwitchAspect.changeDataSource(dataName);
        String hzbh = oldPatientInfoMapper.getHzbhByPatId(patNo);
        return hzbh;
    }
}
