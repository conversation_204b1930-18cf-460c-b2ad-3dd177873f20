package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.Department;

import java.util.List;

/**
 *  本机配置
 * <AUTHOR>
 * @create 2022/08/16 11:26
 */

public interface SysConfigRepository {
    /**
     * 获取本机配置
     *
     * @param systemNo
     * @param key
     * @return
     */
    String getLocalConfig(int systemNo, String key, String hospitalCode, String ip);

}
