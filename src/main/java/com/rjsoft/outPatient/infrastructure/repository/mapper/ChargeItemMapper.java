package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.domain.execDept.dto.ChargeItemExecDto;
import com.rjsoft.outPatient.domain.recipe.dto.ChargeItemDto;
import com.rjsoft.outPatient.domain.recipe.dto.CodeResponse;
import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeItem;
import com.rjsoft.outPatient.infrastructure.repository.entity.ChargeViewItem;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 收费项目
 *
 * <AUTHOR>
public interface ChargeItemMapper extends BaseMapper<ChargeItem>, ExampleMapper<ChargeItem> {


    /**
     * 获取收费项目根据项明细集合
     *
     * @param ids
     * @param hospitalCode
     * @return
     */
    @DatabaseAnnotation(name = "HISDB")
    default List<ChargeItem> getChargeItemByIds(List<Integer> ids, Integer hospitalCode) {
        if (ids == null || ids.size() == 0) {
            return new ArrayList<>();
        }
        Weekend<ChargeItem> weekend = new Weekend<>(ChargeItem.class);
        WeekendCriteria<ChargeItem, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ChargeItem::getItemCode, ids);
        weekendCriteria.andEqualTo(ChargeItem::getHospitalId, hospitalCode);
        weekendCriteria.andEqualTo(ChargeItem::getStopped, 0);
        return selectByExample(weekend);
    }

    /**
     * 查询收费项目单位，包装因子
     *
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    default ChargeItem getChargeItemById(Integer itemCode, Integer hospitalCode) {
        Weekend<ChargeItem> weekend = new Weekend<>(ChargeItem.class);
        WeekendCriteria<ChargeItem, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ChargeItem::getItemCode, itemCode);
        weekendCriteria.andEqualTo(ChargeItem::getHospitalId, hospitalCode);
        weekendCriteria.andEqualTo(ChargeItem::getStopped, 0);
        //weekendCriteria.andIn(ChargeItem::getUseRange, Arrays.asList(0, 1));
        return selectOneByExample(weekend);
    }
    /**
     * 查询收费项目单位，包装因子
     *
     * @param itemCodeList
     * @param hospitalCode
     * @return
     */
    default List<ChargeItem> getChargeItemByIdList(List<Integer> itemCodeList, Integer hospitalCode) {
        if (itemCodeList == null || itemCodeList.size() == 0) {
            return new ArrayList<>();
        }
        Weekend<ChargeItem> weekend = new Weekend<>(ChargeItem.class);
        WeekendCriteria<ChargeItem, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ChargeItem::getItemCode, itemCodeList);
        weekendCriteria.andEqualTo(ChargeItem::getHospitalId, hospitalCode);
        weekendCriteria.andEqualTo(ChargeItem::getStopped, 0);
        return selectByExample(weekend);
    }

    /**
     * 两院合并-查询收费项目
     *
     * @param itemCode
     * @param hospitalCode
     * @return
     */
    default ChargeItem getChargeItemByItemCode(Integer itemCode, Integer hospitalCode, Integer execDept) {
        Weekend<ChargeItem> weekend = new Weekend<>(ChargeItem.class);
        WeekendCriteria<ChargeItem, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(ChargeItem::getItemCode, itemCode);
        weekendCriteria.andEqualTo(ChargeItem::getHospitalId, hospitalCode);
        weekendCriteria.andEqualTo(ChargeItem::getStopped, 0);
        weekendCriteria.andEqualTo(ChargeItem::getExeDept, execDept);
        weekendCriteria.andIn(ChargeItem::getUseRange, Arrays.asList(0, 1));
        return selectOneByExample(weekend);
    }

    /**
     * 根据项目大类，输入码，分页信息获取收费项目字典 关联查询Drug_Tb_LocalDeptDrug
     *
     * @param inputCode    输入码
     * @param hospitalCode 医院编码
     * @param itemCategory
     * @return {@link List<ChargeItem>}
     */
    List<ChargeItemDto> getChargeItemByInputCodeAndDeptId(@Param("inputCode") String inputCode,
                                                          @Param("hospitalCode") Integer hospitalCode,
                                                          @Param("itemCategory") Integer itemCategory,
                                                          @Param("inputType") String inputType,
                                                          @Param("useDept") Integer useDept,
                                                          @Param("curTime") String curTime,
                                                          @Param("storeTbname") String storeTbname);

    /**
     * 根据处方项目编码获取收费表对应的医院处方代码和医保处方代码
     *
     * @param itemCode
     * @param hospitalCode
     */
    List<CodeResponse> getCode(@Param("list") List<Integer> itemCode, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据处方项目编码获取收费表对应的医院处方代码和医保处方代码
     *
     * @param itemCode
     * @param hospitalCode
     */
    List<CodeResponse> getCodeByItemCode(@Param("list") List<Integer> itemCode, @Param("hospitalCode") Integer hospitalCode, @Param("execDept")Integer execDept);

    /**
     * 根据项目大类，输入码，分页信息获取收费项目字典 关联查询Drug_Tb_LocalDeptDrug
     *
     * @param inputCode    输入码
     * @param useDept      处方录入科室
     * @param hospitalCode 医院编码
     * @param itemCategory
     * @return {@link List<ChargeItem>}
     */
    List<ChargeViewItem> getNonChargeItemByInputCodeAndDeptId(@Param("inputCode") String inputCode,
                                                              @Param("hospitalCode") Integer hospitalCode,
                                                              @Param("list") List<Integer> itemCategory,
                                                              @Param("inputType") String inputType,
                                                              @Param("useDept") Integer useDept);

    /**
     * 查询门诊默认收费项目列表
     *
     * @param inputCode
     * @return
     */
    List<ChargeItemExecDto> getChargeItemDtos(@Param("inputCode")String inputCode, @Param("hospitalId")Integer hospitalId);

    /**
     * 两院合并-查询收费项目
     *
     * @param itemCode
     * @param hospitalCode
     * @param stopped
     * @return
     */
    ChargeItem getChargeItemByIdIsStopped(@Param("itemCode") Integer itemCode,
                                                @Param("deptId") Integer deptId,
                                                @Param("hospitalCode") Integer hospitalCode,
                                                @Param("stopped") Integer stopped,
                                                @Param("ydStopped") Integer ydStopped,
                                                @Param("ypUsed") Integer ypUsed);

}
