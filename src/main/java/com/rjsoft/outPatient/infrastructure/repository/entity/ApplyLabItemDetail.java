package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 化验指标明细表
 *
 * <AUTHOR>
@Data
@Table(name = "Apply_Tb_LabItemDetail")
public class ApplyLabItemDetail implements Serializable {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    private Integer id;

    /**
     * 主表ID
     */
    @Column(name = "LabItemId")
    private Integer labItemId;


    /**
     * 收费项目编码
     */
    @Column(name = "payItemId")
    private Integer payItemId;

    /**
     * 收费项目名称
     */
    @Column(name = "payItemName")
    private String payItemName;

    /**
     * 删除标记
     */
    @Column(name = "delFlag")
    private Integer delFlag;

    /**
     *
     */
    @Column(name = "payItemStatus")
    private Integer payItemStatus;

    /**
     * 医院编码
     */
    @Column(name = "hospId")
    private Integer hospId;


}
