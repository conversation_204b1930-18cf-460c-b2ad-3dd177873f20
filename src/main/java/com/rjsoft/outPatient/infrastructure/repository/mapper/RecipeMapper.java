package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.domain.prescriptionAudit.dto.AuditPrescriptionDto;
import com.rjsoft.outPatient.domain.reception.dto.InsuranceOutRecipeDto;
import com.rjsoft.outPatient.domain.recipe.dto.ItemCodeRelation;
import com.rjsoft.outPatient.domain.recipe.dto.PrescriptionCommentDto;
import com.rjsoft.outPatient.domain.recipe.dto.RecipeTypeResponse;
import com.rjsoft.outPatient.domain.recipe.vo.DoctorFeeQuotaVo;
import com.rjsoft.outPatient.infrastructure.repository.entity.Recipe;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ExampleMapper;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 门诊处方
 *
 * <AUTHOR>
public interface RecipeMapper extends BaseMapper<Recipe>, ExampleMapper<Recipe> {

    /**
     * 查询医生日费用指标
     *
     * @param doctorId     医生id
     * @param dateTime     当前日期
     * @param hospitalCode 医院编码
     */
    @Select("exec zxhis.dbo.uspMdGetAvgFeesDay #{doctorId} , #{dateTime}")
    DoctorFeeQuotaVo getDoctorDayFeeQuota(@Param("doctorId") Integer doctorId, @Param("dateTime") Date dateTime, Integer hospitalCode);

    /**
     * 查询医生月费用指标
     *
     * @param doctorId     医生id
     * @param dateTime     当前日期
     * @param hospitalCode 医院编码
     */
    @Select("exec zxhis.dbo.uspMdGetAvgFeesMonth #{doctorId} , #{dateTime}")
    DoctorFeeQuotaVo getDoctorMonthFeeQuota(@Param("doctorId") Integer doctorId, @Param("dateTime") Date dateTime, Integer hospitalCode);

    /**
     * 查询医生基药费用占比
     *
     * @param doctorId     医生id
     * @param type         日基药：0，月基药：1
     * @param dateTime     当前日期
     * @param hospitalCode 医院编码
     */
    @Select("exec zxhis.dbo.usp_Zxhis_GetDoctorFeesRatio #{doctorId}, #{dateTime}, #{type}")
    BigDecimal getDoctorBasicDrugFeeQuota(@Param("doctorId") Integer doctorId, @Param("dateTime") Date dateTime, @Param("type") Integer type, Integer hospitalCode);

    /**
     * 根据处方ID加载处方
     *
     * @param recipeNo
     * @param hospitalCode
     * @param table
     * @return
     */
    // FIXME: yutao 2024/6/26 为测多线程下切换数据源，暂时关闭缓存
    @Options(flushCache = Options.FlushCachePolicy.TRUE)
    default Recipe getRecipeById(Long recipeNo, Integer hospitalCode, String table) {
        Weekend weekend = new Weekend(Recipe.class);
        WeekendCriteria<Recipe, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(Recipe::getRecipeNo, recipeNo);
        weekendCriteria.andEqualTo(Recipe::getHospitalCode, hospitalCode);
        if (!StringUtils.isEmpty(table)) {
            weekend.setTableName(table);
        }
        return selectOneByExample(weekend);
    }

    /**
     * 根据处方类型加载处方
     *
     * @param receptionNo
     * @param feeCategory
     * @param recipeCategory
     * @param hospitalCode
     * @return
     */
    default List<Recipe> getRecipeByType(Long receptionNo, Integer feeCategory, Integer recipeCategory, Integer hospitalCode, String table) {
        Weekend weekend = new Weekend(Recipe.class);
        WeekendCriteria<Recipe, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(Recipe::getReceptionNo, receptionNo);
        if (feeCategory != null) {
            weekendCriteria.andEqualTo(Recipe::getFeeCategory, feeCategory);
        }
        weekendCriteria.andEqualTo(Recipe::getRecipeCategory, recipeCategory);
        weekendCriteria.andEqualTo(Recipe::getHospitalCode, hospitalCode);
        weekend.setTableName(table);

        return selectByExample(weekend);
    }


    List<Recipe> getRecipeByTypeBy(@Param("receptionNo") Long receptionNo,
                                   @Param("recipeCategory") Integer recipeCategory,
                                   @Param("hospitalCode") Integer hospitalCode);


    /**
     * 保存处方
     *
     * @param recipe
     * @return
     */
    default boolean saveRecipe(Recipe recipe) {
        if (recipe.getDataFlag() == 0) {
            return insertSelective(recipe) > 0;
        } else if (recipe.getDataFlag() == 1) {
            return updateByPrimaryKeySelective(recipe) > 0;
        } else if (recipe.getDataFlag() == 2) {
            Weekend weekend = new Weekend(Recipe.class);
            weekend.setTableName("MZYS_TB_MZCF_DATA");
            return updateByExampleSelective(recipe, weekend) > 0;
        } else {
            return false;
        }
    }

    List<AuditPrescriptionDto> getRecipeInfoByReceptionNo(@Param("receptionNo") String receptionNo,
                                                          @Param("doctorId") Integer doctorId,
                                                          @Param("hospitalCode") String hospitalCode);

    /**
     * 获取处方的最大序号
     *
     * @param feeCategory  收费类型
     * @param receptionNo  就诊流水号
     * @param hospitalCode 医院编码
     * @return
     */
    Integer getRecipeMaxSerialNumberNext(@Param("feeCategory") Integer feeCategory,
                                         @Param("receptionNo") Long receptionNo,
                                         @Param("hospitalCode") Integer hospitalCode);

    /**
     * 获取未审方药品处方数量
     *
     * @param receptionNo
     */
    int getRecipeCount(@Param("receptionNo") Long receptionNo);

    /**
     * 获取就诊流水号对应的病历数据数量
     *
     * @param receptionNo
     * @param hospId
     */
    int getBlCount(@Param("receptionNo") Long receptionNo, @Param("hospId") Integer hospId);

    /**
     * 根据处方流水号获取处方类型和处方名称
     *
     * @param recipeNo
     * @param hospitalCode
     */
    RecipeTypeResponse getRecipeType(@Param("recipeNo") Long recipeNo, @Param("hospitalCode") Integer hospitalCode);

    /**
     * 根据套餐项目编码获取套餐下详情编码
     *
     * @param itemCode
     */
    // FIXME: yutao 2024/6/26 为测多线程下切换数据源，暂时关闭缓存
    @Options(flushCache = Options.FlushCachePolicy.TRUE)
    List<Integer> getItemCodeTail(@Param("itemCode") Integer itemCode);

    /**
     * 根据套餐项目编码获取套餐下详情编码
     *
     * @param itemCodeList
     */
    List<ItemCodeRelation> getItemCodeTailByItemCodeList(@Param("itemCodeList") List<Integer> itemCodeList);

    /**套餐下详情编码
     * 取所有
     *
     */
    List<ItemCodeRelation> getAllItemCodeTail();

    /**
     * 保存CA处方信息
     *
     * @param receptionNo
     * @param caId
     */
    void savePrescription(@Param("receptionNo") String receptionNo, @Param("caId") Long caId);

    /**
     * 查询CA保存处方信息
     *
     * @param caId
     * @return
     */
    List<Integer> getCaPrescriptionId(@Param("caId") Long caId);

    void updateRecipeDetailExedept(@Param("recipeDetailNo") Long recipeDetailNo,
                                   @Param("exeDept") Integer exeDept,
                                   @Param("exeHospitalId") Integer exeHospitalId,
                                   @Param("hospitalCode") Integer hospitalCode);

    /**
     * 查询处方点评结果
     *
     * @param doctorId
     * @param startDate
     * @param endDate
     * @return
     */
    List<PrescriptionCommentDto> getPrescriptionCommentByDoctorId(@Param("doctorId") Integer doctorId
            , @Param("startDate") String startDate
            , @Param("endDate") String endDate);

    /**
     * 根据条件查询外配中间表是否存在记录
     *
     * @param ReceptionNo
     * @param RecipeNo
     * @param HospitalCode
     */
    int getInsuranceOutRecipeCount(@Param("ReceptionNo") String ReceptionNo
            , @Param("RecipeNo") String RecipeNo
            , @Param("HospitalCode") Integer HospitalCode);

    /**
     * 处方外配往中间表插入数据
     */
    int insertInsuranceOutRecipeDto(@Param("insuranceOutRecipeDto") InsuranceOutRecipeDto insuranceOutRecipeDto);

    /**
     * 删除处方外配往中间表数据
     */
    int deleteInsuranceOutRecipeByRecipeNo(@Param("RecipeNo") String RecipeNo
            , @Param("ReceptionNo") String ReceptionNo
            , @Param("HospitalCode") Integer HospitalCode);

    /**
     * 根据条件查询外配中间表记录是否可删除
     *
     * @param ReceptionNo
     * @param RecipeNo
     * @param HospitalCode
     */
    int getInsuranceOutRecipeByStatus(@Param("ReceptionNo") String ReceptionNo
            , @Param("RecipeNo") String RecipeNo
            , @Param("HospitalCode") Integer HospitalCode);
}
