package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 疾病上报类型
 *
 * <AUTHOR>
@Data
@Table(name = "System_Tb_UploadType")
public class DiagnoseUploadType implements Serializable {

    @Id
    @Column(name = "Id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer uploadTypeId;

    @Column(name = "uploadTypeCode")
    private String uploadTypeCode;

    @Column(name = "uploadTypeName")
    private String uploadTypeName;

    @Column(name = "uploadType")
    private Integer uploadType;

    @Column(name = "description")
    private String description;

    @Column(name = "delFlag")
    private Integer delFlag;

    @Column(name = "enanleFlag")
    private Integer enanleFlag;

    @Column(name = "creatorId")
    private Integer creatorId;

    @Column(name = "createTime")
    private Date createTime;

    @Column(name = "updatorId")
    private Integer updateId;

    @Column(name = "updatorTime")
    private Date updateTime;

    @Column(name = "hospId")
    private Integer hospitalCode;

    @Column(name = "ReportDiseaseId")
    private Integer reportDiseaseId;

    @Column(name = "ReportDiseaseCode")
    private String reportDiseaseCode;

    @Column(name = "ReportDiseaseName")
    private String reportDiseaseName;

    @Column(name = "diagnosisCode")
    private String diagnosisCode;

    @Column(name = "diagnosisCodeName")
    private String diagnosisCodeName;

}
