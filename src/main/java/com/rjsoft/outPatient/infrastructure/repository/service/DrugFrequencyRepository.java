package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.infrastructure.repository.entity.DrugFrequency;
import com.rjsoft.outPatient.infrastructure.repository.entity.FrequencyResult;

import java.util.List;

/**
 * 药品频次
 *
 * <AUTHOR>
public interface DrugFrequencyRepository {

    /**
     * 根据医院编码加载所有频次
     *
     * @param hospitalCode
     * @return
     */
    List<DrugFrequency> getDrugFrequency(Integer hospitalCode);

    /**
     * 根据输入码查询频次
     *
     * @param hospitalCode 医院编码
     * @param inputCode    输入码
     * @return
     */
    List<DrugFrequency> getDrugFrequency(Integer hospitalCode, String inputCode);

    /**
     * 根据输入码查询住院频次
     *
     * @param hospitalCode 医院编码
     * @param inputCode    输入码
     * @return
     */
    List<FrequencyResult> selFrequency(Integer hospitalCode, String inputCode);

    /**
     * 根据id查询住院频次
     *
     * @param hospitalCode 医院编码
     * @param id
     * @return
     */
    FrequencyResult selFrequencyById(Integer hospitalCode, Integer id);

    /**
     * 查询药品频次根据id 唯一标识符
     *
     * @param id           唯一标识符
     * @param hospitalCode 医院编码
     * @return DrugFrequency
     */
    DrugFrequency getDrugFrequencyById(Integer id, Integer hospitalCode);

    /**
     * 查询药品频次编码 唯一标识符
     *
     * @param code           唯一标识符
     * @param hospitalCode 医院编码
     * @return DrugFrequency
     */
    DrugFrequency getDrugFrequencyByCode(String code, Integer hospitalCode);
}
