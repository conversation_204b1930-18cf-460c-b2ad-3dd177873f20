package com.rjsoft.outPatient.infrastructure.repository.service;

import com.rjsoft.outPatient.domain.sickleave.dto.SickLeaveDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.SickLeave;
import com.rjsoft.outPatient.infrastructure.repository.entity.SickLeaveDiagnosis;
import com.rjsoft.outPatient.infrastructure.repository.entity.SickLeaveOld;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/7/29 - 14:32
 */
public interface SickLeaveRepository {

    /**
     * 查询
     *
     * @param entity {@link SickLeave}
     * @return
     */
    SickLeave getSickLeaveOne(SickLeave entity);

    /**
     * 查询病假单列表
     *
     * @param cardNo
     * @param hisCardNo
     * @param patName
     * @param status
     * @param hospitalCode
     * @return
     */
    List<SickLeave> getSickLeaveList(String cardNo, String hisCardNo, String patName, Integer status, Integer hospitalCode);

    /**
     * 根据患者id查询患者所有的病假单列表
     *
     * @param patId
     * @return
     */
    List<SickLeaveDto> getAllSickLeaveListByPatId(Integer patId);

    /**
     * 根据患者id查询患者所有的病假单列表
     *
     * @param patId
     * @return
     */
    List<SickLeaveOld> getOldSickLeaveListByPatId(String patId, Integer hospitalCode);

    /**
     * 查询老病假单列表
     *
     * @param cardNo
     * @param hisCardNo
     * @param patName
     * @param status
     * @param hospitalCode
     * @return
     */
    List<SickLeaveOld> getOldSickLeaveList(String cardNo, String hisCardNo, String patName, Integer status, Integer hospitalCode);
    /**
     * 根据挂号流水号查询病假单
     *
     * @param regNo        挂号流水号
     * @param hospitalCode 医院编码
     * @return
     */
    SickLeave getOneSickLeaveByRegNo(Long regNo, Integer hospitalCode);

    /**
     * 根据挂号流水号查询病假单
     *
     * @param regNos       挂号流水号
     * @param hospitalCode 医院编码
     * @return
     */
    List<SickLeave> getSickLeaveInRegNo(List<Long> regNos, Integer hospitalCode);

    /**
     * 根据id查询
     *
     * @param id 唯一标识符
     * @return
     */
    SickLeave getSickLeaveById(Integer id);

    /**
     * 修改
     *
     * @param entity {@link SickLeave}
     */
    void updateSickLeave(SickLeave entity);

    /**
     * 保存
     *
     * @param entity             {@link SickLeave}
     * @param sickLeaveDiagnoses
     */
    void saveSickLeave(SickLeave entity, List<SickLeaveDiagnosis> sickLeaveDiagnoses);


    /**
     * 获取病假单与疾病证明信息
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    String getSickInfo(Integer regNo, Integer hospitalCode);

    /**
     * 根据患者id查询患者所有的病假单列表
     *
     */
    List<SickLeave> querySickLeaveListByPatIds(List<Integer> patIds, Integer status, Integer hospitalCode);


}
