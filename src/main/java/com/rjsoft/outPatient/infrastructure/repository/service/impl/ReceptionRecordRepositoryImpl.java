package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.log.Log;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.common.enums.SequenceEnum;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatBlDto;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatBlParam;
import com.rjsoft.outPatient.domain.caseHistory.dto.PatRecipeInfoDTO;
import com.rjsoft.outPatient.domain.caseHistoryTemp.dto.BLTimeAndDept;
import com.rjsoft.outPatient.domain.diseaseReport.dto.DiagnoseRecordInfo;
import com.rjsoft.outPatient.domain.reception.dto.CAResult;
import com.rjsoft.outPatient.domain.reception.dto.ReceptionDiagnose;
import com.rjsoft.outPatient.domain.recipe.dto.HistoryReceptionDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.OldReceptionRecordMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ReceptionLogsMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.ReceptionRecordMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SysFunctionMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.ReceptionRecordRepository;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.StopWatch;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 接诊
 *
 * <AUTHOR>
@Service
@AllArgsConstructor
public class ReceptionRecordRepositoryImpl implements ReceptionRecordRepository {
    ReceptionRecordMapper receptionRecordMapper;
    SysFunctionMapper functionMapper;
    ReceptionLogsMapper receptionLogsMapper;
    OldReceptionRecordMapper oldReceptionRecordMapper;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Override
    @DatabaseAnnotation
    public ReceptionRecord getReceptionById(Long receptionNo, Integer hospitalCode) {
        return receptionRecordMapper.getReceptionById(receptionNo, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public List<ReceptionRecord> getReceptionByIds(List<Long> receptionNo, Integer hospitalCode) {
        StopWatch yusw = new StopWatch();
        yusw.start("getReceptionByIds 前");
        if (receptionNo == null || receptionNo.size() == 0) {
            return new ArrayList<>();
        }
        Weekend weekend = new Weekend(ReceptionRecord.class);
        WeekendCriteria<ReceptionRecord, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andIn(ReceptionRecord::getReceptionNo, receptionNo);
        weekendCriteria.andEqualTo(ReceptionRecord::getHospitalCode, hospitalCode);
        yusw.stop();
        yusw.start("sql 前");
        List<ReceptionRecord> list = receptionRecordMapper.selectByExample(weekend);

        yusw.stop();
        String yuswName = "getReceptionByIds";
        Log.info(yuswName + yusw.prettyPrint());
        Log.info(yuswName + yusw.shortSummary());
        Log.info(yuswName + "任务总耗时：" + yusw.getTotalTimeMillis());
        return list;
    }

    @Override
    @DatabaseAnnotation
    public List<ReceptionRecord> getReceptionByRegNo(List<Long> regNos, Integer hospitalCode) {
        return receptionRecordMapper.getReceptionByRegNo(regNos, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public List<ReceptionRecord> getReceptionByRegNoAndTable(List<Long> regNos, Integer hospitalCode, String tableName) {
        return receptionRecordMapper.getReceptionByRegNoAndTable(regNos, hospitalCode,tableName);
    }

    @Override
    @DatabaseAnnotation
    public List<ReceptionRecord> getReceptionByRegNoList(List<Long> regNos, Integer hospitalCode, Date startTime, Date endTime) {
        return receptionRecordMapper.getReceptionByRegNoList(regNos, hospitalCode, startTime, endTime);
    }

    @Override
    public List<OldReceptionRecord> getOldReceptionByRegNo(List<String> regNos, Integer hospitalCode, Date startTime, Date endTime) {
        String dataSourceName = HospitalClassify.GENERAL.getHospitalCode().equals(hospitalCode) ? DatasourceName.MZYS : DatasourceName.MZYS3;
        DataSourceSwitchAspect.changeDataSource(dataSourceName);
        return oldReceptionRecordMapper.getOldReceptionByRegNo(regNos, hospitalCode,startTime,endTime);
    }

    @Override
    @DatabaseAnnotation
    public ReceptionRecord getReceptionByRegNoAndDoctor(Long regNo, Integer doctorId, Integer hospitalCode) {
        List<Long> regNos = Collections.singletonList(regNo);
        List<ReceptionRecord> list = receptionRecordMapper.getReceptionByRegNo(regNos, hospitalCode);
        for (ReceptionRecord record : list) {
            if (record.getDoctorId().equals(doctorId)) {
                return record;
            }
        }
        return null;
    }

    @Override
    @DatabaseAnnotation
    public boolean saveReceptionRecord(ReceptionRecord reception) {
        Date date = functionMapper.getDate();
        boolean success;
        reception.setLastDate(date);
        if (reception.getReceptionNo() == null || reception.getReceptionNo() == 0) {
            //手动创建事务
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            //获取事务状态
            TransactionStatus status = platformTransactionManager.getTransaction(def);
            //避免调用时生成主键重复问题
            Long receptionNo = functionMapper.getGetSequences(SequenceEnum.RECEPTION_NO);
            //提交事务
            platformTransactionManager.commit(status);
            reception.setFirstDate(date);
            reception.setReceptionNo(receptionNo);
            success = receptionRecordMapper.insert(reception) > 0;
        } else {
            success = receptionRecordMapper.updateByPrimaryKeySelective(reception) > 0;
        }
        return success;
    }

    /**
     * 删除接诊记录
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public boolean delReceptionRecord(Long receptionNo, Integer hospitalCode) {
        ReceptionRecord entity = new ReceptionRecord();
        entity.setReceptionNo(receptionNo);
        entity.setHospitalCode(hospitalCode);
        return receptionRecordMapper.deleteByPrimaryKey(entity) > 0;
    }

    /**
     * 添加接诊日志
     *
     * @param registerList
     * @param receptionRecord
     */
    @Override
    @DatabaseAnnotation
    public void addReceptionLog(RegisterList registerList, ReceptionRecord receptionRecord, String opType) {
        Date date = functionMapper.getDate();
        ReceptionLogs entity = new ReceptionLogs();
        entity.setReceptionNo(receptionRecord.getReceptionNo());
        entity.setRegNo(registerList.getRegNo());
        entity.setReceptionDoctor(receptionRecord.getDoctorId());
        entity.setRegDept(registerList.getDeptID());
        entity.setRegDoctor(registerList.getGhDoctor());
        entity.setOpType(opType);
        entity.setCreateTime(date);
        entity.setHospitalCode(registerList.getHospitalCode());
        receptionLogsMapper.insert(entity);
    }

    @Override
    @DatabaseAnnotation
    public void addSciReceptionLog(ReceptionRecord receptionRecord, String opType) {
        Date date = functionMapper.getDate();
        ReceptionLogs entity = new ReceptionLogs();
        entity.setReceptionNo(receptionRecord.getReceptionNo());
        entity.setRegNo(receptionRecord.getRegNo());
        entity.setReceptionDoctor(receptionRecord.getDoctorId());
        entity.setRegDept(receptionRecord.getDeptId());
        entity.setRegDoctor(receptionRecord.getDoctorId());
        entity.setOpType(opType);
        entity.setCreateTime(date);
        entity.setHospitalCode(receptionRecord.getHospitalCode());
        receptionLogsMapper.insert(entity);
    }

    /**
     * 根据挂号流水号加载接诊记录
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public List<ReceptionLogs> getReceptionLogByRegNo(Long regNo, Integer hospitalCode) {
        return receptionLogsMapper.getReceptionLogByRegNo(regNo, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public List<ReceptionRecord> getReceptionByParam(ReceptionRecord reception) {
        String firstDateStr = new SimpleDateFormat("yyyy-MM-dd").format(reception.getFirstDate());
        try {
            reception.setFirstDate(new SimpleDateFormat("yyyy-MM-dd").parse(firstDateStr));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return receptionRecordMapper.getReceptionByParam(reception);
    }

    @Override
    @DatabaseAnnotation
    public List<PatRecipeInfoDTO> getPatRecipe(Set<Long> receptionNoSet, Integer hospitalCode) {
        return receptionRecordMapper.getPatRecipe(receptionNoSet, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    public List<ReceptionRecord> getReceptionListByPatIds(Set<Integer> patIdList, Date startDate, Date endDate) {
        return receptionRecordMapper.getReceptionListByPatIds(patIdList, startDate, endDate);
    }

    /**
     * 加载本次看诊中符合六大类的诊断
     *
     * @param receptionNo 就诊流水号
     * @param hospId      医院Id
     */
    @Override
    @DatabaseAnnotation
    public List<ReceptionDiagnose> selectDiagnoseList(Long receptionNo, Integer hospId) {
        return receptionRecordMapper.selectDiagnoseList(receptionNo, hospId);
    }

    /**
     * 加载本次看诊中符合六大类的诊断[根据诊断编码]
     *
     * @param receptionDiagnose
     */
    @Override
    @DatabaseAnnotation(name = "ZYEMR")
    public List<ReceptionDiagnose> selectDiagnoseListByDiagnoseNo(List<ReceptionDiagnose> receptionDiagnose) {
        return receptionRecordMapper.selectDiagnoseListByDiagnoseNo(receptionDiagnose);
    }

    /**
     * 获取已经上报的疾病名称列表
     *
     * @param certificateNo
     */
    @Override
    @DatabaseAnnotation
    public List<String> getSbName(String certificateNo, Integer hospitalCode) {
        return receptionRecordMapper.getSbName(certificateNo, hospitalCode);
    }

    /**
     * 电子病历验签结果
     *
     * @param doctorId 医生id
     */
    @Override
    public CAResult getCAResult(Integer doctorId) {
        return receptionRecordMapper.getCAResult(doctorId);
    }

    /**
     * 根据患者编号获取患者历史就诊记录
     *
     * @param patIds
     */
    @Override
    @DatabaseAnnotation
    public List<ReceptionRecord> getReceptionRecord(Set<Integer> patIds, Integer hospitalCode) {
        return receptionRecordMapper.getReceptionRecord(patIds, hospitalCode);
    }

    /**
     * 根据就诊流水号获取看诊时间和科室
     *
     * @param hospitalCode
     * @param receptionNo
     */
    @Override
    @DatabaseAnnotation
    public BLTimeAndDept getBlRecord(Integer hospitalCode, Integer receptionNo) {
        return receptionRecordMapper.getBlRecord(hospitalCode, receptionNo);
    }

    /**
     * 根据就诊流水号获取总院看诊记录
     *
     * @param receptions
     * @param hospitalCode
     */
    @Override
    public List<DiagnoseRecordInfo> getReceptionRecordByReceptions(Set<String> receptions, Integer hospitalCode) {
        DataSourceSwitchAspect.changeDataSource(DatasourceName.PRIMARY);
        List<DiagnoseRecordInfo> recordInfoList = receptionRecordMapper.getReceptionAndPatNo(receptions);
        if (hospitalCode.equals(1)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS);
        } else {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.MZYS3);
        }
        recordInfoList.addAll(receptionRecordMapper.getReceptionAndPatNo(receptions));
        return recordInfoList;
    }

    /**
     * 获取新门诊医生未书写病历jzlsh
     *
     * @param param
     */
    @Override
    @DatabaseAnnotation
    public List<PatBlDto> notFilledReceptionRecord(PatBlParam param) {
        return receptionRecordMapper.notFilledReceptionRecord(param);
    }

    /**
     * 获取新门诊医生未提交病历jzlsh
     *
     * @param param
     */
    @Override
    @DatabaseAnnotation
    public List<PatBlDto> notCommittedReceptionRecord(PatBlParam param) {
        return receptionRecordMapper.notCommittedReceptionRecord(param);
    }

    /**
     * 获取新门诊医生病历jzlsh
     *
     * @param param
     */
    @Override
    @DatabaseAnnotation
    public List<PatBlDto> getCaseHistoryByDoctorId(PatBlParam param, Integer status){
        return receptionRecordMapper.getCaseHistoryByDoctorId(param,status);
    }

    @Override
    public List<PatBlDto> getOldCaseHistoryByDoctorId(PatBlParam param, Integer status) {
        String dataSource = HospitalClassify.GENERAL.getHospitalCode().equals(param.getHospitalCode())?DatasourceName.MZYS:DatasourceName.MZYS3;
        DataSourceSwitchAspect.changeDataSource(dataSource);
        return receptionRecordMapper.getOldCaseHistoryByDoctorId(param,status);
    }

    /**
     * 获取新门诊医生未签名病历jzlsh and regno
     *
     * @param param
     */
    @Override
    @DatabaseAnnotation
    public List<PatBlDto> notSignedReceptionRecord(PatBlParam param) {
        return receptionRecordMapper.notSignedReceptionRecord(param);
    }

    /**
     * 根据挂号流水号加载医保类型
     *
     * @param regNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Integer getInsuranceByRegNo(Long regNo, Integer hospitalCode) {
        return receptionRecordMapper.getInsuranceByRegNo(regNo, hospitalCode);
    }

    /**
    根据收费类型判断是否医保
     **/
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Integer getInsuranceByChargeType(Integer chargeType, Integer hospitalCode) {
        return receptionRecordMapper.getInsuranceByChargeType(chargeType,hospitalCode);
    }

    /**
     * 根据挂号流水号获取有药品处方明细的看诊记录
     *
     * @param regNos
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public List<ReceptionRecord> getDrugReceptionRecord(List<Long> regNos, Integer hospitalCode) {
        return receptionRecordMapper.getDrugReceptionRecord(regNos, hospitalCode);
    }

    /**
     * 查询互联网处方详情信息
     *
     * @param internetRegNos
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<HistoryReceptionDto> getInternetDrugReceptionRecord(List<String> internetRegNos, Integer hospitalCode) {
        return receptionRecordMapper.getInternetDrugReceptionRecord(internetRegNos, hospitalCode);
    }

    @Override
    public List<HistoryReceptionDto> getInternetDrugReceptionTimeByRegNos(List<Long> regNos, Integer hospitalCode) {
        List<String> regNoSt = regNos.stream().map(String::valueOf).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(hospitalCode)) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
            List<HistoryReceptionDto> internetDrugReception1 = receptionRecordMapper.getInternetDrugReceptionTimeByRegNos(regNoSt);
            List<HistoryReceptionDto> internetReceptions = new ArrayList<>(internetDrugReception1);
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
            List<HistoryReceptionDto> internetDrugReception3 = receptionRecordMapper.getInternetDrugReceptionTimeByRegNos(regNoSt);
            internetReceptions.addAll(internetDrugReception3);
            return internetReceptions;
        }else if (1 == hospitalCode) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
            return receptionRecordMapper.getInternetDrugReceptionTimeByRegNos(regNoSt);
        } else if (3 == hospitalCode) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
            return receptionRecordMapper.getInternetDrugReceptionTimeByRegNos(regNoSt);
        } else  {
            return null;
        }
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public Long getLastReceptionByDoctorId(Integer doctorId, Integer deptId, Integer hospitalCode) {
        return receptionRecordMapper.getLastReceptionByDoctorId(doctorId,deptId,hospitalCode);
    }


    /**
     * 根据就诊流水号获取看诊记录
     *
     * @param receptionNo
     * @param hospitalCode
     */
    @Override
    public ReceptionRecord getReceptionRecordByReceptionNo(Long receptionNo, Integer hospitalCode) {
        return receptionRecordMapper.getReceptionById(receptionNo, hospitalCode);
    }


    /**
     * 根据就诊流水号获取看诊记录
     *
     * @param receptionNos
     * @param hospitalCode
     */
    @Override
    @DatabaseAnnotation
    public List<ReceptionRecord> getRegNoByReceptions(Set<Integer> receptionNos, Integer hospitalCode) {
        return receptionRecordMapper.getRegNoByReceptions(receptionNos, hospitalCode);
    }


}