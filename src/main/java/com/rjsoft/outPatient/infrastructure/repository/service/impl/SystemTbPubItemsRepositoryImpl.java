package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.utils.TyMapUtil;
import com.rjsoft.common.utils.TyMdc;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.consts.PreLoad;
import com.rjsoft.outPatient.domain.preload.PreLoadCache;
import com.rjsoft.outPatient.domain.recipe.constant.TyKey;
import com.rjsoft.outPatient.domain.recipe.dto.SystemTvPubItemsDto;
import com.rjsoft.outPatient.infrastructure.repository.entity.*;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SpecialItemMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.SystemTbPubItemsMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.SystemTbPubItemsRepository;
import org.apache.commons.lang3.tuple.Pair;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class SystemTbPubItemsRepositoryImpl implements SystemTbPubItemsRepository {
    SystemTbPubItemsMapper systemTbPubItemsMapper;
    SpecialItemMapper specialItemMapper;
    PreLoadCache preLoadCache;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public SystemTbPubItems getChargeItemById(Integer itemCode, Integer hospitalCode, Integer stopped) {
        SystemTbPubItems pubItemsPreLoad = preLoadCache.get(PreLoad.SYSTEM_TB_PUB_ITEMS+"_"+hospitalCode+"_"+itemCode);
        if(pubItemsPreLoad!= null){
            preLoadCache.put(PreLoad.SYSTEM_TB_PUB_ITEMS+"_"+hospitalCode+"_"+itemCode,pubItemsPreLoad);
            return pubItemsPreLoad;
        }

        Map<Integer,SystemTbPubItems> chargeItemByItemCodeMap = TyMdc.get(TyKey.CHARGE_ITEM_BY_ITEM_CODE_MAP);
        if(chargeItemByItemCodeMap == null) {
            Weekend<SystemTbPubItems> weekend = new Weekend<>(SystemTbPubItems.class);
            weekend.weekendCriteria().andEqualTo(SystemTbPubItems::getItemCode, itemCode)
                    .andEqualTo(SystemTbPubItems::getHospitalId, hospitalCode)
                    .andEqualTo(SystemTbPubItems::getStopped, stopped);
            return systemTbPubItemsMapper.selectOneByExample(weekend);
        }else{
            return chargeItemByItemCodeMap.get(itemCode);
        }
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<SystemTbPubItems> getChargeItemByIdList(List<Integer> itemCodeList, Integer hospitalCode, Integer stopped) {
        if (itemCodeList == null || itemCodeList.size() == 0) {
            return new ArrayList<>();
        }
        Weekend<SystemTbPubItems> weekend = new Weekend<>(SystemTbPubItems.class);
        weekend.weekendCriteria().andIn(SystemTbPubItems::getItemCode,itemCodeList)
                .andEqualTo(SystemTbPubItems::getHospitalId,hospitalCode)
                .andEqualTo(SystemTbPubItems::getStopped,stopped);
        return systemTbPubItemsMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public SystemTbPubItems getChargeItemByMzItemId(Integer mzItemId, Integer hospitalCode, Integer stopped) {
        Weekend<SystemTbPubItems> weekend = new Weekend<>(SystemTbPubItems.class);
        weekend.weekendCriteria().andEqualTo(SystemTbPubItems::getMzItemId,mzItemId)
                .andEqualTo(SystemTbPubItems::getHospitalId,hospitalCode)
                .andEqualTo(SystemTbPubItems::getStopped,stopped);
        return systemTbPubItemsMapper.selectOneByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<SystemTbPubItems> getSystemTbPubItems(String inputCode, Integer hospitalCode) {
        Weekend<SystemTbPubItems> weekend = new Weekend<>(SystemTbPubItems.class);

        final WeekendCriteria<SystemTbPubItems, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(SystemTbPubItems::getHospitalId, hospitalCode)
                .andEqualTo(SystemTbPubItems::getStopped, 0);

        if (org.apache.commons.lang3.StringUtils.isNotBlank(inputCode)) {
            inputCode = "%" + inputCode + "%";
            WeekendCriteria<SystemTbPubItems, Object> keywordCriteria1 = weekend.weekendCriteria();
            keywordCriteria1.andLike(SystemTbPubItems::getItemName, inputCode).orLike(SystemTbPubItems::getInputCode1, inputCode)
                    .orLike(SystemTbPubItems::getInputCode2, inputCode).orLike(SystemTbPubItems::getInputCode3, inputCode);
            weekend.and(keywordCriteria1);
        }
        return systemTbPubItemsMapper.selectByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public SystemTvPubItemsDto getChargeItemTv(Integer itemCode, Integer hospitalCode, Integer exeDept, Integer exeHospitalId, Integer stopped) {
        return systemTbPubItemsMapper.getChargeItemTv(itemCode, hospitalCode, exeDept, exeHospitalId, stopped);
    }

    /**
     * 字段空格处理，门诊特殊包装单位处理
     *
     * @param item
     * @return
     */
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public SystemTbPubItems chargeItemTrim(SystemTbPubItems item) {
        if (!StringUtils.isEmpty(item.getItemName())) {
            item.setItemName(item.getItemName().trim());
        }
        if (!StringUtils.isEmpty(item.getDosageUnit())) {
            item.setDosageUnit(item.getDosageUnit().trim());
        }
        if (!StringUtils.isEmpty(item.getWardUnit())) {
            item.setWardUnit(item.getWardUnit().trim());
        }



        //根据项目编码获取门诊特殊包装配置
        Map<Integer, SpecialItems>  specialItemsMap = TyMdc.get(TyKey.SPECIAL_ITEM_BY_ID_MAP);
        SpecialItems specialItem = null;
        if(specialItemsMap==null) {
            specialItem = preLoadCache.getOrLoadAndPut(PreLoad.SPECIAL_ITEM +"_"+item.getItemCode()+"_"+item.getHospitalId(),()->{
                return specialItemMapper.getSpecialItemById(item.getItemCode(), item.getHospitalId());
            });
        }else{
            specialItem = specialItemsMap.get(""+item.getHospitalId()+"_"+item.getItemCode()+"_");
        }
        if (specialItem != null) {
            item.setClinicUnit(specialItem.getClinicUnit());
            item.setClinicQty(specialItem.getClinicQuantity());
        }
        if (!StringUtils.isEmpty(item.getClinicUnit())) {
            item.setClinicUnit(item.getClinicUnit().trim());
        }
        final SystemTbPubItems chargeItem = new SystemTbPubItems();
        BeanUtils.copyProperties(item, chargeItem);
        return chargeItem;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<SpecialItems> querySpecialItemByIdList(List<Integer> itemCodeList, Integer hospitalId){
        List<SpecialItems> specialItemList = specialItemMapper.querySpecialItemByIdList(itemCodeList, hospitalId);
        return specialItemList;
    }

    /**
     * 字段空格处理，门诊特殊包装单位处理
     * <h1>1个单表sql</h1>
     * @param item
     * @return
     */
    @Override
    public SystemTbPubItems chargeItemBatchTrim(SystemTbPubItems item, Map<String, SpecialItems> specialItemMap) {
        if (!StringUtils.isEmpty(item.getItemName())) {
            item.setItemName(item.getItemName().trim());
        }
        if (!StringUtils.isEmpty(item.getDosageUnit())) {
            item.setDosageUnit(item.getDosageUnit().trim());
        }
        if (!StringUtils.isEmpty(item.getWardUnit())) {
            item.setWardUnit(item.getWardUnit().trim());
        }

        //根据项目编码获取门诊特殊包装配置
        SpecialItems specialItem = specialItemMap.get(item.getItemCode()+"_"+item.getHospitalId());
        if (specialItem != null) {
            item.setClinicUnit(specialItem.getClinicUnit());
            item.setClinicQty(specialItem.getClinicQuantity());
        }
        if (!StringUtils.isEmpty(item.getClinicUnit())) {
            item.setClinicUnit(item.getClinicUnit().trim());
        }
        final SystemTbPubItems chargeItem = new SystemTbPubItems();
        BeanUtils.copyProperties(item, chargeItem);
        return chargeItem;
    }
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public SystemTbPubItems getChargeItemByIdIsStopped(Integer itemCode, Integer deptId, Integer hospitalCode, Integer stopped) {
        int ydStopped = 0;  //药典停用状态 1停用 0启用
        int ypUsed = 1;  //本部门药品状态 0停用 1启用
        if(stopped == null){
            stopped = 0;
        }
        if(stopped == 1){
            ydStopped=1;
            ypUsed=0;
        }
        return systemTbPubItemsMapper.getChargeItemByIdIsStopped(itemCode, deptId, hospitalCode, stopped,ydStopped,ypUsed);
    }
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
     public SystemTbPubItems tyMdcGetChargeItemByIdIsStopped(Integer itemCode, Integer deptId, Integer hospitalCode, Integer stopped) {
         Map<Integer,SystemTbPubItems> systemTbPubItemMapByExecDeptAndItemCode =
                 TyMdc.get(TyKey.SYSTEM_TB_PUB_ITEM_MAP_BY_ITEM_CODE);
        if (systemTbPubItemMapByExecDeptAndItemCode == null) {
            return getChargeItemByIdIsStopped(itemCode, deptId, hospitalCode, stopped);
        }
        return systemTbPubItemMapByExecDeptAndItemCode.get(itemCode);
    }


    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<SystemTbPubItems> ListChargeItemByIdIsStopped(List<Integer> itemCodeList, List<Integer> exeDeptIdList, Integer hospitalCode, Integer stopped) {
        if(itemCodeList==null || itemCodeList.size()==0){
            return new ArrayList<>();
        }
        if(exeDeptIdList==null || exeDeptIdList.size()==0){
            return new ArrayList<>();
        }
        int ydStopped = 0;  //药典停用状态 1停用 0启用
        int ypUsed = 1;  //本部门药品状态 0停用 1启用
        if(stopped == null){
            stopped = 0;
        }
        if(stopped == 1){
            ydStopped=1;
            ypUsed=0;
        }
        return systemTbPubItemsMapper.listChargeItemByIdIsStopped(itemCodeList, exeDeptIdList, hospitalCode, stopped,ydStopped,ypUsed);
    }

    // XXX: yutao 2023/4/17 add 4 重构
    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public SystemTbPubItems getChargeItemByIdIsStopped2(Integer itemCode, Integer deptId, Integer hospitalCode, Integer stopped) {
        int ydStopped = 0;  //药典停用状态 1停用 0启用
        int ypUsed = 1;  //本部门药品状态 0停用 1启用
        if(stopped == null){
            stopped = 0;
        }
        if(stopped == 1){
            ydStopped=1;
            ypUsed=0;
        }
        return systemTbPubItemsMapper.getChargeItemById(itemCode, deptId, hospitalCode, stopped,ydStopped,ypUsed);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<Pair<String,SystemTbPubItems>> queryChargeItemByItemCodeList(List<PreRecipeDetail>detailList, Integer hospitalCode, Integer stopped) {
        int ydStopped = 0;  //药典停用状态 1停用 0启用
        int ypUsed = 1;  //本部门药品状态 0停用 1启用
        if(stopped == null){
            stopped = 0;
        }
        if(stopped == 1){
            ydStopped=1;
            ypUsed=0;
        }
        List<Integer> itemCodes = detailList.stream().map(PreRecipeDetail::getItemCode).collect(Collectors.toList());
        List<Integer> deptIds = detailList.stream().map(PreRecipeDetail::getExecDept).distinct().collect(Collectors.toList());

        List<SystemTbPubItems> chargeItemList = systemTbPubItemsMapper.queryChargeItemByItemCodeList(itemCodes, hospitalCode, stopped);
        Map<String, SystemTbPubItems> chargeItemMap = TyMapUtil.listToMap(chargeItemList, "getItemCode");

        List<DrugInfomation> drugInfomationList = systemTbPubItemsMapper.queryDrugInfoByItemCodeList(itemCodes, hospitalCode, ydStopped);
        Map<String, DrugInfomation> drugInfomationMap = TyMapUtil.listToMap(drugInfomationList, "getDrugId");
        List<LocalDeptDrug> localDeptDrugList = systemTbPubItemsMapper.queryLocalDeptDrugByItemCodeList(itemCodes, deptIds, hospitalCode,ypUsed);
        Map<String, LocalDeptDrug> localDeptDrugmapx = TyMapUtil.listToMap(localDeptDrugList, "getDrugId");

        Map<String, LocalDeptDrug> localDeptDrugmap = new HashMap<>();
        for (LocalDeptDrug ld: localDeptDrugList) {
            localDeptDrugmap.put(ld.getDrugId() + "_" + ld.getDeptId(),ld);
        }

        Map<String, PreRecipeDetail> detailMap = new HashMap<>();
        for (PreRecipeDetail prd : detailList) {
            String key = prd.getItemCode()+"_"+prd.getExecDept();
            detailMap.put(key, prd);
        }
        List<Pair<String,SystemTbPubItems>> retList = new ArrayList<>();
        for(PreRecipeDetail pd:detailList){
            Integer itemCode = pd.getItemCode();
            Integer deptId = pd.getExecDept();
            if(chargeItemMap.get(""+itemCode)==null||drugInfomationMap.get(""+itemCode)==null){
                continue;
            }
            if(localDeptDrugmap.get(itemCode+"_"+deptId)==null){
                continue;
            }
            retList.add(new Pair(itemCode+"_"+deptId,chargeItemMap.get(""+itemCode)));
        }


        //过滤，只保留在drugInfomationMap，localDeptDrugmap中有的

        return retList;
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public List<SystemTbPubItems> queryChargeItemByIdList(List<Integer> itemCodeList, Integer hospitalCode) {
        if (CollUtil.isEmpty(itemCodeList)) {
            return new ArrayList<>();
        }
        Weekend<SystemTbPubItems> weekend = new Weekend<>(SystemTbPubItems.class);
        weekend.weekendCriteria().andIn(SystemTbPubItems::getItemCode,itemCodeList)
                .andEqualTo(SystemTbPubItems::getHospitalId,hospitalCode);
        return systemTbPubItemsMapper.selectByExample(weekend);
    }
}
