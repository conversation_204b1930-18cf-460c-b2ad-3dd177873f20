package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.common.enums.HospitalClassify;
import com.rjsoft.outPatient.infrastructure.repository.entity.RisRequestDtl;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RisRequestDtlMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.RisRequestDtlRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/10/13-2:13 下午
 */
@Service
@AllArgsConstructor
public class RisRequestDtlRepositoryImpl implements RisRequestDtlRepository {

    private final RisRequestDtlMapper risRequestDtlMapper;

    @Override
    public RisRequestDtl getRisRequestDtlByRecipeDetailId(Integer recipeDetailId, Integer hospitalCode) {
        // 切换数据源
        DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS);
        if (Objects.equals(hospitalCode, HospitalClassify.BRANCH.getHospitalCode())) {
            DataSourceSwitchAspect.changeDataSource(DatasourceName.ZXHIS3);
        }
        final RisRequestDtl record = new RisRequestDtl();
        record.setRecipeDetailId(recipeDetailId);
        return risRequestDtlMapper.selectOne(record);
    }
}
