package com.rjsoft.outPatient.infrastructure.repository.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 草药处方患者联系地址
 * @TableName Herb_Patient_Address
 */
@Data
@Table(name = "Herb_Patient_Address")
public class HerbPatientAddress implements Serializable {
    /**
     * id
     */
    @Id
    @Column(name = "id", insertable = false, updatable = false)
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 患者id
     */
    @Column(name = "patid")
    private Integer patId;

    /**
     * 联系人姓名
     */
    @Column(name = "contactname")
    private String contactName;

    /**
     * 联系人电话
     */
    @Column(name = "contactphone")
    private String contactPhone;

    /**
     * 联系人地址
     */
    @Column(name = "contactaddress")
    private String contactAddress;

    /**
     * 是否删除，1：是，0：否
     */
    @Column(name = "isDel")
    private Integer isDel;

    /**
     * 医院编码
     */
    @Column(name = "hospitalid")
    private Integer hospitalId;

    @Column(name = "updatetime")
    private Date updateTime;

    @Column(name = "updateuserid")
    private Integer updateUserId;

    @Column(name = "createtime")
    private Date createTime;

    @Column(name = "createuserid")
    private Integer createUserId;

}