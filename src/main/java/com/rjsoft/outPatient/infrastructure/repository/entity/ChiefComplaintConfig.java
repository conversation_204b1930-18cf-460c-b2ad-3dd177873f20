package com.rjsoft.outPatient.infrastructure.repository.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 主诉配置信息
 */
@Data
@Table(name = "MZYS_TB_MZZSPZ")
public class ChiefComplaintConfig  implements Serializable {

    @Id
    @Column(name = "Id", insertable = false, updatable = false)
    private Integer id;

    @Column(name = "type")
    private Integer type;

    @Column(name = "name")
    private String name;

    @Column(name = "status")
    private Integer status;

    @Column(name = "hospitalCode")
    private Integer hospitalCode;

}
