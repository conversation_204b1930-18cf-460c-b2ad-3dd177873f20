<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rjsoft.outPatient.infrastructure.repository.mapper.SciRegisterlistProjectMapper">

    <update id="updateSciRegisterlistProject" >
        update Sci_RegisterList_Project
            set followup = #{followup} , followupTitle = #{followupTitle}
        WHERE RegNo = #{regNo}
            and ProjectId = #{projectId}
            and HospitalCode = #{hospitalCode}
    </update>

</mapper>