package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.convert.Converter;
import com.rjsoft.common.db.DataSourceSwitchAspect;
import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.common.dictionary.AutoTranslate;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.EnterHospitalRequisition;
import com.rjsoft.outPatient.infrastructure.repository.entity.MzysTbMzCfMx;
import com.rjsoft.outPatient.infrastructure.repository.entity.OldRecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.entity.RecipeDetail;
import com.rjsoft.outPatient.infrastructure.repository.mapper.AdmissionApplicationFormDaoMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.OldRecipeDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.mapper.RecipeDetailMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.OldRecipeDetailRepository;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/4 - 9:26
 */
@Service
@AllArgsConstructor
public class OldRecipeDetailRepositoryImpl implements OldRecipeDetailRepository {

    private final OldRecipeDetailMapper oldRecipeDetailMapper;
    private final RecipeDetailMapper recipeDetailMapper;
    private final AdmissionApplicationFormDaoMapper admissionApplicationFormDaoMapper;

    @Override
    @DatabaseAnnotation
    //@Cacheable(cacheNames = "getDrugRecipeByRegNo1", unless = "#result == null", key = "'SimpleKey ['+#regNo+','+#dataSource+']'")
    public List<OldRecipeDetail> getDrugRecipeByRegNo(String regNo, String dataSource, Date firstDate) {
        DataSourceSwitchAspect.changeDataSource(dataSource);
        List<OldRecipeDetail> oldRecipeDetailList = oldRecipeDetailMapper.getDrugRecipeByRegNo(regNo, firstDate, "");
        // TODO: 2022/5/28 分院上线时需要打开改方法
        if (!DatasourceName.MZYS3.equals(dataSource)) {
            List<OldRecipeDetail> oldRecipeDetailListMove = oldRecipeDetailMapper.getDrugRecipeByRegNo(regNo, firstDate, "MZYS_TB_MZCFMX_DATA");
            oldRecipeDetailList.addAll(oldRecipeDetailListMove);
        }
        return oldRecipeDetailList;
    }

    /**
     * 根据就诊流水号查询处方明细信息
     *
     * @param receptionNo
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    @AutoTranslate
    public MzysTbMzCfMx getTopMzCfMx(Long receptionNo, Integer hospitalCode) {
        return admissionApplicationFormDaoMapper.getTopMzCfMx(receptionNo, hospitalCode);
    }

    /**
     * 查询有效期内的处方明细
     *
     * @param jzlsh
     * @param months
     * @param hospitalCode
     * @return
     */
    @Override
    @DatabaseAnnotation
    public MzysTbMzCfMx getTimeMzCfMx(Long jzlsh, Integer months, Integer hospitalCode) {
        return admissionApplicationFormDaoMapper.listMzCfMx(jzlsh, months, hospitalCode);
    }

    @Override
    @DatabaseAnnotation
    @AutoTranslate
    public MzysTbMzCfMx getRecipeByRecipeDetailId(Long cfmxlsh, Integer hospitalCode) {
        return admissionApplicationFormDaoMapper.getRecipeByRecipeDetailId(cfmxlsh, hospitalCode);
    }


    @Override
    @DatabaseAnnotation
    public int listMzysTbMzcfmx(String jzlsh, List<String> tcmc, Integer hospitalCode) {
        if (tcmc == null || tcmc.size() == 0) {
            return 0;
        }
        Weekend weekend = new Weekend(RecipeDetail.class);
        WeekendCriteria<RecipeDetail, Object> weekendCriteria = weekend.weekendCriteria();
        weekendCriteria.andEqualTo(RecipeDetail::getReceptionNo, Converter.toInt64(jzlsh));
        weekendCriteria.andIn(RecipeDetail::getApplicationForm, tcmc);
        weekendCriteria.andEqualTo(RecipeDetail::getHospitalCode, hospitalCode);
        return recipeDetailMapper.selectCountByExample(weekend);
    }

    @Override
    @DatabaseAnnotation
    public void changeIssueInspectStatus(EnterHospitalRequisition requisition) {
        admissionApplicationFormDaoMapper.changeIssueInspectStatus(requisition.getReceptionNo(), requisition.getHospitalCode(), requisition.getIssueInspectStatus());
    }
}
