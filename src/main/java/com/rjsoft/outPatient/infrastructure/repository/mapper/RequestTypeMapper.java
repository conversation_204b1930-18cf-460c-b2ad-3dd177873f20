package com.rjsoft.outPatient.infrastructure.repository.mapper;

import com.rjsoft.outPatient.infrastructure.repository.entity.RequestType;
import tk.mybatis.mapper.common.BaseMapper;

import java.util.List;

/**
 * 申请类型
 * <AUTHOR>
public interface RequestTypeMapper extends BaseMapper<RequestType> {

    /**
     * 根据类型ID获取字典
     * @param requestType
     * @return
     */
    default  RequestType getRequestTypeById(Integer requestType){
        RequestType entity=new RequestType();
        entity.setRequestType(requestType);
        List<RequestType>list=select(entity);
        if(list.stream().count()==0){
            return null;
        }
        return list.get(0);
    }
}
