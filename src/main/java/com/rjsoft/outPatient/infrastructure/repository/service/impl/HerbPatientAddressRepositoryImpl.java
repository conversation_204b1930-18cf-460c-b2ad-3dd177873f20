package com.rjsoft.outPatient.infrastructure.repository.service.impl;

import com.rjsoft.common.db.DatabaseAnnotation;
import com.rjsoft.outPatient.common.consts.DatasourceName;
import com.rjsoft.outPatient.infrastructure.repository.entity.HerbPatientAddress;
import com.rjsoft.outPatient.infrastructure.repository.mapper.HerbPatientAddressMapper;
import com.rjsoft.outPatient.infrastructure.repository.service.HerbPatientAddressRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

@Service
@AllArgsConstructor
public class HerbPatientAddressRepositoryImpl implements HerbPatientAddressRepository {
    private HerbPatientAddressMapper herbPatientAddressMapper;

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public HerbPatientAddress getContactAddress(Integer patId, Integer hospitalCode) {
        Weekend<HerbPatientAddress> weekend = new Weekend<>(HerbPatientAddress.class);
        weekend.weekendCriteria().andEqualTo(HerbPatientAddress::getPatId,patId).andEqualTo(HerbPatientAddress::getHospitalId,hospitalCode).andEqualTo(HerbPatientAddress::getIsDel,0);
        return herbPatientAddressMapper.selectOneByExample(weekend);
    }

    @Override
    @DatabaseAnnotation(name = DatasourceName.HISDB)
    public boolean addContactAddress(HerbPatientAddress herbPatientAddress) {
        Weekend<HerbPatientAddress> weekend = new Weekend<>(HerbPatientAddress.class);
        weekend.weekendCriteria().andEqualTo(HerbPatientAddress::getPatId,herbPatientAddress.getPatId())
                .andEqualTo(HerbPatientAddress::getHospitalId,herbPatientAddress.getHospitalId())
                .andEqualTo(HerbPatientAddress::getIsDel,0);
        List<HerbPatientAddress> herbPatientAddressList = herbPatientAddressMapper.selectByExample(weekend);
        for(HerbPatientAddress herbPatientAddress1:herbPatientAddressList){
            herbPatientAddress1.setIsDel(1);
            herbPatientAddressMapper.updateByPrimaryKeySelective(herbPatientAddress1);
        }
        return  herbPatientAddressMapper.insertSelective(herbPatientAddress)>0;
    }
}
