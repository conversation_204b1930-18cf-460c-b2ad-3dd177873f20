server:
  port: 18846
  servlet.context-path: /outPatient
  use-auto-translate: true
  debug-log: true
  exception-path: com.rjsoft
his:
  insuranceApi: http://localhost:18842
  insuranceFyApi: http://localhost:18842
  appointmentApi: http://**********:8888
  wxAppointmentApi: http://**********:8888
  gzdServiceAdd: http://**********:8025/GzdService.asmx/Add
  gzdServiceAddJgy: http://**********:8025/GzdService.asmx/AddJGY
  gzdServiceDelete: http://**********:8025/GzdService.asmx/Delete
  gzdServiceDeleteJgy: http://**********:8025/GzdService.asmx/DeleteJGY
  linkingPay:
    apiUrl: http://************:8082/LopSdkPay/Pay/LopSDK
    appId: LCOP20180410224309266

mzys:
  recipe:
    drug:
      ## 处方最大天数限制
      maxDays: 999
      maxDose: 999
    nonDrug:
      maxQuantity: 100
  averageMonthlyFee:
    dateBenchmark: 22

mybatis:
  configuration:
    map-underscore-to-camel-case: false
    cache-enabled: true
    lazyLoadingEnabled: true
#    aggressive-lazy-loading: false
    multipleResultSetsEnabled: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    call-setters-on-nulls: true



spring:
  jackson:
    #date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  datasource:
    primary:
      url: jdbc:sqlserver://**********;DatabaseName=Mzys_New
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    HISDB:
      url: jdbc:sqlserver://**********;DatabaseName=HISDB
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    Apply:
      url: jdbc:sqlserver://**********;DatabaseName=APPLY
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    # 总院数据库配置
    RJCONFIGER:
      url: jdbc:sqlserver://**********;DatabaseName=RJ_Configer
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    MZEMR:
      url: jdbc:sqlserver://**********;DatabaseName=MzEmr
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    MZYS:
      url: jdbc:sqlserver://**********;DatabaseName=Mzys
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    ZXHIS:
      url: jdbc:sqlserver://**********;DatabaseName=ZXHIS
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    ZYEMR:
      url: jdbc:sqlserver://**********;DatabaseName=Zyemr
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    RIS:
      url: jdbc:sqlserver://**********;DatabaseName=Ris
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver

    # 分院数据库配置
    MZEMR3:
      url: **********************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    MZYS3:
      url: *********************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    ZXHIS3:
      url: **********************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    ZYEMR3:
      url: **********************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    RIS3:
      url: ********************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver

    # 报告中心
    REPORTS:
      url: ************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      # 科研数据库配置
    RDR_ResHisExchange:
      url: ***********************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      #CA
      CA:
        url: jdbc:sqlserver://**********;DatabaseName=CALocalService
        username: rjhis
        password: rjhis
        driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver

      # 医技预约数据库配置
#      YLSYS_NEW:
#        url: ***************************************************
#          username: his
#          password: lqaz2wsx
#          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
  #      REPORTS:
  #        url: **************************************************
  #        username: rjhis
  #        password: rjhis
  #        driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
  #    LISDATABASE:
  #      url: ****************************************************
  #      username: rjhis
  #      password: rjhis
  #      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver

  cache:
    type: redis
    cache-names: SysConfig
    redis:
      time-to-live: 300s
      use-key-prefix: false
  redis:
    host: **********
    port: 6379
    password: 12345
    timeout: 1000ms
    database: 1

logging:
  level.root: info
  level.com.rjsoft.outPatient.infrastructure.repository: debug
  config: classpath:logback-spring-dev.xml
wsdl:
  gzdService:
    url: http://**********:8025/GzdService.asmx?wsdl
