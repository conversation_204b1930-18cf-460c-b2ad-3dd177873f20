INSERT INTO HISDB.dbo.System_Tb_Config([KeyId], [SystemNo], [KeyCode], [KeyValue], [KeyType], [KeySource],
                                       [Description], [SID], [CreateOn], [CreateUserId], [UpdateUserId], [UpdateOn],
                                       [HospitalId])
VALUES (1000343, N'107', N'CurrentADLYPZL', N'3', NULL, NULL, N'每次就诊安定类开药数量限制', NULL, NULL, NULL, NULL, NULL,
        1);
INSERT INTO HISDB.dbo.System_Tb_Config([KeyId], [SystemNo], [KeyCode], [KeyValue], [KeyType], [KeySource],
                                       [Description], [SID], [CreateOn], [CreateUserId], [UpdateUserId], [UpdateOn],
                                       [HospitalId])
VALUES (1000344, N'107', N'CurrentJELYPZL', N'2', NULL, NULL, N'每次就诊精二类开药数量限制', NULL, NULL, NULL, NULL, NULL, 1);

INSERT INTO HISDB.dbo.Drug_Tb_LocalDeptDrug (DeptId, DrugId, StoreNo, IsCheck, IsUsed, CreateUserId, CreateUserName,
                                             CreateOn, UpdateUserId, UpdateUserName, UpdateOn, HospitalId,
                                             IsExpensiveDrag, IsBatch)
VALUES (1064, 73074, null, 1, DEFAULT, -1, N'系统导入', N'2021-09-29 16:53:47.000', null, null, null, 1, null, 1);

INSERT INTO HISDB.dbo.Drug_Tb_LocalDeptDrug (DeptId, DrugId, StoreNo, IsCheck, IsUsed, CreateUserId, CreateUserName,
                                             CreateOn, UpdateUserId, UpdateUserName, UpdateOn, HospitalId,
                                             IsExpensiveDrag, IsBatch)
VALUES (1064, 73047, null, 1, DEFAULT, -1, N'系统导入', N'2021-09-29 16:53:47.000', null, null, null, 1, null, 1);

UPDATE HISDB.dbo.System_Tb_PubItems
SET ExeDept = 1073
WHERE ItemCode = 73074
  AND HospitalId = 1;
UPDATE HISDB.dbo.System_Tb_PubItems
SET ExeDept = 1073
WHERE ItemCode = 73047
  AND HospitalId = 1;

delete
from HISDB.dbo.System_Tb_Config
where HospitalId is null;
alter table HISDB.dbo.System_Tb_Config
    alter column SystemNo varchar(50) not null;
alter table HISDB.dbo.System_Tb_Config
    alter column KeyCode varchar(50) not null;
alter table HISDB.dbo.System_Tb_Config
    alter column HospitalId int not null;