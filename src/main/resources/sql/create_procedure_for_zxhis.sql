CREATE procedure [dbo].[UspSelNewMZYSDoctPreInfoForDoctorWorkerStation_Interface] @StartDate datetime = null, @doctorId int = null, @deptId int = null
as
--set @StartDate=dateadd(day,-1,@StartDate)
declare
@sourcegrade int, @linkid int
select @sourcegrade = SourceGrade
from Rj_configer.dbo.TB_Dic_SourceType
where EnName = 'zhenjian'
select @linkid = a.linkID
from Rj_configer.dbo.TB_Cinfiger_linkSource a
         inner join Rj_configer.dbo.TB_Dic_SourceType b on a.SourceCode = b.Code
where b.EnName = 'zhenjian' if CONVERT(varchar(10), @StartDate, 121) = CONVERT(varchar(10), GETDATE(), 121)
begin
select distinct a.id                      as                            HID,
                c.DeptCode                as                            Dept_Code,
                rtrim(b.DEPTNAME)         as                            Dept_Name,
                c.DctCode                 as                            Doctor_Code,
                rtrim(d.Name)             as                            Doctor_Name,
                a.DutyDate                as                            Hb_Date,
                ''                        as                            Hb_Time,
                ''                        as                            Am_Pm,
                isnull((select CONVERT(decimal (12, 2), sum(TotalFee))
                        from RJ_Configer.dbo.TB_Cinfiger_SubjectFeeItem g
                        where g.SubjectID = c.SubjectID),
                       c.ghfee)                                         Sum_Fee,
                --a.PtCzSys as Appoint_Count,
                --a.czghzs-isnull(a.ptczyys,0) Appoint_Count,
                0                                                       Appoint_Count,
                --a.PtFzSys as Appoint_Count_Re,
                --a.fzghzs-isnull(a.ptfzyys,0) Appoint_Count_Re,
                0                                                       Appoint_Count_Re,
                0                         as                            Reg_Count,
                case isnull(b.PARENTCODE, -1)
                    when '' then -1
                    when -1 then -1
                    else b.PARENTCODE end as                            Sec_Dept_Code,
                isnull(b.DeptType, 0)     as                            Dept_Type,
                (case isnull(b.PARENTCODE, 0) when 0 then 0 else 1 end) Zbmz
into #tmp
from RJ_Configer.dbo.TB_Cinfiger_Scheduling a (nolock)
         left join RJ_Configer.dbo.TB_Cinfiger_SubjectItem c (nolock) on a.Subjectid = c.SubjectID
         left join RJ_Configer.dbo.TB_Dic_DeptExt b (nolock) on c.DeptCode = b.DEPTCODE
         left join RJ_Configer.dbo.TB_Dic_Staff d (nolock) on c.DctCode = d.Code
         left join RJ_Configer.dbo.[TB_Dic_HisDictionaryExt] e (nolock)
on c.TimeType = e.[HisDictionaryID]
    --left join RJ_Configer.dbo.TB_Dic_TimeSpanExt f(nolock) on e.TimeGroupCode =f.[Group]
    --left join RJ_Configer.dbo.TB_Cinfiger_SchedulingSourceDetail g (nolock)  on g.mainID=a.ID and SourceID=(select ID from RJ_Configer.dbo.TB_Dic_SourceType (nolock) where Name='诊间')
where b.DeptCode = @deptId
  and (d.Code = @doctorId
   or isnull(@doctorId
    , '') = '')
  and a.status = 0
  and a.IsDelete = 0
  and a.IsUse = 1
  and ISNULL(a.StopNumber
    , 0) = 0
  and (e.HisDictionaryName = '上午'
   or e.HisDictionaryName = '下午')
order by c.DeptCode

update #tmp
set Dept_Name = b.DeptName from #tmp a,
                 RJ_Configer.dbo.TB_Dic_DeptExt b
where a.Dept_Code = b.DEPTCODE

update #tmp
set Dept_Name     = b.DeptName,
    Sec_Dept_Code = a.Dept_Code,
    Dept_Code     = b.DEPTCODE from #tmp a,
                 RJ_Configer.dbo.TB_Dic_DeptExt b
where a.Sec_Dept_Code = b.DEPTCODE
  and isnull(a.Sec_Dept_Code
    , -1) <> -1

select *
from #tmp

end
else
begin
create table #tmp1
(
    id               int identity (1,1),
    HID              int,
    SID              int,
    Dept_Code        varchar(50),
    Dept_Name        varchar(50),
    Doctor_Code      varchar(50),
    Doctor_Name      varchar(50),
    Hb_Date          datetime,
    Hb_Time          varchar(50),
    Am_Pm            varchar(50),
    Sum_Fee          float,
    Appoint_Count    int,
    Appoint_Count_Re int,
    Reg_Count        int,
    Sec_Dept_Code    varchar(50),
    Dept_Type        int,
    Zbmz             varchar(50),
    TotalCzNum       int,
    TotalCzLyNum     int,
    TotalFzNum       int,
    TotalFzLyNum     int,
    AppointCzNum     int,
    AppointFznum     int
)

create index ix_deptcode on #tmp1 (Dept_Code)
create index ix_doctor on #tmp1 (Doctor_Code)
create index ix_hbtime on #tmp1 (Hb_Time)
create index ix_hbdate on #tmp1 (Hb_Date) insert into #tmp1
select distinct a.id                      as                            HID,
                c.subjectid               as                            SID,
                c.DeptCode                as                            Dept_Code,
                rtrim(b.DEPTNAME)         as                            Dept_Name,
                c.DctCode                 as                            Doctor_Code,
                rtrim(d.Name)             as                            Doctor_Name,
                a.DutyDate                as                            Hb_Date,
                e.HisDictionaryName       as                            Hb_Time,
                e.HisDictionaryName       as                            Am_Pm,
                isnull((select CONVERT(decimal (12, 2), sum(TotalFee))
                        from RJ_Configer.dbo.TB_Cinfiger_SubjectFeeItem g
                        where g.SubjectID = c.SubjectID),
                       c.ghfee)                                         Sum_Fee,
                0                                                       Appoint_Count,
                0                                                       Appoint_Count_Re,
                0                         as                            Reg_Count,
                case isnull(b.PARENTCODE, -1)
                    when '' then -1
                    when -1 then -1
                    else b.PARENTCODE end as                            Sec_Dept_Code,
                isnull(b.DeptType, 0)     as                            Dept_Type,
                (case isnull(b.PARENTCODE, 0) when 0 then 0 else 1 end) Zbmz,
                0,
                0,
                0,
                0,
                0,
                0
from RJ_Configer.dbo.TB_Cinfiger_Scheduling a (nolock)
         left join RJ_Configer.dbo.TB_Cinfiger_SubjectItem c (nolock) on a.Subjectid = c.SubjectID
         left join RJ_Configer.dbo.TB_Dic_DeptExt b (nolock) on c.DeptCode = b.DEPTCODE
         left join RJ_Configer.dbo.TB_Dic_Staff d (nolock) on c.DctCode = d.Code
         left join RJ_Configer.dbo.[TB_Dic_HisDictionaryExt] e (nolock)
on c.TimeType = e.[HisDictionaryID]
    and e.lang = 1 and e.isdelete = 0 and e.isuse = 1
where c.DeptCode = @deptId
  and (c.DctCode = @doctorId
   or isnull(@doctorId
    , '') = '')
  and a.DutyDate >= @StartDate
  and a.DutyDate <= dateadd(month
    , 3
    , @StartDate)
  and a.IsUse = 1
  and a.status = 0
  and a.IsDelete = 0
  and a.IsUse = 1
  and ISNULL(a.StopNumber
    , 0) = 0
  and (e.HisDictionaryName = '上午'
   or e.HisDictionaryName = '下午')
order by c.DeptCode

----total hy
update #tmp1
set TotalCzNum=isnull((select SUM(b.quatity)
                       from RJ_Configer.dbo.TB_Cinfiger_SchedulingTimespanDetail b
                       where b.mainid = HID), 0),
    TotalFzNum=ISNULL((select SUM(b.maxNum)
                       from RJ_Configer.dbo.TB_Cinfiger_SchedulingTimespanDetail b
                       where b.mainid = HID), 0)

----total ly
update #tmp1
set TotalCzLyNum=ISNULL((select SUM(b.quatity)
                         from Rj_configer.dbo.TB_Cinfiger_SchedulingSourceDetail b
                         where b.mainID = HID
                           and b.SourceID in (select st.Code
                                              from Rj_configer.dbo.TB_Dic_SourceType st
                                                       inner join Rj_configer.dbo.TB_Cinfiger_linkSource ls on st.Code = ls.SourceCode
                                              where st.SourceGrade <= @sourcegrade
                                                and ls.linkID = @linkid)
                        ), 0),
    TotalFzLyNum=ISNULL((select SUM(b.maxNum)
                         from Rj_configer.dbo.TB_Cinfiger_SchedulingSourceDetail b
                         where b.mainID = HID
                           and b.SourceID in (select st.Code
                                              from Rj_configer.dbo.TB_Dic_SourceType st
                                                       inner join Rj_configer.dbo.TB_Cinfiger_linkSource ls on st.Code = ls.SourceCode
                                              where st.SourceGrade <= @sourcegrade
                                                and ls.linkID = @linkid)
                        ), 0)

update #tmp1
set TotalCzNum=TotalCzLyNum
where TotalCzLyNum < TotalCzNum
update #tmp1
set TotalFzNum=TotalFzLyNum
where TotalFzLyNum < TotalFzNum


----use hy
update #tmp1
set AppointCzNum=ISNULL((select count(1)
                         from RJ_Configer.dbo.TB_Appointment
                         where SubjectID = SID
                           and CheckDate = Hb_Date
                           and AppointmentStatus not in (2, 3)
                           and AppointmentType = 1), 0),
    AppointFznum=ISNULL((select count(1)
                         from RJ_Configer.dbo.TB_Appointment
                         where SubjectID = SID
                           and CheckDate = Hb_Date
                           and AppointmentStatus not in (2, 3)
                           and AppointmentType = 2), 0)

update #tmp1
set Dept_Name = b.DeptName from #tmp1 a,
                 RJ_Configer.dbo.TB_Dic_DeptExt b
where a.Dept_Code = b.DEPTCODE


--    update #tmp1
-- set Dept_Name = b.DeptName, Sec_Dept_Code = a.Dept_Code, Dept_Code = b.DEPTCODE
--from #tmp1 a
--inner join RJ_Configer.dbo.TB_Dic_DeptExt b  on a.Sec_Dept_Code = b.DEPTCODE
--  where isnull(a.Sec_Dept_Code,-1) <> -1

update #tmp1
set Appoint_Count=TotalCzNum - AppointCzNum,
    Appoint_Count_Re=TotalFzNum - AppointFznum


update #tmp1
set Appoint_Count_Re=0,
    Appoint_Count=Appoint_Count_Re + Appoint_Count
where Appoint_Count_Re < 0

select *
from #tmp1
order by Hb_Date, Dept_Code

drop table #tmp1

end
go

CREATE
proc [dbo].[usp_Zxhis_GetDoctorFeesRatio]
(
 @DoctorId int='',
 @DateTime datetime=null,
 @TypeId int='' --0：当天，1：当月
 )
as
begin

   declare
@TotalAmount decimal(10, 2), @TotalRatioAmount decimal(10, 2)

select a.RecipeItem                                       ItemCode,
       a.ItemName,
       case when c.Bzbm in ('SJ', 'GJ') then 1 else 0 end Jybm,
       a.TotAmt * 0.0001                                  Moneys,
       d.WorkerNo,
       d.Name                                             WorkerName,
       b.RecipeDate
into #dt1
from zxhis..Tbt_SfRecipeDetl a
         inner join zxhis..Tbt_SfRecipeList b on a.RecipeNo = b.RecipeNo
         inner join zxhis..Tbt_DrugBasicInfo c on a.RecipeItem = c.Code
         inner join zxhis..Tbt_PubWorker d on b.DoctorId = d.WorkerId
where b.DoctorId = @DoctorId
  and ((@TypeId = 0 and b.RecipeDate > convert(char (10), @DateTime, 120)) or
       (@TypeId = 1 and convert(varchar (6), b.RecipeDate, 112) = convert(varchar (6), @DateTime, 112)))

select @TotalAmount = Sum(Moneys)
from #dt1
select @TotalRatioAmount = Sum(Moneys)
from #dt1
where Jybm = 1

--基药金额/总金额
select @TotalRatioAmount / @TotalAmount DrugAmountRatio

drop table #dt1
end
go


CREATE
proc [dbo].[usp_Zxhis_GetDoctorFeesRatio]
(
 @DoctorId int='',
 @DateTime datetime=null,
 @TypeId int='' --0：当天，1：当月
 )
as
begin

   declare
@TotalAmount decimal(10, 2), @TotalRatioAmount decimal(10, 2)

select a.RecipeItem                                       ItemCode,
       a.ItemName,
       case when c.Bzbm in ('SJ', 'GJ') then 1 else 0 end Jybm,
       a.TotAmt * 0.0001                                  Moneys,
       d.WorkerNo,
       d.Name                                             WorkerName,
       b.RecipeDate
into #dt1
from zxhis..Tbt_SfRecipeDetl a
         inner join zxhis..Tbt_SfRecipeList b on a.RecipeNo = b.RecipeNo
         inner join zxhis..Tbt_DrugBasicInfo c on a.RecipeItem = c.Code
         inner join zxhis..Tbt_PubWorker d on b.DoctorId = d.WorkerId
where b.DoctorId = @DoctorId
  and ((@TypeId = 0 and b.RecipeDate > convert(char (10), @DateTime, 120)) or
       (@TypeId = 1 and convert(varchar (6), b.RecipeDate, 112) = convert(varchar (6), @DateTime, 112)))

select @TotalAmount = Sum(Moneys)
from #dt1
select @TotalRatioAmount = Sum(Moneys)
from #dt1
where Jybm = 1

--基药金额/总金额
select @TotalRatioAmount / @TotalAmount DrugAmountRatio

drop table #dt1
end
go