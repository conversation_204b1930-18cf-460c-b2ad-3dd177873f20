server:
  port: 18848
  servlet.context-path: /outPatient
  use-auto-translate: true
  debug-log: false
  exception-path: com.rjsoft

his:
  insuranceApi: http://localhost:18842
  insuranceFyApi: http://localhost:18842
  appointmentApi: http://**********:8888
  appointmentBranchApi: http://**********:8888
  wxAppointmentApi: http://**********:8888
  wxAppointmentBranchApi: http://**********:8888
  gzdServiceAdd: http://**********:8025/GzdService.asmx/Add
  gzdServiceAddJgy: http://**********:8025/GzdService.asmx/AddJGY
  gzdServiceDelete: http://**********:8025/GzdService.asmx/Delete
  gzdServiceDeleteJgy: http://**********:8025/GzdService.asmx/DeleteJGY
  gzdServiceAddFY: http://**********:8025/GzdService.asmx/Add
  gzdServiceAddJgyFY: http://**********:8025/GzdService.asmx/AddJGY
  gzdServiceDeleteFY: http://**********:8025/GzdService.asmx/Delete
  gzdServiceDeleteJgyFY: http://**********:8025/GzdService.asmx/DeleteJGY
  zkGetInspectInfo: http://***********:990/DataService.asmx/GetInspectInfo
  zkGetDiseInfo: http://***********:990/DataService.asmx/GetDiseInfo
  zkGetInspectionAnalysisResults: http://***********:990/DataService.asmx/GetInspectionAnalysisResults
  zkGetMultiReferenceValue: http://***********:990/DataService.asmx/GetMultiReferenceValue
  zkPostDateAnalysis: http://***********:990/DataService.asmx/PostDateAnalysis
  zkPostDiseaseAnalysis: http://***********:990/DataService.asmx/PostDiseaseAnalysis
  zkPostAppExit: http://***********:990/DataService.asmx/PostAppExit
  msgCenterApi: http://**********:8006
  outPatientUrls: **********:18846;**********:18846
  inPatientApi: http://**********:8090/io/diseaseProof/getInPatientInfo
  IsHistoryVersion: false
  IsSeriousIllness: true
  OldMZYSIP: **********
  drugService: http://**********:18888/drug         #药品服务地址前缀
  workflowService: http://localhost:8080/engine-rest/process-definition/key


  linkingPay:
    apiUrl: http://************:8082/LopSdkPay/Pay/LopSDK
    appId: LCOP20180410224309266

apply:
  enableJudgeItemExists: true
  url: http://**********:18888

#ignite缓存地址
#ignite:
#  base-url: http://*************:8088
#  query-url: /query

mzys:
  recipe:
    drug:
      ## 处方最大天数限制
      maxDays: 999
      maxDose: 999
    nonDrug:
      maxQuantity: 100
  averageMonthlyFee:
    dateBenchmark: 22

mybatis:
  configuration:
    map-underscore-to-camel-case: false
    cache-enabled: false
    lazyLoadingEnabled: true
    multipleResultSetsEnabled: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
hikari:
  connectionTimeout: 20000
  maximumPoolSize: 30
  # minimumIdle: 10 先关了它，有问题再打开
  idleTimeout: 30000
  maxLifetime: 1800000
  poolName: YourAppDBConnectionPool
spring:
  rabbitmq:
    host: **********
    port: 5672
    username: MsgCenter
    password: MsgCenter
    virtual-host: /
  jackson:
    #date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  datasource:
    primary:
      url: ****************************************************
      username: rjhis
      password: Rjsoft222@SunHealth
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    HISDB:
      url: ****************************************************
      username: rjhis
      password: Rjsoft222@SunHealth
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000

    # 总院数据库配置
    RJCONFIGER:
      url: **********************************************************
      username: rjhis
      password: Rjsoft106@SunHealth
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    # 总院数据库配置
    MZEMR:
      url: ****************************************************
      username: rjhis
      password: Rjsoft106@SunHealth
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    MZYS:
      url: ***************************************************
      username: rjhis
      password: Rjsoft106@SunHealth
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    ZXHIS:
      url: ****************************************************
      username: rjhis
      password: Rjsoft106@SunHealth
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    ZYEMR:
      url: ****************************************************
      username: rjhis
      password: Rjsoft106@SunHealth
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    RIS:
      url: **************************************************
      username: rjhis
      password: Rjsoft106@SunHealth
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    # 报告中心
    REPORTS:
      url: ******************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000

    # 分院数据库配置
    RJCONFIGER3:
      url: **********************************************************
      username: rjhis
      password: Rjsoft106@SunHealth
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    MZYS3:
      url: ***************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    ZXHIS3:
      url: ****************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    ZYEMR3:
      url: ****************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    RIS3:
      url: **************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000

    # 不良事件库（通用表单保存时使用）
    MSIRS:
      url: **********************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000

      # 科研数据库配置
    RDR_ResHisExchange:
      url: *****************************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000

      # 申请单
    Apply:
      url: ****************************************************
      username: rjhis
      password: Rjsoft222@SunHealth
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000

    CA:
      url: *************************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000

      # 医技预约数据库配置
  #      YLSYS_NEW:
  #        url: *********************************************************
  #        username: his
  #        password: lqaz2wsx
  #        driver-class-name: com.p6spy.engine.spy.P6SpyDriver
  #      LISDATABASE:
  #        url: **********************************************************
  #        username: rjhis
  #        password: rjhis
  #        driver-class-name: com.p6spy.engine.spy.P6SpyDriver

  cache:
    type: redis
    cache-names: SysConfig
    redis:
      time-to-live: 300s
      use-key-prefix: false
  redis:
    host: **********
    port: 6379
    password: 12345
    timeout: 30000ms

logging:
  level.root: info
  level.com.rjsoft.outPatient.infrastructure.repository: info
  config: classpath:logback-spring.xml

wsdl:
  gzdService:
    url: http://**********:8025/GzdService.asmx?wsdl
app:
  cache:
    queryurl: http://localhost:8088/query
    tabletail: _TEST222
  usecache: 1