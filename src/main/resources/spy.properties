module.log=com.p6spy.engine.logging.P6LogFactory,com.p6spy.engine.outage.P6OutageFactory

# ???????????,???com.p6spy.engine.spy.P6SpyFactory
#modulelist=com.p6spy.engine.spy.P6SpyFactory,com.p6spy.engine.logging.P6LogFactory,com.p6spy.engine.outage.P6OutageFactory


# ??JDBC driver , ??? ?? ?? ????
driverlist=com.microsoft.sqlserver.jdbc.SQLServerDriver

# ?????? ?? flase
#autoflush=false

# ??SimpleDateFormat???? ????
#dateformat=

# ???????? ??flase
#stacktrace=false

# ?? stacktrace=true?????????????????
#stacktraceclass=

# ????????????????
#reloadproperties=false

# ??????????????????:? ??60s
#reloadpropertiesinterval=60

# ?? Log ? appender????
appender=com.p6spy.engine.spy.appender.Slf4JLogger
#appender=com.p6spy.engine.spy.appender.StdoutLogger
#appender=com.p6spy.engine.spy.appender.FileLogger

# ?? Log ???? ?? spy.log
#logfile=spy.log

# ????????? Log???? false ?????????? ??true
#append=true

# ????????  ???com.p6spy.engine.spy.appender.SingleLineFormat , ???? ??????
#logMessageFormat=com.p6spy.engine.spy.appender.SingleLineFormat
# ?????  com.p6spy.engine.spy.appender.CustomLineFormat ????????, ????%(currentTime)|%(executionTime)|%(category)|connection%(connectionId)|%(sqlSingleLine)
# ??????:
#   %(connectionId)            connection id
#   %(currentTime)             ????
#   %(executionTime)           ????
#   %(category)                ????
#   %(effectiveSql)            ???SQL ??
#   %(effectiveSqlSingleLine)  ???SQL ?????
#   %(sql)                     ?????SQL????????
#   %(sqlSingleLine)           ?????SQL???????? ?????
#customLogMessageFormat=%(currentTime)|%(executionTime)|%(category)|connection%(connectionId)|%(sqlSingleLine)
#customLogMessageFormat=%(currentSqlService)|%(executionTime)|%(category)|connection%(connectionId)|%(sqlSingleLine)
logMessageFormat=com.rjsoft.common.p6spy.TyFormattingStrategy
# date???????????????? ??dd-MMM-yy
databaseDialectDateFormat=yyyy-MM-dd-MMM

# boolean???????????????? ??boolean ???numeric
#databaseDialectBooleanFormat=boolean

# ????jmx???? ??true
#jmx=true

# ??jmx???true ????jmx???????? ????
# com.p6spy(.<jmxPrefix>)?:name=<optionsClassName>
#jmxPrefix=

# ?????? ??false
#useNanoTime=false

# ????? JNDI
#realdatasource=/RealMySqlDS
# ????? datasource class
#realdatasourceclass=com.mysql.jdbc.jdbc2.optional.MysqlDataSource

# ????????????? ? k=v ???? ? ?? ??
#realdatasourceproperties=port;3306,serverName;myhost,databaseName;jbossdb,foo;bar

# jndi?????
# ?? JNDI ???? NamingContextFactory?
#jndicontextfactory=org.jnp.interfaces.NamingContextFactory
# ?? JNDI ???????? URL?
#jndicontextproviderurl=localhost:1099
# ?? JNDI ?????????????????
#jndicontextcustom=java.naming.factory.url.pkgs;org.jboss.naming:org.jnp.interfaces

# ???????? ??false? ?????????????? include/exclude/sqlexpression
#filter=false

# ?? Log ??????????????? ????
#include=
# ?? Log ??????????????? ????
#exclude=

# ?? Log ?? SQL ???????  ????
#sqlexpression=

#?????? Log ???????????: error, info, batch, debug, statement,
#commit, rollback, result and resultset are valid values
# (?? info,debug,result,resultset,batch)
excludecategories=info,debug,result,resultset,batch

# ?????????
# (default is false)
#excludebinary=false

# P6Log ???????????? (??????)??????????????? Log? ???0
#executionThreshold=

# P6Outage ??????????????? ??false
# outagedetection=true|false
# ?????SQL??
outagedetection=true

# P6Outage ???????????? ??????)???????????????? Log? ??30s
# outagedetectioninterval=integer time (seconds)
# ?SQL???? ?
outagedetectioninterval=2

# ????p6spy driver????
deregisterdrivers=true

# ????
dateformat=yyyy-MM-dd HH:mm:ss