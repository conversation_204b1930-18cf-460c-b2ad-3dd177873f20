server:
  port: 18848
  servlet.context-path: /outPatient
  use-auto-translate: true
  debug-log: false
  exception-path: com.rjsoft

his:
  insuranceApi: http://localhost:18842
  insuranceFyApi: http://localhost:18842
  appointmentApi: http://**********:8888
  appointmentBranchApi: http://**********:8888
  wxAppointmentApi: http://**********:8888
  wxAppointmentBranchApi: http://**********:8888
  gzdServiceAdd: http://**********:8025/GzdService.asmx/Add
  gzdServiceAddJgy: http://**********:8025/GzdService.asmx/AddJGY
  gzdServiceDelete: http://**********:8025/GzdService.asmx/Delete
  gzdServiceDeleteJgy: http://**********:8025/GzdService.asmx/DeleteJGY
  gzdServiceAddFY: http://**********:8025/GzdService.asmx/Add
  gzdServiceAddJgyFY: http://**********:8025/GzdService.asmx/AddJGY
  gzdServiceDeleteFY: http://**********:8025/GzdService.asmx/Delete
  gzdServiceDeleteJgyFY: http://**********:8025/GzdService.asmx/DeleteJGY
  zkGetInspectInfo: http://***********:990/DataService.asmx/GetInspectInfo
  zkGetDiseInfo: http://***********:990/DataService.asmx/GetDiseInfo
  zkGetInspectionAnalysisResults: http://***********:990/DataService.asmx/GetInspectionAnalysisResults
  zkGetMultiReferenceValue: http://***********:990/DataService.asmx/GetMultiReferenceValue
  zkPostDateAnalysis: http://***********:990/DataService.asmx/PostDateAnalysis
  zkPostDiseaseAnalysis: http://***********:990/DataService.asmx/PostDiseaseAnalysis
  zkPostAppExit: http://***********:990/DataService.asmx/PostAppExit
  msgCenterApi: http://**********:8006
  outPatientUrls: **********:18846;**********:18846
  inPatientApi: http://**********:8090/io/diseaseProof/getInPatientInfo
  IsHistoryVersion: false
  IsSeriousIllness: true
  OldMZYSIP: **********
  drugService: http://**********:18888/drug         #药品服务地址前缀
  workflowService: http://localhost:8080/engine-rest/process-definition/key


  linkingPay:
    apiUrl: http://************:8082/LopSdkPay/Pay/LopSDK
    appId: LCOP20180410224309266

apply:
  enableJudgeItemExists: true
  url: http://*********:8088

#ignite缓存地址
#ignite:
#  base-url: http://*********:19071
#  query-url: /query

mzys:
  recipe:
    drug:
      ## 处方最大天数限制
      maxDays: 999
      maxDose: 999
    nonDrug:
      maxQuantity: 100
  averageMonthlyFee:
    dateBenchmark: 22

mybatis:
  configuration:
    map-underscore-to-camel-case: false
    cache-enabled: false
    lazyLoadingEnabled: true
    multipleResultSetsEnabled: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
hikari:
  connectionTimeout: 20000
  maximumPoolSize: 30
  # minimumIdle: 10 先关了它，有问题再打开
  idleTimeout: 30000
  maxLifetime: 1800000
  poolName: YourAppDBConnectionPool
spring:
  rabbitmq:
    host: **********
    port: 5672
    username: MsgCenter
    password: MsgCenter
    virtual-host: /
  jackson:
    #date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  datasource:
    primary:
      url: jdbc:p6spy:sqlserver://*********;DatabaseName=HISDB
      username: rjhis
      password: Rjsoft@2023
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    HISDB:
#      url: jdbc:p6spy:sqlserver://**********;DatabaseName=HISDB
#      username: rjhis
#      password: Rjsoft222@SunHealth
      url: jdbc:p6spy:sqlserver://*********;DatabaseName=HISDB
      username: rjhis
      password: Rjsoft@2023
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000

    # 总院数据库配置
    RJCONFIGER:
      url: jdbc:p6spy:sqlserver://*********;DatabaseName=RJ_Configer
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    # 总院数据库配置
    MZEMR:
      url: jdbc:p6spy:sqlserver://*********;DatabaseName=MzEmr
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    MZYS:
      url: jdbc:p6spy:sqlserver://*********;DatabaseName=Mzys
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    ZXHIS:
      url: jdbc:p6spy:sqlserver://*********;DatabaseName=ZXHIS
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    ZYEMR:
      url: jdbc:p6spy:sqlserver://*********;DatabaseName=Zyemr
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    RIS:
      url: jdbc:p6spy:sqlserver://*********;DatabaseName=Ris
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    # 报告中心
    REPORTS:
      url: ******************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000

    # 分院数据库配置
    RJCONFIGER3:
      url: jdbc:p6spy:sqlserver://**********;DatabaseName=RJ_Configer
      username: rjhis
      password: Rjsoft106@SunHealth
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    MZYS3:
      url: ***************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    ZXHIS3:
      url: ****************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    ZYEMR3:
      url: ****************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000
    RIS3:
      url: **************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000

    # 不良事件库（通用表单保存时使用）
    MSIRS:
      url: **********************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000

      # 科研数据库配置
    RDR_ResHisExchange:
      url: *****************************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000

      # 申请单
    Apply:
      url: jdbc:p6spy:sqlserver://*********;DatabaseName=HISDB
      username: rjhis
      password: Rjsoft@2023
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000

    CA:
      url: *************************************************************
      username: rjhis
      password: rjhis
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      min-idle: 20
      max-active: 60
      max-wait: 60000

      # 医技预约数据库配置
  #      YLSYS_NEW:
  #        url: jdbc:p6spy:sqlserver://***********;DatabaseName=ylsys_New
  #        username: his
  #        password: lqaz2wsx
  #        driver-class-name: com.p6spy.engine.spy.P6SpyDriver
  #      LISDATABASE:
  #        url: jdbc:p6spy:sqlserver://**********;DatabaseName=lisdatabase
  #        username: rjhis
  #        password: rjhis
  #        driver-class-name: com.p6spy.engine.spy.P6SpyDriver

  cache:
    type: redis
    cache-names: SysConfig
    redis:
      time-to-live: 300s
      use-key-prefix: false
  redis:
    host: *********
#    host: 127.0.0.1
    port: 6379
    password: 12345
    timeout: 30000ms
    lettuce:
      pool:
        max-active: 12 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20   # 连接池中的最大空闲连接
        min-idle: 0   # 连接池中的最小空闲连接
        max-wait: -1ms # 连接池最大阻塞等待时间（使用负值表示没有限制）

logging:
  level.root: debug
  level.com.rjsoft.outPatient.infrastructure.repository: debug
  config: classpath:logback-spring.xml

wsdl:
  gzdService:
    url: http://**********:8025/GzdService.asmx?wsdl
#    使用rj-common1.2，必须指定下面的值
app:
  cache:
#    queryurl: http://localhost:8088/query
    queryurl: http://*********:19071/query
    tabletail: _TEST222
  usecache: 0
