<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <jmxConfigurator/>
    <property name="app-name" value="outpatient" />
    <property name="LOGS" value="log-center" />

    <appender name="consoleAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <!--<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} TRACE_ID:[%X{TRACE_ID}] REMOTE:[%X{REMOTE}]  CONTROL:[%X{CONTROL}]  ACTION:[%X{ACTION}]  %highlight(%-5level) - %highlight(%msg) %n</pattern>-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %highlight(%-5level) %cyan(%logger{50}:%L) - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 按照每天生成日志文件 -->
    <appender name="infoFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
<!--        <filter class="ch.qos.logback.classic.filter.LevelFilter">-->
<!--            <level>INFO</level>-->
<!--            <onMatch>ACCEPT</onMatch>-->
<!--            <onMismatch>DENY</onMismatch>-->
<!--        </filter>-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名 -->
            <FileNamePattern>${LOGS}/${app-name}-info.%d.%i.log</FileNamePattern>
            <!--日志文件保留天数 -->
            <MaxHistory>30</MaxHistory>
            <maxFileSize>10MB</maxFileSize>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50}:%L - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="errorFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名 -->
            <FileNamePattern>${LOGS}/${app-name}-error.%d.%i.log</FileNamePattern>
            <!--日志文件保留天数 -->
            <MaxHistory>30</MaxHistory>
            <maxFileSize>10MB</maxFileSize>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50}:%L - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--连接数据库配置-->
    <appender name="dbAppender" class="com.rjsoft.common.log.LogDbAppender">
        <connectionSource class="ch.qos.logback.core.db.DataSourceConnectionSource">
            <dataSource class="org.apache.commons.dbcp.BasicDataSource">
                <driverClassName>com.microsoft.sqlserver.jdbc.SQLServerDriver</driverClassName>
                <url>****************************************************</url>
                <username>rjhis</username>
                <password>Rjsoft222@SunHealth</password>
            </dataSource>
        </connectionSource>
    </appender>
    <!-- 异步日志记录 -->
    <appender name="asyncAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="dbAppender" />
        <includeCallerData>true</includeCallerData>
        <discardingThreshold >0</discardingThreshold>
        <!-- 更改默认的队列的深度,默认值为256 -->
        <queueSize>500</queueSize>
    </appender>


    <!--监控sql日志输出 -->
    <logger name="jdbc.sqlonly" level="OFF" additivity="false">
        <appender-ref ref="consoleAppender" />
    </logger>

    <logger name="jdbc.resultset" level="ERROR" additivity="false">
        <appender-ref ref="consoleAppender" />
    </logger>

    <logger name="jdbc.resultsettable" level="OFF" additivity="false">
        <appender-ref ref="consoleAppender" />
    </logger>

    <logger name="jdbc.connection" level="OFF" additivity="false">
        <appender-ref ref="consoleAppender" />
    </logger>

    <logger name="jdbc.sqltiming" level="INFO" additivity="false">
        <appender-ref ref="consoleAppender" />
        <appender-ref ref="infoFileAppender" />
    </logger>

    <logger name="jdbc.audit" level="OFF" additivity="false">
        <appender-ref ref="consoleAppender" />
    </logger>

    <root level="DEBUG" additivity="false">
        <appender-ref ref="consoleAppender"/>
        <!-- 异步数据库-->
        <appender-ref ref="infoFileAppender"/>
    </root>
</configuration>